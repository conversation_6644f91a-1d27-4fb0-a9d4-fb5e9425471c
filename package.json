{"private": true, "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@inertiajs/inertia": "^0.11.0", "@inertiajs/inertia-vue3": "^0.6.0", "@inertiajs/progress": "^0.2.7", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-vue": "^3.0.0", "autoprefixer": "^10.4.12", "axios": "^1.8.2", "laravel-vite-plugin": "^0.7.2", "lodash": "^4.17.19", "postcss": "^8.4.18", "tailwindcss": "^3.2.1", "vite": "^3.2.11", "vue": "3"}, "dependencies": {"@bugsnag/js": "^7.20.2", "@bugsnag/plugin-vue": "^7.19.0", "@chenfengyuan/vue-countdown": "2", "@headlessui/vue": "^1.7.9", "@splidejs/splide": "^4.1.4", "@splidejs/splide-extension-auto-scroll": "^0.5.3", "@splidejs/splide-extension-video": "^0.8.0", "@splidejs/vue-splide": "^0.6.12", "@vuepic/vue-datepicker": "^3.6.8", "maska": "^2.1.6", "notivue": "^2.4.5", "vite-plugin-commonjs": "^0.6.1", "vue-star-rating": "^2.1.0", "vue-tel-input": "^5.12.0", "vue3-text-clamp": "^0.1.1"}}