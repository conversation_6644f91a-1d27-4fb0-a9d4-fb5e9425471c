<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Payment Token Configuration
    |--------------------------------------------------------------------------
    |
    | Bu konfigürasyon SMS ödeme sistemi için token ayarlarını içerir.
    | Token'ların formatı, geçerlilik süresi ve güvenlik ayarları.
    |
    */

    'token' => [
        'length' => env('PAYMENT_TOKEN_LENGTH', 32),
        'expires_in_hours' => env('PAYMENT_TOKEN_EXPIRES_HOURS', 24),
        'allowed_attempts' => env('PAYMENT_TOKEN_ATTEMPTS', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment URLs
    |--------------------------------------------------------------------------
    |
    | Ödeme işlemi sonrasında yönlendirme URL'leri.
    |
    */

    'urls' => [
        'success_redirect' => env('PAYMENT_SUCCESS_URL', '/'),
        'error_redirect' => env('PAYMENT_ERROR_URL', '/'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Ödeme işlemleri için rate limiting ayarları.
    |
    */

    'rate_limits' => [
        'token_check_per_minute' => env('PAYMENT_RATE_TOKEN_CHECK', 60),
        'payment_process_per_minute' => env('PAYMENT_RATE_PROCESS', 5),
        'success_page_per_minute' => env('PAYMENT_RATE_SUCCESS', 10),
    ],
];
