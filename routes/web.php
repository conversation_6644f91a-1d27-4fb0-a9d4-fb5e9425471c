<?php

use App\Http\Controllers\BrandController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ProductController;
use App\Services\Hopi\Hopi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Inertia\Inertia;
use App\Http\Controllers\SurveyController;

Route::get('/kategoriler/tum-urunler-demo', function (Request $request) {

    // $category = Cache::tags(['kategori'])->remember(
    //     key: 'get_category_items_all_' . md5($request->fullUrl()),
    //     ttl: now()->addMinutes(60 * 24),
    //     callback: function () use ($request) {
    //         if ($request->has('filter')) {
    //             $url = config('app.product_api_url') . 'category/all?page=' . $request->query('page') . '&';
    //             foreach ($request->get('filter', []) as $key => $value) {
    //                 if ($value)
    //                     $url .= 'filter[' . $key . ']' . '=' . $value . '&';
    //             }
    //             if ($request->get('orderBy')) {
    //                 $url .= 'orderBy=' . $request->get('orderBy') . '&';
    //             }
    //             $category = \Illuminate\Support\Facades\Http::timeout(80)->get($url)->json();
    //         } else {
    //             $category = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'category/all?page=' . $request->query('page'))->json();
    //         }
    //         return $category;
    //     }
    // );

    if ($request->has('filter')) {
        $url = config('app.product_api_url') . 'category/all?page=' . $request->query('page') . '&';
        foreach ($request->get('filter', []) as $key => $value) {
            if ($value)
                $url .= 'filter[' . $key . ']' . '=' . $value . '&';
        }
        if ($request->get('orderBy')) {
            $url .= 'orderBy=' . $request->get('orderBy') . '&';
        }
        $category = \Illuminate\Support\Facades\Http::timeout(80)->get($url)->json();
    } else {
        $category = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'category/all?page=' . $request->query('page'))->json();
    }

    $category['items']['pagination']['links'] = collect($category['items']['pagination']['links'])->map(function ($link) use ($request) {
        $link['url'] = Str::of($link['url'])->replace(config('app.product_api_url') . 'category/all', config('app.app_url') . '/kategoriler/tum-urunler') . '&' . prepare_filter_query($request->get('filter', []));

        if ($request->get('orderBy')) {
            $link['url'] .= '&orderBy=' . $request->get('orderBy');
        }

        return $link;
    })->toArray();

    // Kullanıcı login ise favori ürünleri getir ve kategori ürünleri ile kesişenlere bu durumu işle
    if (session('user.isUserLoggedIn')) {
        $favourites = cache_http(config('app.api_url') . 'auth/user/favourites', 'post', session('user.token'));
        $favourites = collect($favourites)->pluck('product_id')->toArray();
        $category['items']['data'] = collect($category['items']['data'])->map(function ($product) use ($favourites) {
            $product['favourite'] = in_array($product['id'], $favourites);
            return $product;
        })->toArray();
    }
    $discounted = Cache::tags(['anasayfa'])->get('all_discounted');
    if (!$discounted) {
        //$discounted = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'discounted?page=1&orderBy=yeniden-eskiye')->json();
        $discounted = Http::get(config('app.product_api_url') . 'tag/anasayfaindirim')->json();
        Cache::tags(['anasayfa'])->put('all_discounted', $discounted, now()->addMinutes(60 * 24)); // 1 gün cache
    }

    return Inertia::render('CategoryGridDemo', [
        'discounted' => $discounted,
        'category' => $category,
        'brands' => $category['filters']['brands'],
        // 'categories' => collect($category['filters']['category'])->sortBy('attribute_data.name.tr'),
        'categories' => collect(data_get($category, 'filters.category')), //->sortBy('collection_name'),
        'pageType' => 'search',
        'canonical' => '/kategoriler/tum-urunler',
        'filters' => $request->get('filter', []),
        'orderBy' => $request->get('orderBy', ""), // Empty string means no order by
        'newTypeSeoDefinition' => true,
        'title' => 'Yüzlerce Son Teknoloji Ürünü İncele, Hemen Kirala',
        'meta_description' => 'Telefondan Akıllı Süpürgeye Birçok Teknoloji Ürününü Kolayca Kiralayabildiğin Kiralabunu! Tüm Ürünleri İncelemek İçin Hemen Tıkla, Keşfet, Kirala'
    ]);
});

Route::get('/', [\App\Http\Controllers\HomeController::class, 'index'])->name('welcome');

Route::get('/giris-yap', [\App\Http\Controllers\Auth\LoginController::class, 'login'])->name('giris-yap');
Route::post('/giris-yap', [\App\Http\Controllers\Auth\LoginController::class, 'loginAttempt'])->name('attemptLogin');
Route::get('/auth/redirect', [\App\Http\Controllers\Auth\LoginController::class, 'googleRedirect'])->name('google-redirect');
Route::get('/auth/google/callback', [\App\Http\Controllers\Auth\LoginController::class, 'googleLogin'])->name('google-callback');

Route::post('coupon', function (Request $request) {
    if (!session('user.isUserLoggedIn')) {
        return redirect()->back()->withErrors('Kupon kodu kullanabilmek için giriş yapmalısınız.');
    }

    $res = Http::withToken(session('user.token'))->put(config('app.api_url') . 'auth/user/cart/update', $request->toArray())->json();

    if (data_get($res, 'message') == 'Validation failed') {
        return redirect()->back()->withInput()->withErrors($res['errors']);
    }
});

Route::get('/tags/{tag}', function (Request $request, $tag) {
    $tagContent = Cache::tags(['tags'])->get('get_tag_items_1tl_' . md5($request->fullUrl()));

    if (!$tagContent) {
        $url = config('app.product_api_url') . 'tag/' . $tag . '?page=' . $request->query('page');
        if ($request->has('filter')) {
            foreach ($request->get('filter', []) as $key => $value) {
                if ($value)
                    $url .= 'filter[' . $key . ']' . '=' . $value . '&';
            }
            $tagContent = \Illuminate\Support\Facades\Http::timeout(80)->get($url)->json();
            Cache::tags(['tags'])->put('get_category_items_' . md5($request->fullUrl()), $tagContent, now()->addMinutes(60 * 24)); // 1 gün cache
        } else {
            $tagContent = \Illuminate\Support\Facades\Http::timeout(80)->get($url)->json();
            Cache::tags(['tags'])->put('get_category_items_' . md5($request->fullUrl()), $tagContent, now()->addMinutes(60 * 24)); // 1 gün cache
        }
    }

    // Loop over the category and change pagination links
    $tagContent['items']['pagination']['links'] = collect($tagContent['items']['pagination']['links'])->map(function ($link) use ($request) {
        $link['url'] = Str::of($link['url'])->replace(config('app.product_api_url') . 'tag', config('app.app_url') . '/tags') . '&' . http_build_query($request->get('filter', []));
        return $link;
    })->toArray();

    return Inertia::render('CategoryGridDemo', [
        'category' => $tagContent,
        'pageType' => 'search',
        'paginateType' => 'tag',
        'brands' => $tagContent['filters']['brands'],
        'categories' => $tagContent['filters']['category'],
        'filters' => [],
    ]);
});

Route::get('/marka/{brand}', [BrandController::class, 'index']);

Route::get('/hesabim', function () {
    return Inertia::render('MyAccount', []);
});

//Route::get('/nasil-calisir', function () {
//    return Inertia::render('About', []);
//});

Route::get('/hakkimizda', function () {
    return Inertia::render('About2', []);
});

Route::get('/tekno-girisim-rozeti', function () {
    return Inertia::render('TeknoGirisimRozeti', []);
});

Route::get('/easycep-kiralabunu', function () {
    $mostRentedProducts = Cache::tags(['anasayfa'])->get('most_rented_products');
    if (!$mostRentedProducts) {
        $mostRentedProducts = Http::get(config('app.product_api_url') . 'tag/encokkiralanan')->json();
        Cache::tags(['anasayfa'])->put('most_rented_products', $mostRentedProducts, now()->addMinutes(60 * 24)); // 1 gün cache
    }

    return Inertia::render('EasycepKiralabunu', [
        'title' => 'Teknolojide Kiralama Dönemi | EasyCep & Kiralabunu',
        'meta_description' => 'EasyCep mağazalarında ihtiyacın olan ürünü kiralayarak kullanabilirsin. EasyCep & Kiralabunu iş birliği ile teknoloji ürünlerini kolayca kirala.',
        'mostRentedProducts' => $mostRentedProducts
    ]);
});

Route::get('/surdurulebilirlik', function () {
    return Inertia::render('Sustainability', [
        'title' => 'Kiralabunu Sürdürülebilirlik ve Döngüsel Ekonomi',
        'meta_description' => 'Yeni elektronik ürünler satın alma yerine, finansal tasarruf sağlamak ve karbon emisyonlarını düşürmek için sürdürülebilirlik.'
    ]);
});

// Hash parametreli anket sayfası - önce tanımlanmalı
Route::get('/kiralama-anketi/{hash}', [\App\Http\Controllers\SurveyController::class, 'show'])->name('survey.show');
Route::post('/kiralama-anketi/{hash}', [\App\Http\Controllers\SurveyController::class, 'submitHashBasedSurvey'])->name('survey.submit');
// Aktif Kiralama Anketi Routes
Route::get('/aktif-kiralama-anketi/{hash}', [SurveyController::class, 'showActive'])->name('active-rent-survey.show');
Route::post('/aktif-kiralama-anketi/{hash}', [SurveyController::class, 'submitActive'])->name('active-rent-survey.submit');

Route::post('/aktif-kiralama-anketi-old', function (Request $request) {
    // Form validasyonu - koşullu validasyon için özel kurallar
    $rules = [
        'rentalExperience' => 'required|string',
        'rentalExperienceDetails' => 'nullable|string|max:1000',
        'technicalIssues' => 'required|string',
        'technicalIssuesDetails' => 'nullable|string|max:1000',
        'supportNeeded' => 'required|string',
        'communicationRating' => 'required|integer|between:1,5',
        'howDidYouHear' => 'required|string',
        'howDidYouHearOther' => 'nullable|string|max:500',
        'additionalComments' => 'nullable|string|max:1000',
        'wantsSpecialOffers' => 'boolean'
    ];

    // Eğer "Memnun değilim" seçildiyse detay açıklaması zorunlu
    if ($request->get('rentalExperience') === 'Memnun değilim') {
        $rules['rentalExperienceDetails'] = 'required|string|max:1000';
    }

    // Eğer "Evet, hala çözülmedi" seçildiyse detay açıklaması zorunlu
    if ($request->get('technicalIssues') === 'Evet, hala çözülmedi') {
        $rules['technicalIssuesDetails'] = 'required|string|max:1000';
    }

    // Eğer "Diğer" seçildiyse açıklama zorunlu
    if ($request->get('howDidYouHear') === 'Diğer') {
        $rules['howDidYouHearOther'] = 'required|string|max:500';
    }

    $messages = [
        'rentalExperience.required' => 'Kiralama deneyimi seçimi zorunludur',
        'technicalIssues.required' => 'Teknik sorun durumu seçimi zorunludur',
        'supportNeeded.required' => 'Destek ihtiyacı seçimi zorunludur',
        'communicationRating.required' => 'İletişim memnuniyeti puanı zorunludur',
        'communicationRating.between' => 'İletişim memnuniyeti puanı 1-5 arasında olmalıdır',
        'howDidYouHear.required' => 'Nereden duydunuz seçimi zorunludur',
        'rentalExperienceDetails.max' => 'Deneyim detayları en fazla 1000 karakter olabilir',
        'technicalIssuesDetails.max' => 'Teknik sorun detayları en fazla 1000 karakter olabilir',
        'howDidYouHearOther.max' => 'Diğer açıklaması en fazla 500 karakter olabilir',
        'additionalComments.max' => 'Ek yorumlar en fazla 1000 karakter olabilir',
        'rentalExperienceDetails.required' => 'Memnun olmama sebebinizi açıklamanız gereklidir',
        'technicalIssuesDetails.required' => 'Yaşadığınız sorunu detaylandırmanız gereklidir',
        'howDidYouHearOther.required' => 'Lütfen nereden duyduğunuzu belirtiniz'
    ];

    $validator = Validator::make($request->all(), $rules, $messages);

    if ($validator->fails()) {
        return redirect()->back()
            ->withErrors($validator)
            ->withInput();
    }

    try {
        // Anket verilerini log'a kaydet
        \Log::info('Active Rental Survey Response Submitted', [
            'rental_experience' => $request->get('rentalExperience'),
            'rental_experience_details' => $request->get('rentalExperienceDetails'),
            'technical_issues' => $request->get('technicalIssues'),
            'technical_issues_details' => $request->get('technicalIssuesDetails'),
            'support_needed' => $request->get('supportNeeded'),
            'communication_rating' => $request->get('communicationRating'),
            'how_did_you_hear' => $request->get('howDidYouHear'),
            'how_did_you_hear_other' => $request->get('howDidYouHearOther'),
            'additional_comments' => $request->get('additionalComments'),
            'wants_special_offers' => $request->get('wantsSpecialOffers', false),
            'ip_address' => $request->ip(),
            'submitted_at' => now()
        ]);

        return redirect()->back()->with('success', 'Anket yanıtlarınız başarıyla kaydedildi. Teşekkürler!');
    } catch (\Exception $e) {
        \Log::error('Active rental survey submission error: ' . $e->getMessage());

        return redirect()->back()->withErrors('Anket gönderilirken bir hata oluştu. Lütfen tekrar deneyin.');
    }
})->name('active-rent-survey.submit');

Route::get('/kampanyalar', function () {

    $discounted = Cache::tags(['anasayfa'])->get('all_discounted');
    if (!$discounted) {
        //$discounted = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'discounted?page=1&orderBy=yeniden-eskiye')->json();
        $discounted = Http::get(config('app.product_api_url') . 'tag/anasayfaindirim')->json();
        Cache::tags(['anasayfa'])->put('all_discounted', $discounted, now()->addMinutes(60 * 24)); // 1 gün cache
    }
    return Inertia::render('Campaigns', [
        'discounted' => $discounted
    ]);
});

Route::get('/kirala-satin-al', function (Request $request) {

    $discounted = Cache::tags(['anasayfa'])->get('all_discounted');
    if (!$discounted) {
        //$discounted = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'discounted?page=1&orderBy=yeniden-eskiye')->json();
        $discounted = Http::get(config('app.product_api_url') . 'tag/anasayfaindirim')->json();
        Cache::tags(['anasayfa'])->put('all_discounted', $discounted, now()->addMinutes(60 * 24)); // 1 gün cache
    }

    $currentPage = $request->query('page', 1);
    $perPage = 8;

    // Cache'den installment products verilerini getir (shuffle edilmiş hali)
    $allProducts = Cache::tags(['installment'])->get('installment_products_shuffled');

    if (!$allProducts) {
        $allProducts = [];
        $maxPages = 5; // Maksimum sayfa limiti

        // Tüm sayfaları direkt API'den çek - DOĞRU veri yapısı ile
        for ($page = 1; $page <= $maxPages; $page++) {
            $pageResponse = Http::get(config('app.product_api_url') . 'installment-products?page=' . $page)->json();

            // API'den gelen yapı: products.data array'i
            if (!empty($pageResponse['products']['data']) && count($pageResponse['products']['data']) > 0) {
                $allProducts = array_merge($allProducts, $pageResponse['products']['data']);
            } else {
                break; // Boş sayfa geldi, pagination sonu
            }
        }

        // Ürünleri karıştır (sadece cache kaydedilirken bir kere)
        shuffle($allProducts);

        // Cache'e kaydet - 24 saat
        Cache::tags(['installment'])->put('installment_products_shuffled', $allProducts, now()->addMinutes(60 * 24));
    }

    // Frontend pagination
    $total = count($allProducts);
    $offset = ($currentPage - 1) * $perPage;
    $paginatedProducts = array_slice($allProducts, $offset, $perPage);

    // Custom pagination links oluştur
    $lastPage = ceil($total / $perPage);
    $links = [];

    // Önceki link
    $links[] = [
        'url' => $currentPage > 1 ? '/kirala-satin-al?page=' . ($currentPage - 1) : null,
        'label' => '&laquo; Önceki',
        'active' => false
    ];

    // Sayfa numaraları
    for ($i = 1; $i <= $lastPage; $i++) {
        $links[] = [
            'url' => '/kirala-satin-al?page=' . $i,
            'label' => (string)$i,
            'active' => $i == $currentPage
        ];
    }

    // Sonraki link
    $links[] = [
        'url' => $currentPage < $lastPage ? '/kirala-satin-al?page=' . ($currentPage + 1) : null,
        'label' => 'Sonraki &raquo;',
        'active' => false
    ];

    // Son Eklenen Ürünler için ayrı veri seti (pagination'dan bağımsız)
    $latestProducts = array_slice($allProducts, 0, 16); // İlk 16 ürün sabit

    // Newtagged yapısını oluştur (pagination'lı)
    $newtagged = [
        'items' => [
            'data' => $paginatedProducts,
            'pagination' => [
                'links' => $links,
                'current_page' => $currentPage,
                'per_page' => $perPage,
                'total' => $total,
                'last_page' => $lastPage
            ]
        ]
    ];

    // Manuel pagination aktif - backend destekliyor ama meta bilgi yok


    return Inertia::render('RentBuy', [
        'discounted' => $discounted,
        'newtagged' => $newtagged,
        'latestProducts' => $latestProducts,
    ]);
});

Route::get('/kurumsal', function () {
    return Inertia::render('InstitutionalDemo', []);
});
Route::get('/kurumsal-demo', function () {
    return Inertia::render('InstitutionalDemo', []);
});
Route::get('/musteri-cozumlerimiz', function () {
    return Inertia::render('CustomerSolutions', []);
});
Route::get('/is-ortagi-cozumlerimiz', function () {
    return Inertia::render('PartnerSolutions', []);
});
Route::get('/kurumsal/kategori/{category}', [\App\Http\Controllers\İnstitutionalCategoryController::class, 'getMainCategory']);

Route::post('/kurumsal', function (Request $request) {

    $validator = Validator::make($request->all(), [
        'name' => 'required',
        'email' => 'required|email',
        'gsm' => 'required',
        'firmName' => 'required',
        'message' => 'required',
        'solutions' => 'required',
    ], [
        'name.required' => 'Ad Soyad alanı zorunludur.',
        'email.required' => 'E-posta alanı zorunludur.',
        'email.email' => 'E-posta alanı geçerli bir e-posta adresi olmalıdır.',
        'gsm.required' => 'Telefon alanı zorunludur.',
        'firmName.required' => 'Firma Adı alanı zorunludur.',
        'message.required' => 'Mesaj alanı zorunludur.',
        'solutions.required' => 'Çözüm seçeneği zorunludur.',
    ]);

    if ($validator->fails()) {
        return redirect()->back()
            ->withErrors($validator)
            ->withInput();
    }

    $res = \Illuminate\Support\Facades\Http::post(config('app.api_url') . 'business-request', $request->toArray())->json();
    if ($res['status'] == 'success') {
        return redirect()->back()->with('success', 'Mesajınız başarıyla gönderildi.');
    }
    return redirect()->back()->with('error', 'Bir hata oluştu. Lütfen tekrar deneyiniz.');
})->name('sendBusinessRequest');

Route::post('/kirala-satin-al', function (Request $request) {

    $validator = Validator::make($request->all(), [
        'name' => 'required',
        'email' => 'required|email',
        'gsm' => 'required',
        'firmName' => 'required',
        'message' => 'required',
        'solutions' => 'required',
    ], [
        'name.required' => 'Ad Soyad alanı zorunludur.',
        'email.required' => 'E-posta alanı zorunludur.',
        'email.email' => 'E-posta alanı geçerli bir e-posta adresi olmalıdır.',
        'gsm.required' => 'Telefon alanı zorunludur.',
        'firmName.required' => 'Firma Adı alanı zorunludur.',
        'message.required' => 'Mesaj alanı zorunludur.',
        'solutions.required' => 'Çözüm seçeneği zorunludur.',
    ]);

    if ($validator->fails()) {
        return redirect()->back()
            ->withErrors($validator)
            ->withInput();
    }

    $res = \Illuminate\Support\Facades\Http::post(config('app.api_url') . 'rent-buy-request', $request->toArray())->json();
    if ($res['status'] == 'success') {
        return redirect()->back()->with('success', 'Mesajınız başarıyla gönderildi.');
    }
    return redirect()->back()->with('error', 'Bir hata oluştu. Lütfen tekrar deneyiniz.');
})->name('rentBuyRequest');


Route::get('/nasil-calisir', function () {
    $allProducts = Cache::tags(['anasayfa'])->get('all_products');
    if (!$allProducts) {
        $allProducts = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'category/all?orderBy=yeniden-eskiye')->json();
        Cache::tags(['anasayfa'])->put('all_products', $allProducts, now()->addMinutes(60 * 24)); // 1 gün cache
    }
    return Inertia::render('AboutNew', [
        'allProducts' => $allProducts,
    ]);
});

Route::get('/kategori-detay', function () {
    $allProducts = Cache::tags(['anasayfa'])->get('all_products');
    if (!$allProducts) {
        $allProducts = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'category/all?orderBy=yeniden-eskiye')->json();
        Cache::tags(['anasayfa'])->put('all_products', $allProducts, now()->addMinutes(60 * 24)); // 1 gün cache
    }

    $sliders = Cache::tags(['anasayfa'])->get('sliders');
    if (!$sliders) {
        $sliders = Http::withToken(config('app.api_token'))->get(config('app.api_url') . 'sliders');
        //        ds($sliders->body())->label('main');
        $sliders = $sliders->json();
        Cache::tags(['anasayfa'])->put('sliders', $sliders, now()->addMinutes(60 * 24)); // 1 gün cache
    }

    return Inertia::render('CategoryDetail', [
        'allProducts' => $allProducts,
        'sliders' => $sliders,
    ]);
});

Route::get('/hesap-olustur', function () {
    return Inertia::render('CreateAccountDemo', []);
});

Route::post('/hesap-olustur', function (Request $request) {
    $res = \Illuminate\Support\Facades\Http::withToken(session()->getId())->post(config('app.api_url') . 'auth/register', $request->toArray())->json();
    if ($res['message'] == 'Registered') {
        $user = session('user');
        $user['isUserLoggedIn'] = true;
        $user['token'] = $res['data']['accessToken'];
        $user['user'] = $res['data']['user'];
        $user['status'] = 'Authenticated';
        $user['checkCart'] = true;
        session(['user' => $user]);

        //        $cookie = cookie('user', $res['data']['accessToken'], 48 * 60); // 2 gün login kalsın
        return redirect()->route('welcome'); //->withCookie($cookie);
    }

    return Inertia::render('CreateAccount', [
        'errors' => $res['errors'],
    ]);
});

Route::get('/sifremi-unuttum', function () {
    return Inertia::render('ForgotPassword', []);
});

Route::get('/sifremi-yenile', function () {
    return Inertia::render('ChangePassword', []);
});
Route::get('/404', function () {
    return Inertia::render('404', []);
});

Route::post('/sifremi-yenile', function (Request $request) {
    $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->put(config('app.api_url') . 'auth/user/update-password', $request->toArray())->json();
    return Inertia::render('ChangePassword', [
        'saveStatus' => true
    ]);
});

Route::get('/sms-onayi', function () {
    return Inertia::render('SMS', []);
});

Route::get('/form-popup', function () {
    return Inertia::render('FormPopup', []);
});
Route::get('get-mostrentedproducts', function () {
    $mostRentedProducts = Cache::tags(['anasayfa'])->get('most_rented_products');
    if (!$mostRentedProducts) {
        $mostRentedProducts = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'tag/encokkiralanan')->json();
        Cache::tags(['anasayfa'])->put('most_rented_products', $mostRentedProducts, now()->addMinutes(60 * 24)); // 1 gün cache
    }
    return response()->json($mostRentedProducts);
});

Route::get('/ara', function (Request $request) {

    $product = Cache::tags(['arama'])->get('get_search_items_' . md5($request->fullUrl()));

    if (!$product) {
        $product = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'search?term=' . $request->get('term') . '&page=' . $request->query('page'))->json();
        Cache::tags(['arama'])->put('get_search_items_' . md5($request->fullUrl()), $product, now()->addMinutes(60 * 24)); // 1 gün cache
    }

    // Loop over the category and change pagination links
    $product['items']['pagination']['links'] = collect($product['items']['pagination']['links'])->map(function ($link) use ($request) {
        //dump($link['url'], config('app.product_api_url') . 'search', config('app.app_url') . '/ara');
        //$link['url'] = Str::of($link['url'])->replace(config('app.product_api_url') . 'search', config('app.app_url') . '/ara')->replace('query', 'term') . '&term=' . $request->get('term') . http_build_query($request->get('filter', []));
        $link['url'] = Str::of($link['url'])->replace(config('app.product_api_url') . 'search', config('app.app_url') . '/ara')->replace('query', 'term') . '&term=' . $request->get('term') . '&' . prepare_filter_query($request->get('filter', []));
        return $link;
    })->toArray();

    $mostRentedProducts = [];
    if (count($product['items']['data']) == 0) {
        $mostRentedProducts = Cache::tags(['anasayfa'])->get('most_rented_products');
        if (!$mostRentedProducts) {
            $mostRentedProducts = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'tag/encokkiralanan')->json();
            Cache::tags(['anasayfa'])->put('most_rented_products', $mostRentedProducts, now()->addMinutes(60 * 24)); // 1 gün cache
        }
    }

    return Inertia::render('CategoryGridDemo', [
        'category' => $product,
        'pageType' => 'search',
        'brands' => $product['filters']['brands'],
        'categories' => $product['filters']['category'],
        'filters' => $request->get('filter', []),
        'canonical' => '/ara',
        'orderBy' => $request->get('orderBy', ""), // Empty string means no order by
        'mostRentedProducts' => Inertia::lazy(fn() => $mostRentedProducts),
    ]);
})->name('searchResults');

Route::get('/profile-menu', function () {
    return Inertia::render('ProfileMenu', []);
});

Route::get('/urun/{productId}', [ProductController::class, 'index'])->name('product.index'); // ürün detay sayfası (is_installment değerine göre otomatik component seçimi)
Route::get('/urun/{productId}/demo', [ProductController::class, 'indexDemo']);
Route::post('/lead/rent-and-buy', [\App\Http\Controllers\LeadController::class, 'rentandbuy'])->name('lead.rentandbuy');

Route::post('/favourite/{pid}/toogle', function (Request $request, $pid) {
    Cache::tags(['http'])->forget(config('app.api_url') . 'auth/user/favourites' . session('user.token'));
    return $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->post(config('app.api_url') . 'auth/user/favourite/' . $pid . '/toogle')->json();
})->middleware(['checkCookieAuth']);

Route::get('/kategoriler/tum-urunler-eski', function (Request $request) {

    //    $category = Cache::tags(['kategori'])->get('get_category_items_all_' . md5($request->fullUrl()));
    $category = Cache::tags(['kategori'])->remember(
        key: 'get_category_items_all_' . md5($request->fullUrl()),
        ttl: now()->addMinutes(60 * 24),
        callback: function () use ($request) {
            if ($request->has('filter')) {
                $url = config('app.product_api_url') . 'category/all?page=' . $request->query('page') . '&';
                foreach ($request->get('filter', []) as $key => $value) {
                    if ($value)
                        $url .= 'filter[' . $key . ']' . '=' . $value . '&';
                }
                if ($request->get('orderBy')) {
                    $url .= 'orderBy=' . $request->get('orderBy') . '&';
                }
                $category = \Illuminate\Support\Facades\Http::timeout(80)->get($url)->json();
            } else {
                $category = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'category/all?page=' . $request->query('page'))->json();
            }
            return $category;
        }
    );

    //    ray('get_category_items_all _' . md5($request->fullUrl()), $category);
    //    ds($url = config('app.product_api_url') . 'category/all?page=' . $request->query('page') . '&');

    //    if (!$category) {
    //        if ($request->has('filter')) {
    //            $url = config('app.product_api_url') . 'category/all?page=' . $request->query('page') . '&';
    //            foreach ($request->get('filter', []) as $key => $value) {
    //                if ($value)
    //                    $url .= 'filter[' . $key . ']' . '=' . $value . '&';
    //            }
    //            //ray($url);
    //            //dd($url);
    //            $category = \Illuminate\Support\Facades\Http::timeout(80)->get($url)->json();
    //        } else {
    ////            $category = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'category/all')->json();
    ////            cache(['get_category_items_all' => $category], now()->addMinutes(60 * 24)); // 1 gün cache
    //
    //            $category = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'category/all?page=' . $request->query('page'))->json();
    //            Cache::tags(['kategori'])->put('get_category_items_all_' . md5($request->fullUrl()), $category, now()->addMinutes(60 * 24)); // 1 gün cache
    //        }
    //    }

    // Loop over the category and change pagination links
    $category['items']['pagination']['links'] = collect($category['items']['pagination']['links'])->map(function ($link) use ($request) {
        //    $category['links'] = collect($category['items']['links'])->map(function ($link) use ($request) {
        //$link['url'] = \Illuminate\Support\Str::of($link['url'])->replace('http://demo-store.test/api/category/all', config('app.app_url') . '/kategoriler/tum-urunler') . '&' . http_build_query($request->get('filter', []));
        //dd(config('app.product_api_url') . 'category/all', $link['url'], config('app.app_url') . '/kategoriler/tum-urunler');

        //dd('http://demo-store.test/api/category/all?page=1', config('app.product_api_url') . 'category/all', config('app.app_url') . '/kategoriler/tum-urunler' . '&' . prepare_filter_query($request->get('filter', [])));
        $link['url'] = Str::of($link['url'])->replace(config('app.product_api_url') . 'category/all', config('app.app_url') . '/kategoriler/tum-urunler') . '&' . prepare_filter_query($request->get('filter', []));

        if ($request->get('orderBy')) {
            $link['url'] .= '&orderBy=' . $request->get('orderBy');
        }

        return $link;
    })->toArray();

    // Kullanıcı login ise favori ürünleri getir ve kategori ürünleri ile kesişenlere bu durumu işle
    if (session('user.isUserLoggedIn')) {
        $favourites = cache_http(config('app.api_url') . 'auth/user/favourites', 'post', session('user.token'));
        $favourites = collect($favourites)->pluck('product_id')->toArray();
        $category['items']['data'] = collect($category['items']['data'])->map(function ($product) use ($favourites) {
            $product['favourite'] = in_array($product['id'], $favourites);
            return $product;
        })->toArray();
    }
    $discounted = Cache::tags(['anasayfa'])->get('all_discounted');
    if (!$discounted) {
        //$discounted = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'discounted?page=1&orderBy=yeniden-eskiye')->json();
        $discounted = Http::get(config('app.product_api_url') . 'tag/anasayfaindirim')->json();
        Cache::tags(['anasayfa'])->put('all_discounted', $discounted, now()->addMinutes(60 * 24)); // 1 gün cache
    }
    return Inertia::render('CategoryGridDemo', [
        'discounted' => $discounted,

        'category' => $category,
        //        'brands' => collect(Arr::pluck(cache('get_category_items_all'), 'brand'))->unique(),
        'brands' => $category['filters']['brands'],
        'categories' => collect($category['filters']['category'])->sortBy('attribute_data.name.tr'),
        'pageType' => 'search',
        'canonical' => '/kategoriler/tum-urunler',
        'filters' => $request->get('filter', []),
        'orderBy' => $request->get('orderBy', ""), // Empty string means no order by
        'newTypeSeoDefinition' => true,
        'title' => 'Yüzlerce Son Teknoloji Ürünü İncele, Hemen Kirala',
        'meta_description' => 'Telefondan Akıllı Süpürgeye Birçok Teknoloji Ürününü Kolayca Kiralayabildiğin Kiralabunu! Tüm Ürünleri İncelemek İçin Hemen Tıkla, Keşfet, Kirala'
    ]);
});

Route::get('/indirimli-urunler', function (Request $request) {
    $category = Cache::tags(['kategori'])->get('get_discounted_items_all_' . md5($request->fullUrl()));
    //    ray('get_discounted_items_all_' . md5($request->fullUrl()));

    if (!$category) {
        if ($request->has('filter')) {
            $url = config('app.product_api_url') . 'discounted?';
            foreach ($request->get('filter', []) as $key => $value) {
                if ($value)
                    $url .= 'filter[' . $key . ']' . '=' . $value . '&';
            }
            $category = Http::timeout(80)->get($url)->json();
        } else {
            $category = Http::get(config('app.product_api_url') . 'discounted?page=' . $request->query('page'))->json();
            Cache::tags(['kategori'])->put('get_discounted_items_all_' . md5($request->fullUrl()), $category, now()->addMinutes(60 * 24)); // 1 gün cache
        }
    }
    // Loop over the category and change pagination links
    $category['items']['pagination']['links'] = collect($category['items']['pagination']['links'])->map(function ($link) use ($request) {
        $link['url'] = Str::of($link['url'])->replace(config('app.product_api_url') . 'discounted', config('app.app_url') . '/indirimli-urunler') . '&' . prepare_filter_query($request->get('filter', []));
        return $link;
    })->toArray();

    return Inertia::render('CategoryGridDemo', [
        'category' => $category,
        'brands' => $category['filters']['brands'],
        'categories' => $category['filters']['category'],
        'pageType' => 'search',
        'filters' => $request->get('filter', []),
        'newTypeSeoDefinition' => true,
        'title' => 'Kiralabunu | Yüzlerce Son Teknoloji İndirimli Ürünü Kirala',
        'meta_description' => 'Telefondan Akıllı Süpürgeye Birçok Teknoloji Ürününü Kolayca Kiralayabildiğin Kiralabunu! İndirimli Ürünler için Tıkla, Kirala.'
    ]);
});

Route::get('/kategoriler/kiralamobil', [CategoryController::class, 'getMobilityCategory']);
Route::get('/kategoriler/tum-urunler', [CategoryController::class, 'getAllProducts']);
//Route::get('/kategoriler/{menuName}/demo', [CategoryController::class, 'getMainCategoryDemo']); // Alt kategorilerin demo sayfası
Route::get('/kategoriler/{mainCategori}/{menuName}', [CategoryController::class, 'getCategory']);
Route::get('/kategoriler/{menuName}', [CategoryController::class, 'getMainCategory']);


Route::get('/iletisim', function () {
    return Inertia::render('Contact', []);
});

Route::get('/kiralamalar', [\App\Http\Controllers\OrderController::class, 'index'])->middleware('checkCookieAuth');
Route::post('/process-rent-payment', [\App\Http\Controllers\OrderController::class, 'processRentPayment'])->middleware('checkCookieAuth');

Route::get('/siparislerim', function () {
    $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/orders')->json();
    return Inertia::render('MyOrders', [
        'orders' => $res['data'],
    ]);
})->middleware('checkCookieAuth');

Route::get('/destek-taleplerim', [\App\Http\Controllers\TicketController::class, 'index']);
Route::get('/gecmis-taleplerim', [\App\Http\Controllers\TicketController::class, 'allRequests']);
Route::get('/talep-olustur', [\App\Http\Controllers\TicketController::class, 'create']);
Route::post('/destek-talebi-olustur', [\App\Http\Controllers\TicketController::class, 'store'])->name('sendSupportRequest');

Route::post('/yorum-yap', function (Request $request) {

    $validator = Validator::make($request->all(), [
        'product' => 'required',
        'reviewMessage' => 'required',
        'reviewTitle' => 'required',
        'reviewRating' => 'required',
    ], [
        'product.required' => 'Ürün seçimi zorunludur.',
        'reviewMessage.required' => 'Yorum zorunludur.',
        'reviewTitle.required' => 'Başlık zorunludur.',
        'reviewRating.required' => 'Yıldız seçimi zorunludur.',
    ]);

    // Yorum gönderildikten sonra başarılı mesajı göstermek için session'a atıyoruz
    session(['commentSubmittedSuccessfully_' . request('product') => false]);

    if ($validator->fails()) {
        return redirect()->back()
            ->withErrors($validator, 'commentValidation')
            ->withInput();
    }

    $request->merge([
        'productId' => $request->get('product'),
        'reviewSource' => 'web',
    ]);

    $res = \Illuminate\Support\Facades\Http::asForm()->post('https://stamped.io/api/reviews3?apiKey=pubkey-vO95ESN67VCe6cwn2Sf1rDp0tv5i21&sId=172071', $request->toArray());
    if ($res->successful()) {
        $res = $res->json();
        if ($res['id'] > 0) {
            session(['commentSubmittedSuccessfully_' . request('product') => true]);
            return redirect()->back()->with('success', 'Yorumunuz Alındı');
        }
    }

    return redirect()->back()->withErrors([
        'Bir hata oluştu. Lütfen tekrar deneyiniz.'
    ], 'commentValidation');
})->name('sendComment');

Route::get('/odeme-yontemlerim', function () {
    $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/cc')->json();
    return Inertia::render('PaymentMethods', [
        'cards' => $res,
    ]);
})->name('paymentMethods')->middleware('checkCookieAuth');

Route::get('/sepetim', [\App\Http\Controllers\CartController::class, 'myCart'])->name('cart.index');
Route::post('/sepetden-kaldir', [\App\Http\Controllers\CartController::class, 'removeProduct']);
Route::post('/sepet-guncelle', [\App\Http\Controllers\CartController::class, 'updateCart'])->name('updateCart');
Route::post('/sepet-toplu-odeme', [\App\Http\Controllers\CartController::class, 'massUpdateCart']);
Route::post('/sepete-ekle', [\App\Http\Controllers\CartController::class, 'addToCart']);

/** TODO: Yeni ekleme konusunda şu anda bu ekranda ilerlemenin bir manası yok ama eklenmeli **/
Route::get('/sepet-adres', function () {
    $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/cart')->json();
    return Inertia::render('CheckoutAddress', [
        "products" => $res['data']['items'],
        "total" => $res['data']['total'],
        "user" => $res['data']['user'],
    ]);
});

Route::get('/adres-ekle', function () {
    $cities = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/addresses/cities')->json();
    return Inertia::render('AddAddress', [
        'cities' => $cities,
    ]);
});

Route::get('/adres-duzenle', function (Request $request) {
    $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/addresses/' . $request->get('id'))->json();
    $cities = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/addresses/cities')->json();
    return Inertia::render('AddAddress', [
        'address' => $res['data'],
        'cities' => $cities,
        'id' => (int)$request->get('id'),
    ]);
});

Route::post('/adres-duzenle', function (Request $request) {
    if ($request->get('id') != null) {
        $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->put(config('app.api_url') . 'auth/user/addresses/' . $request->get('id'), $request->toArray())->json();
    } else {
        $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->post(config('app.api_url') . 'auth/user/addresses', $request->toArray())->json();
    }

    if (data_get($res, 'message') == 'Validation failed') {
        return redirect()->back()->withInput()->withErrors($res['errors']);
    }

    return redirect()->to('/adreslerim');
});

Route::post('/adres-duzenle-sepet', function (Request $request) {

    if ($request->get('id') != null) {
        $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->put(config('app.api_url') . 'auth/user/addresses/' . $request->get('id'), $request->toArray());
    } else {
        $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->post(config('app.api_url') . 'auth/user/addresses', $request->toArray());
    }

    $res = $res->json();

    if (data_get($res, 'message') == 'Validation failed') {
        return redirect()->back()->withInput()->withErrors($res['errors']);
    }

    $resUser = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/cart/get-auth')->json();
    if ($resUser['id']) {
        session(['user.user' => $resUser]);
    }
});

Route::get('/odeme', [\App\Http\Controllers\PaymentController::class, 'paymentPage'])->middleware('checkCookieAuth');
Route::post('/odeme', [\App\Http\Controllers\PaymentController::class, 'saveCCOnCart'])->middleware('checkCookieAuth')->name('saveCCOnPaymentScreen');
Route::get('/show-3d-page', [\App\Http\Controllers\PaymentController::class, 'show3DPage'])->middleware('checkCookieAuth')->name('3dPage');
Route::post('/kart-kaydet', [\App\Http\Controllers\PaymentController::class, 'saveCCOnProfile'])->middleware('checkCookieAuth')->name('saveCC');

Route::get('/odeme-yontemi-ekle', function () {
    return Inertia::render('AddBillingType', []);
})->middleware('checkCookieAuth');

Route::get('/odeme-yontemi-sec', function () {
    $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/cart')->json();

    return Inertia::render('BillingType', [
        "products" => $res['data']['items'],
        "total" => $res['data']['total'],
        "user" => $res['data']['user'],
    ]);
})->middleware('checkCookieAuth');

Route::get('/siparis-basarili', function (Request $request) {

    // Eğer sayfa yenilenir ise ana sayfaya yönlendir
    if (!$request->session()->get('order'))
        return redirect()->route('welcome');

    return Inertia::render('OrderSucces', [
        'order' => $request->session()->get('order'),
    ]);
})->name('orderSuccess')->middleware('checkCookieAuth');

Route::post('/compelete-payment-with-registered-card', function (Request $request) {

    $request->merge([
        'affiliate_teknosa' => request()->cookie('affiliate_teknosa'),
        'affiliate_abonesepeti' => request()->cookie('affiliate_abonesepeti'),
        'affiliate_aktifbank' => request()->cookie('affiliate') === 'aktifbank' ? 'aktifbank' : null,
        'affiliate_migros' => request()->cookie('affiliate') === 'migros' ? 'migros' : null,
        'pegasus_auth' => session('user.params.pegasus.isLoggedIn') ? [
            'phone' => session('user.params.pegasus.phone'),
            'travel_count' => session('user.params.pegasus.travelCount'),
            'campaign_source' => session('user.params.pegasus.campaignSource')
        ] : null
    ]);

    $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))
        ->timeout(60)
        ->post(config('app.api_url') . 'auth/user/cart/checkout-with-registered-card', $request->toArray());

    logger(data_get($res, 'type'), [$res->body()]);

    $res = $res->json();

    if (data_get($res, 'type') == 'iyzipay_error') {
        return redirect()->back()->withInput()->withErrors($res['message']);
    }

    if (data_get($res, 'type') == 'wait') {
        session()->flash('paymnentValidationMessage', $res['message']);
        return redirect()->back()->withInput();
    }

    // redirect to order success page
    return redirect()->route('orderSuccess')->with('order', $res);
})->middleware('checkCookieAuth');

Route::post('/siparis-basarili', function (Request $request) {

    $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))
        ->post(config('app.api_url') . 'auth/user/cart/checkout', $request->toArray())
        ->json();

    $responseType = $res['type'] ?? 'Success';
    if ($responseType == 'iyzipay_error')
        return redirect()->back()->withErrors($res['message'])->with('paymentType', 'nonRegisteredCard');

    return Inertia::render('OrderSucces', [
        'order' => $res,
    ]);
})->middleware('checkCookieAuth');

Route::get('/sifre-basarili', function () {
    return Inertia::render('PasswordSucces', []);
});

Route::get('/istek-listem', function () {
    //return $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->post(config('app.api_url') . 'auth/user/favourites')->body();
    //return $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->post(config('app.api_url') . 'auth/user/favourites')->body();
    $res = Http::withToken(session('user.token'))->post(config('app.api_url') . 'auth/user/favourites');
    if (!$res->successful()) {
        return redirect()->route('giris-yap');
    }

    $res = collect($res->json())->map(function ($product) {
        $product['product']['favourite'] = true;
        return $product;
    })->toArray();

    $allProducts = Cache::tags(['anasayfa'])->get('all_products');
    if (!$allProducts) {
        $allProducts = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'category/all?orderBy=yeniden-eskiye')->json();
        Cache::tags(['anasayfa'])->put('all_products', $allProducts, now()->addMinutes(60 * 24)); // 1 gün cache
    }

    return Inertia::render('MyWishlistFilled', [
        'products' => $res['data'],
        'allProducts' => $allProducts,
    ]);
})->middleware('checkCookieAuth');

Route::get('/istek-listem-tavsiye', function () {
    return Inertia::render('MyWishlistAdvice', []);
})->middleware('checkCookieAuth');

Route::get('/istek-listem-dolu', function () {
    return Inertia::render('MyWishlistFilled', []);
})->middleware('checkCookieAuth');

Route::get('/kuponlarim', function () {
    return Inertia::render('Coupons', []);
})->middleware('checkCookieAuth');

Route::get('/kuponlar-dolu', function () {
    return Inertia::render('CouponsFilled', []);
})->middleware('checkCookieAuth');

Route::get('/adreslerim', function () {
    $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/addresses')->json();
    return Inertia::render('Addresses', [
        'addresses' => $res['data']
    ]);
})->middleware('checkCookieAuth');

//Route::get('/adres-sec', function () {
//    return Inertia::render('SelectAddress', []);
//});

Route::get('/profili-duzenle', function () {
    return Inertia::render('EditProfile', [
        'user' => session('user'),
    ]);
})->middleware('checkCookieAuth');

Route::post('/profili-duzenle', function (Request $request) {
    $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->put(config('app.api_url') . 'auth/user/update', $request->toArray())->json();
    if (!($res['status'] ?? false)) {
        return Inertia::render('EditProfile', [
            'errors' => $res,
        ]);
    }

    session(['user' => $res['data']]);
    return redirect()->back();
})->middleware('checkCookieAuth');

Route::post('/tckn-dogrula', function (Request $request) {
    $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->put(config('app.api_url') . 'auth/user/validate-tckn/update', $request->toArray())->json();
    if (data_get($res, 'message') == 'Validation failed') {
        return redirect()->back()->withInput()->withErrors($res['errors']);
    }

    if (data_get($res, 'status') === false) {
        return redirect()->back()->withInput()->with('validationMessage', $res['message']);
    }

    //    session(['user' => $res['data']]);
    return redirect()->back();
})->middleware('checkCookieAuth');

Route::get('/iletisim-bilgileri', function () {
    return Inertia::render('ContactInformation', []);
});

Route::get('/sifre-degistir', function () {
    return Inertia::render('EditPassword', []);
});

Route::get('/sikca-sorulan-sorular', function () {
    return Inertia::render('SSS', []);
});

Route::get('/kiralamobil-bilgilendirme', function () {
    return Inertia::render('KiralaMobileInformation', []);
});
Route::get('/kiralamotor-bilgilendirme', function () {
    return Inertia::render('KiralaMotorInformation', []);
})->name('KiralaMotorInformation');

Route::get('/kvkk', function () {
    return Inertia::render('KVKK', []);
});

Route::get('/uyelik-sozlesmesi', function () {
    return Inertia::render('MembershipAgreement', []);
});

Route::get('/aydinlatma-metni', function () {
    return Inertia::render('ClarificationText', []);
});

Route::get('/cayma-fesih-ve-iade-kosullari', function () {
    return Inertia::render('ReturnConditions', []);
});

Route::get('/cerez-politikasi', function () {
    return Inertia::render('Cookies', []);
});

Route::get('/odeme-ve-teslimat', function () {
    return Inertia::render('BillingDelivery', []);
});

Route::get('/kimlik-findeks-raporu', function () {
    return Inertia::render('Findeks', []);
});

//Route::get('/mobile-menu', function () {
//    return Inertia::render('MobileMenu', []);
//});

Route::get('/badges', function () {
    return Inertia::render('Badges', []);
});

Route::get('/sepet-popup', function () {
    return Inertia::render('CheckoutPopup', []);
});

Route::get('/blog-haberler', function () {
    return Inertia::render('BlogNews', []);
});

Route::get('/blog', function () {
    return Inertia::render('BlogNew', [
        'topBlogs' => \App\Models\BlogTag::where('slug', 'topblog')->with('blogs.coverImage', "blogs.tags")->first()->blogs->where('is_active', true)->where('is_publish', true)->take(3),
        'techology' => \App\Models\BlogTag::where('slug', 'teknoloji')->with('blogs.coverImage', "blogs.tags")->first()->blogs->where('is_active', true)->where('is_publish', true)->take(4),
        'all' => \App\Models\Blog::where('is_active', true)->where('is_publish', true)->orderBy('created_at', 'desc')->with('coverImage', 'tags')->get(),
        'sideBlogs' => \App\Models\Blog::where('is_active', true)->where('is_publish', true)->orderBy('order')->with('coverImage', 'tags')->get(),
    ]);
});
Route::get('/blog-old', function () {
    return Inertia::render('Blogs', []);
});

Route::prefix('/blog')->group(function () {
    Route::get('{slug}', function ($slug) {
        $blog = \App\Models\Blog::where('slug', $slug)
            ->where('is_active', true)
            ->with('coverImage', 'tags')
            ->first();

        if ($blog) {
            $blogContent = collect(json_decode($blog->content, true));
            $updatedContent = $blogContent->map(function ($block) {
                if ($block['layout'] === 'image-library') {
                    // image_id değerini istediğiniz URL ile değiştiriyoruz
                    $block['attributes']['image_id'] = getS3URL($block['attributes']['image_id']);
                }
                if ($block['layout'] === '2-photo') {
                    // image_id değerini istediğiniz URL ile değiştiriyoruz
                    $block['attributes']['image_id1'] = getS3URL($block['attributes']['image_id1']);
                    $block['attributes']['image_id2'] = getS3URL($block['attributes']['image_id2']);
                }
                if ($block['layout'] === '3-photo') {
                    // image_id değerini istediğiniz URL ile değiştiriyoruz
                    $block['attributes']['image_id1'] = getS3URL($block['attributes']['image_id1']);
                    $block['attributes']['image_id2'] = getS3URL($block['attributes']['image_id2']);
                    $block['attributes']['image_id3'] = getS3URL($block['attributes']['image_id3']);
                }
                return $block;
            });
            $blog->content = $updatedContent;
        }

        // Güncellenmiş content'i JSON formatına çevirip geri kaydediyoruz
        return Inertia::render('BlogDetail', [
            'blog' => $blog,
            'sideBlogs' => \App\Models\Blog::where('is_active', true)->where('is_publish', true)->orderBy('order')->with('coverImage', 'tags')->get(),
        ]);
    });
});

Route::get('/dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::get('/cikis-yap', function () {
    $cookie = \Illuminate\Support\Facades\Cookie::forget('user');
    //    session()->forget('user');
    session()->flush();
    session()->regenerate();

    $cacheKey = config('app.api_url') . 'auth/user/cart' . session('user.token');
    $cache = Cache::tags(['http'])->forget($cacheKey);

    return redirect()->route('welcome')->withCookie($cookie);
})->middleware('checkCookieAuth');

Route::get('/email/verify/{id}/{hash}', function (\App\Http\Requests\EmailVerifyRequest $request) {
    $request->fulfill();
    //return redirect('/');
});
//->middleware(['checkCookieAuth']);
//->name('verification.verify');

Route::get('/get-token', function () {
    return request()->cookie('user');
});

Route::get('/set-token/{token}', function ($token) {
    session(['user.token' => $token]);
    session(['user.isUserLoggedIn' => true]);
    session(['user.checkCart' => true]);
    session(['user.status' => 'Authenticated']);
    return redirect()->to('/')->withCookie(cookie('user', $token, 60 * 24 * 30));
});

Route::get('/get-me', function () {
    return \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/cart/get-auth')->json();
});

Route::get('/clear', function () {
    return Cache::tags(['http', 'anasayfa', 'arama', 'kategori', 'tags'])->flush();
});

Route::post('/callback', [App\Http\Controllers\SaveCreditCardController::class, 'save'])->name('callback');

Route::post('/reset-passport', function (Request $request) {
    $user = \App\Models\User::where('email', $request->get('mail'))->first();

    $message = 'E-posta adresi bulunamadı.';
    if ($user) {
        //        DB::table('password_resets')->insert([
        //            'email' => $request->get('mail'),
        //            'token' => Str::random(64),
        //            'created_at' => now()
        //        ]);

        $res = \Illuminate\Support\Facades\Http::get(config('app.api_url') . 'auth/send-forgot-password-email?email=' . $request->get('mail'))->json();
        $message = $res['message'];
    }

    return redirect()->back()->withErrors($message);
})->name('resetPassword');

Route::get('/reset-password/{userId}/{token}', function (Request $request, $userId, $token) {
    $reset = DB::table('password_resets')->where('token', $token)->first();

    if (!$reset) {
        return redirect()->route('welcome');
    }

    return Inertia::render('ChangePasswordNew', [
        'token' => $token
    ]);
})->name('resetPasswordForm');

Route::post('/save-reset-passport', function (Request $request) {

    if ($request->get('pass1') != $request->get('pass2')) {
        return redirect()->back()->withErrors('Şifreler uyuşmuyor.');
    }

    $reset = DB::table('password_resets')->where('token', $request->get('token'))->first();

    $message = 'Geçersiz Token';
    if ($reset) {
        DB::table('users')->where('email', $reset->email)->update([
            'password' => Hash::make($request->get('pass1')),
            'updated_at' => now(),
            'is_password_migrated' => '1'
        ]);

        //$res = \Illuminate\Support\Facades\Http::get(config('app.api_url') . 'auth/send-forgot-password-email?email=' . $request->get('mail'))->json();
        $message = 'Şifreniz başarıyla değiştirildi.';
    }

    return redirect()->back()->withErrors($message);
})->name('saveResetPassword');

Route::get('google-sitemap', function () {
    return response(Storage::disk('s3')->get('xml/sitemap.xml'), 200, [
        'Content-Type' => 'application/xml'
    ]);
});

Route::post('hopi-user-login', function (Request $request) {
    $hopi = new Hopi();
    $res = $hopi->GetBirdUserInfo(Str::of($request->get('userToken'))->remove(' '));
    //    dd($res);
    // if response has error return back with error
    if (data_get($res, 'status') === 'error') {
        if (Str::of($res['message'])->contains('INVALID_TOKEN')) {
            $userErrorMessage = 'Hatalı Hopi QR Kodu';
        } elseif (Str::of($res['message'])->contains('EXPIRED_TOKEN')) {
            $userErrorMessage = 'Hopi QR Kodu\'nun süresi geçmiş';
        } else {
            $userErrorMessage = $res['message'];
        }
        return redirect()->back()->withErrors($userErrorMessage, 'userToken');
    }

    // set hopi bird id to session
    session(['user.params.birdId' => $res->birdId]);
    session(['user.integrations.hopi.campaigns' => $res->joinedCampaigns ?? []]);
    session(['user.integrations.hopi.balance' => $res->coinBalance]);
    // dispatch a job to save hopi bird id to user
})->name('hopiUserLogin');

Route::post('hopi-user-logout', function () {
    session(['user.params.birdId' => null]);
    session(['user.integrations.hopi.campaigns' => null]);
    session(['user.integrations.hopi.balance' => null]);
})->name('hopiUserLogout');

Route::post('pegasus-user-login', function (Request $request) {
    // validate request
    $request->validate([
        'userPhone' => 'required|string|max:255',
        'userTravelCount' => 'required|integer|min:0',
        'userCampaignSource' => 'required|string|max:255',
    ], [
        'userPhone.required' => 'Telefon numarası gereklidir.',
        'userTravelCount.required' => 'Yılda kaç kere seyahat edersiniz gereklidir.',
        'userTravelCount.min' => 'Yılda en az 100 kere seyahat etmelisiniz.',
        'userCampaignSource.required' => 'Kampanyamızı nereden duydunuz gereklidir.',
    ]);

    // set user params to session
    session(['user.params.pegasus.isLoggedIn' => true]);
    session(['user.params.pegasus.phone' => $request->get('userPhone')]);
    session(['user.params.pegasus.travelCount' => $request->get('userTravelCount')]);
    session(['user.params.pegasus.campaignSource' => $request->get('userCampaignSource')]);

    return redirect()->back();
})->name('pegasusUserLogin');

//  kiralabunu.com/n-kolay?utm_source=aktifbank&utm_medium=cpc
Route::get('n-kolay', function () {
    return redirect()->route('welcome', ['utm_source' => 'aktifbank', 'utm_medium' => 'cpc'])->cookie('affiliate', "aktifbank", 30); // expire in 30 minutes
})->name('n-kolay');

Route::get('migros', function () {
    return redirect()->route('welcome', ['utm_source' => 'migros', 'utm_medium' => 'cpc'])->cookie('affiliate', "migros", 30); // expire in 30 minutes
})->name('migros');

require __DIR__ . '/motorcycle-route.php';
require __DIR__ . '/auth.php';

/*
|--------------------------------------------------------------------------
| Payment Routes
|--------------------------------------------------------------------------
|
| SMS ödeme sistemi için token tabanlı payment route'ları.
| Token format: 32 karakter alfanumerik string
|
*/

Route::prefix('payment')->name('payment.')
    // ->middleware('payment.security')
    ->group(function () {
        Route::get('/{token}', 'App\Http\Controllers\PaymentController@show')
            ->name('show')
            ->where('token', '[a-zA-Z0-9]{32}');

        Route::post('/{token}/process', 'App\Http\Controllers\PaymentController@process')
            ->name('process')
            ->where('token', '[a-zA-Z0-9]{32}')
            ->middleware('throttle:10,1');

        Route::get('/{token}/success', 'App\Http\Controllers\PaymentController@success')
            ->name('success')
            ->where('token', '[a-zA-Z0-9]{32}');
    });

Route::fallback(function () {
    $url = url()->current();
    $url = Str::of($url)->replace(config('app.app_url') . '/', '')->__toString();
    $redirection = \App\Models\Redirect::where('from', $url)->first();
    if ($redirection) {
        $redirection->increment('count');
        return redirect()->to($redirection->to, $redirection->status_code);
    }

    // if not exist log request
    \App\Models\NonExistRedirection::create([
        'url' => $url,
    ]);

    return Inertia::render('404', [])->toResponse(\request())->setStatusCode(404);
    //return redirect()->to('/');
});
