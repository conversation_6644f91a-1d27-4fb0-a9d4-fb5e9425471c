<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

// Route for rendering pages
Route::get('/kiralamotor', function () {
    $products = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'tag/kiralamotor')->json();
    //dd($products['items']['data']);
    return Inertia::render('KiralaMotor', [
        'products' => $products['items']['data'] ?? []
    ]);
});
Route::get('/kiralamotor/honda-activa-125-scooter', fn() => Inertia::render('KiralaMotor/HondaActiva125', []));
Route::get('/kiralamotor/honda-dio-110-scooter', fn() => Inertia::render('KiralaMotor/HondaDio125', []));
Route::get('/kiralamotor/honda-pcx-125-scooter', fn() => Inertia::render('KiralaMotor/HondaPCX125', []));
Route::get('/kiralamotor/honda-cb125f-motosiklet', fn() => Inertia::render('KiralaMotor/HondaCb125F', []));
Route::get('/kiralamotor/honda-em1-e-scooter', fn() => Inertia::render('KiralaMotor/HondaEM1E', []));
Route::get('/kiralamotor/yamaha-xmax-250-scooter', fn() => Inertia::render('KiralaMotor/YamahaXMAX250', []));
Route::get('/kiralamotor/yamaha-nmax-125-scooter', fn() => Inertia::render('KiralaMotor/YamahaNMAX125', []));
Route::get('/kiralamotor/arora-freedom-125', fn() => Inertia::render('KiralaMotor/AroraFreedom125', []));
Route::get('/kiralamotor/volta-rs7', fn() => Inertia::render('KiralaMotor/VoltaRS7', []));

// Helper function for validation and submission
if (!function_exists('validateAndSubmit')) {
    function validateAndSubmit(Request $request, $motorcycleName)
    {
        //dd($request->all());
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email',
            'gsm' => 'required',
            'firmName' => 'required',
            //            'dateofbirth' => 'required',
            'motorcycleNumber' => 'required',
            'city' => 'required',
            'Contract1' => 'required|accepted',
            //            'Contract2' => 'required|accepted',
            'motorcycleName' => 'required',
            //            'IDnumber' => 'required|digits:11',
        ], [
            'name.required' => 'Ad Soyad alanı zorunludur.',
            'email.required' => 'E-posta alanı zorunludur.',
            'email.email' => 'E-posta alanı geçerli bir e-posta adresi olmalıdır.',
            'gsm.required' => 'Telefon alanı zorunludur.',
            'firmName.required' => 'Firma Adı alanı zorunludur.',
            'motorcycleNumber.required' => 'Motor Adeti alanı zorunludur.',
            'city.required' => 'Şehir alanı zorunludur.',
            'Contract1.required' => 'Sözleşmeleri kabul etmek zorunludur.',
            //            'Contract2.required' => 'Sözleşmeleri kabul etmek zorunludur.',
            'Contract1.accepted' => 'Sözleşmeleri kabul etmek zorunludur.',
            //            'Contract2.accepted' => 'Sözleşmeleri kabul etmek zorunludur.',
            'motorcycleName.required' => 'Motosiklet Adı alanı zorunludur.',
            //            'IDnumber.required' => 'T.C. Kimlik No. zorunludur.',
            //            'IDnumber.digits' => 'T.C. Kimlik No. 11 haneden oluşmaktadır.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        $res = Http::post(config('app.api_url') . 'kiralamotor-request', $request->toArray())->json();
        //$res['status'] = 'success';
        if ($res['status'] === 'success') {
            return redirect()->back()->with('success', 'Mesajınız başarıyla gönderildi.');
        }

        return redirect()->back()->with('error', 'Bir hata oluştu. Lütfen tekrar deneyiniz.');
    }
}

// Routes for submitting forms
Route::post('/urun/honda-activa-125-scooter', fn(Request $request) => validateAndSubmit($request, 'Honda Activa 125'))->name('sendHondaActiva125');
Route::post('/urun/honda-dio', fn(Request $request) => validateAndSubmit($request, 'Honda Dio 110'))->name('sendHondaDio110');
Route::post('/urun/honda-pcx-125', fn(Request $request) => validateAndSubmit($request, 'Honda PCX STD 125'))->name('sendHondaPCX125');
Route::post('/urun/honda-cb125f-motosiklet', fn(Request $request) => validateAndSubmit($request, 'Honda CB125F'))->name('sendHondaCb125F');
Route::post('/urun/honda-em1-e-scooter', fn(Request $request) => validateAndSubmit($request, 'Honda EM1-E'))->name('sendHondaEM1E');
Route::post('/urun/yamaha-xmax-250-scooter', fn(Request $request) => validateAndSubmit($request, 'Yamaha XMAX 250'))->name('sendYamahaXMAX250');
Route::post('/urun/yamaha-nmax-125-scooter', fn(Request $request) => validateAndSubmit($request, 'Yamaha NMAX 125'))->name('sendYamahaNMAX125');
Route::post('/urun/honda-sh-125', fn(Request $request) => validateAndSubmit($request, 'Honda SH 125'))->name('sendYamahaNMAX125');
Route::post('/urun/aprilla-sr-125', fn(Request $request) => validateAndSubmit($request, 'Aprilla SR 125'))->name('sendYamahaNMAX125');
Route::post('/urun/addres', fn(Request $request) => validateAndSubmit($request, 'Suzuki Address'))->name('sendYamahaNMAX125');
Route::post('/urun/suzuki-avensis', fn(Request $request) => validateAndSubmit($request, 'Suzuki Avensis'))->name('sendYamahaNMAX125');
Route::post('/urun/suzuki-burgman', fn(Request $request) => validateAndSubmit($request, 'Suzuki Burgman'))->name('sendYamahaNMAX125');
Route::post('/urun/motorcyle', fn(Request $request) => validateAndSubmit($request, ''))->name('sendMotorcycleInstitutionalForm');
