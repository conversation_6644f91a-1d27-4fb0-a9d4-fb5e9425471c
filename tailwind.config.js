const defaultTheme = require("tailwindcss/defaultTheme");

/** @type {import("tailwindcss").Config} */
module.exports = {
    content: ["./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php", "./storage/framework/views/*.php", "./resources/views/**/*.blade.php", "./resources/js/**/*.vue"],

    theme: {
        listStyleType: {
            none: "none",
            disc: "disc",
            decimal: "decimal",
            square: "square",
            roman: "upper-roman"
        },
        extend: {
            zIndex: {
                60: "60",
                70: "70",
                80: "80"
            },
            screens: {
                ts: "1280px",
                lg: "1100px",
                mts: "710px"
            },
            fontFamily: {
                sans: ["kiralabunufont", ...defaultTheme.fontFamily.sans],
                kiralabunuthin: ["kiralabunuthin", ...defaultTheme.fontFamily.sans],
                santralextrabold: ["santralextrabold", ...defaultTheme.fontFamily.sans],
                santralregular: ["santralregular", ...defaultTheme.fontFamily.sans]
            },
            lineHeight: {
                6.5: "26px"
            },
            boxShadow: {
                searchshadow: "0 8px 20px rgb(108 108 108 / 17%)",
                buttonshadow: "0 10px 20px rgb(108 108 108 / 17%)",
                aboutshadow: "0 0px 8px rgb(108 108 108 / 17%)",
                productshadow: "0 8px 8px rgb(108 108 108 / 17%)",
                institutional: "0 8px 8px rgb(95 74 244 / 17%)",
                institutionalcategory: "5px 5px 30px 0 (0 0 0 / %17)",
                kiralastep: "rgba(0, 0, 0, 0.05) 0px 0px 3px, rgba(0, 0, 0, 0.13) 0px 4px 10px"
            },
            colors: {
                "brand-color": "#1f7eff",
                "icon-gray": "#f7f7f7",
                kbgray: "#989898",
                textgray: "#9B9B9B",
                checkoutgray: "#797979",
                "kb-light-grey": "#ebebeb",
                "kb-mid-grey": "#f8f8f8",
                kbgreen: "#70d44b",
                kbred: "#FF4B4B",
                kbdvred: "#FD103A",
                kbyellow: "#FCC579",
                "kb-light-green": "#e1f2db",
                "kb-mid-green": "#F4FFF0",
                "acordion-green": "#15A559",
                "acordion-light-green": "rgb(21 165 89 / 0.05)",
                kbblue: "#5F4AF4",
                black: "#000000",
                bordergray: "#EBEBEB",
                "left-menu-gray": "#BABABA",
                placeholdergray: "#C7C7C7",
                "hopi-pink": "#e30d7d"
            },
            width: {
                13: "53px",
                25: "105px",
                38: "150px",
                67: "278px",
                350: "350px",
                580: "580px",
                18: "4.5rem",
                19: "4.75rem",
                202: "202px"
            },
            height: {
                13: "53px",
                25: "105px",
                375: "3.75rem",
                67: "278px"
            },
            maxHeight: {
                "p-box": "600px"
            },
            minHeight: {
                15: "60px",
                14: "56px",
                68: "308px"
            },
            maxWidth: {
                "v-p-box": "275px",
                "smv-p-box": "245px",
                67: "278px",
                tablet: "90%",
                ts: "1112px",
                kbmobile: "82%",
                "8xl": "95rem"
            },
            space: {
                13: "50px",
                15: "60px",
                "18px": "18px"
            },
            fontSize: {
                "2xs": ".625rem",
                "3xs": ".5rem"
            },
            borderRadius: {
                xl: "2rem",
                "10p": "10p",
                "2lg": "1rem",
                "32": "32px"
            },
            borderWidth: {
                3: "3px",
                1: "1px"
            },
            spacing: {
                18: "77px",
                15: "60px"
            },
            backgroundImage: {
                "about-bg": "url('/resources/images/basinda-bg.png')",
                "news-white-bg": "url('/resources/images/<EMAIL>')",
                "tosla-card-desktop-png": "url('/resources/images/billing/tosla-card-desktop.png')",
                "tosla-card-mobile-png": "url('/resources/images/billing/tosla-card-mobile.png')",
                "tosla-card-desktop-webp": "url('/resources/images/billing/tosla-card-desktop.webp')",
                "tosla-card-mobile-webp": "url('/resources/images/billing/tosla-card-mobile.webp')"
            }
        }
    },

    plugins: [require("@tailwindcss/forms")]
};
