{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "guzzlehttp/guzzle": "^7.2", "inertiajs/inertia-laravel": "^0.6.3", "laravel/framework": "^9.19", "laravel/octane": "^1.5", "laravel/sanctum": "^2.8", "laravel/socialite": "^5.16", "laravel/tinker": "^2.7", "league/flysystem-aws-s3-v3": "^3.15", "opcodesio/log-viewer": "^3.14", "predis/predis": "^2.1", "spatie/laravel-ray": "^1.31", "tightenco/ziggy": "^1.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "fakerphp/faker": "^1.9.1", "laravel/breeze": "^1.14", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}