<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * Payment Token Validation Result DTO
 * 
 * Token doğrulama işleminin sonucunu temsil eder.
 */
class PaymentTokenResult
{
    public function __construct(
        public readonly bool $isValid,
        public readonly ?string $status = null,        // pending|sent|clicked|paid|expired
        public readonly ?float $amount = null,
        public readonly ?float $rentAmount = null,
        public readonly ?string $description = null,
        public readonly ?Carbon $expiresAt = null,
        public readonly ?string $errorMessage = null,
        public readonly ?string $currency = 'TRY',
        public readonly ?int $statusCode = null,
        public readonly ?string $transactionId = null,
        public readonly array $scoringRequest = []
    ) {}

    /**
     * Token'ın süresi dolmuş mu?
     */
    public function isExpired(): bool
    {
        return $this->expiresAt && $this->expiresAt->isPast();
    }

    /**
     * Token'ın ödeme için kullanılabilir durumda olup olmadığını kontrol eder
     */
    public function isPayable(): bool
    {
        return $this->isValid &&
            in_array($this->status, ['pending', 'sent', 'clicked']) &&
            !$this->isExpired();
    }

    /**
     * Ödeme tamamlanmış mı?
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
        // return $this->isValid && $this->status === 'paid';
    }

    /**
     * Array formatında döndür
     */
    public function toArray(): array
    {
        return [
            'is_valid' => $this->isValid,
            'status' => $this->status,
            'amount' => $this->amount,
            'rent_amount' => $this->rentAmount,
            'description' => $this->description,
            'expires_at' => $this->expiresAt?->toDateTimeString(),
            'error_message' => $this->errorMessage,
            'currency' => $this->currency,
            'is_expired' => $this->isExpired(),
            'is_payable' => $this->isPayable(),
            'is_paid' => $this->isPaid(),
            'status_code' => $this->statusCode,
            'transaction_id' => $this->transactionId,
            'scoring_request' => $this->scoringRequest
        ];
    }
}
