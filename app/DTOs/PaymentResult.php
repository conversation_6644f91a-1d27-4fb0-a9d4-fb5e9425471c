<?php

namespace App\DTOs;

/**
 * Payment Process Result DTO
 * 
 * Ödeme işleminin sonucunu temsil eder.
 */
class PaymentResult
{
    public function __construct(
        public readonly bool $success,
        public readonly ?string $transactionId = null,
        public readonly ?string $errorMessage = null,
        public readonly ?string $errorCode = null,
        public readonly ?array $additionalData = null,
        public readonly ?int $statusCode = null,
        public readonly ?string $threeDSHtml = null,
        public readonly ?string $iyzToken = null,
        public readonly bool $requires3DS = false
    ) {}

    /**
     * Başarılı işlem mi?
     */
    public function isSuccessful(): bool
    {
        return $this->success;
    }

    /**
     * Hata var mı?
     */
    public function hasError(): bool
    {
        return !$this->success || $this->errorMessage !== null;
    }

    /**
     * 3D Secure gerekli mi?
     */
    public function requires3DS(): bool
    {
        return $this->requires3DS;
    }

    /**
     * Array formatında döndür
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'transaction_id' => $this->transactionId,
            'error_message' => $this->errorMessage,
            'error_code' => $this->errorCode,
            'additional_data' => $this->additionalData,
            'is_successful' => $this->isSuccessful(),
            'has_error' => $this->hasError(),
            'status_code' => $this->statusCode,
            'requires_3ds' => $this->requires3DS(),
            'threeds_html' => $this->threeDSHtml,
            'iyz_token' => $this->iyzToken,
        ];
    }
}
