<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * Payment Status DTO
 * 
 * Ödeme durumu sorgulama sonucunu temsil eder.
 */
class PaymentStatus
{
    public function __construct(
        public readonly string $status,                    // pending|sent|clicked|paid|expired
        public readonly ?float $amount = null,
        public readonly ?string $transactionId = null,
        public readonly ?Carbon $paidAt = null,
        public readonly ?Carbon $expiresAt = null,
        public readonly ?string $description = null,
        public readonly ?string $currency = 'TRY'
    ) {}

    /**
     * Ödeme tamamlanmış mı?
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * Ödeme süresi dolmuş mu?
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired' ||
            ($this->expiresAt && $this->expiresAt->isPast());
    }

    /**
     * Ödeme beklemede mi?
     */
    public function isPending(): bool
    {
        return in_array($this->status, ['pending', 'sent', 'clicked']);
    }

    /**
     * Array formatında döndür
     */
    public function toArray(): array
    {
        return [
            'status' => $this->status,
            'amount' => $this->amount,
            'transaction_id' => $this->transactionId,
            'paid_at' => $this->paidAt?->toDateTimeString(),
            'expires_at' => $this->expiresAt?->toDateTimeString(),
            'description' => $this->description,
            'currency' => $this->currency,
            'is_paid' => $this->isPaid(),
            'is_expired' => $this->isExpired(),
            'is_pending' => $this->isPending(),
        ];
    }
}
