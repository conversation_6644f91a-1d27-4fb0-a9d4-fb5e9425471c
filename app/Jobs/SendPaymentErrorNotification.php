<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendPaymentErrorNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public string $token;
    public string $errorType;
    public string $errorMessage;
    public array $context;

    /**
     * Create a new job instance.
     */
    public function __construct(string $token, string $errorType, string $errorMessage, array $context = [])
    {
        $this->token = $token;
        $this->errorType = $errorType;
        $this->errorMessage = $errorMessage;
        $this->context = $context;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $emailContent = $this->buildEmailContent();

            Mail::raw($emailContent, function ($message) {
                $message->to('<EMAIL>')
                    ->subject('[Payment API Error] ' . $this->errorType . ' - ' . now()->format('Y-m-d H:i:s'));
            });

            Log::channel('sms-payment')->info('Payment error notification sent', [
                'token' => $this->token,
                'error_type' => $this->errorType,
                'recipient' => '<EMAIL>'
            ]);
        } catch (\Exception $e) {
            Log::channel('sms-payment')->error('Failed to send payment error notification', [
                'token' => $this->token,
                'error_type' => $this->errorType,
                'mail_error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Build email content for error notification
     */
    private function buildEmailContent(): string
    {
        $timestamp = now()->format('Y-m-d H:i:s');

        return "PAYMENT API ERROR NOTIFICATION\n\n" .
            "Timestamp: {$timestamp}\n" .
            "Error Type: {$this->errorType}\n" .
            "Token: " . $this->token . "\n" .
            "Error Message: {$this->errorMessage}\n\n" .
            "Context:\n" . json_encode($this->context, JSON_PRETTY_PRINT) . "\n\n" .
            "Environment: " . app()->environment() . "\n" .
            "URL: " . request()->url() . "\n" .
            "User Agent: " . request()->userAgent() . "\n" .
            "IP: " . request()->ip() . "\n\n" .
            "Please investigate this issue as soon as possible.\n\n" .
            "Generated by: SMS Payment System";
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::channel('sms-payment')->critical('Payment error notification job failed', [
            'token' => $this->token,
            'error_type' => $this->errorType,
            'job_error' => $exception->getMessage()
        ]);
    }
}
