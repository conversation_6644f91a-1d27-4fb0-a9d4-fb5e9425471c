<?php

use Illuminate\Support\Facades\Cache;

if (!function_exists('is_user_authenticated')) {
    function is_user_authenticated()
    {
        return session('user.isUserLoggedIn', false);
        //$userAuthCode = request()->cookie('user');
        //        $userAuthCode = session('user.token');
        //        if ($userAuthCode == null) {
        //            return false;
        //        }
        //        return $userAuthCode;
    }
}
if (!function_exists('getS3URL')) {
    function getS3URL($image_id)
    {
        //dd($image = \App\Models\BlogMediaHub::where('id', $image_id)->first());
        $image = \App\Models\BlogMediaHub::where('id', $image_id)->first();
        // https://kiralabunu.fra1.digitaloceanspaces.com/kb-media/27/cocuk-saat-1-800x500.jpg
        return 'https:///kiralabunu.fra1.digitaloceanspaces.com/kb-media/' . $image->id . '/' . $image->file_name;
    }
}

if (!function_exists('cache_http')) {
    function cache_http($url, $type = 'get', $token = '', $minutes = null, $passCache = false)
    {
        if (!$minutes) {
            $minutes = now()->addMinutes(5);
        }

        $cacheKey = $url . $token;
        $cache = Cache::tags(['http'])->get(cache_key($cacheKey));

        if ($cache && !$passCache) {
            return $cache;
        }

        try {
            $response = \Illuminate\Support\Facades\Http::withToken($token)->{$type}($url);
            if ($response->successful()) {
                Cache::tags(['http'])->put(cache_key($cacheKey), $response->json(), $minutes);
                //            ds('çıkış cache kontrol', Cache::tags(['http'])->get(cache_key($cacheKey)));
                return $response->json();
            }
            return [];
        } catch (\Exception $e) {
            return [];
        }
    }
}


if (!function_exists('check_is_rented_before')) {
    function check_is_rented_before($productId)
    {
        $orders = cache_http(config('app.api_url') . 'auth/user/orders', 'get', request()->cookie('user'), passCache: true);

        if (!isset($orders['data'])) {
            return false;
        }

        $isPurchased = false;
        foreach ($orders['data'] as $datum) {
            foreach ($datum['items'] as $item) {
                $isPurchased = $isPurchased || $item['product']['product_id'] == $productId;
            }
        }

        return $isPurchased;
    }
}

if (!function_exists('prepare_filter_query')) {
    function prepare_filter_query($filters)
    {
        $query = '';
        return "filter[brand]=" . ($filters['brand'] ?? "") . "&filter[price]=" . ($filters['price'] ?? "") . "&filter[collections]=" . ($filters['collections'] ?? "");
        return $query;
    }
}

if (!function_exists('hopi_logger')) {
    function hopi_logger($message, array $context = []): void
    {
        \Illuminate\Support\Facades\Log::channel('hopi')->info($message, $context);
    }
}

if (!function_exists('cache_key')) {
    function cache_key($clearKey)
    {
        return md5($clearKey);
    }
}

//if (!function_exists('is_user_not_authenticated_redirect_to_homepage')) {
//    function is_user_not_authenticated_redirect_to_homepage()
//    {
//        if(!is_user_authenticated()){
//            redirect()->route('welcome');
//        }
//    }
//}

if (!function_exists('getClientIPAddress')) {
    /**
     * CloudFlare proxy arkasından gerçek client IP adresini alır
     * IPv6 durumunda CloudFlare'nin IPv4 fallback header'ını tercih eder
     *
     * @param \Illuminate\Http\Request|null $request
     * @return string|null IP adresi (IPv4 veya IPv6)
     */
    function getClientIPAddress(\Illuminate\Http\Request $request = null): ?string
    {
        // Request yoksa global request'i kullan
        if (!$request) {
            $request = request();
        }

        // CloudFlare'ın sağladığı gerçek client IP'yi al
        $cfConnectingIp = $request->header('cf-connecting-ip');

        if (!empty($cfConnectingIp)) {
            // IP geçerli mi kontrol et
            if (filter_var($cfConnectingIp, FILTER_VALIDATE_IP)) {
                // IPv6 ise CloudFlare'nin IPv4 fallback'ini tercih et
                if (filter_var($cfConnectingIp, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
                    $cfPseudoIpv4 = $request->header('cf-pseudo-ipv4');
                    if (!empty($cfPseudoIpv4) && filter_var($cfPseudoIpv4, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                        return $cfPseudoIpv4;
                    }
                }

                // IPv4 ise direkt kullan
                return $cfConnectingIp;
            }
        }

        // CloudFlare header'ları yoksa diğer proxy header'larını kontrol et
        $forwardedIps = $request->header('x-forwarded-for');
        if (!empty($forwardedIps)) {
            // İlk IP'yi al (genellikle gerçek client IP)
            $firstIp = trim(explode(',', $forwardedIps)[0]);
            if (filter_var($firstIp, FILTER_VALIDATE_IP)) {
                return $firstIp;
            }
        }

        // X-Real-IP header'ını kontrol et
        $realIp = $request->header('x-real-ip');
        if (!empty($realIp) && filter_var($realIp, FILTER_VALIDATE_IP)) {
            return $realIp;
        }

        // CloudFlare pseudo IPv4'ü son çare olarak kontrol et
        $cfPseudoIpv4 = $request->header('cf-pseudo-ipv4');
        if (!empty($cfPseudoIpv4) && filter_var($cfPseudoIpv4, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return $cfPseudoIpv4;
        }

        // Hiçbiri yoksa Laravel'in varsayılan IP metodunu kullan
        $defaultIp = $request->ip();
        if (filter_var($defaultIp, FILTER_VALIDATE_IP)) {
            return $defaultIp;
        }

        // Son çare olarak server IP'lerini kontrol et
        foreach (['REMOTE_ADDR', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED'] as $key) {
            $serverIp = $_SERVER[$key] ?? null;
            if (!empty($serverIp) && filter_var($serverIp, FILTER_VALIDATE_IP)) {
                return $serverIp;
            }
        }

        return null;
    }
}
