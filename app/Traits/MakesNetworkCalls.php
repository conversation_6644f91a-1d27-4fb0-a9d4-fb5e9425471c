<?php

namespace App\Traits;

use App\Extensions\CallHelper;

trait MakesNetworkCalls
{

    public static function sendPost($route, $payload)
    {
        $callHelper = new CallHelper();

        $resp = $callHelper->sendPost($route, $payload);

        return $resp;
    }

    public static function sendGet($route, $payload)
    {
        $callHelper = new CallHelper();

        $resp = $callHelper->sendGet($route, $payload);

        return $resp;
    }
}
