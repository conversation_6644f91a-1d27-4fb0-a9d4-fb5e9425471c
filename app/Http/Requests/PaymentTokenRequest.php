<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Payment Token Validation Request
 * 
 * SMS ödeme sistemi için token validation kuralları
 */
class PaymentTokenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Public endpoint, herkes erişebilir
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'token' => [
                'required',
                'string',
                'size:32',
                'alpha_num'
            ]
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'token.required' => 'Ödeme token\'ı gereklidir.',
            'token.string' => 'Ödeme token\'ı metin formatında olmalıdır.',
            'token.size' => 'Ödeme token\'ı 32 karakter uzunluğunda olmalıdır.',
            'token.alpha_num' => 'Ödeme token\'ı sadece harf ve rakam içerebilir.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'token' => 'ödeme token\'ı',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        // Custom error handling if needed
        parent::failedValidation($validator);
    }
}
