<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Process Payment Request
 * 
 * SMS ödeme sistemi için kart bilgileri validation kuralları
 */
class ProcessPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Public endpoint, herkes er<PERSON>ilir
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $currentYear = (int) date('Y');
        $maxYear = $currentYear + 15; // 15 yıl sonrası maksimum

        return [
            'card_number' => [
                'required',
                'string',
                'min:13',
                'max:19',
                'regex:/^[0-9\s]+$/' // Rakam ve boşluk kabul et (formatting için)
            ],
            'expiry_month' => [
                'required',
                'integer',
                'min:1',
                'max:12'
            ],
            'expiry_year' => [
                'required',
                'integer',
                'min:' . $currentYear,
                'max:' . $maxYear
            ],
            'cvv' => [
                'required',
                'string',
                'size:3',
                'regex:/^[0-9]+$/'
            ],
            'cardholder_name' => [
                'required',
                'string',
                'max:255',
                'min:2',
                'regex:/^[a-zA-ZçÇğĞıİöÖşŞüÜ\s]+$/' // Türkçe karakter desteği
            ],
            'email' => [
                'required',
                'email',
                'max:255'
            ],
            'invoice_address' => [
                'required',
                'string',
                'min:30',
                'max:500'
            ]
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'card_number.required' => 'Kart numarası gereklidir.',
            'card_number.min' => 'Kart numarası en az 13 haneli olmalıdır.',
            'card_number.max' => 'Kart numarası en fazla 19 haneli olmalıdır.',
            'card_number.regex' => 'Kart numarası sadece rakam içerebilir.',

            'expiry_month.required' => 'Son kullanma ay bilgisi gereklidir.',
            'expiry_month.integer' => 'Son kullanma ay bilgisi sayı olmalıdır.',
            'expiry_month.min' => 'Geçerli bir ay seçiniz (1-12).',
            'expiry_month.max' => 'Geçerli bir ay seçiniz (1-12).',

            'expiry_year.required' => 'Son kullanma yıl bilgisi gereklidir.',
            'expiry_year.integer' => 'Son kullanma yıl bilgisi sayı olmalıdır.',
            'expiry_year.min' => 'Kartın son kullanma tarihi geçmiş olamaz.',
            'expiry_year.max' => 'Geçerli bir son kullanma tarihi giriniz.',

            'cvv.required' => 'CVV kodu gereklidir.',
            'cvv.size' => 'CVV kodu 3 haneli olmalıdır.',
            'cvv.regex' => 'CVV kodu sadece rakam içerebilir.',

            'cardholder_name.required' => 'Kart sahibi adı gereklidir.',
            'cardholder_name.min' => 'Kart sahibi adı en az 2 karakter olmalıdır.',
            'cardholder_name.max' => 'Kart sahibi adı en fazla 255 karakter olmalıdır.',
            'cardholder_name.regex' => 'Kart sahibi adı sadece harf içerebilir.',

            'email.required' => 'E-posta adresi gereklidir.',
            'email.email' => 'Geçerli bir e-posta adresi girin.',
            'email.max' => 'E-posta adresi en fazla 255 karakter olmalıdır.',

            'invoice_address.required' => 'Fatura adresi gereklidir.',
            'invoice_address.min' => 'Fatura adresi en az 30 karakter olmalıdır.',
            'invoice_address.max' => 'Fatura adresi en fazla 500 karakter olmalıdır.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'card_number' => 'kart numarası',
            'expiry_month' => 'son kullanma ayı',
            'expiry_year' => 'son kullanma yılı',
            'cvv' => 'CVV kodu',
            'cardholder_name' => 'kart sahibi adı',
            'email' => 'e-posta adresi',
            'invoice_address' => 'fatura adresi',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $data = [];

        // Kart numarasındaki boşlukları ve tire'leri temizle
        if ($this->has('card_number')) {
            $data['card_number'] = preg_replace('/[\s\-]/', '', $this->get('card_number'));
        }

        // Kart sahibi adını büyük harfe çevir
        if ($this->has('cardholder_name')) {
            $data['cardholder_name'] = mb_strtoupper(trim($this->get('cardholder_name')), 'UTF-8');
        }

        // Email'i küçük harfe çevir ve temizle
        if ($this->has('email')) {
            $data['email'] = strtolower(trim($this->get('email')));
        }

        // Invoice address'i temizle
        if ($this->has('invoice_address')) {
            $data['invoice_address'] = trim($this->get('invoice_address'));
        }

        if (!empty($data)) {
            $this->merge($data);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Kartın son kullanma tarihini kontrol et
            if ($this->filled(['expiry_month', 'expiry_year'])) {
                $currentYear = (int) date('Y');
                $currentMonth = (int) date('m');
                $expiryYear = (int) $this->get('expiry_year');
                $expiryMonth = (int) $this->get('expiry_month');

                if ($expiryYear == $currentYear && $expiryMonth < $currentMonth) {
                    $validator->errors()->add('expiry_month', 'Kartın son kullanma tarihi geçmiş.');
                }
            }

            // Luhn algoritması ile kart numarası kontrolü (opsiyonel)
            if ($this->filled('card_number')) {
                if (!$this->isValidCardNumber($this->card_number)) {
                    $validator->errors()->add('card_number', 'Geçersiz kart numarası.');
                }
            }
        });
    }

    /**
     * Luhn algoritması ile kart numarası validation
     */
    private function isValidCardNumber(string $cardNumber): bool
    {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        $sum = 0;
        $alternate = false;

        for ($i = strlen($cardNumber) - 1; $i >= 0; $i--) {
            $n = (int) $cardNumber[$i];

            if ($alternate) {
                $n *= 2;
                if ($n > 9) {
                    $n = ($n % 10) + 1;
                }
            }

            $sum += $n;
            $alternate = !$alternate;
        }

        return ($sum % 10) === 0;
    }
}
