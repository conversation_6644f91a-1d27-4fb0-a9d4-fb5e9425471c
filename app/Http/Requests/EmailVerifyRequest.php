<?php

namespace App\Http\Requests;

use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Http\FormRequest;
use mysql_xdevapi\Exception;

class EmailVerifyRequest extends FormRequest
{

    protected $user;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {

        if (!is_user_authenticated())
            return false;

        $this->user = session('user');

        if (!hash_equals((string)$this->route('id'),
            (string)$this->user['id'])) {
            return false;
        }

        if (!hash_equals((string)$this->route('hash'),
            sha1($this->user['email']))) {
            return false;

        }

        return true;
    }


    public function fulfill()
    {
        $token = request()->cookie('user');
        if (!$token)
            throw new Exception('Lütfem giriş yapınız');

        $res = \Illuminate\Support\Facades\Http::withToken($token)->post(config('app.api_url') . 'auth/user/email/verification/' . $this->user['id'])->json();
        if ($res['message'] == 'verified') {
            return redirect()->route('welcome');
        }

    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            //
        ];
    }
}
