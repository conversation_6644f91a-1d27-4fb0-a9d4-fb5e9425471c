<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Inertia\Middleware;
use Tightenco\Ziggy\Ziggy;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that is loaded on the first page visit.
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determine the current asset version.
     *
     * @param \Illuminate\Http\Request $request
     * @return string|null
     */
    public function version(Request $request)
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @param \Illuminate\Http\Request $request
     * @return mixed[]
     */
    public function share(Request $request)
    {
        //session()->forget('user');
        $user = session('user');
        if (!$user) {
            //$key = md5('user_' . session()->getId());
            //$user = Cache::has($key) ? Cache::get($key) : null;
            $cart = cache_http(config('app.api_url') . 'auth/user/cart/guest/cart', 'get', session()->getId())['data']['items'] ?? [];

            //            ds('Kullancı cache de de yok o yüzden servise git');
            //            ds($cart)->color('red')->label('cart');
            //            ds(session()->getId())->color('red')->label('session()->getId()');
            $user = Http::withToken(session()->getId())->get(config('app.api_url') . 'auth/user/cart/get-auth')->json();
            //            ds($user)->color('red')->label('user servis');
            if (data_get($user, 'message') == 'Unauthenticated') {
                // Kullanıcı Unauthenticated ise şu işlenler yapılır
                $user = null;
                session(['user' => [
                    'status' => 'Unauthenticated',
                    'isUserLoggedIn' => false,
                    'token' => session()->getId(),
                    'cart' => [
                        'items' => $cart,
                        'total' => count($cart),
                    ],
                ]]);
            } else {
                //Cache::put($key, $user, now()->addMinutes(60)); // 60 minutes
                //                session(['user' => [
                //                    'status' => 'Authenticated',
                //                    'isUserLoggedIn' => true,
                //                    'token' => $user['token'],
                //                    'user' => $user['user'],
                //                    'cart' => [
                //                        'items' => $cart,
                //                        'total' => count($cart),
                //                    ],
                ////                    'wishlist' => $user['wishlist'],
                ////                    'orders' => $user['orders'],
                //                ]]);
            }

            //            session(['user' => $user]);
            //            session(['user' => session('user')]);
        } else {
            // Session içerisinde kullanıcı bilgisi yok ama sessionda user var ise
            if (!session('user.user')) {
                // session user Authenticated ama user bilgisi yok ise o zaman alalım
                if (session('user.status') == 'Authenticated' || session('user.user.message') == 'Unauthenticated') {
                    $user = Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/cart/get-auth')->json();
                    session(['user.user' => $user]);
                }
            }

            //            session(['user' => [
            //                'status' => 'Unauthenticated',
            //                'isUserLoggedIn' => false,
            //                'token' => session()->getId(),
            //                'cart' => [
            //                    'items' => $cart,
            //                    'total' => count($cart),
            //                ],
            //            ]]);
        }

        //        ds(session('user.checkCart'));
        //        ds(session('user.isUserLoggedIn'));
        //        ds(session('user.token'));

        if (session('user.checkCart')) {
            if (session('user.isUserLoggedIn')) {
                $cart = cache_http(config('app.api_url') . 'auth/user/cart', 'get', session('user.token'), passCache: true)['data']['items'] ?? [];
            } else {
                $cart = cache_http(config('app.api_url') . 'auth/user/cart/guest/cart', 'get', session('user.token'), passCache: true)['data']['items'] ?? [];
            }

            //            ds($cart)->color('red')->label('cart');
            //            if (!session('user.user')) {
            //                session(['user.user' => $cart['user']]);
            //            }

            session(['user.cart' => [
                'items' => $cart,
                'total' => count($cart),
            ]]);
            session(['user.checkCart' => false]);
        }

        return array_merge(parent::share($request), [
            'auth' => session('user'),
            //            'order_count' => count(\Illuminate\Support\Facades\Http::withToken(request()->cookie('user'))->get(config('app.api_url') . 'auth/user/orders')->collect()['data'] ?? []),
            //            'order_count' => count(cache_http(config('app.api_url') . 'auth/user/orders', 'get', request()->cookie('user'))['data'] ?? []),
            'order_count' => count($user['orders'] ?? []),

            'ziggy' => function () use ($request) {
                return array_merge((new Ziggy)->toArray(), [
                    'location' => $request->url(),
                ]);
            },
            'flash' => [
                'basketStatus' => fn() => $request->session()->get('basketStatus')
            ],
            'success' => function () use ($request) {
                return [
                    'success' => $request->session()->get('success'),
                ];
            },
            // get laravel errors
            //            'errors' => function () use ($request) {
            //                return $request->session()->get('errors')?->getMessageBag() ?? (object)[]; // return empty object if no errors
            //            },
            'menuItems' => \App\Models\Navigation::where('handle', 'ana-menu')->first()->items,
            'menuItemsMobile' => \App\Models\Navigation::getItems(),
            'apiEP' => config('app.api_url'),
            //            'wishlist' => is_user_authenticated() ? \Illuminate\Support\Facades\Http::withToken(request()->cookie('user'))->post(config('app.api_url') . 'auth/user/favourites')->collect()->count() : 0,
            //            'wishlist' => is_user_authenticated() ? count(cache_http(config('app.api_url') . 'auth/user/favourites', 'post', request()->cookie('user'))) : 0,
            'wishlist' => is_user_authenticated() ? count($user['wishlist'] ?? []) : 0,
            //            'cart' => is_user_authenticated() ? count(\Illuminate\Support\Facades\Http::withToken(request()->cookie('user'))->get(config('app.api_url') . 'auth/user/cart')->collect()['data']['items'] ?? []) : count(\Illuminate\Support\Facades\Http::withToken(session()->getId())->get(config('app.api_url') . 'auth/user/cart')->collect()['data']['items'] ?? []),
            //            'cart' => is_user_authenticated() ? count(cache_http(config('app.api_url') . 'auth/user/cart', 'get', request()->cookie('user'))['data']['items'] ?? []) : count(cache_http(config('app.api_url') . 'auth/user/cart/guest/cart', 'get', session()->getId())['data']['items'] ?? []),
            //'cart' => is_user_authenticated() ? count($user['cart']['items'] ?? []) : 0,
            'affiliate' => $request->cookie('affiliate') ?? null,
        ]);
    }
}
