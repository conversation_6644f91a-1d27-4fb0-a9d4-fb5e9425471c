<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

/**
 * Payment Security Headers Middleware
 * 
 * SMS ödeme sistemi için özel güvenlik header'ları ekler
 */
class PaymentSecurityHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Payment sayfaları için özel güvenlik header'ları
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin');
        $response->headers->set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

        // Cache control for sensitive payment pages
        $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate, private');
        $response->headers->set('Pragma', 'no-cache');
        $response->headers->set('Expires', '0');

        // Content Security Policy (CSP) temporarily disabled for 3DS integration
        // $csp = implode('; ', [
        //     "default-src 'self'",
        //     "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://kbtest.test https://cdn.jsdelivr.net http://localhost:5173 https://sandbox-api.iyzipay.com https://api.iyzipay.com", // Vite, maska için + iyzipay
        //     "style-src 'self' 'unsafe-inline'",
        //     "img-src 'self' data: https:",
        //     "font-src 'self' https:",
        //     "connect-src 'self' https://kbtest.test ws://localhost:5173 https://sandbox-api.iyzipay.com https://api.iyzipay.com https://*.bkm.com.tr https://goguvenliodeme.bkm.com.tr",
        //     "frame-ancestors 'none'",
        //     "base-uri 'self'",
        //     "form-action 'self' https://sandbox-api.iyzipay.com https://api.iyzipay.com https://*.bkm.com.tr https://goguvenliodeme.bkm.com.tr https://3dsecure.bkm.com.tr https://secure.bkm.com.tr", // 3DS form submissions için - BKM eklendi
        //     "frame-src 'self' data: blob: https://sandbox-api.iyzipay.com https://api.iyzipay.com https://*.bkm.com.tr https://goguvenliodeme.bkm.com.tr https://*.iyzipay.com" // iframe srcdoc ve 3DS için - BKM wildcard subdomain eklendi
        // ]);
        // $response->headers->set('Content-Security-Policy', $csp);

        return $response;
    }
}
