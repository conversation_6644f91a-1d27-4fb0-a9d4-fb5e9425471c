<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;
use Laravel\Socialite\Facades\Socialite;
use App\Models\User;

class LoginController extends Controller
{
    public function login(Request $request)
    {
        $intended = redirect()->intended()->getTargetUrl(); // Get the intended url, it changes if it calls multiple times
        return Inertia::render('MyAccountDemo', [
            'intended' => Str::of($intended)->contains('odeme') ? $intended : request()->headers->get('referer'),
        ]);
    }

    public function loginAttempt(Request $request)
    {
        $res = Http::withToken(session()->getId())->post(config('app.api_url') . 'auth/login', $request->toArray())->json();

        if ($res['message'] == 'OK') {
            $user = session('user');
            $user['isUserLoggedIn'] = true;
            $user['token'] = $res['data']['accessToken'];
            $user['user'] = $res['data']['user'];
            $user['status'] = 'Authenticated';
            $user['checkCart'] = true;
            session(['user' => $user]);
            $cookie = cookie('user', $res['data']['accessToken'], 48 * 60); // 2 gün login kalsın
            return redirect()->to($request->get('intended', '/'))->withCookie($cookie);
        }

        return Inertia::render('MyAccountDemo', [
            'errors' => $res['errors'],
        ]);
    }

    public function googleRedirect(Request $request)
    {
        return Socialite::driver('google')->redirect();
    }

    public function googleLogin(Request $request)
    {
        $user = Socialite::driver('google')->user();
        logger()->channel('google')->info('google callback user', [$user]);

        if (!$user) {
            return redirect()->back()->withErrors('Google hesabı bulunamadı', 'ouathError');
        }

        if (User::where('email', $user->email)->exists()) {
            logger()->channel('google')->info('user exists ' . $user->email);
            // Login işlemi yap ve ana sayfaya yönlendir
            $res = Http::withToken(session()->getId())->post(config('app.api_url') . 'auth/google-login', [
                'email' => $user->user['email'],
                'given_name' => $user->user['given_name'],
                'family_name' => $user->user['family_name'],
                'id' => $user->user['id'],
                'token' => $user->token,
                'refresh_token' => $user->refreshToken,
            ]);

            logger()->channel('google')->info('google login response for user ' . $user->user['email'], [$res->body()]);
        } else {
            logger()->channel('google')->info('user not exists ' . $user->user['email']);
            // Register işlemi yap ve login işlemi yap sonra ana sayfaya yönlendir
            $res = Http::withToken(session()->getId())->post(config('app.api_url') . 'auth/google-register', [
                'email' => $user->user['email'],
                'given_name' => $user->user['given_name'],
                'family_name' => $user->user['family_name'],
                'id' => $user->user['id'],
                'token' => $user->token,
                'refresh_token' => $user->refreshToken,
            ]);
            logger()->channel('google')->info('google register response for user ' . $user->user['email'], [$res->body()]);
        }

        $res = $res->json();
        logger()->channel('google')->info('google register response for user ', [
            'message' => data_get($res, 'message'),
            'message_code' => data_get($res, 'message') == 'OK',
            'data' => data_get($res, 'data'),
        ]);

        if (data_get($res, 'message') == 'OK') {
            $this->setUserSession($res);
            return redirect()->to('/');
        }
    }

    private function setUserSession($res)
    {
        session(['user.isUserLoggedIn' => true]);
        session(['user.token' => data_get($res, 'data.accessToken')]);
        session(['user.user' => data_get($res, 'data.user')]);
        session(['user.status' => 'Authenticated']);
        session(['user.checkCart' => true]);
    }
}
