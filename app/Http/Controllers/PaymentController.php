<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProcessPaymentRequest;
use App\Services\Hopi\Hopi;
use App\Services\PaymentServiceInterface;
use App\Services\UserMetrik\UserMetrik;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Inertia\Inertia;

class PaymentController extends Controller
{
    public function __construct(
        private PaymentServiceInterface $paymentService
    ) {}

    public function paymentPage(Request $request)
    {
        $res = Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/cart')->json();
        
        // Sepet kontrolü - ürün yoksa sepet sayfasına yönlendir
        $products = data_get($res, 'data.items', []);
        if (empty($products) || count($products) == 0) {
            return redirect()->route('cart.index');
        }
        
        // Hem vadeli hem de normal ürün varsa sepete yönlendir
        $productsWithInstallment = collect($products)->filter(function ($product) {
            return data_get($product, 'is_installment', false) === true;
        })->count();
        
        $productsWithoutInstallment = collect($products)->filter(function ($product) {
            return data_get($product, 'is_installment', false) === false;
        })->count();
        
        if ($productsWithInstallment > 0 && $productsWithoutInstallment > 0) {
            return redirect()->route('cart.index', ['mixed-cart-error' => true]);
        }
        
        $cities = Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/addresses/cities')->json();
        $cards = Http::withToken(session('user.token'))->withHeaders([
            'Trigger' => 'Klaviyo',
        ])->get(config('app.api_url') . 'auth/user/cc')->json();

        UserMetrik::createMetrik('payment_page_visited');
        $total = data_get($res, 'data.total', 0);

        if (session('saveStatus')) {
            $saveCardStatus = true;
        } else
            $saveCardStatus = false;

        return Inertia::render('BillingDemo', [
            "products" => data_get($res, 'data.items'),
            "total" => $total < 0 ? 1 : $total,
            "user" => data_get($res, 'data.user'),
            "discount_amount" => data_get($res, 'data.discount_amount', 0),
            'insurance' => data_get($res, 'data.insurance_total', 0),
            'coupon' => data_get($res, 'data.coupon'),
            'is_mass_payment_enabled' => data_get($res, 'data.is_mass_payment_enabled', false),
            'non_mass_payment_total' => data_get($res, 'data.non_mass_payment_total', 0),
            'cities' => $cities,
            'cards' => $cards,
            'paymentType' => $request->session()->get('paymentType'),
            'validationMessage' => $request->session()->get('validationMessage'),
            "hopi_campaigns" => Hopi::CalculateUserCampaigns(session('user.integrations.hopi.campaigns')),
            "hopi_bird" => session('user.params.birdId'),
            "hopi_balance" => (float)session('user.integrations.hopi.balance'),
            "tosla_active" => session('user.user.id') === "1" ? true : config('tosla.active'),
            'paymnentValidationMessage' => $request->session()->get('paymnentValidationMessage'),
            'saveCardStatus' => $saveCardStatus
        ]);
    }

    public function paymentPageDemo(Request $request)
    {
        $res = Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/cart')->json();
        $cities = Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/addresses/cities')->json();
        $cards = Http::withToken(session('user.token'))->withHeaders([
            'Trigger' => 'Klaviyo',
        ])->get(config('app.api_url') . 'auth/user/cc')->json();
        UserMetrik::createMetrik('payment_page_visited');

        return Inertia::render('BillingDemo', [
            "products" => data_get($res, 'data.items'),
            "total" => data_get($res, 'data.total'),
            "user" => data_get($res, 'data.user'),
            "discount_amount" => data_get($res, 'data.discount_amount', 0),
            'insurance' => data_get($res, 'data.insurance_total', 0),
            'coupon' => data_get($res, 'data.coupon'),
            'is_mass_payment_enabled' => data_get($res, 'data.is_mass_payment_enabled', false),
            'non_mass_payment_total' => data_get($res, 'data.non_mass_payment_total', 0),
            'cities' => $cities,
            'cards' => $cards,
            'paymentType' => $request->session()->get('paymentType'),
            'validationMessage' => $request->session()->get('validationMessage'),
            "hopi_campaigns" => Hopi::CalculateUserCampaigns(session('user.integrations.hopi.campaigns')),
            "hopi_bird" => session('user.params.birdId'),
            "hopi_balance" => (float)session('user.integrations.hopi.balance'),
            "tosla_active" => session('user.user.id') === "1" ? true : config('tosla.active'),
            'paymnentValidationMessage' => $request->session()->get('paymnentValidationMessage'),
        ]);
    }

    public function saveCCOnCart(Request $request)
    {
        $operationCode = Str::random(10);
        $payload = $request->toArray() + ['process' => $operationCode];
        $init3D = Http::withToken(session('user.token'))->post(config('app.api_url') . 'auth/user/save-cc-3ds', $payload);

        if (data_get($init3D->json(), 'type') == 'iyzipay_error') {
            return redirect()->back()->withInput()->withErrors(data_get($init3D->json(), 'message'));
        }

        Cache::add('3d_request_operation_' . $operationCode, session('user'), now()->addSeconds(30));
        Cache::add('3d_request_user_' . session('user.token'), $init3D->body(), now()->addSeconds(30));

        return Inertia::location('show-3d-page');
    }

    public function show3DPage()
    {
        return view('3d')->with([
            'init3D' => Cache::get('3d_request_user_' . session('user.token')),
        ]);
    }

    public function saveCCOnProfile(Request $request)
    {
        $operationCode = Str::random(10);
        $payload = $request->toArray() + ['process' => $operationCode];
        $init3D = Http::withToken(session('user.token'))->post(config('app.api_url') . 'auth/user/save-cc-3ds', $payload);

        if (data_get($init3D->json(), 'type') == 'iyzipay_error') {
            return redirect()->back()->withInput()->withErrors(data_get($init3D->json(), 'message'));
        }

        Cache::add('3d_request_operation_' . $operationCode, session('user'), now()->addSeconds(30));
        Cache::add('3d_request_user_' . session('user.token'), $init3D->body(), now()->addSeconds(30));
        return Inertia::location('show-3d-page');
    }

    /*
    |--------------------------------------------------------------------------
    | SMS Payment System Methods
    |--------------------------------------------------------------------------
    |
    | SMS'den gelen payment link'leri için token tabanlı ödeme sistemi
    |
    */

    /**
     * SMS Payment - Token doğrulama ve ödeme formu gösterimi
     */
    public function show(string $token)
    {
        try {
            // Rate limiting check
            //            $key = 'payment_token_check:' . request()->ip();
            //            if (RateLimiter::tooManyAttempts($key, config('payment.rate_limits.token_check_per_minute', 60))) {
            //                \Log::warning('Payment token check rate limit exceeded', [
            //                    'ip' => getClientIPAddress(),,
            //                    'token' => substr($token, 0, 8) . '...' // Log sadece ilk 8 karakter
            //                ]);
            //
            //                return $this->renderErrorState('rate_limited', [
            //                    'error_message' => 'Çok fazla deneme yapıldı. Lütfen bir dakika sonra tekrar deneyiniz.'
            //                ]);
            //            }

            //            RateLimiter::hit($key, 60); // 60 saniye window

            // Token format validation
            if (strlen($token) !== 32 || !ctype_alnum($token)) {
                \Log::warning('Invalid payment token format', [
                    'token_length' => strlen($token),
                    'token' => $token,
                    'ip' => getClientIPAddress(),
                ]);

                return $this->renderErrorState('invalid_token', [
                    'error_message' => 'Geçersiz token formatı'
                ]);
            }

            // Token'ı payment service ile validate et
            $tokenResult = $this->paymentService->validateToken($token);

            if (!$tokenResult->isValid) {
                \Log::info('Payment token validation failed', [
                    'token' => substr($token, 0, 8) . '...',
                    'error' => $tokenResult->errorMessage,
                    'ip' => getClientIPAddress(),
                ]);

                return $this->renderErrorState('invalid_token', [
                    'error_message' => $tokenResult->errorMessage ?? 'Geçersiz ödeme bağlantısı'
                ]);
            }

            // Successful token validation log
            \Log::info('Payment token validated successfully', [
                'token' => $token,
                'status' => $tokenResult->status,
                'amount' => $tokenResult->amount,
                'ip' => getClientIPAddress(),
            ]);

            // Token durumuna göre uygun component'i render et
            return match ($tokenResult->status) {
                'paid' => $this->renderErrorState('already_paid', [
                    'amount' => $tokenResult->amount,
                    'description' => $tokenResult->description,
                    'currency' => $tokenResult->currency
                ]),
                'expired' => $this->renderErrorState('expired', [
                    'amount' => $tokenResult->amount,
                    'description' => $tokenResult->description,
                    'expired_at' => $tokenResult->expiresAt?->format('d.m.Y H:i')
                ]),
                'pending', 'sent', 'clicked' => $this->renderPaymentForm($token, $tokenResult),
                default => $this->renderErrorState('invalid_status', [
                    'error_message' => 'Bilinmeyen ödeme durumu: ' . $tokenResult->status
                ])
            };
        } catch (\Exception $e) {
            \Log::error('SMS Payment Show Error', [
                'token' => $token,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => getClientIPAddress(),
            ]);

            return $this->renderErrorState('system_error', [
                'error_message' => 'Sistem hatası oluştu. Lütfen daha sonra tekrar deneyiniz.'
            ]);
        }
    }

    /**
     * SMS Payment - Ödeme işlemi
     */
    public function process(ProcessPaymentRequest $request, string $token)
    {
        try {
            // Rate limiting check for payment processing
            // $paymentKey = 'payment_process:' . $token;
            // $ipKey = 'payment_process_ip:' . request()->ip();

            // if (RateLimiter::tooManyAttempts($paymentKey, config('payment.rate_limits.payment_process_per_minute', 5))) {
            //     \Log::warning('Payment process rate limit exceeded for token', [
            //         'token' => $token,
            //         'ip' => getClientIPAddress(),
            //     ]);

            //     return back()->withErrors([
            //         'payment_error' => 'Bu ödeme bağlantısında çok fazla deneme yapıldı. Lütfen daha sonra tekrar deneyiniz.'
            //     ])->withInput();
            // }

            // if (RateLimiter::tooManyAttempts($ipKey, config('payment.rate_limits.payment_process_per_minute', 10))) {
            //     \Log::warning('Payment process rate limit exceeded for IP', [
            //         'ip' => getClientIPAddress(),
            //         'token' => $token
            //     ]);

            //     return back()->withErrors([
            //         'payment_error' => 'IP adresinizden çok fazla ödeme denemesi yapıldı. Lütfen daha sonra tekrar deneyiniz.'
            //     ])->withInput();
            // }

            // RateLimiter::hit($paymentKey, 300); // 5 dakika window
            // RateLimiter::hit($ipKey, 3600); // 1 saat window

            // Token'ı tekrar validate et (double-check security)
            $tokenResult = $this->paymentService->validateToken($token);

            if (!$tokenResult->isValid) {
                \Log::warning('Payment process attempted with invalid token', [
                    'token' => $token,
                    'error' => $tokenResult->errorMessage,
                    'ip' => getClientIPAddress(),
                ]);

                return Inertia::render('Payment/Show', [
                    'token' => $token,
                    'payment_data' => null,
                    'error_type' => 'payment_error',
                    'error_data' => [
                        'payment_error' => 'Geçersiz ödeme bağlantısı'
                    ],
                    'old_input' => $request->all()
                ]);
            }

            if (!$tokenResult->isPayable()) {
                \Log::warning('Payment process attempted with non-payable token', [
                    'token' => $token,
                    'status' => $tokenResult->status,
                    'is_expired' => $tokenResult->isExpired(),
                    'ip' => getClientIPAddress(),
                ]);

                $errorMessage = match ($tokenResult->status) {
                    'paid' => 'Bu ödeme zaten tamamlanmış',
                    'expired' => 'Ödeme bağlantısının süresi dolmuş',
                    default => 'Bu ödeme bağlantısı ile ödeme yapılamaz'
                };

                return Inertia::render('Payment/Show', [
                    'token' => $token,
                    'payment_data' => $tokenResult->toArray(),
                    'error_type' => 'payment_error',
                    'error_data' => [
                        'payment_error' => $errorMessage
                    ],
                    'old_input' => $request->all()
                ]);
            }

            // Kart bilgilerini hazırla
            $cardData = [
                'card_number' => $request->validated('card_number'),
                'card_expiry' => Str::of($request->validated('expiry_month'))->padLeft(2, '0') . '/' . $request->validated('expiry_year'),
                'card_cvv' => $request->validated('cvv'),
                'card_holder' => $request->validated('cardholder_name')
            ];

            // Müşteri bilgilerini hazırla
            $customerData = [
                'email' => $request->validated('email'),
                'invoice_address' => $request->validated('invoice_address')
            ];

            \Log::info('Payment process started', [
                'token' => $token,
                'amount' => $tokenResult->amount,
                'cardholder' => substr($cardData['card_holder'], 0, 5) . '***',
                'card_last4' => substr($cardData['card_number'], -4),
                'customer_email' => $customerData['email'],
                'ip' => getClientIPAddress(),
            ]);

            // Ödeme işlemini gerçekleştir
            $paymentResult = $this->paymentService->processPayment($token, $cardData, $customerData);

            // Log payment result details for debugging
            \Log::info('PaymentResult analysis', [
                'token' => $token,
                'payment_result_successful' => $paymentResult->isSuccessful(),
                'payment_result_requires_3ds' => $paymentResult->requires3DS(),
                'payment_result_success_property' => $paymentResult->success,
                'payment_result_requires_3ds_property' => $paymentResult->requires3DS,
                'payment_result_transaction_id' => $paymentResult->transactionId,
                'payment_result_error_message' => $paymentResult->errorMessage,
                'payment_result_error_code' => $paymentResult->errorCode,
                'payment_result_threeds_html_exists' => !empty($paymentResult->threeDSHtml),
                'payment_result_iyz_token_exists' => !empty($paymentResult->iyzToken),
                'payment_result_status_code' => $paymentResult->statusCode,
                'ip' => getClientIPAddress(),
            ]);

            if ($paymentResult->requires3DS()) {
                // 3D Secure gerekli
                \Log::info('Payment requires 3D Secure', [
                    'token' => $token,
                    'iyz_token' => $paymentResult->iyzToken ?? 'not_provided',
                    'has_threeds_html' => !empty($paymentResult->threeDSHtml),
                    'threeds_html_length' => strlen($paymentResult->threeDSHtml ?? ''),
                    'requires_3ds_check' => $paymentResult->requires3DS,
                    'payment_result_success' => $paymentResult->success,
                    'ip' => getClientIPAddress(),
                ]);

                return Inertia::render('Payment/Show', [
                    'token' => $token,
                    'payment_data' => $tokenResult->toArray(),
                    'payment_response' => [
                        'success' => true,
                        'status' => '3ds',
                        'html_content' => $paymentResult->threeDSHtml,
                        'iyz_token' => $paymentResult->iyzToken,
                    ],
                    'error_type' => null,
                    'error_data' => [],
                    'old_input' => $request->all()
                ]);
            } elseif ($paymentResult->isSuccessful()) {
                // Başarılı ödeme (3DS gerektirmeyen)
                \Log::info('Payment completed successfully', [
                    'token' => $token,
                    'transaction_id' => $paymentResult->transactionId,
                    'amount' => $tokenResult->amount,
                    'ip' => getClientIPAddress(),
                ]);

                // Rate limiter'ları temizle (başarılı ödeme sonrası)
                // RateLimiter::clear($paymentKey);
                // RateLimiter::clear($ipKey);

                // Success sayfasına yönlendir
                return redirect()->route('payment.success', ['token' => $token])
                    ->with('payment_success', [
                        'transaction_id' => $paymentResult->transactionId,
                        'amount' => $tokenResult->amount,
                        'rent_amount' => $tokenResult->rentAmount,
                        'currency' => $tokenResult->currency,
                        'completed_at' => now()->toDateTimeString()
                    ]);
            } else {
                // Ödeme hatası
                \Log::warning('Payment failed', [
                    'token' => $token,
                    'error_code' => $paymentResult->errorCode,
                    'error_message' => $paymentResult->errorMessage,
                    'status_code' => $paymentResult->statusCode,
                    'ip' => getClientIPAddress(),
                ]);

                // Error koduna göre user-friendly mesaj
                $userErrorMessage = $this->getPaymentErrorMessage($paymentResult->errorCode, $paymentResult->errorMessage);

                return Inertia::render('Payment/Show', [
                    'token' => $token,
                    'payment_data' => $tokenResult->toArray(),
                    'error_type' => 'payment_error',
                    'error_data' => [
                        'payment_error' => $userErrorMessage
                    ],
                    'errors' => [
                        'payment_error' => $userErrorMessage
                    ],
                    'old_input' => $request->all()
                ]);
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Validation errors - bu durumda rate limiter hit etme
            // Token'ı tekrar validate et validation error durumunda
            $tokenResult = $this->paymentService->validateToken($token);

            return Inertia::render('Payment/Show', [
                'token' => $token,
                'payment_data' => $tokenResult->isValid ? $tokenResult->toArray() : null,
                'error_type' => 'validation_error',
                'error_data' => $e->errors(),
                'old_input' => $request->all()
            ]);
        } catch (\Exception $e) {
            \Log::error('Payment process system error', [
                'token' => $token,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => getClientIPAddress(),
            ]);

            // Token'ı tekrar validate et system error durumunda
            $tokenResult = $this->paymentService->validateToken($token);

            return Inertia::render('Payment/Show', [
                'token' => $token,
                'payment_data' => $tokenResult->isValid ? $tokenResult->toArray() : null,
                'error_type' => 'payment_error',
                'error_data' => [
                    'payment_error' => 'Sistem hatası oluştu. Lütfen daha sonra tekrar deneyiniz.'
                ],
                'errors' => [
                    'payment_error' => 'Sistem hatası oluştu. Lütfen daha sonra tekrar deneyiniz.'
                ],
                'old_input' => $request->all()
            ]);
        }
    }

    /**
     * Payment error kodlarını user-friendly mesajlara çevir
     */
    private function getPaymentErrorMessage(string $errorCode = null, string $originalMessage = null): string
    {
        return match ($errorCode) {
            'INSUFFICIENT_FUNDS' => 'Kartınızda yeterli bakiye bulunmamaktadır.',
            '3D_SECURE_REQUIRED' => '3D Secure doğrulaması gereklidir. Lütfen bankacılık uygulamanızı kontrol ediniz.',
            'FRAUD_DETECTED' => 'İşlem güvenlik kontrolü nedeniyle reddedildi. Lütfen bankanızla iletişime geçiniz.',
            'CARD_VALIDATION_ERROR' => 'Kart bilgileriniz hatalı. Lütfen kontrol ediniz.',
            'INVALID_TOKEN' => 'Geçersiz ödeme bağlantısı.',
            'TOKEN_NOT_PAYABLE' => 'Bu ödeme bağlantısı ile ödeme yapılamaz.',
            'INVALID_REQUEST' => $originalMessage ?? 'Gönderilen bilgiler hatalı. Lütfen kontrol ediniz.',
            'VALIDATION_ERROR' => $originalMessage ?? 'Gönderilen bilgiler eksik veya hatalı.',
            default => $originalMessage ?? 'Ödeme işlemi sırasında bir hata oluştu. Lütfen tekrar deneyiniz.'
        };
    }

    /**
     * SMS Payment - Başarı sayfası
     */
    public function success(string $token)
    {
        try {
            // // Rate limiting check for success page
            // $key = 'payment_success:' . request()->ip();
            // if (RateLimiter::tooManyAttempts($key, config('payment.rate_limits.success_page_per_minute', 10))) {
            //     \Log::warning('Payment success page rate limit exceeded', [
            //         'ip' => getClientIPAddress(),
            //         'token' => $token
            //     ]);

            //     return $this->renderErrorState('rate_limited', [
            //         'error_message' => 'Çok fazla deneme yapıldı. Lütfen bir dakika sonra tekrar deneyiniz.'
            //     ]);
            // }

            // RateLimiter::hit($key, 60); // 60 saniye window

            // Token format validation
            if (strlen($token) !== 32 || !ctype_alnum($token)) {
                \Log::warning('Invalid payment success token format', [
                    'token_length' => strlen($token),
                    'ip' => getClientIPAddress(),
                ]);

                return $this->renderErrorState('invalid_token', [
                    'error_message' => 'Geçersiz token formatı'
                ]);
            }

            // Token'ı validate et
            $tokenResult = $this->paymentService->validateToken($token);

            logger('payment success token result', [
                'token' => $token,
                'token_result' => $tokenResult->toArray(),
            ]);

            // if (!$tokenResult->isValid) {
            //     \Log::warning('Payment success attempted with invalid token', [
            //         'token' => $token,
            //         'error' => $tokenResult->errorMessage,
            //         'ip' => getClientIPAddress(),
            //     ]);

            //     return $this->renderErrorState('invalid_token', [
            //         'error_message' => $tokenResult->errorMessage ?? 'Geçersiz ödeme bağlantısı'
            //     ]);
            // }

            // Ödeme durumu kontrol et
            if (!$tokenResult->isPaid()) {
                \Log::warning('Payment success page accessed for non-paid token', [
                    'token' => $token,
                    'status' => $tokenResult->status,
                    'ip' => getClientIPAddress(),
                ]);

                $errorMessage = match ($tokenResult->status) {
                    'pending', 'sent', 'clicked' => 'Ödeme henüz tamamlanmamış. Lütfen ödeme işlemini tamamlayınız.',
                    'expired' => 'Ödeme bağlantısının süresi dolmuş.',
                    default => 'Bu ödeme bağlantısı için success sayfasına erişim sağlanamaz.'
                };

                // Status'e göre uygun sayfaya yönlendir
                return match ($tokenResult->status) {
                    'pending', 'sent', 'clicked' => redirect()->route('payment.show', ['token' => $token])
                        ->with('info', 'Ödeme işlemini tamamlamak için formu doldurunuz.'),
                    'expired' => $this->renderErrorState('expired', [
                        'amount' => $tokenResult->amount,
                        'description' => $tokenResult->description,
                        'expired_at' => $tokenResult->expiresAt?->format('d.m.Y H:i')
                    ]),
                    default => $this->renderErrorState('invalid_status', [
                        'error_message' => $errorMessage
                    ])
                };
            }

            // Başarılı ödeme - success sayfasını render et
            \Log::info('Payment success page accessed', [
                'token' => $token,
                'amount' => $tokenResult->amount,
                'ip' => getClientIPAddress(),
            ]);

            // Session'dan payment success verisini al (process method'undan gelen)
            // $paymentSuccessData = session('payment_success', []);

            // // Eğer session'da payment_success verisi yoksa, sayfa ikinci kez açılıyor demektir
            // // Kullanıcıyı ana sayfaya yönlendir
            // if (empty($paymentSuccessData)) {
            //     \Log::info('Payment success page accessed without session data - redirecting to home', [
            //         'token' => $token,
            //         'ip' => getClientIPAddress(),
            //     ]);

            //     return redirect('/')->with('info', 'Ödeme işlemi tamamlandı.');
            // }

            return Inertia::render('Payment/Success', [
                'token' => $token,
                // 'payment_data' => $tokenResult->toArray(),
                'transaction_data' => [
                    'transaction_id' => $tokenResult->transactionId,
                    'amount' =>  $tokenResult->amount,
                    'rent_amount' => $tokenResult->amount,
                    'currency' => 'TRY',
                    'completed_at' => now()->toDateTimeString()
                ],
                'success_timestamp' => now()->toDateTimeString()
            ]);
        } catch (\Exception $e) {
            \Log::error('Payment success page error', [
                'token' => $token,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => getClientIPAddress(),
            ]);

            return $this->renderErrorState('system_error', [
                'error_message' => 'Sistem hatası oluştu. Lütfen daha sonra tekrar deneyiniz.'
            ]);
        }
    }

    /**
     * Error state'leri için helper method
     */
    private function renderErrorState(string $errorType, array $data = [])
    {
        return Inertia::render('Payment/Show', [
            'error_type' => $errorType,
            'error_data' => $data,
            'token' => null,
            'payment_data' => null
        ]);
    }

    /**
     * Payment form render helper
     */
    private function renderPaymentForm(string $token, $tokenResult)
    {
        return Inertia::render('Payment/Show', [
            'token' => $token,
            'payment_data' => $tokenResult->toArray(),
            'error_type' => null,
            'error_data' => null
        ]);
    }

    /**
     * Handle 3D Secure callback
     */
    public function callback(Request $request)
    {
        \Log::info('🔄 Payment callback received', [
            'timestamp' => now()->toISOString(),
            'source' => $request->get('source'),
            'status' => $request->get('status'),
            'all_params' => $request->all(),
            'ip' => getClientIPAddress(),
            'user_agent' => $request->userAgent(),
        ]);

        $source = $request->get('source');
        $status = $request->get('status');

        if ($source === 'payment_sms') {
            \Log::info('📱 Payment SMS callback detected', [
                'timestamp' => now()->toISOString(),
                'status' => $status,
                'ip' => getClientIPAddress(),
            ]);

            // Get the referrer URL to redirect back to payment page
            $referrer = $request->header('referer');
            if (!$referrer) {
                \Log::warning('⚠️ No referrer in callback request', [
                    'timestamp' => now()->toISOString(),
                    'source' => $source,
                    'status' => $status,
                ]);
                return redirect('/');
            }

            // Add callback parameters to the referrer URL
            $parsedUrl = parse_url($referrer);
            $query = isset($parsedUrl['query']) ? $parsedUrl['query'] . '&' : '';
            $query .= http_build_query([
                'source' => 'payment_sms',
                'status' => $status
            ]);

            $callbackUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
            if (isset($parsedUrl['port'])) {
                $callbackUrl .= ':' . $parsedUrl['port'];
            }
            $callbackUrl .= $parsedUrl['path'] . '?' . $query;

            \Log::info('🔗 Redirecting to callback URL', [
                'timestamp' => now()->toISOString(),
                'callback_url' => $callbackUrl,
                'referrer' => $referrer,
            ]);

            return redirect($callbackUrl);
        }

        \Log::warning('⚠️ Unknown callback source', [
            'timestamp' => now()->toISOString(),
            'source' => $source,
            'status' => $status,
            'all_params' => $request->all(),
        ]);

        // For unknown sources, redirect to home
        return redirect('/');
    }
}
