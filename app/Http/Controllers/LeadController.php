<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;

class LeadController extends Controller
{
    public function rentandbuy(Request $request)
    {
        // Form validasyonu
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email',
            'phone' => 'required|string',
            'product_id' => 'required',
            'tckn' => 'required|digits:11'
        ], [
            'first_name.required' => 'Ad alanı zorunludur',
            'last_name.required' => 'Soyad alanı zorunludur',
            'email.required' => 'E-posta alanı zorunludur',
            'email.email' => 'Geçerli bir e-posta adresi giriniz',
            'phone.required' => 'Telefon alanı zorunludur',
            'product_id.required' => 'Ürün bilgisi eksik',
            'tckn.required' => 'Kimlik Numarası alanı zorunludur',
            'tckn.digits' => 'Kimlik Numarası 11 haneli olmalıdır'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator, 'leadValidation')
                ->withInput();
        }

        // API'ye gönder
        $response = Http::post(config('app.api_url') . 'lead/rent-and-buy', $request->all());

        if ($response->successful()) {
            return redirect()->back()->with('success', 'Form başarıyla gönderildi');
        }

        return redirect()->back()->withErrors([
            'Bir hata oluştu. Lütfen tekrar deneyiniz.'
        ], 'leadValidation');
    }
}
