<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Illuminate\Support\Facades\Http;

class TicketController extends Controller
{
    public function index()
    {
        $res = Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/orders');

        if ($res->successful()) {
            return Inertia::render('SupportRequests', [
                'orders' => $res->json('data', []),
            ]);
        }

        return Inertia::render('SupportRequests', [
            'orders' => data_get($res, 'data', []),
        ]);
    }

    public function allRequests()
    {
        $res = Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/support-request');

        if ($res->successful()) {
            return Inertia::render('OldRequest', [
                'supportRequests' => $res->json('support_request', []),
            ]);
        }

        return Inertia::render('OldRequest', [
            'supportRequests' => [],
        ]);
    }

    public function create()
    {
        $res = Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/orders');
        return Inertia::render('CreateRequest', [
            'orders' => $res->json('data', []),
            'pid' => request()->get('pid'),
            'req' => request()->get('req'),
            'order' => request()->get('order'),
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product' => 'required',
            'order' => 'required',
            'supportRequestType' => 'required',
            'note' => 'required',
        ], [
            'product.required' => 'Ürün seçimi zorunludur.',
            'order.required' => 'Sipariş seçimi zorunludur.',
            'supportRequestType.required' => 'Destek talep tipi seçimi zorunludur.',
            'note.required' => 'Not girilmesi zorunludur.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->post(config('app.api_url') . 'auth/user/support-request', $request->toArray())->json();
        if (data_get($res, 'message') == 'Destek Talebiniz Oluşturuldu') {
            return redirect()->back()->with('success', 'Destek Talebiniz Oluşturuldu');
        }

        if (data_get($res, 'message') == '1 dakika içerisinde sadece bir destek talebi oluşturabilirsiniz. Lütfen bekleyin.') {
            return redirect()->back()->withErrors(['1 dakika içerisinde sadece bir destek talebi oluşturabilirsiniz. Lütfen bekleyin.']);
        }

        return redirect()->back()->with('error', 'Bir hata oluştu. Lütfen tekrar deneyiniz.');
    }
}
