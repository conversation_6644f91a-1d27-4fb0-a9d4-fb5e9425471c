<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ProductController extends Controller
{
    public function index($productId)
    {
        if (Str::of($productId)->endsWith('-teknosa')) {
            $productId = Str::of($productId)->replace('-teknosa', '');
            $product = Http::get(config('app.product_api_url') . 'products/' . $productId)->json()['product'];
            $cookie = cookie('affiliate_teknosa', $product['id'], 24 * 60 * 30);
            return redirect()->to('/urun/' . $productId . '?teknosa')->withCookie($cookie);
        }

        if (Str::of($productId)->endsWith('-abonesepeti')) {
            $productId = Str::of($productId)->replace('-abonesepeti', '');
            $product = Http::get(config('app.product_api_url') . 'products/' . $productId)->json()['product'];
            $cookie = cookie('affiliate_abonesepeti', $product['id'], 24 * 60 * 30);
            return redirect()->to('/urun/' . $productId . '?abonesepeti')->withCookie($cookie);
        }

        //$product = Http::get(config('app.product_api_url') . 'products/' . $productId)->json()['product'];
        $product = Http::get(config('app.product_api_url') . 'products/' . $productId)->object();
        if (!$product) {
            return Inertia::render('404', [])->toResponse(request())->setStatusCode(404);
        }

        $product = $product->product;
        // Eğer ürün kapalı ise şimdi ana sayfa'ya yönlendir. Kategorisine yönlenecek
        if ($product->status != 'published') {
            return redirect()->route('welcome');
        }

        // Eğer kullanıcı login değil ise zaten ürün favori değil demektir.
        if (session('user.isUserLoggedIn')) {
            $res = Http::withToken(session('user.token'))->post(config('app.api_url') . 'auth/user/favourite/' . $product->id . '/status')->json();
        }

        //$productVariantPrices = (object) \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'product/prices/' . $product->id)->json()['prices'];

        // is_installment değerine göre hangi Vue component'inin render edileceğine karar ver
        // $component = ($product->is_installment ?? false) ? 'ProductDetailInstallment' : 'ProductDetail'; // ProductDetailInstallment.vue başarılı şekilde alt componentlere ayrılır ise bu kullanılabilir.
        $component = ($product->is_installment ?? false) ? 'ProductDetailDemo' : 'ProductDetail';

        return Inertia::render($component, [
            //'product' => \App\Models\Product::getById($productId),
            'product' => $product,
            'favourite' => $res->status ?? 'not_exist',
            'productFeatures' => \App\Models\ProductFeature::getFeatures($product),
            'productBoxContent' => \App\Models\ProductBoxContent::productBoxContent($product),
            //'productVariantPrices' => $productVariantPrices,
            'isPurchased' => Inertia::lazy(fn() => check_is_rented_before($product->id)),
            //'reviews' => Inertia::lazy(fn() => \Illuminate\Support\Facades\Http::connectTimeout(2)->get('https://stamped.io/api/widget/reviews?productId=' . ($product['wp_product_id'] ?? 999999) . '&productType&productTitle&email&isWithPhotos&minRating&take&page&dateFrom&dateTo&sortReviews&tags&storeUrl=kiralabunu.com&apiKey=pubkey-vO95ESN67VCe6cwn2Sf1rDp0tv5i21')->json()),
            'reviews' => Inertia::lazy(fn() => cache_http('https://stamped.io/api/widget/reviews?productId=' . ($product->wp_product_id ?? 999999) . '&productType&productTitle&email&isWithPhotos&minRating&take&page&dateFrom&dateTo&sortReviews&tags&storeUrl=kiralabunu.com&apiKey=pubkey-vO95ESN67VCe6cwn2Sf1rDp0tv5i21', 'get', '', 60 * 24)),
            //'reviews' => Inertia::lazy(fn() => []),
            'isInsurangeOptionsEnabled' => collect($product->collections)->pluck('id')->intersect([47, 49, 54, 84])->count() > 0,
            'commentSubmittedSuccessfully' => Inertia::lazy(fn() => session('commentSubmittedSuccessfully_' . $product->wp_product_id ?? $product->id)),
            'isAynet' => Str::of($productId)->endsWith('-aynet'),
        ]);
    }

    public function indexDemo($productId)
    {
        if (Str::of($productId)->endsWith('-teknosa')) {
            $productId = Str::of($productId)->replace('-teknosa', '');
            $product = Http::get(config('app.product_api_url') . 'products/' . $productId)->json()['product'];
            $cookie = cookie('affiliate_teknosa', $product['id'], 24 * 60 * 30);
            return redirect()->to('/urun/' . $productId . '?teknosa')->withCookie($cookie);
        }

        if (Str::of($productId)->endsWith('-abonesepeti')) {
            $productId = Str::of($productId)->replace('-abonesepeti', '');
            $product = Http::get(config('app.product_api_url') . 'products/' . $productId)->json()['product'];
            $cookie = cookie('affiliate_abonesepeti', $product['id'], 24 * 60 * 30);
            return redirect()->to('/urun/' . $productId . '?abonesepeti')->withCookie($cookie);
        }

        //$product = Http::get(config('app.product_api_url') . 'products/' . $productId)->json()['product'];
        $product = Http::get(config('app.product_api_url') . 'products/' . $productId)->object();
        if (!$product) {
            return Inertia::render('404', [])->toResponse(request())->setStatusCode(404);
        }

        $product = $product->product;
        // Eğer ürün kapalı ise şimdi ana sayfa'ya yönlendir. Kategorisine yönlenecek
        if ($product->status != 'published') {
            return redirect()->route('welcome');
        }

        // Eğer kullanıcı login değil ise zaten ürün favori değil demektir.
        if (session('user.isUserLoggedIn')) {
            $res = Http::withToken(session('user.token'))->post(config('app.api_url') . 'auth/user/favourite/' . $product->id . '/status')->json();
        }

        //$productVariantPrices = (object) \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'product/prices/' . $product->id)->json()['prices'];
        return Inertia::render('ProductDetailDemo', [
            //'product' => \App\Models\Product::getById($productId),
            'product' => $product,
            'favourite' => $res->status ?? 'not_exist',
            'productFeatures' => \App\Models\ProductFeature::getFeatures($product),
            'productBoxContent' => \App\Models\ProductBoxContent::productBoxContent($product),
            //'productVariantPrices' => $productVariantPrices,
            'isPurchased' => Inertia::lazy(fn() => check_is_rented_before($product->id)),
            //'reviews' => Inertia::lazy(fn() => \Illuminate\Support\Facades\Http::connectTimeout(2)->get('https://stamped.io/api/widget/reviews?productId=' . ($product['wp_product_id'] ?? 999999) . '&productType&productTitle&email&isWithPhotos&minRating&take&page&dateFrom&dateTo&sortReviews&tags&storeUrl=kiralabunu.com&apiKey=pubkey-vO95ESN67VCe6cwn2Sf1rDp0tv5i21')->json()),
            'reviews' => Inertia::lazy(fn() => cache_http('https://stamped.io/api/widget/reviews?productId=' . ($product->wp_product_id ?? 999999) . '&productType&productTitle&email&isWithPhotos&minRating&take&page&dateFrom&dateTo&sortReviews&tags&storeUrl=kiralabunu.com&apiKey=pubkey-vO95ESN67VCe6cwn2Sf1rDp0tv5i21', 'get', '', 60 * 24)),
            //'reviews' => Inertia::lazy(fn() => []),
            'isInsurangeOptionsEnabled' => collect($product->collections)->pluck('id')->intersect([47, 49, 54, 84])->count() > 0,
            'commentSubmittedSuccessfully' => Inertia::lazy(fn() => session('commentSubmittedSuccessfully_' . $product->wp_product_id ?? $product->id)),
        ]);
    }
}
