<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use App\Services\Hopi\Hopi;

class CartController extends Controller
{
    // my cart
    public function myCart()
    {
        if (session('user.isUserLoggedIn')) {
            $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/cart');
        } else {
            // Misa<PERSON>r kullanıcı sepeti erişimi EP gerekiyor
            $res = \Illuminate\Support\Facades\Http::withToken(session()->getId())->get(config('app.api_url') . 'auth/user/cart/guest/cart');
        }

        if (true || $res->successful()) {
            $res = $res->json();
            // Sepetin toplam tutarını session'a atıyoruz
            session(['user.cart.total' => $res['data']['total'] ?? 0]);

            $total = data_get($res, 'data.total', 0);

            return Inertia::render('CheckoutDemo', [
                "products" => $res['data']['items'] ?? [],
                "total" => $total < 0 ? 1 : $total,
                "discount_amount" => $res['data']['discount_amount'] ?? 0,
                "sub_total" => $res['data']['sub_total'] ?? 0,
                'insurance' => $res['data']['insurance_total'] ?? 0,
                'coupon' => $res['data']['coupon'] ?? null,
                'is_mass_payment_enabled' => data_get($res, 'data.is_mass_payment_enabled', false),
                'non_mass_payment_total' => data_get($res, 'data.non_mass_payment_total', 0),
                "hopi_campaigns" => Hopi::CalculateUserCampaigns(session('user.integrations.hopi.campaigns')),
                "hopi_bird" => session('user.params.birdId'),
                "hopi_balance" => session('user.integrations.hopi.balance'),
                "hopi_active" => config('hopi.active'),
                'pgs_active' => config('pgs.active'),
            ]);
        }

        // dd($res->body());
    }

    // add to cart
    public function addToCart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|integer',
            'month' => 'required|integer',
            'product_variant_id' => 'required|integer|min:1',
            'is_insurance_requested' => 'nullable|boolean',
        ], [
            'product_id.required' => 'Ürün seçimi zorunludur.',
            'month.required' => 'Kiralama süresi zorunludur.',
            'product_variant_id.required' => 'Ürün renk seçimi zorunludur.',
            'product_variant_id.integer' => 'Ürün renk seçimi zorunludur.',
            'product_variant_id.min' => 'Ürün renk seçimi zorunludur.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withInput()->withErrors($validator->errors());
        }

        if (session('user.isUserLoggedIn')) {
            $cacheKey = config('app.api_url') . 'auth/user/cart' . session('user.token');
            Cache::tags(['http'])->forget(cache_key($cacheKey));
            $res = Http::withToken(session('user.token'))->post(config('app.api_url') . 'auth/user/cart/add-product', $request->toArray());
        } else {
            $cacheKey = config('app.api_url') . 'auth/user/cart/guest/cart' . session()->getId();
            Cache::tags(['http'])->forget(cache_key($cacheKey));
            $res = Http::withToken(session()->getId())->post(config('app.api_url') . 'auth/user/cart/guest/add-product', $request->toArray());
        }

        if ($res->successful()) {
            session(['user.checkCart' => true]);
            session()->flash('basketStatus', [
                'type' => 'added',
                'message' => 'Ürün sepete eklendi',
            ]);
        }
    }

    // update cart
    public function updateCart(Request $request)
    {
        session(['user.checkCart' => true]);
        if (session('user.isUserLoggedIn')) {
            $cacheKey = config('app.api_url') . 'auth/user/cart' . session('user.token');
            Cache::tags(['http'])->forget($cacheKey);
            Http::withToken(session('user.token'))->put(config('app.api_url') . 'auth/user/cart/update-cart-item', $request->toArray())->json();
        } else {
            $cacheKey = config('app.api_url') . 'auth/user/cart/guest/cart' . session()->getId();
            Cache::tags(['http'])->forget($cacheKey);
            Http::withToken(session()->getId())->put(config('app.api_url') . 'auth/user/cart/guest/update-cart-item', $request->toArray())->json();
        }
    }

    // remove product from cart
    public function removeProduct(Request $request)
    {
        session(['user.checkCart' => true]);
        if (session('user.isUserLoggedIn')) {
            $cacheKey = config('app.api_url') . 'auth/user/cart' . session('user.token');
            Cache::tags(['http'])->forget($cacheKey);
            Http::withToken(session('user.token'))->delete(config('app.api_url') . 'auth/user/cart/remove-product', $request->toArray())->json();
        } else {
            $cacheKey = config('app.api_url') . 'auth/user/cart/guest/cart' . session()->getId();
            Cache::tags(['http'])->forget($cacheKey);
            // Misafir kullanıcı sepeti erişimi EP gerekiyor
            Http::withToken(session()->getId())->delete(config('app.api_url') . 'auth/user/cart/guest/remove-product', $request->toArray())->json();
        }

        //    return redirect(RouteServiceProvider::CART);
    }

    // mass update cart
    public function massUpdateCart(Request $request)
    {
        session(['user.checkCart' => true]);
        if (session('user.isUserLoggedIn')) {
            $cacheKey = config('app.api_url') . 'auth/user/cart' . session('user.token');
            Cache::tags(['http'])->forget($cacheKey);
            Http::withToken(session('user.token'))->put(config('app.api_url') . 'auth/user/cart/update', $request->toArray())->json();
        } else {
            $cacheKey = config('app.api_url') . 'auth/user/cart/guest/cart' . session()->getId();
            Cache::tags(['http'])->forget($cacheKey);
            // Misafir kullanıcı sepeti erişimi EP gerekiyor
            Http::withToken(session()->getId())->put(config('app.api_url') . 'auth/user/cart/guest/update', $request->toArray())->json();
        }
    }

    // mycart demo
    public function myCartDemo()
    {
        if (session('user.isUserLoggedIn')) {
            $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/cart')->json();
        } else {
            // Misafir kullanıcı sepeti erişimi EP gerekiyor
            $res = \Illuminate\Support\Facades\Http::withToken(session()->getId())->get(config('app.api_url') . 'auth/user/cart/guest/cart')->json();
        }

        // Sepetin toplam tutarını session'a atıyoruz
        session(['user.cart.total' => $res['data']['total'] ?? 0]);

        return Inertia::render('CheckoutDemo', [
            "products" => $res['data']['items'] ?? [],
            "total" => $res['data']['total'] ?? 0,
            "discount_amount" => $res['data']['discount_amount'] ?? 0,
            "sub_total" => $res['data']['sub_total'] ?? 0,
            'insurance' => $res['data']['insurance_total'] ?? 0,
            'coupon' => $res['data']['coupon'] ?? null,
            'is_mass_payment_enabled' => data_get($res, 'data.is_mass_payment_enabled', false),
            'non_mass_payment_total' => data_get($res, 'data.non_mass_payment_total', 0),
            "hopi_campaigns" => Hopi::CalculateUserCampaigns(session('user.integrations.hopi.campaigns')),
            "hopi_bird" => session('user.params.birdId'),
            "hopi_balance" => session('user.integrations.hopi.balance'),
            "hopi_active" => config('hopi.active'),
            'pgs_active' => config('pgs.active'),
        ]);
    }
}
