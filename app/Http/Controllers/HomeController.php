<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

class HomeController extends Controller
{
    public function index()
    {
        $sliders = Cache::tags(['anasayfa'])->get('sliders');
        if (!$sliders) {
            $sliders = Http::withToken(config('app.api_token'))->get(config('app.api_url') . 'sliders');
            //        ds($sliders->body())->label('main');
            $sliders = $sliders->json();
            Cache::tags(['anasayfa'])->put('sliders', $sliders, now()->addMinutes(60 * 24)); // 1 gün cache
        }

        $allProducts = Cache::tags(['anasayfa'])->get('all_products');
        if (!$allProducts) {
            $allProducts = Http::get(config('app.product_api_url') . 'category/all?orderBy=yeniden-eskiye')->json();
            Cache::tags(['anasayfa'])->put('all_products', $allProducts, now()->addMinutes(60 * 24)); // 1 gün cache
        }
        $shuffledProducts = collect(data_get($allProducts, 'items.data'))->shuffle()->toArray();
        data_set($allProducts, 'items.data', $shuffledProducts);

        $discounted = Cache::tags(['anasayfa'])->get('all_discounted');
        if (!$discounted) {
            //$discounted = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'discounted?page=1&orderBy=yeniden-eskiye')->json();
            $discounted = Http::get(config('app.product_api_url') . 'tag/anasayfaindirim')->json();
            Cache::tags(['anasayfa'])->put('all_discounted', $discounted, now()->addMinutes(60 * 24)); // 1 gün cache
        }
        $newtagged = Cache::tags(['anasayfa'])->get('newtagged');
        if (!$newtagged) {
            $newtagged = Http::get(config('app.product_api_url') . 'tag/YENI')->json();
            Cache::tags(['anasayfa'])->put('newtagged', $newtagged, now()->addMinutes(60 * 24)); // 1 gün cache
        }

        $shuffledNewtagged = collect(data_get($newtagged, 'items.data'))->shuffle()->toArray();
        data_set($newtagged, 'items.data', $shuffledNewtagged);

        $mostRentedProducts = Cache::tags(['anasayfa'])->get('most_rented_products');
        if (!$mostRentedProducts) {
            $mostRentedProducts = Http::get(config('app.product_api_url') . 'tag/encokkiralanan')->json();
            Cache::tags(['anasayfa'])->put('most_rented_products', $mostRentedProducts, now()->addMinutes(60 * 24)); // 1 gün cache
        }

        $telefonSpeedPage = Cache::tags(['anasayfa'])->get('telefon_speed_page');
        if (!$telefonSpeedPage) {
            $telefonSpeedPage = Http::get(config('app.product_api_url') . 'category/telefon?page=1')->json();
            Cache::tags(['anasayfa'])->put('telefon_speed_page', $telefonSpeedPage, now()->addMinutes(60 * 24)); // 1 gün cache
        }

        // Kullanıcı login ise favori ürünleri getir ve kategori ürünleri ile kesişenlere bu durumu işle
        if (session('user.isUserLoggedIn')) {
            //$favourites = \Illuminate\Support\Facades\Http::withToken(request()->cookie('user'))->post(config('app.api_url') . 'auth/user/favourites')->json();
            $favourites = cache_http(config('app.api_url') . 'auth/user/favourites', 'post', session('user.token'));

            $favourites = collect($favourites)->pluck('product_id')->toArray();
            $allProducts['items']['data'] = collect($allProducts['items']['data'])->map(function ($product) use ($favourites) {
                $product['favourite'] = in_array($product['id'], $favourites);
                return $product;
            })->toArray();

            $mostRentedProducts['items']['data'] = collect($mostRentedProducts['items']['data'])->map(function ($product) use ($favourites) {
                $product['favourite'] = in_array($product['id'], $favourites);
                return $product;
            })->toArray();
        }

        return Inertia::render('WelcomeDemo', [
            'canLogin' => Route::has('login'),
            'canRegister' => Route::has('register'),
            'sliders' => $sliders,
            'allProducts' => Inertia::lazy(fn() => $allProducts),
            'mostRentedProducts' => Inertia::lazy(fn() => $mostRentedProducts),
            'newtagged' => Inertia::lazy(fn() => $newtagged),
            'discounted' => Inertia::lazy(fn() => $discounted),
            'category_telefon' => Inertia::lazy(fn() => $telefonSpeedPage),
            'category_bilgisayar' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/main?page=1')),
            'category_tablet' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/tablet?page=1')),
            'category_saat' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/saat?page=1')),
            'category_oyun' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/oyun-konsolu-vr?page=1')),
            'category_kameralar' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/kamera?page=1')),
            'category_ses' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/ses-muzik?page=1')),
            'category_evaletleri' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/elektrikli-ev-aletleri?page=1')),
            'category_emobilite' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/e-mobilite?page=1')),
            'category_saglik' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/saglik?page=1')),
            'category_spor' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/spor?page=1')),
            'category_eveglence' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/ev-eglence?page=1')),
        ]);
    }

    public function indexDemo()
    {
        $sliders = Cache::tags(['anasayfa'])->get('sliders');
        if (!$sliders) {
            $sliders = Http::withToken(config('app.api_token'))->get(config('app.api_url') . 'sliders');
            //        ds($sliders->body())->label('main');
            $sliders = $sliders->json();
            Cache::tags(['anasayfa'])->put('sliders', $sliders, now()->addMinutes(60 * 24)); // 1 gün cache
        }

        $allProducts = Cache::tags(['anasayfa'])->get('all_products');
        if (!$allProducts) {
            $allProducts = Http::get(config('app.product_api_url') . 'category/all?orderBy=yeniden-eskiye')->json();
            Cache::tags(['anasayfa'])->put('all_products', $allProducts, now()->addMinutes(60 * 24)); // 1 gün cache
        }
        $shuffledProducts = collect(data_get($allProducts, 'items.data'))->shuffle()->toArray();
        data_set($allProducts, 'items.data', $shuffledProducts);

        $discounted = Cache::tags(['anasayfa'])->get('all_discounted');
        if (!$discounted) {
            //$discounted = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'discounted?page=1&orderBy=yeniden-eskiye')->json();
            $discounted = Http::get(config('app.product_api_url') . 'tag/anasayfaindirim')->json();
            Cache::tags(['anasayfa'])->put('all_discounted', $discounted, now()->addMinutes(60 * 24)); // 1 gün cache
        }

        $mostRentedProducts = Cache::tags(['anasayfa'])->get('most_rented_products');
        if (!$mostRentedProducts) {
            $mostRentedProducts = Http::get(config('app.product_api_url') . 'tag/encokkiralanan')->json();
            Cache::tags(['anasayfa'])->put('most_rented_products', $mostRentedProducts, now()->addMinutes(60 * 24)); // 1 gün cache
        }

        $telefonSpeedPage = Cache::tags(['anasayfa'])->get('telefon_speed_page');
        if (!$telefonSpeedPage) {
            $telefonSpeedPage = Http::get(config('app.product_api_url') . 'category/telefon?page=1')->json();
            Cache::tags(['anasayfa'])->put('telefon_speed_page', $telefonSpeedPage, now()->addMinutes(60 * 24)); // 1 gün cache
        }

        // Kullanıcı login ise favori ürünleri getir ve kategori ürünleri ile kesişenlere bu durumu işle
        if (session('user.isUserLoggedIn')) {
            //$favourites = \Illuminate\Support\Facades\Http::withToken(request()->cookie('user'))->post(config('app.api_url') . 'auth/user/favourites')->json();
            $favourites = cache_http(config('app.api_url') . 'auth/user/favourites', 'post', session('user.token'));

            $favourites = collect($favourites)->pluck('product_id')->toArray();
            $allProducts['items']['data'] = collect($allProducts['items']['data'])->map(function ($product) use ($favourites) {
                $product['favourite'] = in_array($product['id'], $favourites);
                return $product;
            })->toArray();

            $mostRentedProducts['items']['data'] = collect($mostRentedProducts['items']['data'])->map(function ($product) use ($favourites) {
                $product['favourite'] = in_array($product['id'], $favourites);
                return $product;
            })->toArray();
        }

        return Inertia::render('WelcomeDemo', [
            'canLogin' => Route::has('login'),
            'canRegister' => Route::has('register'),
            'sliders' => $sliders,
            'allProducts' => Inertia::lazy(fn() => $allProducts),
            'mostRentedProducts' => Inertia::lazy(fn() => $mostRentedProducts),
            'discounted' => Inertia::lazy(fn() => $discounted),
            'category_telefon' => Inertia::lazy(fn() => $telefonSpeedPage),
            'category_bilgisayar' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/main?page=1')),
            'category_tablet' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/tablet?page=1')),
            'category_saat' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/saat?page=1')),
            'category_oyun' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/oyun-konsolu-vr?page=1')),
            'category_kameralar' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/kamera?page=1')),
            'category_ses' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/ses-muzik?page=1')),
            'category_evaletleri' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/elektrikli-ev-aletleri?page=1')),
            'category_emobilite' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/e-mobilite?page=1')),
            'category_saglik' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/saglik?page=1')),
            'category_spor' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/spor?page=1')),
            'category_eveglence' => Inertia::lazy(fn() => cache_http(config('app.product_api_url') . 'category/ev-eglence?page=1')),
        ]);
    }
}
