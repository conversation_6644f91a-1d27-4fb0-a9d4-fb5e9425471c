<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Inertia\Inertia;

class CategoryController extends Controller
{
    public function getMainCategory($mainCategori, Request $request)
    {
        return $this->getCategory($mainCategori, $mainCategori, $request); // first parameter wont be used, just adjust the second one
    }

    public function getMobilityCategory(Request $request)
    {
        $mainCategori = 'kiralamobil';
        $menuName = 'kiralamobil';
        //        $startTime = microtime(true);

        $category = Cache::tags(['kategori'])->get('get_category_items_' . md5($request->fullUrl()));
        if (!$category) {
            if ($request->has('filter')) {
                $url = config('app.product_api_url') . 'category/' . $menuName . '?page=' . $request->query('page') . '&';
                foreach ($request->get('filter', []) as $key => $value) {
                    if ($value)
                        $url .= 'filter[' . $key . ']' . '=' . $value . '&';
                }
                $category = \Illuminate\Support\Facades\Http::timeout(80)->get($url)->json();
            } else {
                $category = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'category/' . $menuName . '?page=' . $request->query('page'))->json();
                Cache::tags(['kategori'])->put('get_category_items_' . md5($request->fullUrl()), $category, now()->addMinutes(60 * 24)); // 1 gün cache
            }
        }

        // Loop over the category and change pagination links
        $category['items']['element']['products']['pagination']['links'] = collect($category['items']['element']['products']['pagination']['links'])->map(function ($link) use ($request) {
            $link['url'] = Str::of($link['url'])->replace(config('app.product_api_url') . 'category', config('app.app_url') . '/kategoriler') . '&' . prepare_filter_query($request->get('filter', []));
            return $link;
        })->toArray();

        $cannoical = '/kategoriler/' . $menuName;
        if ($menuName)
            $cannoical = $request->fullUrl();

        $discounted = Cache::tags(['anasayfa'])->get('kiralamobil');
        if (!$discounted) {
            $discounted = Http::get(config('app.product_api_url') . 'tag/kiralamobil')->json();
            Cache::tags(['anasayfa'])->put('kiralamobil', $discounted, now()->addMinutes(60 * 24)); // 1 gün cache
        }
        return Inertia::render('KiralaMobil', [
            'category' => $category,
            'brands' => $category['items']['element']['filters']['brands'],
            'pageType' => 'category',
            'canonical' => $cannoical,
            'filters' => $request->get('filter', []),
            'discounted' => Inertia::lazy(fn() => $discounted),
        ]);
    }

    public function getCategory($mainCategori, ?string $menuName, Request $request)
    {
        $category = Cache::tags(['kategori'])->get('get_category_items_' . md5($request->fullUrl()));
        if (!$category) {
            if ($request->has('filter')) {
                $url = config('app.product_api_url') . 'category/' . $menuName . '?page=' . $request->query('page') . '&';
                foreach ($request->get('filter', []) as $key => $value) {
                    if ($value)
                        $url .= 'filter[' . $key . ']' . '=' . $value . '&';
                }
                if ($request->get('orderBy')) {
                    $url .= 'orderBy=' . $request->get('orderBy') . '&';
                }
                $category = Http::timeout(80)->get($url);
            } else {
                $category = Http::get(config('app.product_api_url') . 'category/' . $menuName . '?page=' . $request->query('page'));

                if ($category->successful()) {
                    Cache::tags(['kategori'])->put('get_category_items_demo_' . md5($request->fullUrl()), $category->json(), now()->addMinutes(60 * 24)); // 1 gün cache
                }
            }

            // Convert the response to a JSON object
            $category = $category->json();
        }

        // if category is empty, redirect 404 page
        if (data_get($category, 'items.element.products.links') === []) {
            \App\Models\NonExistRedirection::create([
                'url' => url()->current(),
            ]);

            return Inertia::render('404', [])->toResponse(\request())->setStatusCode(404);
        }

        // Kullanıcı login ise favori ürünleri getir ve kategori ürünleri ile kesişenlere bu durumu işle
        if (session('user.isUserLoggedIn')) {
            //            ds('kullanıcı cookie var o yüzden favorileri getir');
            //$favourites = \Illuminate\Support\Facades\Http::withToken(request()->cookie('user'))->post(config('app.api_url') . 'auth/user/favourites')->json();
            $favourites = cache_http(config('app.api_url') . 'auth/user/favourites', 'post', request()->cookie('user'));
            $favourites = collect($favourites)->pluck('product_id')->toArray();
            $category['items']['element']['products']['data'] = collect($category['items']['element']['products']['data'])->map(function ($product) use ($favourites) {
                $product['favourite'] = in_array($product['id'], $favourites);
                return $product;
            })->toArray();
        }

        // Loop over the category and change pagination links
        $category['items']['element']['products']['pagination']['links'] = collect($category['items']['element']['products']['pagination']['links'])->map(function ($link) use ($request) {
            $link['url'] = Str::of($link['url'])->replace(config('app.product_api_url') . 'category', config('app.app_url') . '/kategoriler') . '&' . prepare_filter_query($request->get('filter', []));
            if ($request->get('orderBy', ""))
                $link['url'] .= '&orderBy=' . $request->get('orderBy', "");

            return $link;
        })->toArray();

        $cannoical = '/kategoriler/' . $menuName;
        if ($menuName)
            $cannoical = $request->fullUrl();

        $discounted = Cache::tags(['anasayfa'])->get('all_discounted');
        if (!$discounted) {
            $discounted = Http::get(config('app.product_api_url') . 'tag/anasayfaindirim')->json();
            Cache::tags(['anasayfa'])->put('all_discounted', $discounted, now()->addMinutes(60 * 24)); // 1 gün cache
        }

        return Inertia::render('CategoryGridDemo', [
            'category' => $category,
            //'brands' => collect(Arr::pluck(cache('get_category_items_' . $menuName)['items']['element']['products']['data'], 'brand'))->unique(),
            //'brands' => collect(Arr::pluck($category['items']['element']['products']['data'], 'brand'))->unique(),
            'brands' => data_get($category, 'items.element.filters.brands', []),
            'pageType' => 'category',
            'canonical' => $cannoical,
            'showCategoryNote' => $request->get('page', 1) == 1,
            'categoryNote' => data_get($category, 'items.element.sub_category_note'),
            'filters' => $request->get('filter', []),
            'orderBy' => $request->get('orderBy', ""), // Empty string means no order by
            'discounted' => $discounted,
            'categories' => collect(data_get($category, 'items.element.filters.category')), //->sortBy('collection_name'),
            'newTypeSeoDefinition' => true,
            'title' => 'Yüzlerce Son Teknoloji Ürünü İncele, Hemen Kirala',
            'meta_description' => 'Telefondan Akıllı Süpürgeye Birçok Teknoloji Ürününü Kolayca Kiralayabildiğin Kiralabunu! Tüm Ürünleri İncelemek İçin Hemen Tıkla, Keşfet, Kirala'
        ]);
    }

    public function getMainCategoryDemo($mainCategori, Request $request)
    {
        $category = Cache::tags(['kategori'])->get('get_category_items_demo_' . md5($request->fullUrl()));
        if (true || !$category) {
            if ($request->has('filter')) {
                $url = config('app.product_api_url') . 'category/' . $mainCategori . '?page=' . $request->query('page') . '&';
                foreach ($request->get('filter', []) as $key => $value) {
                    if ($value)
                        $url .= 'filter[' . $key . ']' . '=' . $value . '&';
                }
                if ($request->get('orderBy')) {
                    $url .= 'orderBy=' . $request->get('orderBy') . '&';
                }
                $category = Http::timeout(80)->get($url);
            } else {
                $category = Http::get(config('app.product_api_url') . 'category/' . $mainCategori . '?page=' . $request->query('page'));

                if ($category->successful()) {
                    Cache::tags(['kategori'])->put('get_category_items_demo_' . md5($request->fullUrl()), $category->json(), now()->addMinutes(60 * 24)); // 1 gün cache
                }
            }

            // Convert the response to a JSON object
            $category = $category->json();
        }


        // if category is empty, redirect 404 page
        if (data_get($category, 'items.element.products.links') === []) {
            \App\Models\NonExistRedirection::create([
                'url' => url()->current(),
            ]);

            return Inertia::render('404', [])->toResponse(\request())->setStatusCode(404);
        }

        // Kullanıcı login ise favori ürünleri getir ve kategori ürünleri ile kesişenlere bu durumu işle
        if (request()->cookie('user')) {
            $favourites = cache_http(config('app.api_url') . 'auth/user/favourites', 'post', request()->cookie('user'));
            $favourites = collect($favourites)->pluck('product_id')->toArray();
            $category['items']['element']['products']['data'] = collect($category['items']['element']['products']['data'])->map(function ($product) use ($favourites) {
                $product['favourite'] = in_array($product['id'], $favourites);
                return $product;
            })->toArray();
        }

        // Loop over the category and change pagination links
        $category['items']['element']['products']['pagination']['links'] = collect($category['items']['element']['products']['pagination']['links'])->map(function ($link) use ($request) {
            $link['url'] = Str::of($link['url'])->replace(config('app.product_api_url') . 'category', config('app.app_url') . '/kategoriler') . '&' . prepare_filter_query($request->get('filter', []));
            if ($request->get('orderBy', ""))
                $link['url'] .= '&orderBy=' . $request->get('orderBy', "");

            return $link;
        })->toArray();

        $cannoical = '/kategoriler/' . $mainCategori;
        if ($mainCategori)
            $cannoical = $request->fullUrl();

        $discounted = Cache::tags(['anasayfa'])->get('all_discounted');
        if (!$discounted) {
            //$discounted = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'discounted?page=1&orderBy=yeniden-eskiye')->json();
            $discounted = Http::get(config('app.product_api_url') . 'tag/anasayfaindirim')->json();
            Cache::tags(['anasayfa'])->put('all_discounted', $discounted, now()->addMinutes(60 * 24)); // 1 gün cache
        }

        return Inertia::render('CategoryGridDemo', [
            'category' => $category,
            'brands' => data_get($category, 'items.element.filters.brands', []),
            'pageType' => 'category',
            'canonical' => $cannoical,
            'showCategoryNote' => $request->get('page', 1) == 1,
            'categoryNote' => data_get($category, 'items.element.sub_category_note'),
            'filters' => $request->get('filter', []),
            'orderBy' => $request->get('orderBy', ''), // Empty string means no order by
            'discounted' => $discounted,
            'categories' => collect(data_get($category, 'items.element.filters.category')), //->sortBy('collection_name'),
            'newTypeSeoDefinition' => true,
            'title' => 'Yüzlerce Son Teknoloji Ürünü İncele, Hemen Kirala',
            'meta_description' => 'Telefondan Akıllı Süpürgeye Birçok Teknoloji Ürününü Kolayca Kiralayabildiğin Kiralabunu! Tüm Ürünleri İncelemek İçin Hemen Tıkla, Keşfet, Kirala'
        ]);
    }

    public function getAllProducts(Request $request)
    {
        $category = Cache::tags(['kategori'])->remember(
            key: 'get_category_items_all_' . md5($request->fullUrl()),
            ttl: now()->addMinutes(60 * 24),
            callback: function () use ($request) {
                if ($request->has('filter')) {
                    $url = config('app.product_api_url') . 'category/all?page=' . $request->query('page') . '&';
                    foreach ($request->get('filter', []) as $key => $value) {
                        if ($value)
                            $url .= 'filter[' . $key . ']' . '=' . $value . '&';
                    }
                    if ($request->get('orderBy')) {
                        $url .= 'orderBy=' . $request->get('orderBy') . '&';
                    }
                    $category = Http::timeout(80)->get($url)->json();
                } else {
                    $category = Http::get(config('app.product_api_url') . 'category/all?page=' . $request->query('page'))->json();
                }
                return $category;
            }
        );

        $category['items']['pagination']['links'] = collect($category['items']['pagination']['links'])->map(function ($link) use ($request) {
            $link['url'] = Str::of($link['url'])->replace(config('app.product_api_url') . 'category/all', config('app.app_url') . '/kategoriler/tum-urunler') . '&' . prepare_filter_query($request->get('filter', []));

            if ($request->get('orderBy')) {
                $link['url'] .= '&orderBy=' . $request->get('orderBy');
            }

            return $link;
        })->toArray();

        // Kullanıcı login ise favori ürünleri getir ve kategori ürünleri ile kesişenlere bu durumu işle
        if (session('user.isUserLoggedIn')) {
            $favourites = cache_http(config('app.api_url') . 'auth/user/favourites', 'post', session('user.token'));
            $favourites = collect($favourites)->pluck('product_id')->toArray();
            $category['items']['data'] = collect($category['items']['data'])->map(function ($product) use ($favourites) {
                $product['favourite'] = in_array($product['id'], $favourites);
                return $product;
            })->toArray();
        }

        $discounted = Cache::tags(['anasayfa'])->get('all_discounted');
        if (!$discounted) {
            //$discounted = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'discounted?page=1&orderBy=yeniden-eskiye')->json();
            $discounted = Http::get(config('app.product_api_url') . 'tag/anasayfaindirim')->json();
            Cache::tags(['anasayfa'])->put('all_discounted', $discounted, now()->addMinutes(60 * 24)); // 1 gün cache
        }

        return Inertia::render('CategoryGridDemo', [
            'discounted' => $discounted,
            'category' => $category,
            'brands' => data_get($category, 'filters.brands'),
            // 'categories' => collect($category['filters']['category'])->sortBy('attribute_data.name.tr'),
            'categories' => collect(data_get($category, 'filters.category')), //->sortBy('collection_name'),
            'pageType' => 'search',
            'canonical' => '/kategoriler/tum-urunler',
            'filters' => $request->get('filter', []),
            'orderBy' => $request->get('orderBy', ""), // Empty string means no order by
            'newTypeSeoDefinition' => true,
            'title' => 'Yüzlerce Son Teknoloji Ürünü İncele, Hemen Kirala',
            'meta_description' => 'Telefondan Akıllı Süpürgeye Birçok Teknoloji Ürününü Kolayca Kiralayabildiğin Kiralabunu! Tüm Ürünleri İncelemek İçin Hemen Tıkla, Keşfet, Kirala'
        ]);
    }
}
