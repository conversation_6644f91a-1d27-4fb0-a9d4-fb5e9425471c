<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Inertia\Inertia;

class BrandController extends Controller
{
    public function index(Request $request, $brand)
    {
        $brandContent = Cache::tags(['tags'])->get('get_brand_items_' . md5($request->fullUrl()));

        if (!$brandContent) {
            $response = Http::get(config('app.product_api_url') . 'brand/' . $brand . '?page=' . $request->query('page'));
            if ($response->successful()) {
                $brandContent = $response->json();
                Cache::tags(['tags'])->put('get_brand_items_' . md5($request->fullUrl()), $brandContent, now()->addMinutes(60 * 24)); // 1 gün cache
                logger()->info('Brand content cached. Brand: ' . $brand, ['response' => $response->body()]);
            } else {
                logger()->error('Brand content has error. Brand: ' . $brand, ['response' => $response->body()]);
                $brandContent = $response->json();
            }
        }

        // if brandContent is empty, return 404
        if (empty($brandContent)) {
            return Inertia::render('404', [])->toResponse(request())->setStatusCode(404);
        }

        // Loop over the category and change pagination links
        if (isset($brandContent['items']['pagination']['links'])) {
            $brandContent['items']['pagination']['links'] = collect($brandContent['items']['pagination']['links'])->map(function ($link) use ($request) {
                $link['url'] = Str::of($link['url'])->replace(config('app.product_api_url') . 'brand', config('app.app_url') . '/marka') . '&' . prepare_filter_query($request->get('filter', []));
                return $link;
            })->toArray();
        }

        $cannoical = '/marka/' . Str::of($brand)->lower()->toString();

        $mostRentedProducts = Cache::tags(['anasayfa'])->get('most_rented_products');
        if (!$mostRentedProducts) {
            $mostRentedProducts = \Illuminate\Support\Facades\Http::get(config('app.product_api_url') . 'tag/encokkiralanan')->json();
            Cache::tags(['anasayfa'])->put('most_rented_products', $mostRentedProducts, now()->addMinutes(60 * 24)); // 1 gün cache
        }

        return Inertia::render('CategoryGrid', [
            'mostRentedProducts' => $mostRentedProducts,
            'category' => $brandContent,
            'pageType' => 'search',
            'paginateType' => 'tag',
            'brands' => $brandContent['filters']['brands'],
            'categories' => $brandContent['filters']['category'],
            'canonical' => $cannoical,
            'showCategoryNote' => $request->get('page', 1) == 1,
            'categoryNote' => data_get($brandContent, 'brand.sub_category_note'),
            'newTypeSeoDefinition' => true,
            'title' => $brandContent['brand']['title'],
            'meta_description' => $brandContent['brand']['meta_description'],
            'meta_keywords' => $brandContent['brand']['meta_keywords'],
            'filters' => [],
        ]);
    }
}
