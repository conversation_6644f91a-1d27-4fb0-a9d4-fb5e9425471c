<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Inertia\Inertia;

class İnstitutionalCategoryController extends Controller
{
    public function getMainCategory($category)
    {
        $props = [];

        switch ($category) {
            case 'bilgisayar-tablet':
                $props = [
                    'categoryType' => 'bilgisayarTablet'
                ];
                break;
            case 'endustriyel-mutfak-ekipmanlari':
                $props = [
                    'categoryType' => 'endustriyelMutfakEkipmanlari'
                ];
                break;
            case 'endustriyel-ekran-cozumleri':
                $props = [
                    'categoryType' => 'endustriyelEkranCozumleri'
                ];
                break;
            case 'jenerator-kiralama':
                $props = [
                    'categoryType' => 'jeneratorKiralama'
                ];
                break;
            case 'fotokopi-yazicilar':
                $props = [
                    'categoryType' => 'fotokopiYazicilar'
                ];
                break;
            case 'guvenlik-sistemleri':
                $props = [
                    'categoryType' => 'guvenlikSistemleri'
                ];
                break;
            case 'isitma-sogutma-sistemleri':
                $props = [
                    'categoryType' => 'isitmaSogutmaSistemleri'
                ];
                break;
            case 'it-ekipmanlari':
                $props = [
                    'categoryType' => 'itEkipmanlari'
                ];
                break;
            case 'makine-ekipman':
                $props = [
                    'categoryType' => 'makineEkipman'
                ];
                break;
            case 'medikal-cihazlar':
                $props = [
                    'categoryType' => 'medikalCihazlar'
                ];
                break;
            case 'mobilite':
                $props = [
                    'categoryType' => 'mobilite'
                ];
                break;
            case 'ofis-mobilyasi':
                $props = [
                    'categoryType' => 'ofisMobilyasi'
                ];
                break;
            case 'otel-ekipmanlari':
                $props = [
                    'categoryType' => 'otelEkipmanlari'
                ];
                break;
            case 'ses-goruntu-sistemleri':
                $props = [
                    'categoryType' => 'sesGoruntuSistemleri'
                ];
                break;
            case 'spor-ekipmanlari':
                $props = [
                    'categoryType' => 'sporEkipmanlari'
                ];
                break;
            case 'sarj-istasyonlari':
                $props = [
                    'categoryType' => 'sarjIstasyonlari'
                ];
                break;
            case 'telefon-telekomunikasyon':
                $props = [
                    'categoryType' => 'telefonTelekomunikasyon'
                ];
                break;
            case 'yazilim':
                $props = [
                    'categoryType' => 'yazilim'
                ];
                break;
            default:
                $props = [
                    'categoryType' => 'telefonTelekomunikasyon'
                ];
        }

        return Inertia::render('InstitutionalCategory', $props);
    }
}
