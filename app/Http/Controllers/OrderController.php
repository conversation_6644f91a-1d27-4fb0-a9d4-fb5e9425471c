<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;

class OrderController extends Controller
{
    public function index()
    {
        $res = \Illuminate\Support\Facades\Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/orders')->object();
        return Inertia::render('Order', [
            'orders' => $res?->data ?? [],
            'orderTransactions' => Inertia::lazy(fn() => Http::withToken(session('user.token'))->get(config('app.api_url') . 'auth/user/orders/transactions/' . request()->query('orderTransaction'))->object()->data ?? []),
            'payedRentInfo' => Inertia::lazy(fn() => session('payedRentNotificationDetails')),
        ]);
    }

    public function processRentPayment(Request $request)
    {
        $payment = Http::withToken(session('user.token'))
            ->post(config("app.api_url") . "auth/user/orders/pay-transactions", [
                "orderTransactions" => $request->get('orderTransactions')
            ])
            ->object();
        session(['recheckNotpayedRents' => true]);
        session(['payedRentNotificationDetails' => $payment]);
        return redirect()->back();
    }
}
