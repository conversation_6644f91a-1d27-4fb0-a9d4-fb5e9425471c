<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class SurveyController extends Controller
{
    /**
     * Hash koduna göre anket sayfasını göster
     */
    public function show($hash)
    {
        \Log::info('show method called with hash: ' . $hash);

        $hashStatus = 'valid'; // varsayılan durum
        $surveyData = [];
        $errorMessage = '';

        // Varsayılan soru tanımları (başlıklar ve seçenekler)
        $defaultQuestions = [
            'satisfactionRating' => [
                'title' => 'Backend - Genel olarak kiralama sürecinden ne kadar memnun kaldın?',
                'type' => 'emoji',
                'options' => [
                    ['value' => 1, 'icon' => '😠', 'label' => 'Çok kötü'],
                    ['value' => 2, 'icon' => '😞', 'label' => 'Kötü'],
                    ['value' => 3, 'icon' => '😐', 'label' => 'Orta'],
                    ['value' => 4, 'icon' => '🙂', 'label' => 'İyi'],
                    ['value' => 5, 'icon' => '😊', 'label' => 'Çok iyi'],
                ],
            ],
            'productExpectation' => [
                'title' => 'Backend - Kiraladığın ürün beklentini karşıladı mı?',
                'type' => 'radio',
                'options' => [
                    ['value' => 'Evet', 'label' => 'Evet, karşıladı'],
                    ['value' => 'Kısmen', 'label' => 'Kısmen karşıladı'],
                    ['value' => 'Hayır', 'label' => 'Hayır, beklentimin altındaydı'],
                ],
            ],
            'cleanlinessRating' => [
                'title' => 'Backend - Ürünün ulaştığında temizliği ve bakımıyla ilgili memnuniyet seviyenin nedir?',
                'type' => 'score',
                'min' => 0,
                'max' => 10,
            ],
            'returnProcessRating' => [
                'title' => 'Backend - Ürünü iade etme sürecini nasıl değerlendirirsin?',
                'type' => 'score',
                'min' => 0,
                'max' => 10,
            ],
            'customerServiceRating' => [
                'title' => 'Backend - Müşteri hizmetlerinin destek ve iletişimini nasıl değerlendirirsin?',
                'type' => 'score',
                'min' => 0,
                'max' => 10,
            ],
            'pricingRating' => [
                'title' => 'Backend - Kiraladığın ürünün fiyatı, sunduğumuz deneyime göre nasıldı?',
                'type' => 'score',
                'min' => 0,
                'max' => 10,
            ],
            'wouldRecommend' => [
                'title' => "Backend - Kiralabunu'yu bir arkadaşına önerir misiniz?",
                'type' => 'radio',
                'options' => [
                    ['value' => 'Evet', 'label' => 'Evet'],
                    ['value' => 'Hayır', 'label' => 'Hayır'],
                ],
            ],
            'productSuggestions' => [
                'title' => "Backend - Kiralabunu'da / Kiralamini'de görmek istediğiniz ürünler var mı?",
                'type' => 'textarea',
            ],
            'interestedServices' => [
                'title' => 'Backend - Aşağıdaki hizmetlerden hangileri ilgini çeker?',
                'type' => 'checkbox',
                'options' => [
                    ['value' => 'rent_to_own', 'label' => 'Kira öder gibi satın alma (ürün sonunda sizin olur)'],
                    ['value' => 'short_term_campaign', 'label' => 'Kısa dönemli kampanyalı kiralama'],
                    ['value' => 'corporate_solutions', 'label' => 'Kurumsal çözümler (ofisimiz için)'],
                    ['value' => 'not_interested', 'label' => 'Şu an ilgilenmiyorum'],
                ],
            ],
            'additionalComments' => [
                'title' => 'Backend - Son olarak eklemek istediğin öneri veya tavsiyen var mı?',
                'type' => 'textarea',
            ],
        ];

        try {
            // Backend API'den anket bilgilerini kontrol et
            $apiUrl = config('app.api_url') . "survey/{$hash}/check";
            \Log::info('Survey API URL: ' . $apiUrl);

            $response = Http::timeout(30)->get($apiUrl); // POST yerine GET kullanıyoruz
            \Log::info('Survey API Response Status: ' . $response->status());
            \Log::info('Survey API Response Body: ' . $response->body());

            if ($response->failed()) {
                \Log::error('Survey API failed with status: ' . $response->status());
                $hashStatus = 'error';
                $errorMessage = 'Anket yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
            } else {
                $responseData = $response->json();

                if (isset($responseData['status'])) {
                    if ($responseData['status'] === 'completed') {
                        $hashStatus = 'completed';
                        $errorMessage = $responseData['message'] ?? 'Bu anket daha önce doldurulmuş.';
                    } else {
                        // İkinci API'den (sabit) soruları çek
                        try {
                            $questionsEndpoint = config('app.api_url') . 'survey/completed_order/questions';
                            $questionsRes = Http::timeout(30)->get($questionsEndpoint);

                            if ($questionsRes->successful()) {
                                // Gelen yapı: { status: true, data: [ ... ] }
                                $apiQuestions = $questionsRes->json('data') ?? [];
                                \Log::info('Questions API question count: ' . count($apiQuestions));
                            } else {
                                // eğer başarısızsa ilk API içeriğine bak ya da boş dizi kullan
                                //TODO: revize Eğer sorular çekilemediyse vue dosyası üzerinde lütfen sonra deneyin gibi modal çıksın
                                $apiQuestions = $responseData['data']['questions'] ?? [];
                            }
                        } catch (\Exception $e) {
                            \Log::warning('Questions endpoint error: ' . $e->getMessage());
                            $apiQuestions = $responseData['data']['questions'] ?? [];
                        }

                        // Yardımcı: snake_case to camelCase
                        $toCamel = function (string $str) {
                            return lcfirst(str_replace(' ', '', ucwords(str_replace('_', ' ', $str))));
                        };

                        $apiQuestionsTransformed = [];
                        foreach ($apiQuestions as $q) {
                            // Soru kodunu camelCase'e çevir
                            $code = $toCamel($q['question_code'] ?? '');
                            if (!$code) {
                                continue;
                            }

                            // Tip eşlemesi
                            $typeMap = [
                                'rating' => 'score',
                                'text' => 'textarea',
                                'boolean' => 'checkbox',
                                'multiple_choice' => 'radio',
                            ];

                            $type = $typeMap[$q['question_type']] ?? 'text';

                            // Özel durumlar
                            if ($q['question_type'] === 'rating' && !empty($q['options'])) {
                                $type = 'emoji';
                            }
                            if ($code === 'interestedServices') {
                                $type = 'checkbox';
                            }

                            // Seçenekleri normalize et (dizi veya JSON string)
                            $options = $this->normalizeOptions($q['options'] ?? null);

                            $apiQuestionsTransformed[$code] = [
                                'title' => $q['question_text'],
                                'type' => $type,
                            ];

                            if ($options) {
                                $apiQuestionsTransformed[$code]['options'] = $options;
                            }

                            if ($type === 'score') {
                                // rating için 0-10 arası skorlar
                                $apiQuestionsTransformed[$code]['min'] = 0;
                                $apiQuestionsTransformed[$code]['max'] = 10;
                            }
                        }

                        // Varsayılan sorular ile API'den gelen soruları birleştir (API verisi öncelikli)
                        $surveyData = [
                            //'questions' => array_replace_recursive($defaultQuestions, $apiQuestionsTransformed),
                            'questions' => $apiQuestionsTransformed,
                        ];
                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error('Survey API exception: ' . $e->getMessage());
            $hashStatus = 'error';
            $errorMessage = 'Anket yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
        }

        return Inertia::render('RentSurveyForm', [
            'hash' => $hash,
            'surveyData' => $surveyData,
            'hashStatus' => $hashStatus,
            'errorMessage' => $errorMessage,
            'title' => 'Kiralama Deneyimi Anketi - Kiralabunu',
            'meta_description' => 'Kiralama deneyiminizi değerlendirin. Görüşleriniz bizim için değerli.',
        ]);
    }

    /**
     * Hash-based anket submit
     */
    public function submitHashBasedSurvey(Request $request, $hash)
    {

        // Form validasyonu
        $validator = \Validator::make($request->all(), [
            'satisfactionRating' => 'required|integer|between:1,5',
            'productExpectation' => 'required|string',
            'cleanlinessRating' => 'required|integer|between:0,10',
            'returnProcessRating' => 'required|integer|between:0,10',
            'customerServiceRating' => 'required|integer|between:0,10',
            'pricingRating' => 'required|integer|between:0,10',
            'wouldRecommend' => 'required|string',
            'productSuggestions' => 'nullable|string|max:1000',
            'interestedServices' => 'required|array|min:1',
            'additionalComments' => 'nullable|string|max:1000',
            'wantsSpecialOffers' => 'boolean'
        ], [
            'satisfactionRating.required' => 'Memnuniyet puanı zorunludur',
            'satisfactionRating.between' => 'Memnuniyet puanı 1-5 arasında olmalıdır',
            'productExpectation.required' => 'Ürün beklenti değerlendirmesi zorunludur',
            'cleanlinessRating.required' => 'Ürün temizliği puanı zorunludur',
            'cleanlinessRating.between' => 'Ürün temizliği puanı 0-10 arasında olmalıdır',
            'returnProcessRating.required' => 'İade süreci puanı zorunludur',
            'returnProcessRating.between' => 'İade süreci puanı 0-10 arasında olmalıdır',
            'customerServiceRating.required' => 'Müşteri hizmetleri puanı zorunludur',
            'customerServiceRating.between' => 'Müşteri hizmetleri puanı 0-10 arasında olmalıdır',
            'pricingRating.required' => 'Fiyat değerlendirme puanı zorunludur',
            'pricingRating.between' => 'Fiyat değerlendirme puanı 0-10 arasında olmalıdır',
            'wouldRecommend.required' => 'Tavsiye seçimi zorunludur',
            'interestedServices.required' => 'En az bir hizmet seçimi yapınız',
            'interestedServices.min' => 'En az bir hizmet seçimi yapınız',
            'productSuggestions.max' => 'Ürün önerileri en fazla 1000 karakter olabilir',
            'additionalComments.max' => 'Ek yorumlar en fazla 1000 karakter olabilir'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Backend API'ye anket verilerini gönder
            $surveyData = [
                'satisfaction_rating' => $request->get('satisfactionRating'),
                'product_expectation' => $request->get('productExpectation'),
                'cleanliness_rating' => $request->get('cleanlinessRating'),
                'return_process_rating' => $request->get('returnProcessRating'),
                'customer_service_rating' => $request->get('customerServiceRating'),
                'pricing_rating' => $request->get('pricingRating'),
                'would_recommend' => $request->get('wouldRecommend'),
                'product_suggestions' => $request->get('productSuggestions'),
                'interested_services' => $request->get('interestedServices', []),
                'additional_comments' => $request->get('additionalComments'),
                'wants_special_offers' => $request->get('wantsSpecialOffers', false),
                'ip_address' => $request->ip(),
                'submitted_at' => now()
            ];

            $apiUrl = config('app.api_url') . "survey/{$hash}/submit";
            $response = Http::timeout(30)->post($apiUrl, $surveyData);

            if ($response->failed()) {
                // API çağrısı başarısız, ama yine de başarılı mesajı döndür (geçici)
                return redirect()->back()->with('success', 'Anket yanıtlarınız başarıyla kaydedildi. Teşekkürler!');
            }

            $responseData = $response->json();
            return redirect()->back()->with('success', 'Anket yanıtlarınız başarıyla kaydedildi. Teşekkürler!');

        } catch (\Exception $e) {
            // Hata durumunda da başarılı mesajı döndür (geçici)
            return redirect()->back()->with('success', 'Anket yanıtlarınız başarıyla kaydedildi. Teşekkürler!');
        }
    }

    /**
     * Convert snake_case to camelCase
     */
    private function toCamelCase(string $str): string
    {
        return lcfirst(str_replace(' ', '', ucwords(str_replace('_', ' ', $str))));
    }

    /**
     * Convert camelCase to snake_case
     */
    private function toSnakeCase(string $str): string
    {
        return strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $str));
    }

    /**
     * Map form fields from camelCase to snake_case for backend submission
     */
    private function mapFormFieldsToBackend(array $formData): array
    {
        $mappedData = [];
        foreach ($formData as $key => $value) {
            $snakeKey = $this->toSnakeCase($key);
            $mappedData[$snakeKey] = $value;
        }
        return $mappedData;
    }

    /**
     * Map question codes from snake_case to camelCase for frontend consumption
     */
    private function mapQuestionsToFrontend(array $questions): array
    {
        $mappedQuestions = [];

        // Question type mapping
        $typeMap = [
            'rating' => 'score',
            'text' => 'textarea',
            'boolean' => 'checkbox',
            'multiple_choice' => 'radio',
        ];

        foreach ($questions as $question) {
            $camelCode = $this->toCamelCase($question['question_code'] ?? '');
            if (!$camelCode) {
                continue;
            }

            // Map question type
            $type = $typeMap[$question['question_type']] ?? 'text';

            // Special handling for rating with options (emoji type)
            if ($question['question_type'] === 'rating' && !empty($question['options'])) {
                $type = 'emoji';
            }

            // Normalize options (string veya dizi)
            $options = $this->normalizeOptions($question['options'] ?? null);

            $mappedQuestions[$camelCode] = [
                'title' => $question['question_text'],
                'type' => $type,
            ];

            if ($options) {
                $mappedQuestions[$camelCode]['options'] = $options;
            }

            if ($type === 'score') {
                $mappedQuestions[$camelCode]['min'] = 0;
                $mappedQuestions[$camelCode]['max'] = 10;
            }
        }

        return $mappedQuestions;
    }

    public function showActive($hash)
    {
        $hashStatus = 'valid';
        $surveyData = [];
        $errorMessage = '';

        try {
            // 1) Hash kontrol
            $checkUrl = config('app.api_url') . "survey/{$hash}/check";
            $checkRes = Http::timeout(30)->get($checkUrl);

            if ($checkRes->failed()) {
                $hashStatus = 'error';
                $errorMessage = 'Anket yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
            } else {
                $checkData = $checkRes->json();
                \Log::info('Hash check response:', $checkData);

                if (($checkData['status'] ?? null) === 'completed') {
                    $hashStatus = 'completed';
                    $errorMessage = $checkData['message'] ?? 'Bu anket daha önce doldurulmuş.';
                } else {
                    // 2) Soru listesi API'den çek
                    try {
                        $questionsEndpoint = config('app.api_url') . 'survey/active_rental/questions';
                        $questionsResponse = Http::timeout(30)->get($questionsEndpoint);

                        if ($questionsResponse->successful()) {
                            $responseData = $questionsResponse->json();
                            \Log::info('Questions API response structure:', $responseData);

                            // Backend API response format: {status, data, survey_type}
                            $apiQuestions = [];
                            if (isset($responseData['data']) && is_array($responseData['data'])) {
                                $apiQuestions = $responseData['data'];
                            } elseif (isset($responseData['status']) && $responseData['status'] === true && isset($responseData['data'])) {
                                $apiQuestions = $responseData['data'];
                            }

                            // Map questions to frontend format
                            $mappedQuestions = $this->mapQuestionsToFrontend($apiQuestions);

                            $surveyData = [
                                'questions' => $mappedQuestions,
                                'survey_type' => $responseData['survey_type'] ?? 'active_rental'
                            ];

                        } else {
                            \Log::error('Questions API failed:', [
                                'status' => $questionsResponse->status(),
                                'body' => $questionsResponse->body()
                            ]);
                            $hashStatus = 'error';
                            $errorMessage = 'Anket soruları yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
                        }

                    } catch (\Exception $e) {
                        \Log::error('Questions fetch exception: ' . $e->getMessage());
                        $hashStatus = 'error';
                        $errorMessage = 'Anket soruları yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error('showActive exception: ' . $e->getMessage());
            $hashStatus = 'error';
            $errorMessage = 'Anket yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
        }

        return Inertia::render('ActiveRentSurveyForm', [
            'hash' => $hash,
            'surveyData' => $surveyData,
            'hashStatus' => $hashStatus,
            'errorMessage' => $errorMessage,
            'title' => 'Aktif Kiralama Deneyimi Anketi - Kiralabunu',
            'meta_description' => 'Aktif kiralama deneyiminizi değerlendirin. Görüşleriniz bizim için değerli.',
        ]);
    }

    public function submitActive(Request $request, $hash)
    {
        \Log::info('Request data:', $request->all());

        // Dinamik validasyon kuralları - API'den gelen sorular bazında
        $rules = $this->buildDynamicValidationRules($request);
        $messages = $this->getValidationMessages();

        $validator = \Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            \Log::warning('Validation failed:', $validator->errors()->toArray());
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Form verilerini backend formatına çevir (camelCase -> snake_case)
            $formData = $request->except(['_token', 'hash']);
            $backendPayload = $this->mapFormFieldsToBackend($formData);

            // Ek metadata ekle
            $backendPayload['ip_address'] = $request->ip();
            $backendPayload['submitted_at'] = now()->toISOString();

            \Log::info('Backend payload:', $backendPayload);

            $apiUrl = config('app.api_url') . "survey/{$hash}/submit";
            $response = Http::timeout(30)->post($apiUrl, $backendPayload);

            \Log::info('Backend response status: ' . $response->status());
            \Log::info('Backend response body: ' . $response->body());

            if ($response->failed()) {
                \Log::error('Active survey submit failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'payload' => $backendPayload
                ]);

                // Backend'den gelen hata mesajını parse et
                $errorMessage = 'Anket gönderilirken bir hata oluştu.';
                if ($response->status() === 422) {
                    $responseData = $response->json();
                    $errorMessage = $responseData['message'] ?? $errorMessage;
                }

                return back()->withErrors($errorMessage)->withInput();
            }

            $responseData = $response->json();
            $successMessage = $responseData['message'] ?? 'Anket yanıtlarınız başarıyla kaydedildi. Teşekkürler!';

            return back()->with('success', $successMessage);

        } catch (\Exception $e) {
            \Log::error('Active survey submit exception: ' . $e->getMessage());
            return back()->withErrors('Anket gönderilirken bir hata oluştu.')->withInput();
        }
    }

    /**
     * Build dynamic validation rules based on form data
     */
    private function buildDynamicValidationRules(Request $request): array
    {
        $rules = [];
        $formData = $request->all();

        // Ana alanlar için temel kurallar
        foreach ($formData as $key => $value) {
            if ($key === '_token' || $key === 'hash') continue;

            // Detay alanları haricindeki ana alanlar
            if (!str_ends_with($key, 'Details') && !str_ends_with($key, 'Other')) {
                switch ($key) {
                    case 'communicationRating':
                        $rules[$key] = 'required|integer|between:1,5';
                        break;
                    case 'wantsSpecialOffers':
                        $rules[$key] = 'boolean';
                        break;
                    case str_contains($key, 'Comments'):
                        $rules[$key] = 'nullable|string|max:1000';
                        break;
                    default:
                        $rules[$key] = 'required|string|max:1000';
                        break;
                }
            }
        }

        // Koşullu alanlar
        if ($request->get('rentalExperience') === 'Memnun değilim') {
            $rules['rentalExperienceDetails'] = 'required|string|max:1000';
        } else {
            $rules['rentalExperienceDetails'] = 'nullable|string|max:1000';
        }

        if ($request->get('technicalIssues') === 'Evet, hala çözülmedi') {
            $rules['technicalIssuesDetails'] = 'required|string|max:1000';
        } else {
            $rules['technicalIssuesDetails'] = 'nullable|string|max:1000';
        }

        if ($request->get('howDidYouHear') === 'Diğer') {
            $rules['howDidYouHearOther'] = 'required|string|max:500';
        } else {
            $rules['howDidYouHearOther'] = 'nullable|string|max:500';
        }

        return $rules;
    }

    /**
     * Get validation error messages
     */
    private function getValidationMessages(): array
    {
        return [
            'rentalExperience.required' => 'Kiralama deneyimi seçimi zorunludur',
            'technicalIssues.required' => 'Teknik sorun durumu seçimi zorunludur',
            'supportNeeded.required' => 'Destek ihtiyacı seçimi zorunludur',
            'communicationRating.required' => 'İletişim memnuniyeti puanı zorunludur',
            'communicationRating.between' => 'İletişim memnuniyeti puanı 1-5 arasında olmalıdır',
            'howDidYouHear.required' => 'Nereden duydunuz seçimi zorunludur',
            'rentalExperienceDetails.max' => 'Deneyim detayları en fazla 1000 karakter olabilir',
            'technicalIssuesDetails.max' => 'Teknik sorun detayları en fazla 1000 karakter olabilir',
            'howDidYouHearOther.max' => 'Diğer açıklaması en fazla 500 karakter olabilir',
            'additionalComments.max' => 'Ek yorumlar en fazla 1000 karakter olabilir',
            'rentalExperienceDetails.required' => 'Memnun olmama sebebinizi açıklamanız gereklidir',
            'technicalIssuesDetails.required' => 'Yaşadığınız sorunu detaylandırmanız gereklidir',
            'howDidYouHearOther.required' => 'Lütfen nereden duyduğunuzu belirtiniz',
        ];
    }

    /* Generic methods to avoid duplication */
    private function showGeneric($hash, $surveySlug)
    {
        // code from show() but endpoint modification using slug parameter
    }

    private function submitGeneric(Request $request, $hash, $surveySlug)
    {
        // copy logic from submitHashBasedSurvey adjusting endpoint
    }

    /**
     * Safely convert the \$options field to array.
     * Accepts either already-decoded array or a JSON string. Returns null otherwise.
     */
    private function normalizeOptions(mixed $options): ?array
    {
        if (empty($options)) {
            return null;
        }

        if (is_array($options)) {
            return $options;
        }

        if (is_string($options)) {
            // Bazı durumlarda backend zaten dizi gönderebilir; string ise JSON kabul ediyoruz
            $decoded = json_decode($options, true);
            return is_array($decoded) ? $decoded : null;
        }

        return null;
    }
}
