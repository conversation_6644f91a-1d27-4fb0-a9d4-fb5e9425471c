<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class SaveCreditCardController extends Controller
{
    public function save(Request $request)
    {
        logger('3d save cc callback started', ['request' => $request->all()]);
        $complete3D = \Illuminate\Support\Facades\Http::post(config('app.api_url') . 'auth/user/complete-cc-3ds', $request->toArray());
        logger('3d save cc callback completed', ['response' => $complete3D->json()]);

        $source = $request->get('source', 'cart');
        $returnUrl = match ($source) {
            'cart' => '/odeme',
            'profile' => '/odeme-yontemlerim',
            'payment_sms' => '/' . $request->get('token') . '/success',
            default => '/odeme'
        };

        logger('3d save cc callback cache value', ['cache' => cache('3d_request_operation_' . $request->get('operationCode')), 'userdefault' => session('user')]);
        // Set User From Previous Session
        session(['user' => cache('3d_request_operation_' . $request->get('operationCode'), session('user'))]);

        logger('3d save cc callback completed', [
            'response' => $complete3D->json(),
            'status' => data_get($complete3D->json(), 'data.status'),
            'returnUrl' => $returnUrl,
        ]);

        if (!data_get($complete3D->json(), 'data.payment_sms_token') &&  data_get($complete3D->json(), 'data.status')) {
            return redirect()->to($returnUrl)->with('saveStatus', true);
        }

        if ($returnUrl == '/odeme') {

            if (!data_get($complete3D->json(), 'data.status')) {
                return redirect()->to($returnUrl)->withErrors('Ödeme işlemi başarısız oldu.');
            }

            return redirect()->to($returnUrl)->withErrors($complete3D['message']);
        } else if ($returnUrl == '/odeme-yontemlerim') {
            if (!data_get($complete3D->json(), 'data.status')) {
                return redirect()->to('/odeme-yontemi-ekle')->withErrors('Ödeme işlemi başarısız oldu.');
            }

            return redirect()->to('/odeme-yontemi-ekle')->withErrors($complete3D['message']);
        } else {
            // payment_sms source handling
            $paymentToken = data_get($complete3D->json(), 'data.payment_sms_token');

            if (!data_get($complete3D->json(), 'data.status')) {
                logger('3d payment_sms callback failed', [
                    'token' => $paymentToken,
                    'response_status' => data_get($complete3D->json(), 'data.status'),
                    'response' => $complete3D->json()
                ]);

                // For failed payment_sms, send message to parent window and redirect back to payment form
                return response()->view('payment.3ds-callback', [
                    'success' => false,
                    'token' => $paymentToken,
                    'message' => '3DS doğrulaması başarısız oldu.'
                ]);
            }

            logger('3d payment_sms callback success', [
                'token' => $paymentToken,
                'response_status' => data_get($complete3D->json(), 'data.status'),
                'response' => $complete3D->json()
            ]);

            // For successful payment_sms, send message to parent window and redirect to success page
            return response()->view('payment.3ds-callback', [
                'success' => true,
                'token' => $paymentToken,
                'message' => 'Ödeme başarıyla tamamlandı.'
            ]);
        }


        //"status" => "success"
        //"paymentId" => "19096930"
        //"conversationData" => null
        //"conversationId" => "123456789"
        //"mdStatus" => "1"
        //"token" => "Y7TY7K75IQ"
    }
}
