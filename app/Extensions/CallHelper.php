<?php

namespace App\Extensions;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class CallHelper
{
    public function sendPost($route, array $payload = [])
    {
        return $this->handleRequest($route, $payload, 'post');
    }

    public function sendGet($route, array $payload = [])
    {
        return $this->handleRequest($route, $payload, 'get');
    }

    protected function handleRequest($route, array $payload, string $type)
    {
        $response = [];

        try {
            if ($type == 'post' || $type == 'POST') {
                $response = Http::post($route, $payload)->json();
            } elseif ($type == 'get' || $type == 'GET') {
                $response = Http::get($route, $payload)->json();
            }
            logger()->error($route);
            logger()->error($payload);
        } catch (\Exception $e) {
            logger()->error($e->getMessage(), ['exception' => $e]);

            return $response;
        }

        if (!isset($response['data'])) {
            logger()->error('Error response from API', Arr::wrap($response));

            return null;
        }

        return $response['data'];
    }
}
