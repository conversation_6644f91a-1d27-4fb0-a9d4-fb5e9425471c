<?php

namespace App\Providers;

use App\Services\PaymentServiceInterface;
use App\Services\MockPaymentService;
use App\Services\PaymentService;
use Illuminate\Support\ServiceProvider;
use Opcodes\LogViewer\Facades\LogViewer;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if (app()->isLocal()) {
            // Payment Service binding
            $this->app->bind(PaymentServiceInterface::class, MockPaymentService::class);
        } else {
            $this->app->bind(PaymentServiceInterface::class, PaymentService::class);
        }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        LogViewer::auth(function ($request) {
            return session()->get('user.user.id') == 1;
        });
    }
}
