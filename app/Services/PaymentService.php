<?php

namespace App\Services;

use App\DTOs\PaymentTokenResult;
use App\DTOs\PaymentResult;
use App\Jobs\SendPaymentErrorNotification;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PaymentService implements PaymentServiceInterface
{
    private const TIMEOUT_SECONDS = 5;
    private const MAX_RETRIES = 2;

    private string $baseUrl;
    private string $sessionId;

    public function __construct()
    {
        $this->baseUrl = rtrim(config('app.api_url'), '/');
        $this->sessionId = $this->getSessionId();
    }

    /**
     * Validate payment token via API
     */
    public function validateToken(string $token): PaymentTokenResult
    {
        $logContext = ['token' => $this->maskToken($token)];

        Log::channel('sms-payment')->info('Starting token validation', $logContext);

        try {
            $response = $this->makeApiRequest('GET', "/scoring/payment-sms/{$token}");

            if ($response->successful()) {
                $data = $response->json();
                Log::channel('sms-payment')->info('Token validation successful', [
                    'token' => $this->maskToken($token),
                    'status' => $data['status'] ?? 'unknown'
                ]);

                return $this->mapApiResponseToTokenResult(data: $data, isValid: true);
            }

            // Handle specific HTTP status codes
            return $this->handleApiErrorResponse($response, $token);
        } catch (\Exception $e) {
            Log::channel('sms-payment')->error('Token validation failed', [
                'token' => $this->maskToken($token),
                'error' => $e->getMessage()
            ]);

            // Dispatch error notification for network/system errors
            SendPaymentErrorNotification::dispatch(
                $token,
                'TOKEN_VALIDATION_ERROR',
                $e->getMessage(),
                $logContext
            );

            return new PaymentTokenResult(
                isValid: false,
                status: null,
                amount: null,
                description: null,
                expiresAt: null,
                errorMessage: 'Sistem hatası oluştu. Lütfen daha sonra tekrar deneyiniz.'
            );
        }
    }

    /**
     * Process payment via REST API
     */
    public function processPayment(string $token, array $cardData, array $customerData = []): PaymentResult
    {
        $logContext = [
            'token' => $this->maskToken($token)
        ];

        Log::channel('sms-payment')->info('Starting payment process', $logContext);

        // İstek gövdesini hazırla
        $payload = [
            'payment_method' => 'credit_card',
            'card_number'   => data_get($cardData, 'card_number'),
            'card_expiry'   => data_get($cardData, 'card_expiry'),
            'card_cvv'           => data_get($cardData, 'card_cvv'),
            'card_holder' => data_get($cardData, 'card_holder'),
            'customer_email' => data_get($customerData, 'email'),
            'invoice_address' => data_get($customerData, 'invoice_address'),
        ];

        try {
            $response = $this->makeApiRequest('POST', "/scoring/payment-sms/{$token}/process", [
                'json' => $payload
            ]);

            if ($response->successful()) {
                $responseData = $response->json();

                // Log full API response for debugging
                Log::channel('sms-payment')->info('Full payment API response', [
                    'token' => $this->maskToken($token),
                    'status_code' => $response->status(),
                    'full_response' => $responseData,
                    'response_keys' => array_keys($responseData),
                    'has_status' => isset($responseData['status']),
                    'status_value' => $responseData['status'] ?? 'NOT_SET',
                    'has_html_content' => isset($responseData['html_content']),
                    'has_iyz_token' => isset($responseData['iyz_token']),
                ]);

                // Check if 3DS is required
                if (isset($responseData['status']) && $responseData['status'] === '3ds') {
                    Log::channel('sms-payment')->info('Payment requires 3D Secure', [
                        'token' => $this->maskToken($token),
                        'iyz_token' => $responseData['iyz_token'] ?? 'not_provided',
                        'has_html' => isset($responseData['html_content'])
                    ]);

                    // Modify 3DS HTML to include source parameter for callback
                    $threeDSHtml = $responseData['html_content'] ?? null;
                    if ($threeDSHtml) {
                        // Add source=payment_sms and token to the form action URL
                        $threeDSHtml = $this->modify3DSHtmlForPaymentSMS($threeDSHtml, $token);
                    }

                    return new PaymentResult(
                        success: true,
                        transactionId: null,
                        errorMessage: null,
                        errorCode: null,
                        additionalData: $responseData,
                        statusCode: $response->status(),
                        threeDSHtml: $threeDSHtml,
                        iyzToken: $responseData['iyz_token'] ?? null,
                        requires3DS: true
                    );
                }

                Log::channel('sms-payment')->info('Payment processed successfully', [
                    'token' => $this->maskToken($token),
                    'transaction_id' => $responseData['transaction_id'] ?? 'not_provided',
                    'response' => $responseData
                ]);

                return new PaymentResult(
                    success: true,
                    transactionId: $responseData['transaction_id'] ?? null,
                    errorMessage: null,
                    errorCode: null,
                    additionalData: $responseData,
                    statusCode: $response->status()
                );
            }

            // Başarısız HTTP durum kodlarını işle
            return $this->handlePaymentErrorResponse($response, $token);
        } catch (\Exception $e) {
            Log::channel('sms-payment')->error('Payment processing failed', [
                'token' => $this->maskToken($token),
                'error' => $e->getMessage()
            ]);

            // Ağ/sistem hataları için bildirim gönder
            SendPaymentErrorNotification::dispatch(
                $token,
                'PAYMENT_PROCESSING_ERROR',
                $e->getMessage(),
                $logContext
            );

            return new PaymentResult(
                success: false,
                transactionId: null,
                errorMessage: 'Sistem hatası oluştu. Lütfen daha sonra tekrar deneyiniz.',
                errorCode: 'SYSTEM_ERROR',
                statusCode: 500
            );
        }
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus(string $token): \App\DTOs\PaymentStatus
    {
        $result = $this->validateToken($token);

        return new \App\DTOs\PaymentStatus(
            status: $result->status ?? 'unknown',
            amount: $result->amount,
            expiresAt: $result->expiresAt,
            description: $result->description
        );
    }

    /**
     * Make HTTP request to API with retry logic
     */
    private function makeApiRequest(string $method, string $endpoint, array $data = []): \Illuminate\Http\Client\Response
    {
        $url = $this->baseUrl . $endpoint;
        $attempts = 0;
        $lastException = null;

        while ($attempts <= self::MAX_RETRIES) {
            try {
                Log::channel('sms-payment')->debug('Making API request', [
                    'method' => $method,
                    'url' => $url,
                    'attempt' => $attempts + 1,
                    'session_id' => $this->maskSessionId()
                ]);

                $response = Http::timeout(self::TIMEOUT_SECONDS)
                    ->withHeaders([
                        'X-Session-Id' => $this->sessionId,
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json'
                    ])
                    ->send($method, $url, $data);

                Log::channel('sms-payment')->debug('API response received', [
                    'url' => $url,
                    'status' => $response->status(),
                    'attempt' => $attempts + 1,
                    'response_size' => strlen($response->body())
                ]);

                return $response;
            } catch (\Exception $e) {
                $lastException = $e;
                $attempts++;

                Log::channel('sms-payment')->warning('API request failed', [
                    'url' => $url,
                    'attempt' => $attempts,
                    'error' => $e->getMessage()
                ]);

                if ($attempts <= self::MAX_RETRIES) {
                    // Wait before retry (exponential backoff)
                    sleep(pow(2, $attempts - 1));
                }
            }
        }

        // All retries failed
        throw $lastException;
    }

    /**
     * Handle API error responses (409, 410, 429, etc.)
     */
    private function handleApiErrorResponse(\Illuminate\Http\Client\Response $response, string $token): PaymentTokenResult
    {
        $statusCode = $response->status();
        $data = $response->json();

        Log::channel('sms-payment')->warning('API returned error status', [
            'token' => $this->maskToken($token),
            'status_code' => $statusCode,
            'error_code' => $data['error_code'] ?? 'UNKNOWN',
            'message' => $data['message'] ?? 'Unknown error',
            'data' => $data
        ]);

        switch ($statusCode) {
            case 409: // Already paid
                return new PaymentTokenResult(
                    isValid: false,
                    status: 'paid',
                    amount: data_get($data, 'data.scoring_request.additional_data.payment_info.paid_amount'),
                    transactionId: data_get($data, 'data.scoring_request.additional_data.payment_info.payment_provider_transaction_id'),
                    description: null,
                    expiresAt: null,
                    errorMessage: $data['message'] ?? 'Bu ödeme daha önce tamamlanmış'
                );

            case 410: // Expired
                return new PaymentTokenResult(
                    isValid: false,
                    status: 'expired',
                    amount: null,
                    description: null,
                    expiresAt: null,
                    errorMessage: $data['message'] ?? 'Bu ödeme linkinin süresi dolmuş'
                );

            case 429: // Rate limited
                return new PaymentTokenResult(
                    isValid: false,
                    status: null,
                    amount: null,
                    description: null,
                    expiresAt: null,
                    errorMessage: 'Çok fazla deneme yapıldı. Lütfen birkaç dakika bekleyip tekrar deneyiniz.'
                );

            case 404: // Not found
                return new PaymentTokenResult(
                    isValid: false,
                    status: null,
                    amount: null,
                    description: null,
                    expiresAt: null,
                    errorMessage: 'Geçersiz ödeme linki'
                );

            default:
                // Dispatch error notification for unexpected status codes
                SendPaymentErrorNotification::dispatch(
                    $token,
                    'UNEXPECTED_API_RESPONSE',
                    "Unexpected HTTP status: {$statusCode}",
                    [
                        'status_code' => $statusCode,
                        'response_body' => $this->maskSensitiveData($response->body())
                    ]
                );

                return new PaymentTokenResult(
                    isValid: false,
                    status: null,
                    amount: null,
                    description: null,
                    expiresAt: null,
                    errorMessage: 'Sistem hatası oluştu. Lütfen daha sonra tekrar deneyiniz.',
                    statusCode: $statusCode
                );
        }
    }

    /**
     * Handle API error responses for payment processing (400, 402, 409, 429, vb.)
     */
    private function handlePaymentErrorResponse(\Illuminate\Http\Client\Response $response, string $token): PaymentResult
    {
        $statusCode = $response->status();
        $data = $response->json();

        // Servisten gelen error_message field'ını öncelikle kontrol et
        $errorMessage = $data['error_message'] ?? $data['message'] ?? null;
        $errorCode = $data['error_code'] ?? 'UNKNOWN';

        Log::channel('sms-payment')->warning('Payment API returned error status', [
            'token' => $this->maskToken($token),
            'status_code' => $statusCode,
            'error_code' => $errorCode,
            'error_message' => $errorMessage,
            'full_response' => $data
        ]);

        switch ($statusCode) {
            case 400: // Geçersiz istek / kart bilgisi hatalı
                return new PaymentResult(
                    success: false,
                    transactionId: null,
                    errorMessage: $errorMessage ?? 'Geçersiz ödeme isteği',
                    errorCode: $errorCode,
                    additionalData: $data,
                    statusCode: $statusCode
                );

            case 402: // Ödeme reddedildi / yetersiz bakiye vs.
                return new PaymentResult(
                    success: false,
                    transactionId: null,
                    errorMessage: $errorMessage ?? 'Ödeme reddedildi',
                    errorCode: $errorCode,
                    additionalData: $data,
                    statusCode: $statusCode
                );

            case 409: // Daha önce ödenmiş
                return new PaymentResult(
                    success: false,
                    transactionId: null,
                    errorMessage: $errorMessage ?? 'Bu ödeme daha önce tamamlanmış',
                    errorCode: $errorCode,
                    additionalData: $data,
                    statusCode: $statusCode
                );

            case 410: // Token süresi dolmuş / işlem artık geçerli değil
                return new PaymentResult(
                    success: false,
                    transactionId: null,
                    errorMessage: $errorMessage ?? 'Bu ödeme linkinin süresi dolmuş',
                    errorCode: $errorCode,
                    additionalData: $data,
                    statusCode: $statusCode
                );

            case 404: // Token bulunamadı
                return new PaymentResult(
                    success: false,
                    transactionId: null,
                    errorMessage: $errorMessage ?? 'Geçersiz ödeme linki',
                    errorCode: $errorCode,
                    additionalData: $data,
                    statusCode: $statusCode
                );

            case 422: // Validation errors
                return new PaymentResult(
                    success: false,
                    transactionId: null,
                    errorMessage: $errorMessage ?? 'Gönderilen bilgiler hatalı',
                    errorCode: $errorCode,
                    additionalData: $data,
                    statusCode: $statusCode
                );

            case 429: // Rate limited
                return new PaymentResult(
                    success: false,
                    transactionId: null,
                    errorMessage: 'Çok fazla deneme yapıldı. Lütfen birkaç dakika bekleyip tekrar deneyiniz.',
                    errorCode: 'RATE_LIMITED',
                    additionalData: $data,
                    statusCode: $statusCode
                );

            default:
                // Beklenmeyen durumlar için bildirim gönder
                SendPaymentErrorNotification::dispatch(
                    $token,
                    'UNEXPECTED_PAYMENT_API_RESPONSE',
                    "Unexpected HTTP status: {$statusCode}",
                    [
                        'status_code' => $statusCode,
                        'error_message' => $errorMessage,
                        'response_body' => $this->maskSensitiveData($response->body())
                    ]
                );

                return new PaymentResult(
                    success: false,
                    transactionId: null,
                    errorMessage: $errorMessage ?? 'Sistem hatası oluştu. Lütfen daha sonra tekrar deneyiniz.',
                    errorCode: $errorCode,
                    additionalData: $data,
                    statusCode: $statusCode
                );
        }
    }

    /**
     * Map API response to PaymentTokenResult DTO
     */
    private function mapApiResponseToTokenResult(array $data, bool $isValid): PaymentTokenResult
    {
        $paymentData = $data['data'] ?? [];
        $scoringRequest = $paymentData['scoring_request'] ?? [];

        $amount = (float) ($scoringRequest['manual_approved_amount'] ?? $scoringRequest['requested_amount'] ?? 0);
        $rentAmount = $amount / data_get($scoringRequest, 'additional_data.renting_ratio', 1);

        return new PaymentTokenResult(
            isValid: $isValid,
            status: $paymentData['status'] ?? 'pending',
            amount: $amount,
            rentAmount: round($rentAmount, 2),
            description: $this->buildPaymentDescription($scoringRequest),
            expiresAt: $paymentData['expires_at'] ? Carbon::parse($paymentData['expires_at']) : null,
            errorMessage: null,
            scoringRequest: $scoringRequest
        );
    }

    /**
     * Build payment description from scoring request data
     */
    private function buildPaymentDescription(array $scoringRequest): string
    {
        $fullName = $scoringRequest['full_name'] ?? 'Bilinmeyen';
        $amount = $scoringRequest['manual_approved_amount'] ?? $scoringRequest['requested_amount'] ?? 0;

        return "Kiralama Başvurusu - {$fullName} ({$amount} TL)";
    }

    /**
     * Get session ID based on user login status
     */
    private function getSessionId(): string
    {
        if (session('user.isUserLoggedIn', false)) {
            return session('user.token', session()->getId());
        }

        return session()->getId();
    }

    /**
     * Mask token for logging (show first 8 chars + ***)
     */
    private function maskToken(string $token): string
    {
        if (strlen($token) <= 8) {
            return str_repeat('*', strlen($token));
        }

        return substr($token, 0, 8) . '***';
    }

    /**
     * Mask session ID for logging
     */
    private function maskSessionId(): string
    {
        if (strlen($this->sessionId) <= 6) {
            return str_repeat('*', strlen($this->sessionId));
        }

        return substr($this->sessionId, 0, 6) . '***';
    }

    /**
     * Modify 3DS HTML to include source and token parameters for payment SMS callback
     */
    private function modify3DSHtmlForPaymentSMS(string $html, string $token): string
    {
        // Extract the callback URL and add source=payment_sms and token parameters
        $pattern = '/(action\s*=\s*["\'])([^"\']*)(["\']\s*)/i';

        $replacement = function ($matches) use ($token) {
            $url = $matches[2];
            $separator = strpos($url, '?') !== false ? '&' : '?';
            $newUrl = $url . $separator . 'source=payment_sms&token=' . urlencode($token);
            return $matches[1] . $newUrl . $matches[3];
        };

        $modifiedHtml = preg_replace_callback($pattern, $replacement, $html);

        // Log the modification for debugging
        Log::channel('sms-payment')->info('3DS HTML modified for payment SMS', [
            'token' => $this->maskToken($token),
            'original_has_action' => preg_match($pattern, $html) ? 'yes' : 'no',
            'modified_has_action' => preg_match($pattern, $modifiedHtml) ? 'yes' : 'no'
        ]);

        return $modifiedHtml ?: $html; // Fallback to original if modification fails
    }

    /**
     * Mask sensitive data in API responses
     */
    private function maskSensitiveData(string $content): string
    {
        // Mask potential sensitive fields
        $content = preg_replace('/"tckn":"([^"]*)"/', '"tckn":"***"', $content);
        $content = preg_replace('/"email":"([^"]*)"/', '"email":"***"', $content);
        $content = preg_replace('/"phone_number":"([^"]*)"/', '"phone_number":"***"', $content);

        return $content;
    }
}
