<?php

namespace App\Services\UserMetrik;

use App\Models\UserMetrik as ModelsUserMetrik;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;

class UserMetrik
{
    public static function createMetrik($metrikName)
    {
        try {
            if (!self::isMetrikEligible($metrikName)) {
                return;
            }

            $cart_product_list = collect(session('user.cart.items'))->implode('product.name', ', ');
            ModelsUserMetrik::create([
                'user_id' => session('user.user.id'),
                'metrik_name' => $metrikName,
                'metrik_value' => $cart_product_list,
            ]);
        } catch (Exception $e) {
            Log::error($e->getMessage());
        }
    }

    private static function isMetrikEligible($metrikName)
    {
        return match ($metrikName) {
            'payment_page_visited' => self::isPaymentPageVisitedEligible(),
            default => false,
        };
    }

    private static function isPaymentPageVisitedEligible()
    {
        return !Carbon::parse(session('user.user.created_at'), 'Europe/Istanbul')->addDay()->isPast();
    }
}
