<?php

namespace App\Services;

use App\DTOs\PaymentTokenResult;
use App\DTOs\PaymentResult;
use App\DTOs\PaymentStatus;

/**
 * Payment Service Interface
 * 
 * SMS ödeme sistemi için REST servis işlemlerini tanımlayan interface.
 * Mock implementasyon sonrasında gerçek REST API'ye kolayca dönüştürülebilir.
 */
interface PaymentServiceInterface
{
    /**
     * Token'ın geçerliliğini ve durumunu kontrol eder
     *
     * @param string $token 32 karakter alfanumerik payment token
     * @return PaymentTokenResult Token durumu ve detayları
     */
    public function validateToken(string $token): PaymentTokenResult;

    /**
     * Ödeme işlemini gerçekleştirir
     *
     * @param string $token Payment token
     * @param array $cardData Kart bilgileri (number, month, year, cvv, holder)
     * @param array $customerData Müşteri bilgileri (email, invoice_address)
     * @return PaymentResult Ödeme sonucu
     */
    public function processPayment(string $token, array $cardData, array $customerData = []): PaymentResult;

    /**
     * Ödeme durumunu sorgular
     *
     * @param string $token Payment token
     * @return PaymentStatus Mevcut ödeme durumu
     */
    public function getPaymentStatus(string $token): PaymentStatus;
}
