<?php

namespace App\Services;

use App\DTOs\PaymentTokenResult;
use App\DTOs\PaymentResult;
use App\DTOs\PaymentStatus;
use Carbon\Carbon;
use Illuminate\Support\Str;

/**
 * Mock Payment Service
 * 
 * Test amaçlı mock implementasyon. Gerçek REST API'ye kolayca dönüştürülebilir.
 * Çeşitli senaryoları test etmek için önceden tanımlanmış token'lar kullanır.
 */
class MockPaymentService implements PaymentServiceInterface
{
    /**
     * Mock test senaryoları için önceden tanımlanmış token'lar
     */
    private array $mockTokens = [
        // Valid pending token
        'VALID123456789012345678901234567' => [
            'status' => 'pending',
            'amount' => 299.99,
            'description' => 'iPhone 13 Pro Kiralama - Ocak 2024',
            'expires_at' => '+24 hours',
        ],

        // Already paid token
        'PAID1234567890123456789012345678' => [
            'status' => 'paid',
            'amount' => 199.50,
            'description' => 'MacBook Air Kiralama - Şubat 2024',
            'expires_at' => '+24 hours',
            'paid_at' => '-2 hours',
            'transaction_id' => 'TXN123456789',
        ],

        // Expired token
        'EXPIRED123456789012345678901234' => [
            'status' => 'expired',
            'amount' => 150.00,
            'description' => 'iPad Pro Kiralama - Mart 2024',
            'expires_at' => '-1 hour',
        ],

        // Clicked but not paid token
        'CLICKED123456789012345678901234' => [
            'status' => 'clicked',
            'amount' => 399.99,
            'description' => 'PlayStation 5 Kiralama - Nisan 2024',
            'expires_at' => '+12 hours',
        ],
    ];

    /**
     * Token'ın geçerliliğini ve durumunu kontrol eder
     */
    public function validateToken(string $token): PaymentTokenResult
    {
        // Invalid token format check
        if (strlen($token) !== 32 || !ctype_alnum($token)) {
            return new PaymentTokenResult(
                isValid: false,
                errorMessage: 'Geçersiz token formatı'
            );
        }

        // Check if token exists in mock data
        if (!isset($this->mockTokens[$token])) {
            return new PaymentTokenResult(
                isValid: false,
                errorMessage: 'Token bulunamadı veya geçersiz'
            );
        }

        $mockData = $this->mockTokens[$token];
        $expiresAt = Carbon::parse($mockData['expires_at']);

        return new PaymentTokenResult(
            isValid: true,
            status: $mockData['status'],
            amount: $mockData['amount'],
            description: $mockData['description'],
            expiresAt: $expiresAt,
            currency: 'TRY'
        );
    }

    /**
     * Ödeme işlemini gerçekleştirir
     */
    public function processPayment(string $token, array $cardData, array $customerData = []): PaymentResult
    {
        // First validate token
        $tokenResult = $this->validateToken($token);

        if (!$tokenResult->isValid) {
            return new PaymentResult(
                success: false,
                errorMessage: $tokenResult->errorMessage,
                errorCode: 'INVALID_TOKEN'
            );
        }

        if (!$tokenResult->isPayable()) {
            return new PaymentResult(
                success: false,
                errorMessage: 'Bu token ile ödeme yapılamaz (süresi dolmuş veya zaten ödenmiş)',
                errorCode: 'TOKEN_NOT_PAYABLE'
            );
        }

        // Mock card validation
        $cardValidationResult = $this->validateCardData($cardData);
        if (!$cardValidationResult['valid']) {
            return new PaymentResult(
                success: false,
                errorMessage: $cardValidationResult['error'],
                errorCode: 'CARD_VALIDATION_ERROR'
            );
        }

        // Mock payment scenarios based on card number
        $cardNumber = $cardData['number'];

        // Test card numbers for different scenarios
        if (Str::endsWith($cardNumber, '0000')) {
            // Payment failure scenario
            return new PaymentResult(
                success: false,
                errorMessage: 'Kartınızda yeterli bakiye bulunmamaktadır',
                errorCode: 'INSUFFICIENT_FUNDS',
                statusCode: 400,
                threeDSHtml: null,
                iyzToken: null,
                requires3DS: false
            );
        }

        if (Str::endsWith($cardNumber, '1111')) {
            // 3D Secure required scenario
            $mock3DSHtml = '
                <html>
                <body>
                    <h2>Mock 3D Secure Doğrulaması</h2>
                    <p>Bu bir test 3D Secure sayfasıdır.</p>
                    <form id="threeDSForm" action="/callback" method="POST">
                        <input type="hidden" name="source" value="payment_sms">
                        <input type="hidden" name="status" value="success">
                        <button type="submit" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;">
                            Doğrulamayı Tamamla
                        </button>
                    </form>
                    <script>
                        setTimeout(function() {
                            document.getElementById("threeDSForm").submit();
                        }, 3000);
                    </script>
                </body>
                </html>
            ';

            return new PaymentResult(
                success: true,
                transactionId: null,
                errorMessage: null,
                errorCode: null,
                additionalData: [
                    'status' => '3ds',
                    'html_content' => $mock3DSHtml,
                    'iyz_token' => 'MOCK_IYZ_TOKEN_' . time()
                ],
                statusCode: 200,
                threeDSHtml: $mock3DSHtml,
                iyzToken: 'MOCK_IYZ_TOKEN_' . time(),
                requires3DS: true
            );
        }

        if (Str::endsWith($cardNumber, '2222')) {
            // Fraud detection scenario
            return new PaymentResult(
                success: false,
                errorMessage: 'İşlem güvenlik kontrolü nedeniyle reddedildi',
                errorCode: 'FRAUD_DETECTED',
                statusCode: 403,
                threeDSHtml: null,
                iyzToken: null,
                requires3DS: false
            );
        }

        // Success scenario (default)
        $transactionId = 'TXN' . Str::random(10) . time();

        return new PaymentResult(
            success: true,
            transactionId: $transactionId,
            additionalData: [
                'amount' => $tokenResult->amount,
                'currency' => $tokenResult->currency,
                'processed_at' => Carbon::now()->toDateTimeString(),
            ],
            statusCode: 200,
            threeDSHtml: null,
            iyzToken: null,
            requires3DS: false
        );
    }

    /**
     * Ödeme durumunu sorgular
     */
    public function getPaymentStatus(string $token): PaymentStatus
    {
        $tokenResult = $this->validateToken($token);

        if (!$tokenResult->isValid) {
            return new PaymentStatus(
                status: 'invalid',
                description: 'Geçersiz token'
            );
        }

        $mockData = $this->mockTokens[$token];

        return new PaymentStatus(
            status: $mockData['status'],
            amount: $mockData['amount'],
            transactionId: $mockData['transaction_id'] ?? null,
            paidAt: isset($mockData['paid_at']) ? Carbon::parse($mockData['paid_at']) : null,
            expiresAt: Carbon::parse($mockData['expires_at']),
            description: $mockData['description'],
            currency: 'TRY'
        );
    }

    /**
     * Kart verilerini validate eder (mock implementation)
     */
    private function validateCardData(array $cardData): array
    {
        $required = ['number', 'month', 'year', 'cvv', 'holder'];

        foreach ($required as $field) {
            if (empty($cardData[$field])) {
                return [
                    'valid' => false,
                    'error' => ucfirst($field) . ' alanı zorunludur'
                ];
            }
        }

        // Basic card number validation
        $cardNumber = preg_replace('/\D/', '', $cardData['number']);
        if (strlen($cardNumber) < 13 || strlen($cardNumber) > 19) {
            return [
                'valid' => false,
                'error' => 'Geçersiz kart numarası'
            ];
        }

        // Expiry date validation
        $currentYear = (int) date('Y');
        $currentMonth = (int) date('m');

        if (
            $cardData['year'] < $currentYear ||
            ($cardData['year'] == $currentYear && $cardData['month'] < $currentMonth)
        ) {
            return [
                'valid' => false,
                'error' => 'Kartın son kullanma tarihi geçmiş'
            ];
        }

        // CVV validation
        if (strlen($cardData['cvv']) !== 3 || !ctype_digit($cardData['cvv'])) {
            return [
                'valid' => false,
                'error' => 'Geçersiz CVV kodu'
            ];
        }

        return ['valid' => true];
    }
}
