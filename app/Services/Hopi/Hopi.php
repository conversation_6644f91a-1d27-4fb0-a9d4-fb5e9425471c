<?php

namespace App\Services\Hopi;

use App\Models\HopiCampaign;
use SoapClient;
use SoapHeader;
use SoapVar;

class Hopi
{
    protected SoapClient $query;

    public function __construct()
    {
        $xml = <<<EOT
            <wsse:Security SOAP-ENV:mustUnderstand="1" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
                <wsse:UsernameToken>
                    <wsse:Username>%s</wsse:Username>
                    <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">%s</wsse:Password>
                </wsse:UsernameToken>
            </wsse:Security>
EOT;

        $xml = sprintf($xml, config('hopi.username'), config('hopi.password'));
        $this->query = new SoapClient(config('hopi.ep'), ['trace' => 1, 'cache_wsdl' => WSDL_CACHE_NONE]);
//        $this->query = new SoapClient('https://bird.staging.kartaca.com/pos/soap/pos.wsdl');
        $header = new SoapHeader('http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd', 'Security', new SoapVar($xml, XSD_ANYXML), true);
        $this->query->__setSoapHeaders($header);
    }

    /**
     * @param $token
     * @return mixed
     */
    public function GetBirdUserInfo($token)
    {
        try {
            $res = $this->query->GetBirdUserInfo([
                'merchantCode' => 'kiralamini',
                'storeCode' => 'kiralabunu',
                'token' => $token,
            ]);
            hopi_logger('GetBirdUserInfo', [$this->query->__getLastRequest()]);
            hopi_logger('GetBirdUserInfo', [$this->query->__getLastResponse()]);
            return $res;
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    public static function CalculateUserCampaigns($campaigns): object
    {
        if (session('user.params.birdId') === null)
            return collect();

//        $localCampaigns = collect([
//            [
//                'code' => 'kmp5',
//                'campaign_name' => 'Kiralabunu Tüm ürünlerde 200TL ve üzeri a/v’de 30 Paracık kazanımı',
//            ],
//            [
//                'code' => 'kmp6',
//                'campaign_name' => 'Kiralabunu Tüm ürünlerde 30 Paracık kazanımı',
//            ],
//            [
//                'code' => 'kmp4',
//                'campaign_name' => 'Kiralabunu Tüm ürünlerde %25 Paracık kazanımı',
//            ],
//        ]);
        $localCampaigns = HopiCampaign::all();
        if (gettype($campaigns) === 'object') {
            $campaigns = collect([$campaigns]);
        } else {
            $campaigns = collect($campaigns);
        }
        $resultCampaigns = collect();
        foreach ($localCampaigns as $campaign) {
            $res = $campaigns->where('code', $campaign['code']);
            if (!$res->isEmpty() && $campaign->min_cart_amount <= session('user.cart.total'))
                $resultCampaigns->push(array_merge((array)$res->first(), ['name' => $campaign['name']]));
        }
        $resultCampaigns->push([
            'code' => '',
            'campaign_name' => 'Kampanya kullanmak istemiyorum',
            'name' => 'Kampanya kullanmak istemiyorum',
        ]);
        return $resultCampaigns;
    }

}
