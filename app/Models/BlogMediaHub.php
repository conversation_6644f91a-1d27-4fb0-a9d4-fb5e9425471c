<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BlogMediaHub extends Model
{
    protected $connection = 'mysql-blog';
    protected $table = 'media_hub';

    protected $appends = [
        'S3URL',
        'S3Conversions',
    ];

    public function getS3URLAttribute()
    {
        // https://kiralabunu.fra1.digitaloceanspaces.com/kb-media/27/cocuk-saat-1-800x500.jpg
        return 'https:///kiralabunu.fra1.digitaloceanspaces.com/kb-media/' . $this->id . '/' . $this->file_name;
    }

    public function getS3ConversionsAttribute()
    {
        $conversions = json_decode($this->conversions, true);
        return [
            'thumbnail' => 'https://kiralabunu.fra1.digitaloceanspaces.com/kb-media/' . $this->id . '/conversions/' . ($conversions['thumbnail'] ?? $conversions['150150']),
            'medium2' => 'https://kiralabunu.fra1.digitaloceanspaces.com/kb-media/' . $this->id . '/conversions/' . ($conversions['medium2'] ?? $conversions['480540'] ?? ''),
            'medium3' => 'https://kiralabunu.fra1.digitaloceanspaces.com/kb-media/' . $this->id . '/conversions/' . ($conversions['medium3'] ?? $conversions['800500'] ?? ''),
            'medium4' => 'https://kiralabunu.fra1.digitaloceanspaces.com/kb-media/' . $this->id . '/conversions/' . ($conversions['medium4'] ?? $conversions['8101200'] ?? ''),
            'large1' => 'https://kiralabunu.fra1.digitaloceanspaces.com/kb-media/' . $this->id . '/conversions/' . ($conversions['large1'] ?? $conversions['1440840'] ?? ''),
        ];
    }
}
