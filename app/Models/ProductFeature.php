<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductFeature extends Model
{
    use HasFactory;
    use SoftDeletes;

    public static function getFeatures($product)
    {
        $pf = \App\Models\ProductFeature::where('product_id', $product->wp_product_id)->orWhere('product_id', $product->id)->get();
        if ($pf->count() > 0) {
            return $pf;
        }
    }
}
