<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductBoxContent extends Model
{
    use HasFactory;

    public static function productBoxContent($productBoxId)
    {
        //'productBoxContent' => \App\Models\ProductBoxContent::where('product_id', $product['wp_product_id'])->get(),
        $productBoxContent = self::where('product_id', $productBoxId->wp_product_id)->orWhere('product_id', $productBoxId->id)->get();
        if ($productBoxContent->count() > 0) {
            return $productBoxContent;
        }

        return ProductService::where('wp_product_id', $productBoxId->wp_product_id)->first()?->collections->map(function ($collection) {
            return self::where('product_id', $collection->id)->get();
        })->unique('key')->flatten();
    }
}
