<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Blog extends Model
{
    use HasFactory;

    protected $connection = 'mysql-blog';

    protected $appends = [
        'created_at_pretty',
        'blog_date_pretty',
    ];
    protected $casts = [
        'blog_date' => 'date'
    ];

    public function tags()
    {
        return $this->belongsToMany(BlogTag::class, 'blog_blog_tag', 'blog_id', 'blog_tag_id');
    }

    public function coverImage()
    {
        return $this->belongsTo(BlogMediaHub::class, 'main_img_id', 'id');
    }

    public function getCreatedAtPrettyAttribute()
    {
        return $this->created_at->format('d.m.Y');
    }

    public function getBlogDatePrettyAttribute()
    {
        return $this->blog_date->format('d.m.Y');
    }
}
