<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Navigation extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'items' => 'json',
    ];

    public static function getItems()
    {
        $items = self::where('handle', 'ana-menu')->first()->items;
        $res = [
            'label' => 'menu',
        ];
        foreach ($items as $item) {
            $resDetail = [
                'label' => $item['label'],
                'type' => $item['type'],
                'data' => $item['data'],
            ];
            $resDetail['children'] = empty($item['children']) ? [] : self::getItems2($item['children']);
            $res['children'][] = $resDetail;
        }

        return $res;
    }

    public static function getItems2($items)
    {
        $res = [];
        foreach ($items as $item) {
            $resDetail = [
                'label' => $item['label'],
                'type' => $item['type'],
                'data' => $item['data'],
            ];
            $resDetail['children'] = empty($item['children']) ? [] : self::getItems2($item['children']);
            $res[] = $resDetail;
        }
        return $res;

    }
}
