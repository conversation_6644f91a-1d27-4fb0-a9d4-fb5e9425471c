<?php

namespace App\Models;

use App\Extensions\CallHelper;
use App\Traits\HasRoutes;
use App\Traits\MakesNetworkCalls;
use Illuminate\Support\Facades\Log;


class BaseModel
{

    use MakesNetworkCalls, HasRoutes;

    protected $attributes;
    protected $success;
    protected $errors;
    protected static $baseKeys = [];

    /**
     * Create a new generic User object.
     *
     * @param  array  $attributes
     * @param  bool  $success
     * @param  array  $errors
     * @return void
     */
    public function __construct(array $attributes, bool $success = true, array $errors = [])
    {
        $this->attributes = $attributes;
        $this->success = $success;
        $this->errors = $errors;
    }


    public function __get($key)
    {
        if ($key == 'success') {
            return $this->success;
        }

        if ($key == 'errors') {
            return $this->errors;
        }

        return $this->attributes[$key];
    }

    /**
     * Dynamically set an attribute on the user.
     *
     * @param  string  $key
     * @param  mixed  $value
     * @return void
     */
    public function __set($key, $value)
    {
        $this->attributes[$key] = $value;
    }

    /**
     * Dynamically check if a value is set on the user.
     *
     * @param  string  $key
     * @return bool
     */
    public function __isset($key)
    {
        return isset($this->attributes[$key]);
    }

    /**
     * Dynamically unset a value on the user.
     *
     * @param  string  $key
     * @return void
     */
    public function __unset($key)
    {
        unset($this->attributes[$key]);
    }

    public static function createMany(array $models)
    {
        $col = collect([]);

        foreach ($models as $model) {
            $col->push(
                new static($model)
            );
        }
        return $col;
    }


    public static function find(array $payload = [])
    {

        if (isset(static::$routes['find'])) {
            $callHelper = new CallHelper();

            $res = $callHelper->sendPost(static::getRoute('find'), $payload);

            $attr = static::extractData($res[0], 'find');

            return new static($attr, $res[1], $res[2]);
        }
        return null;
    }

    public static function extractData($data, $key)
    {
        if (!empty(static::$baseKeys[$key]) && array_key_exists(static::$baseKeys[$key], $data)) {
            return $data[static::$baseKeys[$key]];
        }
        return $data;
    }

    public function getRaw()
    {
        return $this->attributes;
    }

    public static function sendPostWithRoute($route, $payload = [])
    {
        $resp = static::sendPost(static::getRoute($route), $payload);
        return $resp;
    }
}
