<?php

namespace App\Models;

class Product extends BaseModel
{
    public static $routes = [
        'list' => '/products',
    ];

    public static function getById($id, array $payload = [])
    {
        $resp =  static::sendGet(static::getRoute('list') . "/" . $id, $payload);

        return $resp;
    }

    public static function list(array $payload = [])
    {
        $resp =  static::sendGet(static::getRoute('list'), $payload);

        return $resp;
    }
}
