<script setup>
import { Link } from "@inertiajs/inertia-vue3";
import ApplicationLogo from "@/Components/ApplicationLogo.vue";
</script>

<template>
    <!-- Payment Layout - Minimal & Secure -->
    <div class="min-h-screen bg-gray-50">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo -->
                    <div class="w-full flex items-center justify-between">
                        <Link href="/" class="flex-shrink-0">
                        <!--                            <ApplicationLogo class="w-8 h-8 text-gray-600" />-->
                        <img src="/images/logo.png" alt="KiralaBunu Logo" class="max-h-6 md:max-h-8">
                        </Link>
                        <span class="ml-3 md:text-lg font-semibold text-gray-900">
                            G<PERSON><PERSON><PERSON>
                        </span>
                        <div class="hidden lg:flex items-center text-sm text-green-600">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            </svg>
                            SSL Güvenli
                        </div>
                    </div>

                    <!-- Security Badge -->

                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-2xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <slot />
        </main>

        <!-- Footer -->
        <footer class="mt-12 border-t border-gray-200 py-8">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center text-sm text-gray-500">
                    <p class="mb-2">
                        Bu sayfa güvenli ödeme işlemleri için tasarlanmıştır.
                    </p>
                    <p>
                        Kart bilgileriniz SSL sertifikası ile korunmaktadır ve saklanmamaktadır.
                    </p>
                    <div class="mt-4 flex justify-center items-center space-x-4">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            256-bit Şifreleme
                        </span>
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            PCI DSS Uyumlu
                        </span>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</template>

<style scoped>
/* Payment Layout specific styles */
.payment-layout {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
</style>
