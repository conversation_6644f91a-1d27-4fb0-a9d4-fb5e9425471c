import "./bootstrap";
import "../css/app.css";

import { createApp, h } from "vue/dist/vue.esm-bundler";
import { createInertiaApp } from "@inertiajs/inertia-vue3";
import { InertiaProgress } from "@inertiajs/progress";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { ZiggyVue } from "../../vendor/tightenco/ziggy/dist/vue.m";

const appName = window.document.getElementsByTagName("title")[0]?.innerText || "Laravel";

import Bugsnag from "@bugsnag/js";
import BugsnagPluginVue from "@bugsnag/plugin-vue";

Bugsnag.start({
    apiKey: "b9a30b5e49f7a62c7ed3880fd63f27a9",
    plugins: [new BugsnagPluginVue()]
});
const bugsnagVue = Bugsnag.getPlugin("vue");

createInertiaApp({
    //title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob("./Pages/**/*.vue")),
    setup({ el, app, props, plugin }) {
        return createApp({ render: () => h(app, props) })
            .use(plugin)
            .use(ZiggyVue, Ziggy)
            .use(bugsnagVue)
            .mount(el);
    }
});

InertiaProgress.init({ color: "#70d44b", showSpinner: true });
