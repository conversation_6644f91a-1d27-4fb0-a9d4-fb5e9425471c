<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import VerticalProductBox from "@/Pages/Shared/VerticalProductBox.vue";
import Loader from "@/Pages/Shared/Loader.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";

export default ({
    components: {
        <PERSON>,
        Head,
        UserMenu,
        VerticalProductBox,
        Loader,
        Splide,
        SplideSlide,
        SplideTrack
    },
    layout: Layout,
    props: ["title", "meta_description", "mostRentedProducts"]
});
</script>

<template>
    <Head :title="title">
        <meta name="description" :content="meta_description">
    </Head>

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="top-banner relative z-40 mt-8 w-full mb-10">
            <div class="flex flex-col w-full rounded-lg my-10">
                <h1 class="text-3xl font-santralextrabold mb-0 text-center">Teknolojide Kiralama Dönemi Easycep'te</h1>
            </div>
            <picture>
                <source srcset="../../images/easycep/easycep-campaign.webp" type="image/webp">
                <img class="w-full rounded-lg" src="../../images/easycep/easycep-campaign.jpeg" alt="EasyCep & Kiralabunu İş Birliği" />
            </picture>
        </section>

        <section class="mt-12 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto pb-12">
            <div class="flex justify-center">
                <div class="w-full lg:w-4/5 xl:w-3/4 px-4">
                    <div class="mb-12">
                        <p class="text-lg text-black text-center font-santralregular leading-relaxed">
                            EasyCep üzerinden yapılan alışveriş ihtiyacını karşılamak için EasyCep & Kiralabunu iş birliğini EasyCep mağazalarında ihtiyacın olan ürünü kiralayarak kullanabilirsin.
                        </p>
                    </div>

                    <div class="mb-12">
                        <div class="text-2xl font-santralextrabold mb-6 text-center">Nasıl Kullanırsın?</div>
                        <div class="space-y-4">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-6 h-6 p-1 bg-kbgreen rounded-full flex items-center justify-center">
                                    <img src="../../images/svg/right-arrow.svg" alt="">
                                </div>
                                <p class="text-base font-santralregular text-black">
                                    EasyCep mağazasından ihtiyacın olan ürünü seçersin.
                                </p>
                            </div>
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-6 h-6 p-1 bg-kbgreen rounded-full flex items-center justify-center">
                                    <img src="../../images/svg/right-arrow.svg" alt="">
                                </div>
                                <p class="text-base font-santralregular text-black">
                                    Mağaza çalışanı tarafından ödeme yöntemi "Kiralabunu" olarak seçilir.
                                </p>
                            </div>
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-6 h-6 p-1 bg-kbgreen rounded-full flex items-center justify-center">
                                    <img src="../../images/svg/right-arrow.svg" alt="">
                                </div>
                                <p class="text-base font-santralregular text-black">
                                    6, 12 veya 24 aylık kira sürelerinden hangisi ile devam etmek istediğini mağaza çalışanı ile paylaşırsın.
                                </p>
                            </div>
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-6 h-6 p-1 bg-kbgreen rounded-full flex items-center justify-center">
                                    <img src="../../images/svg/right-arrow.svg" alt="">
                                </div>
                                <p class="text-base font-santralregular text-black">
                                    Mağaza çalışanı gerekli bilgileri girerek Findeks kontrolü için Kiralabunu'ya başvurur.
                                </p>
                            </div>
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-6 h-6 p-1 bg-kbgreen rounded-full flex items-center justify-center">
                                    <img src="../../images/svg/right-arrow.svg" alt="">
                                </div>
                                <p class="text-base font-santralregular text-black">
                                    Ürün için findeks kontrolü sağlanır ve olumlu sonuçlanırsa ilk ay kira ödemeni yaparsın.
                                </p>
                            </div>
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-6 h-6 p-1 bg-kbgreen rounded-full flex items-center justify-center">
                                    <img src="../../images/svg/right-arrow.svg" alt="">
                                </div>
                                <p class="text-base font-santralregular text-black">
                                    Alışverişin başarıyla tamamlanması durumunda ürününü teslim alarak mağazadan ayrılırsın.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-12">
                        <div class="text-2xl font-santralextrabold mb-6 text-center">Neden Kullanmalısın?</div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-kb-mid-green rounded-lg p-6 border border-bordergray">
                                <h3 class="text-lg font-santralextrabold mb-3 ">PEŞİN ÖDEME YOK</h3>
                                <p class="text-sm font-santralregular text-black">
                                    Banka limitini kullanmadan, kart limitini bloke etmeden istediğin ürüne erişim!
                                </p>
                            </div>

                            <div class="bg-kb-mid-green rounded-lg p-6 border border-bordergray">
                                <h3 class="text-lg font-santralextrabold mb-3 ">24 AYA VARAN SÜRELER</h3>
                                <p class="text-sm font-santralregular text-black">
                                    Bütçeye uygun aylık ödeme planı: <br> 6 ay, 12 ay, 24 ay
                                </p>
                            </div>

                            <div class="bg-kb-mid-green rounded-lg p-6 border border-bordergray">
                                <h3 class="text-lg font-santralextrabold mb-3 ">GÜVENCE</h3>
                                <p class="text-sm font-santralregular text-black">
                                    Sigortalı cihazlar Hasar durumunda masrafın %70'si Kiralabunu'dan!
                                </p>
                            </div>

                            <div class="bg-kb-mid-green rounded-lg p-6 border border-bordergray">
                                <h3 class="text-lg font-santralextrabold mb-3 ">ESNEK KULLANIM</h3>
                                <p class="text-sm font-santralregular text-black">
                                    Süre bitince uzat, iade et ya da satın al. Kolayca üst modele geç!
                                </p>
                            </div>

                            <div class="bg-kb-mid-green rounded-lg p-6 border border-bordergray md:col-span-2">
                                <h3 class="text-lg font-santralextrabold mb-3  text-center">BANKASIZ KREDİSİZ</h3>
                                <p class="text-sm font-santralregular text-black text-center">
                                    Banka kredisi, kefil, ek teminat yok.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <a href="https://easycep.com/stores" target="_blank" class="inline-block bg-kbgreen text-white font-santralextrabold px-8 py-3 rounded-lg hover:bg-green-600 transition-colors duration-300">
                            Mağazaları Gör
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- En Çok Kiralanan Ürünler Section -->
        <section class="mt-14">
            <div class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
                <div class="flex">
                    <div class="text-xl ts:text-2xl self-center md:text-left ts:mx-4 w-full lg:w-auto">En Çok Kiralanan Ürünler</div>
                    <div class="hidden md:flex md:w-2/3 flex-1 ml-6">
                        <div class="flex-1 self-center border-1 border-bordergray"></div>
                        <Link href="/kategoriler/tum-urunler" class="font-santralextrabold border text-sm whitespace-nowrap rounded-full py-1 flex justify-center items-center px-4 border-2 md:w-32 ml-4 hover:bg-kbgreen hover:text-white"> Tümünü Gör</Link>
                    </div>
                </div>
                <div class="w-full">
                    <Splide :has-track="false" aria-label="" class="w-full mt-5" :options="{ rewind: true, perPage: 2, pagination: false, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 } } }" v-if="mostRentedProducts">
                        <SplideTrack class="w-full">
                            <SplideSlide class="flex w-full" v-for="(product, index) in mostRentedProducts.items.data.slice(0, 4)">
                                <VerticalProductBox :class="'!mx-2'" :product="product"></VerticalProductBox>
                            </SplideSlide>
                        </SplideTrack>
                        <div class="splide__arrows">
                            <button class="splide__arrow splide__arrow--prev !-left-9">
                                <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                          transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                            <button class="splide__arrow splide__arrow--next !-right-9">
                                <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                          transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                        </div>
                    </Splide>
                    <div v-else>
                        <Loader :active="true" message="Please wait 5 seconds" />
                    </div>
                </div>
            </div>
        </section>
    </div>
</template>

