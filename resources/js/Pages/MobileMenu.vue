<script>
import { <PERSON> } from "@inertiajs/inertia-vue3";
import MobileMenuItem from "@/Pages/Shared/MobileMenuItem.vue";
import ProfileMenu from "@/Pages/ProfileMenu.vue";

export default {
    components: {
        ProfileMenu,
        Link,
        MobileMenuItem
    },
    props: {
        active: {
            type: Boolean,
            default: true
        },
        wishlist: Number,
        menuItems: Object,
        auth: Object
    },
    data: () => ({
        userCartLength: 0,
        isProfileMenu: false
    }),
    mounted() {
        this.userCart = JSON.parse(localStorage.getItem("userCart")) || [];
        this.userCartLength = this.userCart.length;
    },
    emits: ["menuchange"],
    methods: {
        menuShown(val) {
            console.log("mobil menu", val);
            this.$emit("menuchange", val);
        },
        backGround() {
            this.menuShown(false);
        },
        MenuEvent() {
        }
    }
};
</script>

<template>
    <main class="w-screen bg-[#0000007a] absolute top-[-60px] left-[-35px] z-60 h-[500vh] md:hidden"
          @click="backGround()" v-if="active" @mouseenter="menuShown(true)" @mouseleave="menuShown(false)"></main>
    <header class="absolute top-[-60px] left-[-35px] bg-white max-w-67 px-5 pt-2 pb-5 z-70 min-w-[300px] md:hidden"
            @click="MenuEvent()" v-if="active && !isProfileMenu" @mouseenter="menuShown(true)"
            @mouseleave="menuShown(false)">
        <!--            <div class="mr-4">-->
        <!--                <h1>-->
        <!--                    <Link :href="`/`"><img class="w-full mx-auto" src="../../images/logo.png" alt="" /></Link>-->
        <!--                </h1>-->
        <!--            </div>-->
        <div class="flex items-center pb-1 border-b-2 border-bordergray">
            <ul class="flex w-full justify-around">
                <li class="text-sm md:text-base relative">
                    <Link href="/istek-listem" class="flex flex-col justify-center items-center">
                        <svg class="w-6 h-6 stroke-[#231f20] overflow-visible" viewBox="0 0 30.669 28.092">
                            <g id="heart" transform="translate(0 -1.511)" fill="none">
                                <path
                                    d="M30.071,6.622A13.219,13.219,0,0,1,27.842,18.6,33.873,33.873,0,0,1,22.3,24.456c-1.823,1.7-5.9,5.054-6.983,5.147-.956-.182-2.028-1.263-2.787-1.82-4.264-3.24-8.853-7.181-11.18-11.49C-.6,12.156-.6,7.039,2.433,3.867a8.977,8.977,0,0,1,12.885.853A8.726,8.726,0,0,1,18.3,2.245a8.977,8.977,0,0,1,11.77,4.377Z"
                                    stroke="none"
                                />
                                <path
                                    d="M 22.02387619018555 4.511138916015625 C 21.19887924194336 4.511205673217773 20.36529541015625 4.668869018554688 19.54391098022461 4.980022430419922 C 18.81119155883789 5.373607635498047 18.20499801635742 5.888843536376953 17.69393539428711 6.551767349243164 L 15.37918663024902 9.55436897277832 L 12.98918724060059 6.611328125 C 11.93668746948242 5.315288543701172 10.24892616271973 4.541538238525391 8.474416732788086 4.541547775268555 C 7.442102432250977 4.541547775268555 5.927898406982422 4.800741195678711 4.52613639831543 6.020847320556641 C 3.402303695678711 7.246023178100586 3.083917617797852 8.801969528198242 3.016265869140625 9.90241813659668 C 2.916469573974609 11.52574920654297 3.276287078857422 13.31153869628906 4.030189514160156 14.94011974334717 C 4.942806243896484 16.60567092895508 6.383369445800781 18.40690231323242 8.313325881958008 20.29546737670898 C 9.916957855224609 21.86470985412598 11.82877540588379 23.48118019104004 14.32891082763672 25.38162803649902 C 14.57612991333008 25.56426811218262 14.81914901733398 25.76386070251465 15.05441665649414 25.95709800720215 C 15.1480712890625 26.03402137756348 15.26153087615967 26.12721252441406 15.37966251373291 26.22183418273926 C 16.63759994506836 25.36079597473145 18.77116775512695 23.64326858520508 20.25698661804199 22.26020812988281 L 20.30599594116211 22.2145881652832 L 20.35699653625488 22.17119789123535 C 22.4555778503418 20.38569831848145 24.10916709899902 18.6432991027832 25.41224670410156 16.84445953369141 L 25.43560600280762 16.81265830993652 C 27.5122241973877 14.02390670776367 28.17683982849121 10.72176933288574 27.27249336242676 7.72297477722168 C 26.7924976348877 6.758964538574219 26.07523345947266 5.959152221679688 25.18952560424805 5.402528762817383 C 24.26146697998047 4.819278717041016 23.16680717468262 4.511037826538086 22.02387619018555 4.511138916015625 M 22.02363014221191 1.511137008666992 C 25.47772216796875 1.510845184326172 28.58621597290039 3.41154670715332 30.07120704650879 6.62156867980957 L 30.07119750976562 6.62156867980957 C 31.46405601501465 10.92536735534668 30.29865646362305 15.30498790740967 27.84177589416504 18.60439872741699 C 26.22061729431152 20.84233856201172 24.28254699707031 22.77018737792969 22.30101776123047 24.45609855651855 C 20.47795677185059 26.15307807922363 16.39905548095703 29.51041793823242 15.31799697875977 29.60327911376953 C 14.36215591430664 29.42096900939941 13.28961753845215 28.33990859985352 12.53142738342285 27.78361892700195 C 8.267665863037109 24.54384803771973 3.678485870361328 20.60295867919922 1.351097106933594 16.29318809509277 C -0.5997524261474609 12.15636825561523 -0.6031627655029297 7.038997650146484 2.433006286621094 3.867378234863281 C 6.3704833984375 0.318359375 12.3065242767334 1.011802673339844 15.31799697875977 4.720129013061523 C 16.12644577026367 3.671438217163086 17.12146759033203 2.845949172973633 18.30135726928711 2.244508743286133 C 19.5473575592041 1.74708366394043 20.80727195739746 1.511240005493164 22.02363014221191 1.511137008666992 Z"
                                    stroke="none"
                                    fill="#231f20"
                                />
                            </g>
                        </svg>
                        <span
                            class="flex items-center justify-center font-santralextrabold text-xs text-white bg-kbgreen rounded-full absolute right-1 w-4 h-4 font-normal bottom-5 right-2 text-center leading-none">
                            {{ wishlist }}
                        </span>

                        <span class="mt-1">Listem</span>
                    </Link>
                </li>
                <!--                <li class="text-sm md:text-base flex flex-col justify-center items-center">-->
                <!--                    <Link href="/sepetim" class="flex flex-col justify-center items-center relative">-->
                <!--                        <svg class="w-6 h-6 ml-1 stroke-[#231f20] overflow-visible" viewBox="0 0 30.324 31.234">-->
                <!--                            <g id="Group_3491" data-name="Group 3491" transform="translate(1.51 1.5)">-->
                <!--                                <path-->
                <!--                                    id="Path_2958"-->
                <!--                                    data-name="Path 2958"-->
                <!--                                    d="M212.736,218.3H199.28c-2.21,0-4.076-1.109-4.357-2.59l-2.53-13.314c-.337-1.774,1.709-3.345,4.357-3.345h18.515c2.648,0,4.695,1.571,4.358,3.345l-2.53,13.314C216.812,217.194,214.946,218.3,212.736,218.3Z"-->
                <!--                                    transform="translate(-192.356 -190.068)"-->
                <!--                                    fill="none"-->
                <!--                                    stroke="#231f20"-->
                <!--                                    stroke-miterlimit="10"-->
                <!--                                    stroke-width="3"-->
                <!--                                />-->
                <!--                                <path id="Path_2959" data-name="Path 2959"-->
                <!--                                      d="M202.477,192.194l5.167-7.892c.942-1.439,2.268-1.46,3.222-.052l5.381,7.944"-->
                <!--                                      transform="translate(-195.558 -183.208)" fill="none" stroke="#231f20"-->
                <!--                                      stroke-miterlimit="10" stroke-width="3"/>-->
                <!--                            </g>-->
                <!--                        </svg>-->
                <!--                        <span-->
                <!--                            class="text-xs text-white bg-kbgreen rounded-full absolute right-1 w-4 h-4 font-normal bottom-5 right-2 text-center leading-[1.2rem]">{{-->
                <!--                                userCartLength-->
                <!--                            }}</span>-->

                <!--                        <span class="mt-1">Sepetim</span>-->
                <!--                    </Link>-->
                <!--                </li>-->
                <li class="text-sm md:text-base flex flex-col justify-center items-center" v-if="auth.isUserLoggedIn">
                    <div class="flex flex-col justify-center items-center" @click="isProfileMenu = true">
                        <div
                            class="font-santralextrabold text-3xs leading-none text-kbgreen w-6 h-6 border-2 rounded-full border-black flex justify-center items-center">
                            {{
                                auth.user?.full_name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")
                            }}
                        </div>
                        <span class="mt-1 text-sm">Hesabım</span>
                    </div>
                </li>
                <li class="flex flex-col justify-center items-center" @click="menuShown(false)"
                    v-if="!auth.isUserLoggedIn">
                    <Link :href="`/giris-yap`" class="flex">
                        <svg class="w-6 h-6 stroke-[#231f20] overflow-visible" viewBox="0 0 34 34">
                            <g id="Ellipse_138" data-name="Ellipse 138" fill="none" stroke="#231f20" stroke-width="3">
                                <circle cx="17" cy="17" r="17" stroke="none" />
                                <circle cx="17" cy="17" r="15.5" fill="none" />
                            </g>
                        </svg>
                    </Link>
                    <Link :href="`/giris-yap`" class="flex">
                        <span class="text-sm mt-1">Giriş/Üye Ol</span>
                    </Link>
                </li>
            </ul>
        </div>
        <nav class="mt-3 border-bordergray">
            <ul class="flex flex-col font-bold text-sm cd-accordion-menu" v-if="!isProfileMenu">
                <MobileMenuItem v-for="(menuItemSub, index2) in menuItems.children" :key="index2"
                                :model="menuItemSub"></MobileMenuItem>
            </ul>
        </nav>
        <div class="mt-3 pb-1 border-b-2 border-bordergray">
            <ul class="flex flex-col text-xl leading-6.5 font-medium">

                <li class="p-0 m-0 mb-2 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                    <Link href="/nasil-calisir">Nasıl Çalışır?</Link>
                </li>
                <li class="p-0 m-0 mb-2 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                    <Link href="/kurumsal">Kurumsal</Link>
                </li>
                <li class="p-0 m-0 mb-2 text-base text-left text-gray-900 box-border whitespace-no-wrap"><a href="">İndirimli
                    Ürünler</a></li>
                <li class="p-0 m-0 mb-2 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                    <a href="https://kiralamini.com/" target="_blank">
                        <!--                        <div class="flex items-center"><span class="whitespace-nowrap">KİRALA</span><span-->
                        <!--                            class="text-[#ffbd64]">m</span><span class="text-[#ff64cf]">i</span><span-->
                        <!--                            class="text-[#61ba3f]">n</span><span class="text-[#8854d0]">i</span></div>-->
                        <img class="w-full max-w-[120px]"
                             src="https://kiralamini.com/wp-content/uploads/2023/02/cropped-logo-2048x344.png" alt="">
                    </a>
                </li>
            </ul>
        </div>
        <div v-if="auth.isUserLoggedIn" class="flex items-center mt-3">
            <Link href="/cikis-yap">
                <svg xmlns="http://www.w3.org/2000/svg" width="22.401" height="25.546" viewBox="0 0 22.401 25.546">
                    <g id="noun-exit-119208" transform="translate(-128.547 -26.727)">
                        <path id="Path_3738" data-name="Path 3738"
                              d="M130.729,212.969H141.32a.642.642,0,0,0,0-1.284H130.729l1.861-1.861a.635.635,0,1,0-.9-.9l-2.953,2.953c-.064.064-.128.128-.128.192a.545.545,0,0,0,0,.513c.064.064.064.128.128.192l2.953,2.953a.621.621,0,0,0,.9,0,.62.62,0,0,0,0-.9Z"
                              transform="translate(0 -172.827)" fill="#231f20" />
                        <path
                            id="Path_3739"
                            data-name="Path 3739"
                            d="M263.757,26.727h-9.885a3.15,3.15,0,0,0-3.145,3.145v5.841a.642.642,0,0,0,1.284,0V29.872a1.863,1.863,0,0,1,1.861-1.861h9.949a1.863,1.863,0,0,1,1.861,1.861V49.128a1.863,1.863,0,0,1-1.861,1.861h-9.949a1.863,1.863,0,0,1-1.861-1.861v-5.52a.642.642,0,0,0-1.284,0v5.52a3.15,3.15,0,0,0,3.145,3.145h9.949a3.15,3.15,0,0,0,3.145-3.145V29.872a3.165,3.165,0,0,0-3.209-3.145Z"
                            transform="translate(-116.018 0)"
                            fill="#231f20"
                        />
                    </g>
                </svg>
            </Link>
            <Link href="/cikis-yap" class="ml-3 whitespace-nowrap text-base font-bold">Çıkış Yap</Link>
        </div>
    </header>
    <profile-menu v-if="active && isProfileMenu" :auth="auth" :activeSubMenu="isProfileMenu"
                  @menuShown2="isProfileMenu = false"></profile-menu>
</template>
