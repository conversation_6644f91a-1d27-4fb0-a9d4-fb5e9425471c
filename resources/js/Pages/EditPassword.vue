<script>
import {Head, <PERSON>} from '@inertiajs/inertia-vue3';
import Layout from '@/Pages/Shared/Layout.vue';
import UserMenu from '@/Pages/Shared/UserMenu.vue';

export default {
    components: {
        Link,
        Head,
        UserMenu,

    },
    layout: Layout,
    data() {
        return {
            password: "",
            passwordFieldType: "password",
            password2: "",
            passwordFieldType2: "password",
            password3: "",
            passwordFieldType3: "password",
        };
    },
    methods: {
        switchVisibility() {
            this.passwordFieldType = this.passwordFieldType === "password" ? "text" : "password";
        },
        switchVisibility2() {
            this.passwordFieldType2 = this.passwordFieldType2 === "password" ? "text" : "password";
        },
        switchVisibility3() {
            this.passwordFieldType3 = this.passwordFieldType3 === "password" ? "text" : "password";
        }
    }
}
</script>

<template>
    <Head title="Profil Düzenle" />
    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <user-menu :active="EditProfile" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 lg:px-4 pt-2">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-center md:text-left text-gray-900 box-border whitespace-no-wrap mb-7 w-full">Şifre Değiştir </h3>
                </div>
                <form class="flex flex-col justify-center items-center w-full mt-0 lg:mt-10" action="#">
                    <div class="w-full lg:w-6/12 text-center">
                        <p class="mb-8 text-center ts:text-left text-textgray text-sm">Şifren en az bir harf, rakam veya özel karakter içermeli. Ayrıca şifren en az 8 karakterden oluşmalı.</p>
                        <div class="relative group">
                            <input id="oldpass" :type="passwordFieldType" v-model="password" class="peer w-full mb-7 rounded-2lg border-2 border-bordergray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-lg tracking-wider" required placeholder="" name="EskiParola" autofocus>
                            <label for="oldpass" class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Mevcut Parola*</label>
                            <button @click="switchVisibility" class="absolute mt-3 top-1 right-5">
                                <svg id="eye-line" xmlns="http://www.w3.org/2000/svg" width="23.026" height="15" viewBox="0 0 23.026 15">
                                    <path id="Path_171" data-name="Path 171" d="M24.56,14.69A12.939,12.939,0,0,0,13.233,7.53,12.893,12.893,0,0,0,1.92,14.69l-.2.337.186.344a12.939,12.939,0,0,0,11.327,7.16A12.863,12.863,0,0,0,24.56,15.37l.186-.344ZM13.233,21.062a11.389,11.389,0,0,1-9.881-6.036,11.389,11.389,0,0,1,9.881-6.036,11.436,11.436,0,0,1,9.874,6.036A11.43,11.43,0,0,1,13.233,21.062Z" transform="translate(-1.72 -7.53)" fill="#a8a8a8"/>
                                    <path id="Path_172" data-name="Path 172" d="M16.156,11.17a4.912,4.912,0,1,0,3.489,1.418,4.912,4.912,0,0,0-3.489-1.418Zm0,8.391a3.487,3.487,0,1,1,2.489-1.016,3.48,3.48,0,0,1-2.489,1.016Z" transform="translate(-4.435 -8.563)" fill="#a8a8a8"/>
                                </svg>
                            </button>
                            <div class="absolute -right-8 top-4">
                                <svg id="check-circle-fill" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
                                    <path id="Path_173" data-name="Path 173" d="M20,10A10,10,0,1,1,10,0,10,10,0,0,1,20,10ZM15.037,6.213a.938.938,0,0,0-1.35.027L9.346,11.771,6.73,9.154a.938.938,0,0,0-1.325,1.325l3.307,3.308a.938.938,0,0,0,1.349-.025l4.99-6.238a.938.938,0,0,0-.012-1.313Z" fill="#70d44b" fill-rule="evenodd"/>
                                </svg>
                            </div>
                        </div>

                        <div class="relative">
                            <input id="newpass" :type="passwordFieldType2" v-model="password2" class="peer w-full mb-7 rounded-2lg border-2 border-bordergray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-lg tracking-wider" required placeholder="" name="YeniParola" >
                            <label for="newpass" class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Yeni Parola*</label>
                            <button @click="switchVisibility2" class="absolute mt-3 top-1 right-5">
                                <svg id="eye-line" xmlns="http://www.w3.org/2000/svg" width="23.026" height="15" viewBox="0 0 23.026 15">
                                    <path id="Path_171" data-name="Path 171" d="M24.56,14.69A12.939,12.939,0,0,0,13.233,7.53,12.893,12.893,0,0,0,1.92,14.69l-.2.337.186.344a12.939,12.939,0,0,0,11.327,7.16A12.863,12.863,0,0,0,24.56,15.37l.186-.344ZM13.233,21.062a11.389,11.389,0,0,1-9.881-6.036,11.389,11.389,0,0,1,9.881-6.036,11.436,11.436,0,0,1,9.874,6.036A11.43,11.43,0,0,1,13.233,21.062Z" transform="translate(-1.72 -7.53)" fill="#a8a8a8"/>
                                    <path id="Path_172" data-name="Path 172" d="M16.156,11.17a4.912,4.912,0,1,0,3.489,1.418,4.912,4.912,0,0,0-3.489-1.418Zm0,8.391a3.487,3.487,0,1,1,2.489-1.016,3.48,3.48,0,0,1-2.489,1.016Z" transform="translate(-4.435 -8.563)" fill="#a8a8a8"/>
                                </svg>
                            </button>
                        </div>
                        <div class="relative">
                            <input id="renewpass" :type="passwordFieldType3" v-model="password3" class="peer w-full mb-7 rounded-2lg border-2 border-bordergray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-lg tracking-wider" required placeholder="" name="ParolaTekrar" >
                            <label for="renewpass" class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Yeni Parola Tekrar*</label>
                            <button @click="switchVisibility3" class="absolute mt-3 top-1 right-5">
                                <svg id="eye-line" xmlns="http://www.w3.org/2000/svg" width="23.026" height="15" viewBox="0 0 23.026 15">
                                    <path id="Path_171" data-name="Path 171" d="M24.56,14.69A12.939,12.939,0,0,0,13.233,7.53,12.893,12.893,0,0,0,1.92,14.69l-.2.337.186.344a12.939,12.939,0,0,0,11.327,7.16A12.863,12.863,0,0,0,24.56,15.37l.186-.344ZM13.233,21.062a11.389,11.389,0,0,1-9.881-6.036,11.389,11.389,0,0,1,9.881-6.036,11.436,11.436,0,0,1,9.874,6.036A11.43,11.43,0,0,1,13.233,21.062Z" transform="translate(-1.72 -7.53)" fill="#a8a8a8"/>
                                    <path id="Path_172" data-name="Path 172" d="M16.156,11.17a4.912,4.912,0,1,0,3.489,1.418,4.912,4.912,0,0,0-3.489-1.418Zm0,8.391a3.487,3.487,0,1,1,2.489-1.016,3.48,3.48,0,0,1-2.489,1.016Z" transform="translate(-4.435 -8.563)" fill="#a8a8a8"/>
                                </svg>
                            </button>
                        </div>
                        <div class="ts:text-right">
                            <button class="w-full ts:w-auto bg-black mb-7 text-white text-lg rounded-full py-2 px-4 self-center font-bold">Şifreyi Güncelle</button>

                        </div>
                    </div>
                </form>
            </div>
        </section>
    </main>

</template>
