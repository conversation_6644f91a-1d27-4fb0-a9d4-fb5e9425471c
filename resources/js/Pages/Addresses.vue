<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu
    },
    props: {
        addresses: Object,
        order_count: Number
    },
    data() {
        return {
            selectedAddress: null
        };
    },
    layout: Layout
};
</script>

<template>

    <Head title="Adresler" />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <user-menu :order_count="order_count" :active="`Addresses`" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 px-4 pt-2">
                <div class="flex justify-between items-center mb-7">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap">
                        Ad<PERSON>ler</h3>
                    <Link href="/adres-ekle" class="hidden md:block w-32 bg-black text-white text-base text-center rounded-full py-2 px-1 font-bold">Adres Ekle
                    </Link>
                </div>
                <div class="flex flex-col justify-center items-center mb-10" v-if="Object.entries(addresses).length == 0">
                    <div class="flex justify-center items-center bg-kb-light-grey rounded-full w-32 h-32 mb-5">
                        <div class="flex justify-center items-center bg-black rounded-full w-20 h-20">
                            <svg id="Group_1039" data-name="Group 1039" xmlns="http://www.w3.org/2000/svg" width="66.681" height="47.75" viewBox="0 0 66.681 47.75">
                                <path id="home"
                                    d="M12.222,6.77a.78.78,0,0,1-.572.237.769.769,0,0,1-.566-.231L10.9,6.593v5.1a.758.758,0,0,1-.225.547.743.743,0,0,1-.554.231H7.787v-3.5A.4.4,0,0,0,7.4,8.576H5.061a.4.4,0,0,0-.389.389v3.5H2.336a.743.743,0,0,1-.554-.231.757.757,0,0,1-.225-.548v-5.1l-.183.183a.769.769,0,0,1-.566.231A.78.78,0,0,1,.237,**********,0,0,1,0,6.2a.769.769,0,0,1,.231-.566l5.4-5.4A.771.771,0,0,1,6.229,0a.771.771,0,0,1,.6.231l5.4,5.4a.769.769,0,0,1,.231.566.78.78,0,0,1-.237.572Z"
                                    transform="translate(0 35.037)" fill="#fff" />
                                <path id="building"
                                    d="M43.614,47.75H1.492a1.434,1.434,0,0,1-1.049-.443,1.464,1.464,0,0,1,0-2.1,1.434,1.434,0,0,1,1.049-.443H2.984a2.848,2.848,0,0,0,2.122-.886,2.9,2.9,0,0,0,.862-2.1V2.984a2.868,2.868,0,0,1,.886-2.1A2.868,2.868,0,0,1,8.953,0H38.8a2.848,2.848,0,0,1,2.122.886,2.9,2.9,0,0,1,.862,2.1v38.8a2.848,2.848,0,0,0,.886,2.122c.591.576.139.863.947.862ZM17.906,7.461a1.513,1.513,0,0,0-1.492-1.492H13.43a1.513,1.513,0,0,0-1.492,1.492v2.984a1.513,1.513,0,0,0,1.492,1.492h2.984a1.513,1.513,0,0,0,1.492-1.492Zm0,8.953a1.513,1.513,0,0,0-1.492-1.492H13.43a1.513,1.513,0,0,0-1.492,1.492V19.4a1.513,1.513,0,0,0,1.492,1.492h2.984A1.513,1.513,0,0,0,17.906,19.4Zm0,8.953a1.513,1.513,0,0,0-1.492-1.492H13.43a1.513,1.513,0,0,0-1.492,1.492v2.984a1.513,1.513,0,0,0,1.492,1.492h2.984a1.513,1.513,0,0,0,1.492-1.492ZM26.859,7.461a1.513,1.513,0,0,0-1.492-1.492H22.383a1.513,1.513,0,0,0-1.492,1.492v2.984a1.513,1.513,0,0,0,1.492,1.492h2.984a1.513,1.513,0,0,0,1.492-1.492Zm0,8.953a1.513,1.513,0,0,0-1.492-1.492H22.383a1.513,1.513,0,0,0-1.492,1.492V19.4a1.513,1.513,0,0,0,1.492,1.492h2.984A1.513,1.513,0,0,0,26.859,19.4Zm0,8.953a1.513,1.513,0,0,0-1.492-1.492H22.383a1.513,1.513,0,0,0-1.492,1.492v2.984a1.513,1.513,0,0,0,1.492,1.492h2.984a1.513,1.513,0,0,0,1.492-1.492Zm1.492,10.445H19.4A1.513,1.513,0,0,0,17.906,37.3v5.969A1.513,1.513,0,0,0,19.4,44.766h8.953a1.513,1.513,0,0,0,1.492-1.492V37.3a1.513,1.513,0,0,0-1.492-1.492ZM35.813,7.461A1.513,1.513,0,0,0,34.32,5.969H31.336a1.513,1.513,0,0,0-1.492,1.492v2.984a1.513,1.513,0,0,0,1.492,1.492H34.32a1.513,1.513,0,0,0,1.492-1.492Zm0,8.953a1.513,1.513,0,0,0-1.492-1.492H31.336a1.513,1.513,0,0,0-1.492,1.492V19.4a1.513,1.513,0,0,0,1.492,1.492H34.32A1.513,1.513,0,0,0,35.813,19.4Zm0,8.953a1.513,1.513,0,0,0-1.492-1.492H31.336a1.513,1.513,0,0,0-1.492,1.492v2.984a1.513,1.513,0,0,0,1.492,1.492H34.32a1.513,1.513,0,0,0,1.492-1.492Z"
                                    transform="translate(6.229 0)" fill="#fff" />
                                <path id="home-2" data-name="home"
                                    d="M24.921,13.806a1.589,1.589,0,0,1-1.166.484,1.569,1.569,0,0,1-1.154-.471l-.372-.372v10.4a1.545,1.545,0,0,1-.459,1.116,1.515,1.515,0,0,1-1.129.471H15.878V18.284a.806.806,0,0,0-.794-.794H10.321a.806.806,0,0,0-.794.794v7.145H4.763a1.515,1.515,0,0,1-1.129-.471,1.543,1.543,0,0,1-.458-1.117v-10.4l-.372.372a1.569,1.569,0,0,1-1.154.471,1.589,1.589,0,0,1-1.166-.484A1.589,1.589,0,0,1,0,12.639a1.569,1.569,0,0,1,.471-1.154L11.486.471A1.571,1.571,0,0,1,12.7,0a1.571,1.571,0,0,1,1.216.471L24.933,11.486a1.569,1.569,0,0,1,.471,1.154,1.589,1.589,0,0,1-.484,1.166Z"
                                    transform="translate(41.277 22.321)" fill="#fff" />
                            </svg>
                        </div>
                    </div>
                    <p class="p-0 text-lg font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-5 text-center">
                        Kayıtlı Adresin bulunmamaktadır.</p>
                    <p class="p-0 text-base text-center text-kbgray box-border whitespace-no-wrap mb-5">Yeni bir adres
                        eklemek için aşağıdaki <span class="text-black font-bold">Adres Ekle</span> butonuna tıkla.</p>
                    <Link href="/adres-ekle" class="bg-black text-white rounded-full py-2 px-4 self-center text-lg font-bold">Adres Ekle
                    </Link>
                </div>
                <div v-else>
                    <div class="flex justify-start items-center w-full mb-3 bg-white border-2 border-kb-light-grey py-3 px-5 rounded-lg" v-for="adress in addresses">
                        <input class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" v-model="selectedAddress" />
                        <div class="w-4/6 md:w-5/6 pl-4 flex flex-wrap">
                            <div class="w-full flex">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18.07" height="18.089" viewBox="0 0 18.07 18.089" v-if="adress.type == 'home'">
                                    <path id="home"
                                        d="M17.727,9.82a1.131,1.131,0,0,1-.83.344,1.116,1.116,0,0,1-.821-.335l-.265-.265v7.394a1.1,1.1,0,0,1-.327.794,1.077,1.077,0,0,1-.8.335H11.294V13.005a.573.573,0,0,0-.565-.565H7.341a.573.573,0,0,0-.565.565v5.082H3.388a1.077,1.077,0,0,1-.8-.335,1.1,1.1,0,0,1-.326-.794V9.564l-.265.265a1.116,1.116,0,0,1-.821.335,1.131,1.131,0,0,1-.83-.344A1.131,1.131,0,0,1,0,8.99,1.116,1.116,0,0,1,.335,8.17L8.17.334A1.118,1.118,0,0,1,9.035,0,1.118,1.118,0,0,1,9.9.334L17.735,8.17a1.116,1.116,0,0,1,.335.821,1.131,1.131,0,0,1-.344.83Z"
                                        transform="translate(0 0.002)" fill="#139a52" />
                                </svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16.298" height="16.298" viewBox="0 0 16.298 16.298" v-else>
                                    <path id="building"
                                        d="M15.789,16.3H.509a.489.489,0,0,1-.358-.151.5.5,0,0,1,0-.716.489.489,0,0,1,.358-.151h.509a.972.972,0,0,0,.724-.3.99.99,0,0,0,.294-.716V1.019A.979.979,0,0,1,2.34.3.979.979,0,0,1,3.056,0H13.242a.972.972,0,0,1,.724.3.99.99,0,0,1,.294.716V14.261a.972.972,0,0,0,.3.724.99.99,0,0,0,.716.294h.509a.509.509,0,0,1,0,1.019ZM6.112,2.547A.516.516,0,0,0,5.6,2.037H4.584a.516.516,0,0,0-.509.509V3.565a.516.516,0,0,0,.509.509H5.6a.516.516,0,0,0,.509-.509Zm0,3.056A.516.516,0,0,0,5.6,5.093H4.584a.516.516,0,0,0-.509.509V6.621a.516.516,0,0,0,.509.509H5.6a.516.516,0,0,0,.509-.509Zm0,3.056A.516.516,0,0,0,5.6,8.149H4.584a.516.516,0,0,0-.509.509V9.677a.516.516,0,0,0,.509.509H5.6a.516.516,0,0,0,.509-.509ZM9.168,2.547a.516.516,0,0,0-.509-.509H7.64a.516.516,0,0,0-.509.509V3.565a.516.516,0,0,0,.509.509H8.658a.516.516,0,0,0,.509-.509Zm0,3.056a.516.516,0,0,0-.509-.509H7.64A.516.516,0,0,0,7.13,5.6V6.621a.516.516,0,0,0,.509.509H8.658a.516.516,0,0,0,.509-.509Zm0,3.056a.516.516,0,0,0-.509-.509H7.64a.516.516,0,0,0-.509.509V9.677a.516.516,0,0,0,.509.509H8.658a.516.516,0,0,0,.509-.509Zm.509,3.565H6.621a.516.516,0,0,0-.509.509V14.77a.516.516,0,0,0,.509.509H9.677a.516.516,0,0,0,.509-.509V12.733a.516.516,0,0,0-.509-.509Zm2.547-9.677a.516.516,0,0,0-.509-.509H10.7a.516.516,0,0,0-.509.509V3.565a.516.516,0,0,0,.509.509h1.019a.516.516,0,0,0,.509-.509Zm0,3.056a.516.516,0,0,0-.509-.509H10.7a.516.516,0,0,0-.509.509V6.621a.516.516,0,0,0,.509.509h1.019a.516.516,0,0,0,.509-.509Zm0,3.056a.516.516,0,0,0-.509-.509H10.7a.516.516,0,0,0-.509.509V9.677a.516.516,0,0,0,.509.509h1.019a.516.516,0,0,0,.509-.509Z"
                                        fill="#139a52" />
                                </svg>
                                <label class="pl-2 text-base text-black font-bold cursor-pointer" for="yanlis">{{ adress.name }}</label>
                            </div>
                            <p class="hidden md:block mt-1 text-sm text-textgray cursor-pointer max-w-[350px] ts:max-w-none">
                                {{ adress.address }}
                            </p>
                        </div>
                        <p class="w-2/6 md:w-1/6 mt-1 text-base text-black cursor-pointer font-bold text-right">
                            <Link href="/adres-duzenle" :data="{
                                id: adress.id,
                            }" method="get" as="button">Düzenle
                            </Link>
                        </p>
                    </div>
                    <Link class="block md:hidden w-full ts:w-32 mt-5 bg-black text-white text-base text-center rounded-full py-2 px-1 font-bold" href="/adres-ekle">Adres Ekle
                    </Link>
                </div>
            </div>
        </section>
    </main>
</template>
