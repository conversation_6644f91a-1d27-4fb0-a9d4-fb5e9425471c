<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import { vMaska } from "maska";
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle
} from "@headlessui/vue";

export default {
    components: {
        Link,
        Head,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle
    },
    directives: { maska: vMaska },
    props: {
        errors: { type: Object, default: false }
    },
    data() {
        return {
            form: this.$inertia.form({
                first_name: null,
                last_name: null,
                email: null,
                phone: null,
                password: null,
                password2: null,
                passwordFieldType: "password",
                passwordFieldType2: "password"
            }),
            isOpen: false
        };
    },
    methods: {
        submit() {
            console.log(this.form.password, this.form.password2);
            // let password2 = document.getElementById("password2");
            // if (this.form.password != this.form.password2) {
            //     password2.setCustomValidity("Girmiş olduğunuz şifreler eşleşmiyor.");
            //     this.form.password = null;
            //     this.form.password2 = null;
            //     return;
            // } else {
            //     password2.setCustomValidity("");
            // }

            this.form.post("/hesap-olustur");
        },
        checkPasswordValidity() {
            let password2 = document.getElementById("password2");
            if (this.form.password != this.form.password2) {
                password2.setCustomValidity("Girmiş olduğunuz şifreler eşleşmiyor.");
            } else {
                password2.setCustomValidity("");
            }
        },
        preventNumericInput($event) {
            console.log($event.keyCode); //will display the keyCode value
            //console.log($event.key); will show the key value

            var charCode = $event.keyCode ? $event.keyCode : $event.which;
            if ((charCode <= 93 && charCode >= 65) || (charCode <= 122 && charCode >= 97) || charCode == 32 || charCode == 8 || charCode == 350 || charCode == 351 || charCode == 304 || charCode == 286 || charCode == 287 || charCode == 231 || charCode == 199 || charCode == 305 || charCode == 214 || charCode == 246 || charCode == 220 || charCode == 252) {
                if (charCode != 33 || charCode != 43) {
                    return true;
                } else {
                    $event.preventDefault();
                }
            } else {
                $event.preventDefault();
            }
        },
        switchVisibility() {
            this.form.passwordFieldType = this.form.passwordFieldType === "password" ? "text" : "password";
        },
        switchVisibility2() {
            this.form.passwordFieldType2 = this.form.passwordFieldType2 === "password" ? "text" : "password";
        },
        closeModal() {
            //console.log(this.isOpen)
            this.isOpen = false;
        },
        openModal() {
            this.isOpen = true;
        }
    },
    layout: Layout
};
</script>

<template>

    <Head title="Üyelik Oluştur" />
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white px-5 py-5 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 62.051 62.051">
                                <g id="Group_4884" data-name="Group 4884" transform="translate(-73.036 -250.295)">
                                    <circle id="Ellipse_199" data-name="Ellipse 199" cx="31.025" cy="31.025" r="31.025" transform="translate(73.036 250.295)" fill="#70d44b" />
                                    <path id="Path_3299" data-name="Path 3299"
                                        d="M351.649,476.232a3.45,3.45,0,0,1-2.761-1.381l-8.464-11.286a3.451,3.451,0,0,1,5.522-4.141l5.538,7.383,14.223-21.334a3.451,3.451,0,1,1,5.743,3.829l-16.928,25.393a3.453,3.453,0,0,1-2.776,1.536Z"
                                        transform="translate(-251.82 -178.764)" fill="#fff" />
                                </g>
                            </svg>
                            <DialogTitle>
                                <h2 class="text-base font-semibold text-center mb-2 mt-2">Tebrikler</h2>
                            </DialogTitle>
                            <p class="text-xs font-kiralabunuthin ts:text-sm text-center ">Üyeliğiniz başarıyla
                                oluşturuldu.</p>
                            <!--                            <div class="mt-4">-->
                            <!--                                <button-->
                            <!--                                    type="button"-->
                            <!--                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"-->
                            <!--                                    @click="closeModal"-->
                            <!--                                >-->
                            <!--                                    Got it, thanks!-->
                            <!--                                </button>-->
                            <!--                            </div>-->
                            <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal">x</div>

                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>

    <main class="my-6">
        <section class="mt-6">
            <form name="RegisterForm" class="flex mx-auto flex-col max-w-[350px] md:max-w-lg py-8 rounded-2lg bg-white shadow-searchshadow" @submit.prevent="submit">
                <img src="../../images/logo.png" class="block lg:hidden mx-auto w-60 mb-10" alt="Logo" />
                <div class="font-bold text-xl self-center">Üyelik Oluştur</div>
                <div class="flex self-center mt-6 justify-between w-80 md:w-96">
                    <div class="group relative">
                        <input @keypress="preventNumericInput" id="name" type="text" v-model="form.first_name" autofocus
                            class="peer border-2 rounded-lg border-kb-light-grey w-36 md:w-44 hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" required autocomplete="off" />
                        <label for="name"
                            class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Ad*</label>
                    </div>
                    <div class="group relative">
                        <input @keypress="preventNumericInput" id="surname" type="text" v-model="form.last_name" placeholder=""
                            class="peer border-2 rounded-lg border-kb-light-grey w-36 md:w-44 hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" required autocomplete="off" />
                        <label for="surname"
                            class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Soyad*</label>
                    </div>
                </div>
                <div class="self-center mt-6 group relative">
                    <input id="email" type="email" placeholder="E-posta adresi*" v-model="form.email" autocomplete="off"
                        class="peer placeholder-transparent border-2 rounded-lg border-kb-light-grey w-80 md:w-96 hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" required />
                    <!--                    <label
                                            for="email"
                                            class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                            >E-posta Adresi*</label
                                        >-->
                    <label for="email"
                        class="absolute left-4 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">E-posta
                        adresi*</label>

                </div>
                <div class="self-center mt-6">
                    <!--                    <vue-tel-input v-model="phone"></vue-tel-input>-->
                    <!--                    <code>-->
                    <!--                        {{ results }}-->
                    <!--                    </code>-->
                    <div class="flex">
                        <button id="dropdown-button-2" data-dropdown-toggle="dropdown-search-city"
                            class="flex-shrink-0 z-10 inline-flex items-center py-1 px-4 text-sm font-medium text-center text-gray-500 bg-white border-2 border-bordergray rounded-l-lg hover:bg-gray-200 focus:ring-4 focus:outline-none focus:ring-gray-100"
                            type="button">
                            <svg aria-hidden="true" class="h-7 pt-2" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill="#E30A17" d="M0 0h12v8H0z" />
                                <circle cx="4.25" cy="4" r="2" fill="#fff" />
                                <circle cx="4.75" cy="4" r="1.6" fill="#e30a17" />
                                <path fill="#fff" d="M5.83334 4l1.80901 .58779-1.11804-1.53885v1.90212l1.11804-1.53885z" />
                            </svg>
                            TR
                            <svg aria-hidden="true" class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>

                        <div id="dropdown-search-city" class="z-10 hidden bg-white divide-y divide-gray-100 rounded shadow w-44 dark:bg-gray-700" data-popper-reference-hidden="" data-popper-escaped="" data-popper-placement="top"
                            style="position: absolute; inset: auto auto 0px 0px; margin: 0px; transform: translate3d(897px, 5637px, 0px)">
                            <ul class="py-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdown-button-2">
                                <li>
                                    <button type="button" class="inline-flex w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">
                                        <div class="inline-flex items-center">
                                            <svg aria-hidden="true" class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-us" viewBox="0 0 512 512">
                                                <g fill-rule="evenodd">
                                                    <g stroke-width="1pt">
                                                        <path fill="#bd3d44" d="M0 0h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0z" transform="scale(3.9385)" />
                                                        <path fill="#fff" d="M0 10h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0z" transform="scale(3.9385)" />
                                                    </g>
                                                    <path fill="#192f5d" d="M0 0h98.8v70H0z" transform="scale(3.9385)" />
                                                    <path fill="#fff"
                                                        d="M8.2 3l1 2.8H12L9.7 7.5l.9 2.7-2.4-1.7L6 10.2l.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8H45l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7L74 8.5l-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9L92 7.5l1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm-74.1 7l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7H65zm16.4 0l1 2.8H86l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm-74 7l.8 2.8h3l-2.4 1.7.9 2.7-2.4-1.7L6 24.2l.9-2.7-2.4-1.7h3zm16.4 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8H45l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9L92 21.5l1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm-74.1 7l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7H65zm16.4 0l1 2.8H86l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm-74 7l.8 2.8h3l-2.4 1.7.9 2.7-2.4-1.7L6 38.2l.9-2.7-2.4-1.7h3zm16.4 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8H45l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9L92 35.5l1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm-74.1 7l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7H65zm16.4 0l1 2.8H86l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm-74 7l.8 2.8h3l-2.4 1.7.9 2.7-2.4-1.7L6 52.2l.9-2.7-2.4-1.7h3zm16.4 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8H45l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9L92 49.5l1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm-74.1 7l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7H65zm16.4 0l1 2.8H86l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm-74 7l.8 2.8h3l-2.4 1.7.9 2.7-2.4-1.7L6 66.2l.9-2.7-2.4-1.7h3zm16.4 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8H45l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9L92 63.5l1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9z"
                                                        transform="scale(3.9385)" />
                                                </g>
                                            </svg>
                                            United States
                                        </div>
                                    </button>
                                </li>
                                <li>
                                    <button type="button" class="inline-flex w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">
                                        <div class="inline-flex items-center">
                                            <svg aria-hidden="true" class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-de" viewBox="0 0 512 512">
                                                <path fill="#ffce00" d="M0 341.3h512V512H0z" />
                                                <path d="M0 0h512v170.7H0z" />
                                                <path fill="#d00" d="M0 170.7h512v170.6H0z" />
                                            </svg>
                                            Germany
                                        </div>
                                    </button>
                                </li>
                                <li>
                                    <button type="button" class="inline-flex w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
                                        <div class="inline-flex items-center">
                                            <svg aria-hidden="true" class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-it" viewBox="0 0 512 512">
                                                <g fill-rule="evenodd" stroke-width="1pt">
                                                    <path fill="#fff" d="M0 0h512v512H0z" />
                                                    <path fill="#009246" d="M0 0h170.7v512H0z" />
                                                    <path fill="#ce2b37" d="M341.3 0H512v512H341.3z" />
                                                </g>
                                            </svg>
                                            Italy
                                        </div>
                                    </button>
                                </li>
                                <li>
                                    <button type="button" class="inline-flex w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
                                        <div class="inline-flex items-center">
                                            <svg aria-hidden="true" class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="flag-icon-css-cn" viewBox="0 0 512 512">
                                                <defs>
                                                    <path id="a" fill="#ffde00" d="M1-.3L-.7.8 0-1 .6.8-1-.3z" />
                                                </defs>
                                                <path fill="#de2910" d="M0 0h512v512H0z" />
                                                <use width="30" height="20" transform="matrix(76.8 0 0 76.8 128 128)" xlink:href="#a" />
                                                <use width="30" height="20" transform="rotate(-121 142.6 -47) scale(25.5827)" xlink:href="#a" />
                                                <use width="30" height="20" transform="rotate(-98.1 198 -82) scale(25.6)" xlink:href="#a" />
                                                <use width="30" height="20" transform="rotate(-74 272.4 -114) scale(25.6137)" xlink:href="#a" />
                                                <use width="30" height="20" transform="matrix(16 -19.968 19.968 16 256 230.4)" xlink:href="#a" />
                                            </svg>
                                            China
                                        </div>
                                    </button>
                                </li>
                            </ul>
                        </div>
                        <div class="relative w-full group">
                            <input v-model="form.phone" id="location-search"
                                class="peer block p-2.5 w-full z-20 text-sm text-gray-900 rounded-r-lg border-l-gray-50 border-l-2 border border-gray-300 hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen border-2 border-kb-light-grey w-56 md:w-72"
                                placeholder="" required v-maska data-maska="(5##) ### ## ##" />
                            <!--                            <button type="submit" class="absolute top-0 right-0 p-2.5 text-sm font-medium text-white bg-blue-700 rounded-r-lg border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">-->
                            <!--                                <svg aria-hidden="true" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>-->
                            <!--                                <span class="sr-only">Search</span>-->
                            <!--                            </button>-->
                            <label for="location-search"
                                class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Telefon*</label>
                        </div>
                    </div>
                </div>
                <div class="self-center mt-5 ts:mt-6 group relative">
                    <input id="pass" :type="form.passwordFieldType" autocomplete="current-password" v-model="form.password" name="password" placeholder="Şifre*"
                        class="border-2 placeholder-transparent rounded-lg border-kb-light-grey w-80 md:w-96 hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white peer" required />
                    <!--                    <label
                                            for="pass"
                                            class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                            >Şifre*</label
                                        >-->
                    <label for="password"
                        class="absolute left-4 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">Şifre*</label>
                    <div @click="switchVisibility" class="absolute mt-3 top-0 right-5">
                        <svg id="eye-slash" xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 17.749 15">
                            <path id="Path_2948" data-name="Path 2948"
                                d="M20.762,15.939a16.033,16.033,0,0,0,2.93-3.822s-3.328-6.492-8.874-6.492a7.4,7.4,0,0,0-3.095.694l.854.91a6.24,6.24,0,0,1,2.241-.424,8.088,8.088,0,0,1,5.733,2.9,15.359,15.359,0,0,1,1.841,2.412c-.065.1-.135.216-.216.34a15.4,15.4,0,0,1-1.625,2.072c-.183.195-.374.387-.573.573Z"
                                transform="translate(-5.943 -4.617)" fill="#231f20" />
                            <path id="Path_2949" data-name="Path 2949"
                                d="M17.661,15.641a4.323,4.323,0,0,0-.91-4.312A3.717,3.717,0,0,0,12.7,10.36l.913.972a2.672,2.672,0,0,1,2.354.835,3.069,3.069,0,0,1,.784,2.5l.912.97ZM14.4,17.175l.912.97a3.717,3.717,0,0,1-4.053-.969,4.323,4.323,0,0,1-.91-4.312l.913.972a3.069,3.069,0,0,0,.784,2.5,2.672,2.672,0,0,0,2.354.835Z"
                                transform="translate(-5.13 -6.753)" fill="#231f20" />
                            <path id="Path_2950" data-name="Path 2950"
                                d="M3.716,11.55q-.3.283-.574.575A15.359,15.359,0,0,0,1.3,14.537l.216.34a15.4,15.4,0,0,0,1.625,2.072,8.09,8.09,0,0,0,5.733,2.9,6.219,6.219,0,0,0,2.241-.425l.854.911a7.4,7.4,0,0,1-3.095.694C3.328,21.029,0,14.537,0,14.537a15.978,15.978,0,0,1,2.93-3.822l.785.837Z"
                                transform="translate(0 -7.036)" fill="#231f20" />
                            <path id="Path_2951" data-name="Path 2951" d="M17.015,18.7,3.7,4.539,4.489,3.7,17.8,17.867Z" transform="translate(-1.878 -3.703)" fill="#231f20" fill-rule="evenodd" />
                        </svg>
                    </div>
                </div>
                <div class="self-center mt-5 ts:mt-6 relative group">
                    <input id="password2" :type="form.passwordFieldType2" v-model="form.password2" @blur="checkPasswordValidity" name="password2" placeholder="Şifre Tekrar*"
                        class="peer placeholder-transparent border-2 rounded-lg border-kb-light-grey w-80 md:w-96 hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" required />
                    <!--                    <label
                                            for="password2"
                                            class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                            >Şifre Tekrar*</label
                                        >-->
                    <label for="password2"
                        class="absolute left-4 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">Şifre
                        Tekrar*</label>
                    <div @click="switchVisibility2" class="absolute mt-3 top-0 right-5">
                        <svg id="eye-slash" xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 17.749 15">
                            <path id="Path_2948" data-name="Path 2948"
                                d="M20.762,15.939a16.033,16.033,0,0,0,2.93-3.822s-3.328-6.492-8.874-6.492a7.4,7.4,0,0,0-3.095.694l.854.91a6.24,6.24,0,0,1,2.241-.424,8.088,8.088,0,0,1,5.733,2.9,15.359,15.359,0,0,1,1.841,2.412c-.065.1-.135.216-.216.34a15.4,15.4,0,0,1-1.625,2.072c-.183.195-.374.387-.573.573Z"
                                transform="translate(-5.943 -4.617)" fill="#231f20" />
                            <path id="Path_2949" data-name="Path 2949"
                                d="M17.661,15.641a4.323,4.323,0,0,0-.91-4.312A3.717,3.717,0,0,0,12.7,10.36l.913.972a2.672,2.672,0,0,1,2.354.835,3.069,3.069,0,0,1,.784,2.5l.912.97ZM14.4,17.175l.912.97a3.717,3.717,0,0,1-4.053-.969,4.323,4.323,0,0,1-.91-4.312l.913.972a3.069,3.069,0,0,0,.784,2.5,2.672,2.672,0,0,0,2.354.835Z"
                                transform="translate(-5.13 -6.753)" fill="#231f20" />
                            <path id="Path_2950" data-name="Path 2950"
                                d="M3.716,11.55q-.3.283-.574.575A15.359,15.359,0,0,0,1.3,14.537l.216.34a15.4,15.4,0,0,0,1.625,2.072,8.09,8.09,0,0,0,5.733,2.9,6.219,6.219,0,0,0,2.241-.425l.854.911a7.4,7.4,0,0,1-3.095.694C3.328,21.029,0,14.537,0,14.537a15.978,15.978,0,0,1,2.93-3.822l.785.837Z"
                                transform="translate(0 -7.036)" fill="#231f20" />
                            <path id="Path_2951" data-name="Path 2951" d="M17.015,18.7,3.7,4.539,4.489,3.7,17.8,17.867Z" transform="translate(-1.878 -3.703)" fill="#231f20" fill-rule="evenodd" />
                        </svg>
                    </div>
                </div>
                <!--                <div class="text-xs text-kbred mx-auto text-center md:text-left font-light mt-3">Şifren en az 8 karakter, bir büyük harf, bir küçük harf ve rakam içermelidir.</div>-->

                <div class="flex mt-4 w-80 md:w-96 self-center justify-between">
                    <label for="art" class="cursor-pointer flex">
                        <input type="checkbox" id="art" class="border-2 border-black rounded-md outline-none" required />
                        <a href="/aydinlatma-metni" target="_blank" class="text-xs ml-3"><b>Aydınlatma Metnini</b>
                            okudum ve <b>Açık Rıza Beyanına</b> onay veriyorum.</a>
                    </label>
                </div>
                <div class="flex mt-4 w-80 md:w-96 self-center justify-between">
                    <label for="us" class="cursor-pointer">
                        <input type="checkbox" id="us" class="border-2 border-black rounded-md outline-none" required />
                        <a href="/uyelik-sozlesmesi" target="_blank" class="text-xs ml-3"><b>Üyelik Sözleşmesini</b>
                            okudum ve kabul ediyorum.</a>
                    </label>
                </div>

                <div class="flex mt-4 w-80 md:w-96 self-center flex-col" v-if="Object.entries(errors).length > 0">
                    <div class="text-xs mx-auto text-kbred my-1" v-for="error in errors">{{ error[0] }}</div>
                </div>

                <div class="flex mt-4 w-80 md:w-96 self-center">
                    <button class="bg-black text-white rounded-full py-2 px-4 self-center font-bold w-full hover:bg-kbgreen" type="submit">Üye Ol
                    </button>
                </div>

                <div class="flex w-80 md:w-96 self-center mt-4 flex-col">
                    <div class="text-xs mx-auto"><b>Kiralabunu.com</b>'a üye misin?</div>
                    <div class="mx-auto">
                        <Link href="/hesabim" class="font-semibold text-md text-kb-light-blue hover:text-kbgreen">Giriş
                        Yap
                        </Link>
                    </div>
                </div>
            </form>
        </section>
    </main>
</template>
