<template>

    <TransitionRoot :show="show" as="template">
        <Dialog @close="show = false">
            <!-- Wrap your backdrop in a `TransitionChild`. -->
            <TransitionChild
                enter="duration-300 ease-out"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100"
                leave-to="opacity-0"
            >
                <div class="fixed inset-0 bg-black/30" />
            </TransitionChild>

            <!-- Wrap your panel in a `TransitionChild`. -->
            <TransitionChild
                enter="duration-300 ease-out"
                enter-from="opacity-0 scale-95"
                enter-to="opacity-100 scale-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100 scale-100"
                leave-to="opacity-0 scale-95"
            >
                <DialogPanel>
                    <div class="w-full md:w-[320px] lg:w-[450px] shadow-searchshadow absolute z-70 right-0 bottom-0 md:bottom-auto md:top-3 rounded-t-2lg overflow-hidden checkoutaddpopup bg-white md:h-full">
                        <div
                            class="bg-white flex items-center border-b-2 border-kbblue">
                            <div class="pl-5 bg-white flex justify-center items-center h-[60px] rounded-2lg">
                                <svg xmlns="http://www.w3.org/2000/svg" width="35" height="32" viewBox="0 0 44.079 40.019">
                                    <g id="Group_4963" data-name="Group 4963" transform="translate(-1418.489 -266.332)">
                                        <g id="Group_4103" data-name="Group 4103" transform="translate(1420 267.832)">
                                            <g id="Group_3491" data-name="Group 3491" transform="translate(0 0)">
                                                <path
                                                    id="Path_2958"
                                                    data-name="Path 2958"
                                                    d="M216.467,221.826H200.548c-2.615,0-4.822-1.312-5.155-3.064L192.4,203.011c-.4-2.1,2.022-3.957,5.155-3.957h21.9c3.133,0,5.554,1.858,5.156,3.957l-2.993,15.751C221.289,220.515,219.081,221.826,216.467,221.826Z"
                                                    transform="translate(-192.356 -188.423)"
                                                    fill="none"
                                                    stroke="#5F4AF4"
                                                    stroke-miterlimit="10"
                                                    stroke-width="3"
                                                />
                                                <path id="Path_2959" data-name="Path 2959"
                                                      d="M202.477,193.839l6.113-9.337c1.114-1.7,2.683-1.727,3.811-.061l6.366,9.4"
                                                      transform="translate(-194.291 -183.208)" fill="none" stroke="#5F4AF4"
                                                      stroke-miterlimit="10" stroke-width="3" />
                                            </g>
                                        </g>
                                        <g id="Group_4105" data-name="Group 4105" transform="translate(1436.15 279.934)">
                                            <circle id="Ellipse_287" data-name="Ellipse 287" cx="10" cy="10" r="10"
                                                    transform="translate(3.138 3.181)" fill="#5f4af4" />
                                            <g id="check-circle-fill" transform="translate(0 0)">
                                                <path
                                                    id="Path_173"
                                                    data-name="Path 173"
                                                    d="M26.417,13.209A13.209,13.209,0,1,1,13.209,0,13.209,13.209,0,0,1,26.417,13.209Zm-6.555-5a1.239,1.239,0,0,0-1.783.036l-5.734,7.306L8.889,12.091a1.239,1.239,0,0,0-1.75,1.75l4.368,4.37a1.239,1.239,0,0,0,1.782-.033l6.591-8.239A1.239,1.239,0,0,0,19.864,8.2Z"
                                                    fill="#70d44b"
                                                    fill-rule="evenodd"
                                                />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="w-full h-[60px] flex items-center justify-between px-1 md:px-4">
                                <div class="text-sm md:text-base font-bold whitespace-nowrap text-kbblue">Ürün Sepete Eklendi</div>
                                <button @click="show = false">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20.887" height="20.887" viewBox="0 0 20.887 20.887" class="fill-kbred hover:stroke-kbred hover:fill-white transition-all duration-300">
                                        <path
                                            id="close-filled"
                                            d="M12.693,2.25A10.379,10.379,0,0,0,2.25,12.693,10.379,10.379,0,0,0,12.693,23.137,10.379,10.379,0,0,0,23.137,12.693,10.379,10.379,0,0,0,12.693,2.25Zm4.028,15.665-4.028-4.028L8.665,17.915,7.472,16.722,11.5,12.693,7.472,8.665,8.665,7.472,12.693,11.5l4.028-4.028,1.194,1.194-4.028,4.028,4.028,4.028Z"
                                            transform="translate(-2.25 -2.25)"
                                            fill=""
                                            stroke-width="2px"
                                        />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="flex flex-wrap lg:flex-wrap relative p-3">
                            <template v-if="cart">
                                <div class="w-full flex items-center flex-wrap border-2 border-bordergray rounded-lg p-1 lg:p-2 mb-5" v-for="product in cart" :key="product.id">
                                    <div class="w-2/12 md:w-2/12 lg:w-2/12 flex items-center">
                                        <div class="flex justify-center items-center w-full bg-white">
                                            <picture class="">
                                                <source :srcset="product.product.imagesWebP?.thumb_webp" type="image/webp" />
                                                <source :srcset="product.product.imagesWebP?.zoom_webp" type="image/jpeg" />
                                                <img :src="product.product.imagesWebP?.thumb" alt="Sepet" />
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="w-9/12 md:w-9/12 flex flex-wrap">
                                        <div class="w-full lg:w-full">
                                            <div class="flex flex-wrap justify-between w-full items-start">
                                                <div class="w-full text-xs ts:text-sm text-black font-santralregular font-bold" v-html="product.product.attribute_data.name.tr"></div>
                                                <div class="w-full ">
                                                    <span class="ts:text-xl text-black font-bold font-santralextrabold leading-none" v-html="product.total + ' / '"> </span>
                                                    <span class="font-kiralabunuthin text-xs lg:text-sm text-black font-santralregular ts:pb-2 -mt-1"> Aylık Tutar</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="max-w-[25px] w-full">
                                        <img src="../../../images/svg/rounded-check.svg" class="w-full" alt="" />
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div class="px-1 w-full mb-3">
                            <a href="/sepetim" class="w-full block text-white rounded-full py-3 px-4 self-center font-semibold text-lg leading-none text-center bg-kbgreen ">Sepete Git</a>
                        </div>
                        <div class="h-full overflow-y-scroll">
                            <div class="hidden lg:flex flex-wrap pl-3 pr-2">
                                <div class="w-full my-3 text-lg font-bold font-santralextrabold"> Beğeneceğin Ürünler</div>
                                <div class="w-full mts:w-6/12 lg:w-4/12" v-for=" mostRentedProductItem in mostRentedProductsData.items.data" :key="mostRentedProductItem.id">
                                    <div class="flex flex-wrap bg-white rounded-lg mts:mx-1 border-2 border-bordergray hover:border-transparent hover:shadow-searchshadow my-2">
                                        <div class="w-full mx-auto">
                                            <a :href="'/urun/'+mostRentedProductItem.default_url.slug">
                                                <img :src="mostRentedProductItem.imagesOptimized[0]?.urlThumbWebP" alt="Alt Text!">
                                            </a>
                                        </div>
                                        <div class="w-full px-1">
                                            <div class="block text-xs text-center mt-1">
                                                <a class="text-kbgreen font-semibold" :href="'/marka/'+mostRentedProductItem.brand.name.toLowerCase()">{{ mostRentedProductItem.brand.name }}</a>
                                            </div>
                                            <div class="text-xs font-semibold mt-1 relative">
                                                <h4 class="min-h-[32px] max-h-[32px] overflow-y-hidden">
                                                    <a class="short-description" :href="'/urun/'+mostRentedProductItem.default_url.slug">
                                                        {{ mostRentedProductItem.attribute_data.name.tr }}
                                                    </a>
                                                </h4>
                                            </div>
                                        </div>
                                        <div class="flex flex-col mb-3 mt-1 justify-start w-full tracking-tighter px-2">
                                            <span class="font-semibold text-sm leading-none">
                                                <a :href="'/urun/'+mostRentedProductItem.default_url.slug"> 255 TL / </a>
                                            </span>
                                            <span class="text-2xs leading-none font-santralregular text-checkoutgray pt-1">
                                                <a :href="'/urun/'+mostRentedProductItem.default_url.slug">Aylık Ödenecek Tutar</a>
                                            </span>
                                        </div>
                                        <div class="px-1 w-full mb-2">
                                            <a :href="'/urun/'+mostRentedProductItem.default_url.slug" class="w-full block text-white rounded-full py-2 px-3 self-center font-santralregular font-bold text-xs leading-none text-center bg-kbgreen cursor-pointer">Kirala </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </DialogPanel>
            </TransitionChild>
        </Dialog>
    </TransitionRoot>
</template>

<script>
import { Link } from "@inertiajs/inertia-vue3";
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from "@headlessui/vue";
import { Inertia } from "@inertiajs/inertia";
import axios from "axios";

export default {
    components: {
        Link,
        Dialog,
        DialogPanel,
        DialogTitle,
        TransitionChild,
        TransitionRoot
    },

    props: {
        show: {
            type: Boolean,
            required: true,
            default: false
        },
        cart: {
            type: Array,
            required: true,
            default: []
        }
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            this.show = false;
        },
        splidedArray(arr, size) {
            return arr.reduce((acc, e, i) => (i % size ? acc[acc.length - 1].push(e) : acc.push([e]), acc), []);
        }
    },
    data() {
        return {
            isOpen: this.show,
            mostRentedProductsData: null
        };
    },
    watch: {
        show: function(val) {
            this.isOpen = val;
        }
    },
    created() {
        // API'den mostRentedProducts verilerini al
        axios.get("/get-mostrentedproducts")
            .then(response => {
                this.mostRentedProductsData = response.data;

                if (this.mostRentedProductsData && this.mostRentedProductsData.items && this.mostRentedProductsData.items.data) {
                    let mostRentedProducts = this.mostRentedProductsData.items.data;
                    let mostRentedProductsDL = [];
                    mostRentedProducts.map((item, keys) => {
                        let product = {};
                        let subscribetionMonthsOrdered = [];
                        if (item.variants[0]?.prices.length > 0) subscribetionMonthsOrdered = Object.entries(item.variants[0]?.prices).sort(([, a], [, b]) => a.subscription_months.value - b.subscription_months.value);
                        let nonZeroPrices = item.variants[0]?.prices.filter((price) => price.price.value > 0);
                        subscribetionMonthsOrdered = subscribetionMonthsOrdered.filter((price) => price[1].price.value > 0);
                        let productPrice = subscribetionMonthsOrdered[+Object.keys(nonZeroPrices).length - 1]?.[1]?.price?.value / 100;

                        product.item_id = item.id;
                        product.item_name = item.attribute_data.name.tr;
                        product.price = productPrice;
                        product.item_brand = item.brand.name;
                        product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                        product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                        product.item_list_id = "HOME-ECKU";
                        product.item_list_name = "Homepage - En Çok Kiralanan Ürünler List";
                        product.index = keys;
                        mostRentedProductsDL.push(product);
                    });
                }
            })
            .catch(error => {
                console.error("Error fetching mostRentedProducts:", error);
            });
    }
};
</script>

<style scoped></style>

