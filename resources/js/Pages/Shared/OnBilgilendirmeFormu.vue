<template>
    <div class="font-santralregular font-light">
        <ol>
            <li><b class="!text-base font-bold">1. KONU</b></li>
        </ol>
        <p>
            <span>İşbu Kiralama Sözleşmesi Ön Bilgi Formu’nun konusu, Kiralabunu Elektronik Pazarlama ve Ticaret Anonim Şirketi’ne (“</span><b>Kiralabunu</b><span>”) ait </span><a href="http://www.kiralabunu.com/"><span>www.kiralabunu.com</span></a
        ><span>
            web sitesinden Kullanıcı’ya kiralamasını yaptığı, aşağıda nitelikleri ve kiralama fiyatı belirtilen ürün/ürünlerin kiralanması ve teslimi ile ilgili olarak 6502 sayılı Tüketicilerin Korunması Hakkındaki Kanun ve 29188 sayılı Mesafeli Sözleşmeler Yönetmeliği hükümleri gereğince tarafların hak ve yükümlülüklerini kapsamaktadır. Ön
            Bilgilendirme Formu, ticari veya mesleki olmayan amaçlarla hareket eden gerçek kişi tüketicilere yönelik hazırlanmıştır ve bunlar açısından bağlayıcıdır.&nbsp;</span
        >
        </p>
        <p>
            <span>İşbu Ön Bilgilendirme Formu’nu kabul etmekle Kullanıcı, sözleşme konusu siparişin Kiralabunu tarafından onaylanması durumunda, sipariş konusunun bedeli ve varsa kargo ücreti, vergi gibi belirtilen ek ücretleri ödeme yükümlülüğü altına gireceğini ve bu konuda bilgilendirildiğini peşinen kabul eder.</span>
        </p>
        <p>&nbsp;</p>
        <ol start="2">
            <li><b class="!text-base font-bold">2. KİRALABUNU İLETİŞİM BİLGİLERİ</b></li>
        </ol>
        <table>
            <tbody>
            <tr>
                <td><b class=" font-bold">Unvan</b></td>
                <td><b class=" font-bold">:</b></td>
                <td><span>Kiralabunu Elektronik Pazarlama ve Ticaret Anonim Şirketi</span></td>
            </tr>
            <tr>
                <td><b class=" font-bold">MERSİS Numarası</b></td>
                <td><b class=" font-bold">:</b></td>
                <td><span>563127078200001</span></td>
            </tr>
            <tr>
                <td><b class=" font-bold">Adres</b></td>
                <td><b class=" font-bold">:</b></td>
                <td><span>Gülbahar Mahallesi Avni Dilligil Sokak Çelik İş Merkezi Sitesi B Blok No:9 İç Kapı No: 15 Şişli / İstanbul</span></td>
            </tr>
            <tr>
                <td><b class=" font-bold">E-Posta</b></td>
                <td><b class=" font-bold">:</b></td>
                <td><span><EMAIL></span></td>
            </tr>
            </tbody>
        </table>
        <p><span>&nbsp;</span></p>
        <ol start="3">
            <li><b class="!text-base font-bold">3. SÖZLEŞME KONUSU ÜRÜN BİLGİLERİ</b></li>
        </ol>
        <p>
            <b class=" font-bold">3.1.&nbsp;</b
            ><span>Malın / Ürün/ Ürünlerin / Hizmetin temel özellikleri (türü, miktarı, marka/modeli, rengi, adedi, fiyat) Kiralabunu’ya ait internet sitesinde yer almaktadır. Ürünün temel özelliklerini kampanya süresince inceleyebilirsiniz. Kampanya dönemlerindeki ücretler yalnızca kampanya tarihine kadar geçerlidir.</span>
        </p>
        <p><b class=" font-bold">3.2.&nbsp;</b><span>Listelenen ve sitede ilan edilen fiyatlar, aylık kiralama tutarıdır. İlan edilen fiyatlar ve vaatler güncelleme yapılana ve değiştirilene kadar geçerlidir. Süreli olarak ilan edilen fiyatlar ise belirtilen süre sonuna kadar geçerlidir. Fiyatlara, katma değer vergisi dahildir.&nbsp;</span></p>
        <p><b class=" font-bold">3.3.&nbsp;</b><span>Sözleşme konusu mal ya da hizmetin katma değer vergisi dâhil kiralama fiyatı aşağıdaki tabloda gösterilmiştir.</span></p>
        <table class="productSummaryTable w-full my-4">
            <thead>
            <tr>
                <th class="border px-2">Ürün Adı</th>
                <th class="border px-2">Adet</th>
                <th class="border px-2">KDV Dahil Aylık Kira Bedeli</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="product in products">
                <td class="border px-2">{{ product.product.attribute_data.name.tr }}</td>
                <td class="border px-2 text-center">{{ product.quantity }}</td>
                <td class="border px-4" align="right">
                    <span class="woocommerce-Price-amount amount"><span class="woocommerce-Price-currencySymbol">₺</span>{{ product.total }}</span>
                </td>
            </tr>
            </tbody>
        </table>
        <ol start="4">
            <li><b class="!text-base font-bold">4. GENEL HÜKÜMLER</b></li>
        </ol>
        <p><b class=" font-bold">4.1.&nbsp;</b><span>Kullanıcı, Kiralabunu’ya ait internet sitesinde sözleşme konusu ürünün temel nitelikleri, kiralama fiyatı ve ödeme şekli ile teslimata ilişkin ön bilgileri okuyup, bilgi sahibi olduğunu, elektronik ortamda gerekli teyidi verdiğini kabul, beyan ve taahhüt eder.</span></p>
        <p>
            <b class=" font-bold">4.2.&nbsp;</b
            ><span
        >Kullanıcı, Ön Bilgilendirme Formunu elektronik ortamda teyit etmesi, mesafeli kiralama sözleşmesinin kurulmasından evvel, Kiralabunu tarafından siparişi verilen ürünlere ait temel özellikleri, ürünlerin vergiler dâhil fiyatını, ödeme ve teslimat bilgilerini ve masraflarını doğru ve eksiksiz olarak bilgi edindiğini kabul, beyan ve
            taahhüt eder.</span
        >
        </p>
        <p>
            <b class=" font-bold">4.3.&nbsp;&nbsp;</b
            ><span
        >Kiralabunu, sipariş konusu ürün veya hizmetin yerine getirilmesinin imkânsızlaşması halinde sözleşme konusu yükümlülüklerini yerine getiremezse, bu durumu, öğrendiği tarihten itibaren üç (3) gün içinde yazılı olarak tüketiciye bildireceğini, on dört (14) günlük süre içinde toplam bedeli Kullanıcı’ya iade edeceğini kabul, beyan ve
            taahhüt eder.</span
        ><span><br /> </span><b class=" font-bold">4.4.&nbsp;&nbsp;</b
        ><span
        >Kullanıcı, sözleşme konusu ürünün teslimatı için işbu Ön Bilgilendirme Formu’nu elektronik ortamda teyit edeceğini, herhangi bir nedenle sözleşme konusu ürün bedelinin ödenmemesi ve/veya banka kayıtlarında iptal edilmesi halinde, Kiralabunu’nun sözleşme konusu ürünü teslim yükümlülüğünün sona ereceğini kabul, beyan ve taahhüt
            eder.</span
        >
        </p>
        <p>
            <b class=" font-bold">4.5.&nbsp;</b
            ><span
        >Kiralabunu tarafından internet sitesinde sunulan ürünler Kullanıcı’ya yönelik icaba daveti Kullanıcı tarafından siparişin oluşturulması icap, Kiralabunu tarafından siparişin onaylanması kabul niteliğindedir. Sözleşmesi’nin kurulması için Kullanıcı tarafından sipariş verilmesi ve siparişin Kiralabunu tarafından onaylanması
            gerekmektedir.</span
        >
        </p>
        <p>&nbsp;</p>
        <ol start="5">
            <li><b class="!text-base font-bold">5. TESLİMAT</b></li>
        </ol>
        <p><span>Kiralabunu tarafından siparişin onaylanmasını takip eden beş (5) iş günü içerisinde Ürün / Ürünler Kullanıcı’ya ulaştırılmak üzere anlaşmalı kargo veya kurye firmasına teslim edilir.&nbsp;</span></p>
        <p>
        <span
        >Ürün / Ürünler, yalnızca Kullanıcı tarafından teslim alınabilir. Teslimat, Kullanıcı tarafından bildirilen adrese göre, anlaşmalı kurye firmaları tarafından bildirilen adrese teslim edilecek veya anlaşmalı kargo firmasının en yakın şubesinde Kullanıcı’ya teslim edilecektir. Ürün / Ürünler, yalnızca Kullanıcı’ya kimlik ibrazı
            karşılığında teslim edilecektir. Kimlik olarak geçerli evraklar nüfus cüzdanı, ehliyet veya pasaport belgelerinden biridir. Diğer kimlikler kurye veya kargo firması tarafından teslim sırasında kabul edilmeyebilir.&nbsp;</span
        >
        </p>
        <p><span>Ürün / Ürünler, kullanıcıya hasarsız, temiz ve kullanılabilir şekilde gönderilir. Sipariş sırasında açıkça belirtilmediği hallerde ürün / ürünler aksesuarsız olarak gönderilir.&nbsp;</span></p>
        <p>&nbsp;</p>
        <ol start="6">
            <li><b class="!text-base font-bold"> CAYMA HAKKI</b></li>
        </ol>
        <p><b class=" font-bold">6.1.&nbsp;</b><span>Kullanıcı, cayma hakkı bulunan hallerde, ürün / ürünlerin teslim alındığı gün başlayacak şekilde on dört (14) gün içinde herhangi bir gerekçe göstermeksizin ve cezai şart ödemeksizin sözleşmeden cayma hakkına sahiptir. Cayma hakkı süresinin belirlenmesinde;&nbsp;</span></p>
        <ul>
            <li><span>Tek sipariş konusu olup ayrı ayrı teslim edilen mallarda son malın teslim alındığı gün,&nbsp;</span></li>
            <li><span>Birden fazla parçadan oluşan mallarda son parçanın teslim aldığı gün,</span></li>
            <li><span>Belirli bir süre boyunca malın düzenli tesliminin yapıldığı sözleşmelerde ilk malın teslim alındığı gün esas alınır.</span></li>
        </ul>
        <p><span>Kullanıcı aşağıdaki sözleşmelerde cayma hakkını kullanamaz:&nbsp;</span></p>
        <ul>
            <li><span>Fiyatı finansal piyasalardaki dalgalanmalara bağlı olarak değişen ve satıcı veya sağlayıcının kontrolünde olmayan mal veya hizmetlere ilişkin sözleşmeler,&nbsp;</span></li>
            <li><span>Tüketicinin istekleri veya kişisel ihtiyaçları doğrultusunda hazırlanan mallara ilişkin sözleşmeler,&nbsp;</span></li>
            <li><span>Çabuk bozulabilen veya son kullanma tarihi geçebilecek malların teslimine ilişkin sözleşmeler,&nbsp;</span></li>
            <li><span>Tesliminden sonra ambalaj, bant, mühür, paket gibi koruyucu unsurları açılmış olan mallardan; iadesi sağlık ve hijyen açısından uygun olmayanların teslimine ilişkin sözleşmeler.&nbsp;</span></li>
            <li><span>Tesliminden sonra başka ürünlerle karışan ve doğası gereği ayrıştırılması mümkün olmayan mallara ilişkin sözleşmeler,&nbsp;</span></li>
            <li><span>Malın tesliminden sonra ambalaj, bant, mühür, paket gibi koruyucu unsurları açılmış olması halinde maddi ortamda sunulan kitap, dijital içerik ve bilgisayar sarf malzemelerine ilişkin sözleşmeler,&nbsp;</span></li>
            <li><span>Abonelik sözleşmesi kapsamında sağlananlar dışında, gazete ve dergi gibi süreli yayınların teslimine ilişkin sözleşmeler,&nbsp;</span></li>
            <li><span>Belirli bir tarihte veya dönemde yapılması gereken, konaklama, eşya taşıma, araba kiralama, yiyecek-içecek tedariki ve eğlence veya dinlenme amacıyla yapılan boş zamanın değerlendirilmesine ilişkin sözleşmeler,&nbsp;</span></li>
            <li><span>Elektronik ortamda anında ifa edilen hizmetler veya tüketiciye anında teslim edilen gayri maddi mallara ilişkin sözleşmeler,&nbsp;</span></li>
            <li><span>Cayma hakkı süresi sona ermeden önce, tüketicinin onayı ile ifasına başlanan hizmetlere ilişkin sözleşmeler.</span></li>
        </ul>
        <p>
            <b class=" font-bold">6.2.&nbsp;</b><span>Cayma hakkı, Kullanıcı’nın tek taraflı bildirimi ile gerçekleşir. Cayma hakkının bu süre içerisinde Kiralabunu’ya yöneltilmiş olması gerekir. Cayma hakkı bildirimi, (bu linkte bulunan) cayma formu doldurulup imzalandıktan sonra&nbsp;&nbsp;</span
        ><a href="mailto:<EMAIL>"><span><EMAIL></span></a
        ><span>&nbsp;adresine e-posta atılarak yapılır.&nbsp;</span>
        </p>
        <p>
            <b class=" font-bold">6.3.&nbsp;</b
            ><span
        >Kullanıcı, cayma hakkını kullandığına ilişkin bildirimi yönelttiği tarihten itibaren on (10) gün içinde caymaya konusu ürün / ürünleri Kiralabunu’ya geri göndermek zorundadır. Caymaya konu ürün / ürünler, Kiralabunu tarafından belirtilen kargo şirketi
            vasıtasıyla Kiralabunu tarafından verilen posta adresine karşı ödemeli olarak gönderilecektir. Bilgilendirmeler, e-posta üzerinden yapılacaktır.&nbsp;</span
        >
        </p>
        <p>
            <b class=" font-bold">6.4.&nbsp;</b
            ><span
        >Kullanıcı’nın, cayma hakkı bulunduğu hallerde, ürün / ürünlerin kullanım amacına uygun, titiz bir şekilde, işleyişine, teknik özelliklerine ve kullanım talimatlarına uygun bir olarak kullanılması gerekir. Sözleşme hükümlerine uygun olağan kullanım dışında meydana gelen hasarlar ve eksilmeler Sözleşme’de yer alan tamirat hükümlerine
            göre işleme alınacaktır.</span
        >
        </p>
        <p><b class=" font-bold">6.5.&nbsp;</b><span>Kullanıcı’nın, ürün / ürünleri teslim aldıktan sonra cayma iradesini ortaya koyması durumunda, Kullanıcıya ürün / ürünlerin teslim tarihi ile Kiralabunu’ya iade tarihi arasında geçen süre, Sözleşme’de belirlenen aylık kira tutarı doğrultusunda ücretlendirilir.&nbsp;</span></p>
        <p><b class=" font-bold">6.6.&nbsp;</b><span>İade işleminde Ürün'ün kutusu, ambalajı, varsa aksesuarları ile birlikte eksiksiz ve hasarsız olarak teslim edilmesi gerekmektedir.</span></p>
        <p>&nbsp;</p>
        <p><b class="!text-base font-bold">7.UYUŞMAZLIK ÇÖZÜMÜ</b></p>
        <p>
        <span
        >Kullanıcı’nın ticari veya mesleki olmayan amaçlarla hareket eden gerçek kişi tüketici olması durumunda, Sözleşmeden doğan uyuşmazlıklarda şikâyet ve itirazlar, aşağıdaki kanunda belirtilen parasal sınırlar dâhilinde tüketicinin yerleşim yerinin bulunduğu veya tüketici işleminin yapıldığı yerdeki Tüketici Sorunları Hakem Heyeti’ne veya
            Tüketici Mahkemesi’ne yapılacaktır. 01/01/2024 tarihinden itibaren geçerli olmak üzere, 2024 yılı için yapılacak başvurularda değeri 104.000 ( yüz dört bin) Türk Lirasının altında bulunan uyuşmazlıklarda, İl veya İlçe Tüketici Hakem Heyetleri görevlidir. Kullanıcı’nın ticari veya mesleki amaçlarla hareket eden gerçek kişi veya tüzel
            kişi olması durumunda, Sözleşmeden doğan uyuşmazlıklarda İstanbul Adliyesi mahkemeleri ve icra daireleri yetkilidir.&nbsp;&nbsp;</span
        >
        </p>
        <p>&nbsp;</p>
        <table>
            <tbody>
            <tr>
                <td><b class=" font-bold">KİRALABUNU</b></td>
                <td><span>:</span></td>
                <td></td>
            </tr>
            <tr>
                <td><b class=" font-bold">KULLANICI</b></td>
                <td>
                    <span>: {{ user.full_name }}</span>
                </td>
                <td></td>
            </tr>
            <tr>
                <td><b class=" font-bold">TARİH</b></td>
                <td><span>:</span></td>
                <td>
                    <span>&nbsp;</span>
                    <p></p>
                    <p><span>&nbsp;</span></p>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
export default {
    name: "OnBilgilendirmeFormu",
    props: {
        products: Array,
        user: Object
    }
};
</script>

<style scoped></style>
