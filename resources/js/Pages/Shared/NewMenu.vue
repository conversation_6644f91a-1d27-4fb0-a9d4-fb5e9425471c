<template>
    <div class="bg-white block ts:hidden">
        <!-- Mobile menu -->
        <TransitionRoot as="template" :show="mobileMenuIsOpen">
            <Dialog as="div" class="relative z-80 lg:hidden" @close="closeAfterSecond">
                <TransitionChild as="template" enter="transition-opacity ease-linear duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="transition-opacity ease-linear duration-300" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>
                <div class="fixed inset-0 z-40 flex">
                    <TransitionChild as="template" enter="transition ease-in-out duration-300 transform" enter-from="-translate-x-full" enter-to="translate-x-0" leave="transition ease-in-out duration-300 transform" leave-from="translate-x-0" leave-to="-translate-x-full">
                        <DialogPanel class="relative flex w-full max-w-xs flex-col overflow-y-auto bg-white pb-12 shadow-xl">
                            <div class="flex justify-end px-4 pt-5">
                                <ul class="flex w-full justify-around">
                                    <!--                                    <li class="text-sm md:text-base relative" @click="closeAfterSecond">-->
                                    <!--                                        <Link href="/istek-listem" class="flex flex-col justify-center items-center">-->
                                    <!--                                            <svg class="w-6 h-6 stroke-[#231f20] overflow-visible" viewBox="0 0 30.669 28.092">-->
                                    <!--                                                <g id="heart" transform="translate(0 -1.511)" fill="none">-->
                                    <!--                                                    <path-->
                                    <!--                                                        d="M30.071,6.622A13.219,13.219,0,0,1,27.842,18.6,33.873,33.873,0,0,1,22.3,24.456c-1.823,1.7-5.9,5.054-6.983,5.147-.956-.182-2.028-1.263-2.787-1.82-4.264-3.24-8.853-7.181-11.18-11.49C-.6,12.156-.6,7.039,2.433,3.867a8.977,8.977,0,0,1,12.885.853A8.726,8.726,0,0,1,18.3,2.245a8.977,8.977,0,0,1,11.77,4.377Z"-->
                                    <!--                                                        stroke="none"-->
                                    <!--                                                    />-->
                                    <!--                                                    <path-->
                                    <!--                                                        d="M 22.02387619018555 4.511138916015625 C 21.19887924194336 4.511205673217773 20.36529541015625 4.668869018554688 19.54391098022461 4.980022430419922 C 18.81119155883789 5.373607635498047 18.20499801635742 5.888843536376953 17.69393539428711 6.551767349243164 L 15.37918663024902 9.55436897277832 L 12.98918724060059 6.611328125 C 11.93668746948242 5.315288543701172 10.24892616271973 4.541538238525391 8.474416732788086 4.541547775268555 C 7.442102432250977 4.541547775268555 5.927898406982422 4.800741195678711 4.52613639831543 6.020847320556641 C 3.402303695678711 7.246023178100586 3.083917617797852 8.801969528198242 3.016265869140625 9.90241813659668 C 2.916469573974609 11.52574920654297 3.276287078857422 13.31153869628906 4.030189514160156 14.94011974334717 C 4.942806243896484 16.60567092895508 6.383369445800781 18.40690231323242 8.313325881958008 20.29546737670898 C 9.916957855224609 21.86470985412598 11.82877540588379 23.48118019104004 14.32891082763672 25.38162803649902 C 14.57612991333008 25.56426811218262 14.81914901733398 25.76386070251465 15.05441665649414 25.95709800720215 C 15.1480712890625 26.03402137756348 15.26153087615967 26.12721252441406 15.37966251373291 26.22183418273926 C 16.63759994506836 25.36079597473145 18.77116775512695 23.64326858520508 20.25698661804199 22.26020812988281 L 20.30599594116211 22.2145881652832 L 20.35699653625488 22.17119789123535 C 22.4555778503418 20.38569831848145 24.10916709899902 18.6432991027832 25.41224670410156 16.84445953369141 L 25.43560600280762 16.81265830993652 C 27.5122241973877 14.02390670776367 28.17683982849121 10.72176933288574 27.27249336242676 7.72297477722168 C 26.7924976348877 6.758964538574219 26.07523345947266 5.959152221679688 25.18952560424805 5.402528762817383 C 24.26146697998047 4.819278717041016 23.16680717468262 4.511037826538086 22.02387619018555 4.511138916015625 M 22.02363014221191 1.511137008666992 C 25.47772216796875 1.510845184326172 28.58621597290039 3.41154670715332 30.07120704650879 6.62156867980957 L 30.07119750976562 6.62156867980957 C 31.46405601501465 10.92536735534668 30.29865646362305 15.30498790740967 27.84177589416504 18.60439872741699 C 26.22061729431152 20.84233856201172 24.28254699707031 22.77018737792969 22.30101776123047 24.45609855651855 C 20.47795677185059 26.15307807922363 16.39905548095703 29.51041793823242 15.31799697875977 29.60327911376953 C 14.36215591430664 29.42096900939941 13.28961753845215 28.33990859985352 12.53142738342285 27.78361892700195 C 8.267665863037109 24.54384803771973 3.678485870361328 20.60295867919922 1.351097106933594 16.29318809509277 C -0.5997524261474609 12.15636825561523 -0.6031627655029297 7.038997650146484 2.433006286621094 3.867378234863281 C 6.3704833984375 0.318359375 12.3065242767334 1.011802673339844 15.31799697875977 4.720129013061523 C 16.12644577026367 3.671438217163086 17.12146759033203 2.845949172973633 18.30135726928711 2.244508743286133 C 19.5473575592041 1.74708366394043 20.80727195739746 1.511240005493164 22.02363014221191 1.511137008666992 Z"-->
                                    <!--                                                        stroke="none"-->
                                    <!--                                                        fill="#231f20"-->
                                    <!--                                                    />-->
                                    <!--                                                </g>-->
                                    <!--                                            </svg>-->
                                    <!--                                            <span class="flex items-center justify-center font-santralextrabold text-xs text-white bg-kbgreen rounded-full absolute right-1 w-4 h-4 font-normal bottom-5 right-2 text-center leading-none">-->
                                    <!--                                                {{ wishlist }}-->
                                    <!--                                            </span>-->
                                    <!--                                            <span class="mt-1">Listem</span>-->
                                    <!--                                        </Link>-->
                                    <!--                                    </li>-->
                                    <!--                                    <li class="text-sm md:text-base flex flex-col justify-center items-center" @click="closeAfterSecond" v-if="auth.isUserLoggedIn">-->
                                    <!--                                        <Link href="/profili-duzenle">-->
                                    <!--                                            <div class="flex flex-col justify-center items-center" @click="isProfileMenu = true">-->
                                    <!--                                                <div class="font-santralextrabold text-3xs leading-none text-kbgreen w-6 h-6 border-2 rounded-full border-black flex justify-center items-center">-->
                                    <!--                                                    {{-->
                                    <!--                                                        auth.user?.full_name-->
                                    <!--                                                            .split(" ")-->
                                    <!--                                                            .map((n) => n[0])-->
                                    <!--                                                            .join("")-->
                                    <!--                                                    }}-->
                                    <!--                                                </div>-->
                                    <!--                                                <span class="mt-1 text-sm">Hesabım</span>-->
                                    <!--                                            </div>-->
                                    <!--                                        </Link>-->
                                    <!--                                    </li>-->
                                    <li class="flex flex-col justify-center items-center" @click="closeAfterSecond" v-if="!auth.isUserLoggedIn">
                                        <Link :href="`/giris-yap`" class="flex">
                                            <svg class="w-6 h-6 stroke-[#231f20] overflow-visible" viewBox="0 0 34 34">
                                                <g id="Ellipse_138" data-name="Ellipse 138" fill="none" stroke="#231f20" stroke-width="3">
                                                    <circle cx="17" cy="17" r="17" stroke="none" />
                                                    <circle cx="17" cy="17" r="15.5" fill="none" />
                                                </g>
                                            </svg>
                                        </Link>
                                        <Link :href="`/giris-yap`" class="flex">
                                            <span class="text-sm mt-1">Giriş/Üye Ol</span>
                                        </Link>
                                    </li>
                                </ul>

                                <button type="button" class="-m-2 inline-flex items-center justify-center rounded-md p-2 text-gray-400" @click="closeAfterSecond">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M19.207 6.207a1 1 0 0 0-1.414-1.414L12 10.586 6.207 4.793a1 1 0 0 0-1.414 1.414L10.586 12l-5.793 5.793a1 1 0 1 0 1.414 1.414L12 13.414l5.793 5.793a1 1 0 0 0 1.414-1.414L13.414 12l5.793-5.793z" fill="#000000" />
                                    </svg>
                                </button>
                            </div>
                            <!-- Links -->
                            <TabGroup as="div" class="mt-2">
                                <div class="border-b border-gray-200">
                                    <TabList class="-mb-px flex space-x-8 px-4">
                                        <Tab as="template" v-for="category in categories" :key="category.name" v-slot="{ selected }">
                                            <button v-show="auth.isUserLoggedIn == category.islogged" :class="[selected ? 'border-kbgreen text-kbgreen' : 'border-transparent text-gray-900', 'flex-1 whitespace-nowrap border-b-2 pb-1 px-1 text-base font-medium']">
                                                {{ category.name }}
                                            </button>
                                        </Tab>
                                    </TabList>
                                </div>
                                <TabPanels as="template">
                                    <TabPanel v-for="category in categories" :key="category.name" class="space-y-0 px-4 pt-4">
                                        <div v-for="(column, columnIdx) in category.sections" :key="columnIdx" class="space-y-2">
                                            <div v-for="section in column" :key="section.name" class="mt-4">
                                                <Link :href="`${section.href}`" @click="closeAfterSecond" :id="`${category.id}-${section.id}-heading-mobile`" class="font-medium text-gray-900">{{ section.name }}</Link>
                                                <ul role="list" :aria-labelledby="`${category.id}-${section.id}-heading-mobile`" class="flex flex-col space-y-1">
                                                    <li v-for="item in section.items" :key="item.name" class="flow-root ml-4">
                                                        <Link :href="item.href" @click="closeAfterSecond" class="-m-2 block p-2 text-gray-500">{{ item.name }}</Link>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </TabPanel>
                                    <div class="mt-3 p-4 border-t-2 border-bordergray">
                                        <ul class="flex flex-col text-xl leading-6.5 font-medium">
                                            <li class="p-0 m-0 mb-2 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                                                <Link href="/nasil-calisir" @click="closeAfterSecond">Nasıl Çalışır?</Link>
                                            </li>
                                            <!--                                            <li class="p-0 m-0 mb-2 text-base text-left text-gray-900 box-border whitespace-no-wrap">-->
                                            <!--                                                <Link href="/kurumsal" @click="closeAfterSecond">Kurumsal</Link>-->
                                            <!--                                            </li>-->
                                            <!--                                            <li class="p-0 m-0 mb-2 text-base text-left text-gray-900 box-border whitespace-no-wrap">-->
                                            <!--                                                <a href="">İndirimli Ürünler</a>-->
                                            <!--                                            </li>-->
                                            <li class="">
                                                <a target="_blank" href="https://kiralamini.com/">
                                                    <div class="bg-white rounded-xl flex items-center">
                                                        <svg width="30" height="30" viewBox="0 0 31.013 29.461">
                                                            <g id="bxs-baby-carriage" fill="#FF6C37" stroke-linejoin="round">
                                                                <path
                                                                    d="M30.494,15.935q.156-.51.266-1.032c.005-.027.006-.052.012-.078.031-.152.045-.308.07-.462.039-.243.086-.486.109-.73A12.4,12.4,0,0,0,18.607,0V12.4H7L5.674,9.3H0v3.1H3.63l2.834,6.615a5.421,5.421,0,1,0,7.411,5.79h3.259a5.412,5.412,0,1,0,10.04-3.451c.065-.063.139-.119.2-.184a12.819,12.819,0,0,0,1.515-1.836,12.66,12.66,0,0,0,1.141-2.107c.025-.057.04-.118.063-.176h0l.023-.065c.136-.341.26-.683.364-1.036l.005-.02ZM8.528,26.36a2.326,2.326,0,1,1,2.326-2.326A2.329,2.329,0,0,1,8.528,26.36Zm13.955,0a2.326,2.326,0,1,1,2.326-2.326A2.329,2.329,0,0,1,22.483,26.36Z"
                                                                    stroke="none"
                                                                />
                                                                <path
                                                                    d="M 18.2486457824707 23.14793968200684 C 18.6585578918457 21.18612480163574 20.40156555175781 19.70803451538086 22.48330879211426 19.70803451538086 C 23.54667282104492 19.70803451538086 24.52171325683594 20.09375 25.27578544616699 20.73255157470703 C 25.37454986572266 20.43253707885742 25.54524040222168 20.15317153930664 25.78392791748047 19.92199516296387 C 25.85468864440918 19.85347557067871 25.9190788269043 19.79775619506836 25.96168899536133 19.76089477539062 C 25.96789932250977 19.75552177429199 25.97414779663086 19.75018119812012 25.98035049438477 19.74477195739746 C 26.43758964538574 19.28454971313477 26.86028671264648 18.77144432067871 27.23709297180176 18.2192325592041 C 27.60735511779785 17.66555023193359 27.92375755310059 17.08392715454102 28.17801475524902 16.48962783813477 C 28.1911792755127 16.45002174377441 28.21095085144043 16.39178657531738 28.23727798461914 16.32480621337891 C 28.23892974853516 16.32060241699219 28.24061393737793 16.31636238098145 28.2422924041748 16.31217575073242 C 28.25001335144043 16.29079055786133 28.25810050964355 16.2695369720459 28.26653861999512 16.2484245300293 C 28.39206886291504 15.93458652496338 28.4859790802002 15.66946887969971 28.56107902526855 15.41667366027832 C 28.571044921875 15.37959861755371 28.58206558227539 15.34288120269775 28.59412574768066 15.30654048919678 C 28.66897964477539 15.05740737915039 28.73470687866211 14.80287647247314 28.78985977172852 14.54851150512695 C 28.795654296875 14.51037311553955 28.80318450927734 14.46676158905029 28.81347846984863 14.41811084747314 C 28.81916809082031 14.38687133789062 28.82753562927246 14.32476902008057 28.8343677520752 14.27405548095703 C 28.84464836120605 14.19775581359863 28.85514831542969 14.12141513824463 28.86757850646973 14.04423522949219 C 28.87741851806641 13.98342514038086 28.88765907287598 13.92261505126953 28.89790916442871 13.86179542541504 C 28.9239387512207 13.70741558074951 28.94852828979492 13.56159496307373 28.95910835266113 13.44806575775146 C 28.9595890045166 13.44294548034668 28.9600887298584 13.43783569335938 28.96059799194336 13.43271541595459 C 29.27531814575195 10.31655502319336 28.1843090057373 7.260515213012695 25.96441841125488 5.045305252075195 C 24.99607849121094 4.075055599212646 23.86300849914551 3.316765308380127 22.59668922424316 2.791525363922119 C 21.95019721984863 2.523374557495117 21.28533363342285 2.322727680206299 20.60687828063965 2.190538167953491 L 20.60687828063965 12.40459537506104 C 20.60687828063965 13.50916576385498 19.71144866943359 14.40459537506104 18.60687828063965 14.40459537506104 L 7.004458427429199 14.40459537506104 C 6.884079933166504 14.40459537506104 6.765507221221924 14.39381313323975 6.649740695953369 14.37296772003174 L 8.303188323974609 18.23161506652832 C 8.505120277404785 18.70283317565918 8.518071174621582 19.23270797729492 8.341076850891113 19.71202659606934 C 8.403039932250977 19.70938110351562 8.465546607971191 19.70803451538086 8.528148651123047 19.70803451538086 C 10.60974502563477 19.70803451538086 12.35262203216553 21.18572425842285 12.76269340515137 23.14733505249023 C 13.08244514465332 22.93301582336426 13.46625137329102 22.80918502807617 13.87556838989258 22.80918502807617 L 17.13486862182617 22.80918502807617 C 17.54460906982422 22.80918502807617 17.92869567871094 22.93321228027344 18.2486457824707 23.14793968200684 M 22.48227882385254 29.46090507507324 C 19.75533866882324 29.46090507507324 17.51423835754395 27.43171501159668 17.13486862182617 24.80918502807617 L 13.87556838989258 24.80918502807617 C 13.49618816375732 27.43171501159668 11.25509834289551 29.46090507507324 8.528148651123047 29.46090507507324 C 5.535548210144043 29.46090507507324 3.101148366928101 27.02650451660156 3.101148366928101 24.03389549255371 C 3.104818344116211 21.83585548400879 4.43250846862793 19.85657501220703 6.464858531951904 19.01934623718262 L 3.630408525466919 12.40459537506104 L -1.551513719277864e-06 12.40459537506104 L -1.551513719277864e-06 9.303455352783203 L 5.674068450927734 9.303455352783203 L 7.004458427429199 12.40459537506104 L 18.60687828063965 12.40459537506104 L 18.60687828063965 1.540771518193651e-05 C 21.89919471740723 -0.004194794222712517 25.05516052246094 1.30301558971405 27.38002777099609 3.632495403289795 C 30.01308822631836 6.259995460510254 31.32425880432129 9.932724952697754 30.95047760009766 13.63368511199951 C 30.92773818969727 13.87764549255371 30.88121795654297 14.12056541442871 30.84193801879883 14.36348533630371 C 30.81712913513184 14.51751518249512 30.80264854431152 14.67360496520996 30.77163887023926 14.82556533813477 C 30.76543807983398 14.85140514373779 30.76440811157227 14.8762149810791 30.75923919677734 14.90309524536133 C 30.68602752685547 15.25074577331543 30.59738922119141 15.5949649810791 30.49356842041016 15.93473529815674 L 30.49253845214844 15.93576526641846 L 30.48736763000488 15.95540523529053 C 30.38398933410645 16.30790519714355 30.25994873046875 16.65006446838379 30.12349891662598 16.99119567871094 L 30.09869766235352 17.05631446838379 C 30.07594871520996 17.11420631408691 30.06044769287109 17.17519569396973 30.0356388092041 17.23204612731934 C 29.72551918029785 17.96495628356934 29.34097862243652 18.67201614379883 28.89441871643066 19.33876609802246 C 28.4457893371582 19.99723625183105 27.93719863891602 20.61539459228516 27.37898826599121 21.17463493347168 C 27.31386756896973 21.23976516723633 27.24046897888184 21.29557609558105 27.17534828186035 21.35864448547363 C 27.62914848327637 22.15253448486328 27.90928840637207 23.05703544616699 27.90928840637207 24.03389549255371 C 27.90928840637207 27.02650451660156 25.47488784790039 29.46090507507324 22.48227882385254 29.46090507507324 Z M 22.48330879211426 21.70803451538086 C 21.20150756835938 21.70803451538086 20.15744781494141 22.75208473205566 20.15744781494141 24.03389549255371 C 20.15744781494141 25.31570625305176 21.20150756835938 26.35975456237793 22.48330879211426 26.35975456237793 C 23.76511764526367 26.35975456237793 24.80917930603027 25.31570625305176 24.80917930603027 24.03389549255371 C 24.80917930603027 22.75208473205566 23.76511764526367 21.70803451538086 22.48330879211426 21.70803451538086 Z M 8.528148651123047 21.70803451538086 C 7.245308399200439 21.70803451538086 6.202288627624512 22.75208473205566 6.202288627624512 24.03389549255371 C 6.202288627624512 25.31570625305176 7.24634838104248 26.35975456237793 8.528148651123047 26.35975456237793 C 9.810998916625977 26.35975456237793 10.85401821136475 25.31570625305176 10.85401821136475 24.03389549255371 C 10.85401821136475 22.75208473205566 9.810998916625977 21.70803451538086 8.528148651123047 21.70803451538086 Z"
                                                                    stroke="none"
                                                                    fill="#231f20"
                                                                />
                                                            </g>
                                                        </svg>
                                                        <span class="text-[#FF6C37] text-sm ml-1"> ANNE&BEBEK</span>
                                                        <!--                                        <span class="ml-1 whitespace-nowrap hover:text-kbgreen text-xs">KİRALA</span>-->
                                                        <!--                                        <span class="text-[#ff64cf]">İ</span>-->
                                                        <!--                                        <span class="text-[#61ba3f]">N</span>-->
                                                        <!--                                        <span class="text-[#8854d0]">İ</span>-->
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div v-if="auth.isUserLoggedIn" class="flex items-center mt-3 border-t-2 border-bordergray p-4">
                                        <Link href="/cikis-yap">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="22.401" height="25.546" viewBox="0 0 22.401 25.546">
                                                <g id="noun-exit-119208" transform="translate(-128.547 -26.727)">
                                                    <path
                                                        id="Path_3738"
                                                        data-name="Path 3738"
                                                        d="M130.729,212.969H141.32a.642.642,0,0,0,0-1.284H130.729l1.861-1.861a.635.635,0,1,0-.9-.9l-2.953,2.953c-.064.064-.128.128-.128.192a.545.545,0,0,0,0,.513c.064.064.064.128.128.192l2.953,2.953a.621.621,0,0,0,.9,0,.62.62,0,0,0,0-.9Z"
                                                        transform="translate(0 -172.827)"
                                                        fill="#231f20"
                                                    />
                                                    <path
                                                        id="Path_3739"
                                                        data-name="Path 3739"
                                                        d="M263.757,26.727h-9.885a3.15,3.15,0,0,0-3.145,3.145v5.841a.642.642,0,0,0,1.284,0V29.872a1.863,1.863,0,0,1,1.861-1.861h9.949a1.863,1.863,0,0,1,1.861,1.861V49.128a1.863,1.863,0,0,1-1.861,1.861h-9.949a1.863,1.863,0,0,1-1.861-1.861v-5.52a.642.642,0,0,0-1.284,0v5.52a3.15,3.15,0,0,0,3.145,3.145h9.949a3.15,3.15,0,0,0,3.145-3.145V29.872a3.165,3.165,0,0,0-3.209-3.145Z"
                                                        transform="translate(-116.018 0)"
                                                        fill="#231f20"
                                                    />
                                                </g>
                                            </svg>
                                        </Link>
                                        <Link href="/cikis-yap" class="ml-3 whitespace-nowrap text-base font-bold">Çıkış Yap</Link>
                                    </div>
                                </TabPanels>
                            </TabGroup>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </Dialog>
        </TransitionRoot>
        <header class="relative bg-white">
            <nav aria-label="Top" class="mx-auto max-w-7xl">
                <div class="border-b border-gray-200">
                    <div class="flex h-16 items-center justify-between">
                        <div class="flex items-center lg:hidden">
                            <button type="button" class="-ml-2 rounded-md bg-white p-2 text-gray-400" @click="openMenu">
                                <span class="sr-only">Open menu</span>
                                <svg id="menu-right" class="mx-2 md:mx-0" xmlns="http://www.w3.org/2000/svg" width="32.216" height="22.55" viewBox="0 0 32.216 22.55">
                                    <path id="Path_3735" data-name="Path 3735" d="M18,27.118a1.6,1.6,0,0,0,1.6,1.6H32.5a1.6,1.6,0,0,0,0-3.205H19.6a1.6,1.6,0,0,0-1.6,1.6Z" transform="translate(-18 -6.17)" fill="#70d44b" />
                                    <path id="Path_3736" data-name="Path 3736" d="M3,18.11a1.6,1.6,0,0,0,1.6,1.6h29.01a1.6,1.6,0,1,0,0-3.205H4.6a1.6,1.6,0,0,0-1.6,1.6Z" transform="translate(-2.999 -6.835)" fill="#70d44b" />
                                    <path id="Path_3737" data-name="Path 3737" d="M13.6,10.705a1.6,1.6,0,0,1,0-3.205H32.948a1.6,1.6,0,0,1,0,3.205H13.6Z" transform="translate(-11.999 -7.5)" fill="#70d44b" />
                                </svg>
                            </button>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="mr-4">
                                <div>
                                    <Link href="/"><img class="w-11/12 lg:w-11/12 mx-auto" :id="this.$inertia.page.url" :src="logoUrl" alt="" /></Link>
                                </div>
                            </div>
                            <div class="hidden lg:block ml-3 mb-1">
                                <ul class="flex space-x-3 ts:space-x-8 text-xl leading-6.5 font-medium">
                                    <li class="p-0 m-0 text-base xl:text-lg text-left text-gray-900 box-border whitespace-no-wrap hover:text-checkoutgray">
                                        <Link href="/nasil-calisir" @click="closeAfterSecond">Nasıl Çalışır?</Link>
                                    </li>
                                    <!--                            <li><svg viewBox="0 0 1 18.297" class="h-full"><path class="stroke-neutral-400" d="M 0 0 L 0 18.296875"></path></svg></li>-->
                                    <!--                                    <li class="p-0 m-0 text-lg text-left text-gray-900 box-border whitespace-no-wrap hover:text-checkoutgray">-->
                                    <!--                                        <Link href="/kurumsal" @click="closeAfterSecond">Kurumsal</Link>-->
                                    <!--                                    </li>-->
                                    <!--                                    <li><svg viewBox="0 0 1 18.297" class="h-full"><path class="stroke-neutral-400" d="M 0 0 L 0 18.296875"></path></svg></li>-->
                                    <li class="p-0 m-0 text-base xl:text-lg text-left text-gray-900 box-border whitespace-no-wrap hover:text-checkoutgray">
                                        <Link href="/indirimli-urunler">İndirimli Ürünler</Link>
                                    </li>
                                    <li class="p-0 m-0 text-base xl:text-lg text-left text-gray-900 box-border whitespace-no-wrap hover:text-checkoutgray">
                                        <Link href="/kampanyalar">Kampanyalar</Link>
                                    </li>
                                    <!--                                    <li class="p-0 m-0 text-lg text-left text-gray-900 box-border whitespace-no-wrap hover:text-checkoutgray">-->
                                    <!--                                        <Link href="/kategoriler/tum-urunler">Yaz Kampanyası</Link>-->
                                    <!--                                    </li>-->
                                    <!--                                    <li class="p-0 m-0 text-lg text-left text-gray-900 box-border whitespace-no-wrap hover:text-checkoutgray">-->
                                    <!--                                        <Link href="/tags/1tl">1 TL</Link>-->
                                    <!--                                    </li>-->
                                </ul>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <ul class="flex space-x-2">
                                <li class="text-sm md:text-base relative">
                                    <Link href="/sepetim" class="flex">
                                        <span class="hidden md:block">Sepetim</span>
                                        <span class="font-santralextrabold text-xs lg:text-2xs 2xl:text-xs text-white bg-kbgreen rounded-full absolute w-4 h-4 font-normal -bottom-1 -right-1 flex justify-center items-center leading-none">{{ cart }}</span>
                                        <svg class="w-6 h-6 ml-1 stroke-[#231f20] overflow-visible" viewBox="0 0 30.324 31.234">
                                            <g id="Group_3491" data-name="Group 3491" transform="translate(1.51 1.5)">
                                                <path
                                                    id="Path_2958"
                                                    data-name="Path 2958"
                                                    d="M212.736,218.3H199.28c-2.21,0-4.076-1.109-4.357-2.59l-2.53-13.314c-.337-1.774,1.709-3.345,4.357-3.345h18.515c2.648,0,4.695,1.571,4.358,3.345l-2.53,13.314C216.812,217.194,214.946,218.3,212.736,218.3Z"
                                                    transform="translate(-192.356 -190.068)"
                                                    fill="none"
                                                    stroke="#231f20"
                                                    stroke-miterlimit="10"
                                                    stroke-width="3"
                                                />
                                                <path id="Path_2959" data-name="Path 2959" d="M202.477,192.194l5.167-7.892c.942-1.439,2.268-1.46,3.222-.052l5.381,7.944" transform="translate(-195.558 -183.208)" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="3" />
                                            </g>
                                        </svg>
                                    </Link>
                                </li>
                                <li class="text-sm md:text-base hidden lg:block" v-if="!auth.isUserLoggedIn">
                                    <Link :href="`/giris-yap`" class="flex">
                                        Giriş/Üye Ol
                                        <svg class="w-4 md:w-6 h-4 md:h-6 ml-1 stroke-[#231f20] overflow-visible" viewBox="0 0 34 34">
                                            <g id="Ellipse_138" data-name="Ellipse 138" fill="none" stroke="#231f20" stroke-width="3">
                                                <circle cx="17" cy="17" r="17" stroke="none" />
                                                <circle cx="17" cy="17" r="15.5" fill="none" />
                                            </g>
                                        </svg>
                                    </Link>
                                </li>
                                <li class="text-sm md:text-base hidden lg:block relative" v-if="auth.isUserLoggedIn">
                                    <Link href="/profili-duzenle" class="flex">
                                        Hesabım
                                        <div class="font-santralextrabold text-xs leading-none text-kbgreen w-7 h-7 border-2 rounded-full border-black flex justify-center leading-none items-center ml-1">
                                            {{
                                                auth.user?.full_name
                                                    .split(" ")
                                                    .map((n) => n[0])
                                                    .join("")
                                            }}
                                        </div>
                                    </Link>
                                </li>
                            </ul>
                        </div>
                        <!-- Flyout menus -->
                        <!--                        <PopoverGroup class="hidden lg:block lg:flex-1 lg:self-stretch">-->
                        <!--                            <div class="flex h-full space-x-8">-->
                        <!--                                <Popover v-for="category in navigation.categories" :key="category.name" class="flex"-->
                        <!--                                         v-slot="{ open }">-->
                        <!--                                    <div class="relative flex">-->
                        <!--                                        <PopoverButton-->
                        <!--                                            :class="[open ? 'text-kbgreen' : 'text-gray-700 hover:text-gray-800', 'relative z-10 flex items-center justify-center text-sm font-medium transition-colors duration-200 ease-out']">-->
                        <!--                                            {{ category.name }}-->
                        <!--                                            <span-->
                        <!--                                                :class="[open ? 'bg-kbgreen' : '', 'absolute inset-x-0 bottom-0 h-0.5 transition-colors duration-200 ease-out sm:mt-5 sm:translate-y-px sm:transform']"-->
                        <!--                                                aria-hidden="true" />-->
                        <!--                                        </PopoverButton>-->
                        <!--                                    </div>-->
                        <!--                                    <transition enter-active-class="transition ease-out duration-200"-->
                        <!--                                                enter-from-class="opacity-0" enter-to-class="opacity-100"-->
                        <!--                                                leave-active-class="transition ease-in duration-150"-->
                        <!--                                                leave-from-class="opacity-100" leave-to-class="opacity-0">-->
                        <!--                                        <PopoverPanel class="absolute inset-x-0 top-full z-80">-->
                        <!--                                            &lt;!&ndash; Presentational element used to render the bottom shadow, if we put the shadow on the actual panel it pokes out the top, so we use this shorter element to hide the top of the shadow &ndash;&gt;-->
                        <!--                                            <div class="absolute inset-0 top-1/2 bg-white shadow" aria-hidden="true" />-->
                        <!--                                            <div class="relative bg-white">-->
                        <!--                                                <div class="mx-auto max-w-7xl px-8 pt-6">-->

                        <!--                                                    <div-->
                        <!--                                                        class="grid grid-cols-4 gap-y-10 gap-x-8 text-sm text-gray-500">-->

                        <!--                                                        <div v-for="(column, columnIdx) in category.sections"-->
                        <!--                                                             :key="columnIdx" class="space-y-5">-->
                        <!--                                                            <div v-for="section in column" :key="section.name">-->
                        <!--                                                                <p :id="`${category.id}-${section.id}-heading`"-->
                        <!--                                                                   class="text-base text-black hover:text-gray-600">-->
                        <!--                                                                    {{ section.name }}-->
                        <!--                                                                </p>-->
                        <!--                                                                <ul role="list"-->
                        <!--                                                                    :aria-labelledby="`${category.id}-${section.id}-heading`"-->
                        <!--                                                                    class="mt-4 space-y-2">-->
                        <!--                                                                    <li v-for="item in section.items"-->
                        <!--                                                                        :key="item.name" class="flex">-->
                        <!--                                                                        <a :href="item.href"-->
                        <!--                                                                           class="text-gray-600 hover:text-gray-400 text-sm">{{ item.name-->
                        <!--                                                                            }}</a>-->
                        <!--                                                                    </li>-->
                        <!--                                                                </ul>-->
                        <!--                                                            </div>-->
                        <!--                                                        </div>-->
                        <!--                                                        <div class="h-[462px] hidden ts:block">-->
                        <!--                                                            <a href="/ara?term=s23">-->
                        <!--                                                                <img src="../../../images/category-image.png" alt=""-->
                        <!--                                                                     class="object-contain" />-->
                        <!--                                                            </a>-->
                        <!--                                                        </div>-->
                        <!--                                                    </div>-->

                        <!--                                                </div>-->
                        <!--                                            </div>-->
                        <!--                                        </PopoverPanel>-->
                        <!--                                    </transition>-->
                        <!--                                </Popover>-->
                        <!--                                <a v-for="page in navigation.pages" :key="page.name" :href="page.href"-->
                        <!--                                   class="flex items-center text-sm font-medium text-gray-700 hover:text-gray-800">-->
                        <!--                                    {{ page.name }}-->
                        <!--                                </a>-->
                        <!--                            </div>-->
                        <!--                        </PopoverGroup>-->
                    </div>
                </div>
            </nav>
        </header>
    </div>
</template>

<script>
import { Dialog, DialogPanel, Popover, PopoverButton, PopoverGroup, PopoverPanel, Tab, TabGroup, TabList, TabPanel, TabPanels, TransitionChild, TransitionRoot } from "@headlessui/vue";
import { Link } from "@inertiajs/inertia-vue3";

export default {
    emits: ["update:mobileMenuIsOpen"],
    components: {
        Link,
        Dialog,
        DialogPanel,
        Popover,
        PopoverButton,
        PopoverGroup,
        PopoverPanel,
        Tab,
        TabGroup,
        TabList,
        TabPanel,
        TabPanels,
        TransitionChild,
        TransitionRoot
    },
    props: {
        show: {
            type: Boolean,
            required: true,
            default: false
        },
        mobileMenuIsOpen: {
            type: Boolean,
            required: true,
            default: false
        },
        auth: Object,
        logoUrl: String,
        wishlist: Number,
        cart: Number,
        menuItems: Object
    },
    methods: {
        closeAfterSecond() {
            setTimeout(() => {
                this.$emit("update:mobileMenuIsOpen", false);
            }, 500);
        },
        openMenu() {
            console.log(this.$emit("update:mobileMenuIsOpen", true));
        }
    },
    computed: {
        mobileMenuJson() {
            let firstJson = this.menuItems;
            // İkinci JSON yapısı oluşturulacak boş dizi
            let secondJson = [];

            // İlk JSON yapısını döngü ile işleyerek ikinci JSON yapısını oluşturma
            for (const key in firstJson) {
                const mainCategory = firstJson[key];
                const mainCategoryId = key;

                // Ana kategori için obje oluştur
                const mainCategoryObj = {
                    id: mainCategoryId,
                    name: mainCategory.label,
                    href: `/kategoriler/${mainCategory.data.category_slug}`,
                    items: []
                };

                // Alt kategorileri işle
                for (const childKey in mainCategory.children) {
                    const childCategory = mainCategory.children[childKey];

                    // Alt kategori objesi oluştur
                    const childCategoryObj = {
                        name: childCategory.label,
                        href: `/kategoriler/${childCategory.data.category_slug}`
                    };

                    // Ana kategoriye alt kategoriyi ekle
                    mainCategoryObj.items.push(childCategoryObj);
                }

                // Ana kategoriyi ikinci JSON yapısına ekle
                secondJson.push(mainCategoryObj);
            }

            return secondJson;
        },
        categories() {
            let category = [
                {
                    id: "categories",
                    islogged: true,
                    name: "Kategoriler",
                    sections: [
                        [
                            {
                                id: "kurumsal",
                                name: "Kurumsal Kiralama",
                                href: "/kurumsal"
                            },
                            {
                                id: "en-yeni-urunle",
                                name: "En Yeni Ürünler",
                                href: "/#en-yeni-urunler"
                            },
                            {
                                id: "tum-urunler",
                                name: "İndirimli Ürünler",
                                href: "/indirimli-urunler"
                            },
                            // {
                            //     id: "tum-urunler",
                            //     name: "Yaz Kampanyası",
                            //     href: "/kategoriler/tum-urunler"
                            // },
                            // {
                            //     id: "discount",
                            //     name: "1 TL",
                            //     href: "/tags/1tl"
                            // },
                            {
                                id: "tum-urunler",
                                name: "Tüm Ürünler",
                                href: "/kategoriler/tum-urunler"
                            }
                        ]
                        // [
                        //     {
                        //         "id": "47071950-8ae5-4f6a-a0b3-5b13e8907c9c",
                        //         "name": "Telefon & Aksesuarları",
                        //         "href": "/kategoriler/telefon",
                        //         "items": [
                        //             {
                        //                 "name": "Telefonlar",
                        //                 "href": "/kategoriler/telefon/telefonlar"
                        //             },
                        //             {
                        //                 "name": "Telefon Aksesuarları",
                        //                 "href": "/kategoriler/telefon/telefon-aksesuarlari"
                        //             }
                        //         ]
                        //     },
                        //     {
                        //         "id": "05a1f07a-92f3-4b34-a30a-1640bca89810",
                        //         "name": "Bilgisayar & Tablet",
                        //         "href": "/kategoriler/bilgisayar-tablet",
                        //         "items": [
                        //             {
                        //                 "name": "Laptoplar",
                        //                 "href": "/kategoriler/bilgisayar-tablet/laptoplar"
                        //             },
                        //             {
                        //                 "name": "Desktoplar",
                        //                 "href": "/kategoriler/bilgisayar-tablet/desktoplar"
                        //             },
                        //             {
                        //                 "name": "Oyun Bilgisayarları",
                        //                 "href": "/kategoriler/bilgisayar-tablet/oyun-bilgisayarlari"
                        //             },
                        //             {
                        //                 "name": "Bilgisayar Aksesuarları",
                        //                 "href": "/kategoriler/bilgisayar-tablet/bilgisayar-aksesuarlari"
                        //             },
                        //             {
                        //                 "name": "Monitörler",
                        //                 "href": "/kategoriler/bilgisayar-tablet/monitor"
                        //             },
                        //             {
                        //                 "name": "Tabletler",
                        //                 "href": "/kategoriler/bilgisayar-tablet/tabletler"
                        //             },
                        //             {
                        //                 "name": "Tablet Aksesuarları",
                        //                 "href": "/kategoriler/bilgisayar-tablet/tablet-aksesuarlari"
                        //             },
                        //             {
                        //                 "name": "E-Okuyucular",
                        //                 "href": "/kategoriler/bilgisayar-tablet/e-okuyucular"
                        //             }
                        //         ]
                        //     },
                        //     {
                        //         "id": "5d413dbd-ab05-4833-b483-805de22c80ee",
                        //         "name": "Kişisel Bakım",
                        //         "href": "/kategoriler/kisisel-bakim",
                        //         "items": [
                        //             {
                        //                 "name": "Saç Bakım",
                        //                 "href": "/kategoriler/kisisel-bakim/sac-bakim"
                        //             },
                        //             {
                        //                 "name": "Sağlık",
                        //                 "href": "/kategoriler/kisisel-bakim/saglik"
                        //             }
                        //         ]
                        //     },
                        //     {
                        //         "id": "a25f49f0-136e-4031-acd4-7b3779847796",
                        //         "name": "Spor",
                        //         "href": "/kategoriler/spor",
                        //         "items": [
                        //             {
                        //                 "name": "Spor Aletleri",
                        //                 "href": "/kategoriler/spor/spor-aletleri"
                        //             },
                        //             {
                        //                 "name": "Spor Ekipmanları",
                        //                 "href": "/kategoriler/spor/spor-ekipmanlari"
                        //             }
                        //         ]
                        //     },
                        //     {
                        //         "id": "c2259f8f-f806-4b9d-b47e-75162d8215c0",
                        //         "name": "Ev & Ofis",
                        //         "href": "/kategoriler/ev-eglence",
                        //         "items": [
                        //             {
                        //                 "name": "Televizyon",
                        //                 "href": "/kategoriler/ev-eglence/televizyon"
                        //             },
                        //             {
                        //                 "name": "Akıllı Ev",
                        //                 "href": "/kategoriler/ev-eglence/akilli-ev"
                        //             },
                        //             {
                        //                 "name": "Projeksiyon",
                        //                 "href": "/kategoriler/ev-eglence/projeksiyon"
                        //             },
                        //             {
                        //                 "name": "Evcil Hayvan Ürünleri",
                        //                 "href": "/kategoriler/ev-eglence/evcil-hayvan-urunleri"
                        //             },
                        //             {
                        //                 "name": "Ses Sistemleri",
                        //                 "href": "/kategoriler/ev-eglence/ses-sistemleri"
                        //             },
                        //             {
                        //                 "name": "Isıtma & Soğutma",
                        //                 "href": "/kategoriler/ev-eglence/isitma-sogutma"
                        //             },
                        //             {
                        //                 "name": "Makine ve El Aletleri",
                        //                 "href": "/kategoriler/ev-eglence/makine-ve-el-aletleri"
                        //             },
                        //             {
                        //                 "name": "Fotokopi Makineleri",
                        //                 "href": "/kategoriler/ev-eglence/fotokopi-makineleri"
                        //             }
                        //         ]
                        //     },
                        //     {
                        //         "id": "7112f2e0-a93b-4c36-8cad-700d99107099",
                        //         "name": "Kiralamobil",
                        //         "href": "/kategoriler/kiralamobil",
                        //         "items": [
                        //             {
                        //                 "name": " Elektrikli Araba",
                        //                 "href": "/kategoriler/kiralamobil/elektrikli-araba"
                        //             },
                        //             {
                        //                 "name": " Elektrikli Motor",
                        //                 "href": "/kategoriler/kiralamobil/elektrikli-motor"
                        //             },
                        //             {
                        //                 "name": " Şarj İstasyonları",
                        //                 "href": "/kategoriler/kiralamobil/sarj-istasyonlari"
                        //             },
                        //             {
                        //                 "name": "Elektrikli Scooter",
                        //                 "href": "/kategoriler/kiralamobil/elektrikli-scooter"
                        //             },
                        //             {
                        //                 "name": " Elektrikli Bisiklet",
                        //                 "href": "/kategoriler/kiralamobil/elektrikli-bisiklet"
                        //             },
                        //             {
                        //                 "name": " Elektrikli Kaykay",
                        //                 "href": "/kategoriler/kiralamobil/elektrikli-kaykay"
                        //             },
                        //             {
                        //                 "name": "Elektrikli Robot",
                        //                 "href": "/kategoriler/kiralamobil/elektrikli-robot"
                        //             }
                        //         ]
                        //     },
                        //     {
                        //         "id": "7926ba88-7511-4747-8041-338736e65310",
                        //         "name": "Ses & Müzik",
                        //         "href": "/kategoriler/ses-muzik",
                        //         "items": [
                        //             {
                        //                 "name": "Kulaklık",
                        //                 "href": "/kategoriler/ses-muzik/kulaklik"
                        //             },
                        //             {
                        //                 "name": "Hoparlör",
                        //                 "href": "/kategoriler/ses-muzik/hoparlor"
                        //             },
                        //             {
                        //                 "name": "DJ & Stüdyo Ekipmanları",
                        //                 "href": "/kategoriler/ses-muzik/dj-studyo-ekipmanlari"
                        //             },
                        //             {
                        //                 "name": "Müzik Aletleri",
                        //                 "href": "/kategoriler/ses-muzik/muzik-aletleri"
                        //             },
                        //             {
                        //                 "name": "Ses & Müzik Aksesuarları",
                        //                 "href": "/kategoriler/ses-muzik/ses-muzik-aksesuarlari"
                        //             }
                        //         ]
                        //     },
                        //     {
                        //         "id": "91a9a718-8a6a-4e84-a9f1-004f25d4b69a",
                        //         "name": "Kameralar",
                        //         "href": "/kategoriler/kamera",
                        //         "items": [
                        //             {
                        //                 "name": "Dijital Kameralar",
                        //                 "href": "/kategoriler/kamera/dijital-kameralar"
                        //             },
                        //             {
                        //                 "name": "Aksiyon Kameraları",
                        //                 "href": "/kategoriler/kamera/aksiyon-kameralari"
                        //             },
                        //             {
                        //                 "name": "Kompakt Kameralar",
                        //                 "href": "/kategoriler/kamera/kompakt-kameralar"
                        //             },
                        //             {
                        //                 "name": "Polaroid Kameralar",
                        //                 "href": "/kategoriler/kamera/polaroid-kameralar"
                        //             },
                        //             {
                        //                 "name": "Kayıt Kameraları",
                        //                 "href": "/kategoriler/kamera/kayit-kameralari"
                        //             },
                        //             {
                        //                 "name": "Sinema Kameraları",
                        //                 "href": "/kategoriler/kamera/sinema-kameralari"
                        //             },
                        //             {
                        //                 "name": "Lensler",
                        //                 "href": "/kategoriler/kamera/lensler"
                        //             },
                        //             {
                        //                 "name": "Kamera Aksesuarları",
                        //                 "href": "/kategoriler/kamera/kamera-aksesuarlari"
                        //             }
                        //         ]
                        //     },
                        //     {
                        //         "id": "fb0fe93a-d6d2-4a27-9657-1643bf4ab12e",
                        //         "name": "Elektrikli Ev Aletleri",
                        //         "href": "/kategoriler/elektrikli-ev-aletleri",
                        //         "items": [
                        //             {
                        //                 "name": " Robot Süpürgeler",
                        //                 "href": "/kategoriler/elektrikli-ev-aletleri/robot-supurgeler"
                        //             },
                        //             {
                        //                 "name": " Dikey Süpürgeler",
                        //                 "href": "/kategoriler/elektrikli-ev-aletleri/dikey-supurgeler"
                        //             },
                        //             {
                        //                 "name": " Kahve Makineleri",
                        //                 "href": "/kategoriler/elektrikli-ev-aletleri/kahve-makineleri"
                        //             },
                        //             {
                        //                 "name": "Hava Temizleme ve Nem Alma Cihazları",
                        //                 "href": "/kategoriler/elektrikli-ev-aletleri/hava-temizleme-ve-nem-alma-cihazlari"
                        //             },
                        //             {
                        //                 "name": " Elektrikli Mutfak Aletleri",
                        //                 "href": "/kategoriler/elektrikli-ev-aletleri/elektrikli-mutfak-aletleri"
                        //             }
                        //         ]
                        //     },
                        //     {
                        //         "id": "7985e763-02d0-4733-b4a3-0d99a5bbbb59",
                        //         "name": "Saat",
                        //         "href": "/kategoriler/saat",
                        //         "items": [
                        //             {
                        //                 "name": "Apple Saatler",
                        //                 "href": "/kategoriler/saat/apple-saatler"
                        //             },
                        //             {
                        //                 "name": "Akıllı Saatler",
                        //                 "href": "/kategoriler/saat/akilli-saatler"
                        //             },
                        //             {
                        //                 "name": "Akıllı Bileklik",
                        //                 "href": "/kategoriler/saat/akilli-bileklikler"
                        //             }
                        //         ]
                        //     },
                        //     {
                        //         "id": "f1513674-0d62-4ca1-8d5a-e06514108a6d",
                        //         "name": "Oyun Konsolu & VR",
                        //         "href": "/kategoriler/oyun-konsolu-vr",
                        //         "items": [
                        //             {
                        //                 "name": "Oyun Konsolları",
                        //                 "href": "/kategoriler/oyun-konsolu-vr/oyun-konsollari"
                        //             },
                        //             {
                        //                 "name": "VR",
                        //                 "href": "/kategoriler/oyun-konsolu-vr/vr"
                        //             },
                        //             {
                        //                 "name": "Oyun Aksesuarları",
                        //                 "href": "/kategoriler/oyun-konsolu-vr/oyun-aksesuarlari"
                        //             }
                        //         ]
                        //     },
                        //     {
                        //         "id": "edcf4cb7-d8a3-462d-9659-2f5da5f42ae6",
                        //         "name": "Anne & Bebek",
                        //         "href": "/kategoriler/undefined",
                        //         "items": [
                        //             {
                        //                 "name": "Ana Kucağı",
                        //                 "href": "/kategoriler/undefined"
                        //             },
                        //             {
                        //                 "name": "Bebek Arabası",
                        //                 "href": "/kategoriler/undefined"
                        //             },
                        //             {
                        //                 "name": "Oto Koltuğu",
                        //                 "href": "/kategoriler/undefined"
                        //             }
                        //         ]
                        //     }
                        // ]
                    ]
                },
                {
                    id: "profile",
                    islogged: true,
                    name: "Hesabım",
                    sections: [
                        [
                            {
                                id: "edit-profile",
                                name: "Profilim",
                                href: "/profili-duzenle",
                                items: [
                                    { name: "Hesap Bilgilerim", href: "/profili-duzenle" },
                                    { name: "Şifre Değişikliği", href: "/sifremi-yenile" },
                                    { name: "İletişim Bilgilerim", href: "/iletisim-bilgileri" }
                                ]
                            },
                            {
                                id: "hirings",
                                name: "Kiralamalarım",
                                href: "/kiralamalar"
                            },
                            {
                                id: "requests",
                                name: "Destek Taleplerim",
                                href: "/destek-taleplerim",
                                items: [
                                    { name: "Geçmiş Taleplerim", href: "/gecmis-taleplerim" },
                                    { name: "Talep Oluştur", href: "/talep-olustur" }
                                ]
                            },
                            {
                                id: "addresses",
                                name: "Adreslerim",
                                href: "/adreslerim"
                            },
                            // {
                            //     id: "advice",
                            //     name: "Tavsiye Davetleri",
                            //     href: "/istek-listem-tavsiye"
                            // },
                            {
                                id: "wishlist",
                                name: "İstek Listem",
                                href: "/istek-listem"
                            },
                            {
                                id: "billing-type",
                                name: "Ödeme Yöntemlerim",
                                href: "/odeme-yontemlerim"
                            }
                        ]
                    ]
                }
            ];

            category[0].sections.push(this.mobileMenuJson);
            return category;
        }
    },
    data() {
        return {};
    }
};

// const open = ref(false);
</script>
