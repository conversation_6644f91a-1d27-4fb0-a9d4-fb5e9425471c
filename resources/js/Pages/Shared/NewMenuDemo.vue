<template>
    <div class="bg-white block ts:hidden">
        <!-- Mobile menu -->
        <TransitionRoot as="template" :show="mobileMenuIsOpen">
            <Dialog as="div" class="relative z-80 lg:hidden" @close="closeAfterSecond">
                <TransitionChild as="template" enter="transition-opacity ease-linear duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="transition-opacity ease-linear duration-300" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>
                <div class="fixed inset-0 z-40 flex">
                    <TransitionChild as="template" enter="transition ease-in-out duration-300 transform" enter-from="-translate-x-full" enter-to="translate-x-0" leave="transition ease-in-out duration-300 transform" leave-from="translate-x-0"
                                     leave-to="-translate-x-full">
                        <DialogPanel class="relative flex w-full max-w-xs flex-col overflow-y-auto bg-white pb-12 shadow-xl">
                            <div class="flex justify-end px-4 pt-5">
                                <button type="button" class="-m-2 inline-flex items-center justify-center rounded-md p-2 text-gray-400" @click="closeAfterSecond">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                              d="M19.207 6.207a1 1 0 0 0-1.414-1.414L12 10.586 6.207 4.793a1 1 0 0 0-1.414 1.414L10.586 12l-5.793 5.793a1 1 0 1 0 1.414 1.414L12 13.414l5.793 5.793a1 1 0 0 0 1.414-1.414L13.414 12l5.793-5.793z"
                                              fill="#000000" />
                                    </svg>
                                </button>
                            </div>
                            <!-- Links -->
                            <TabGroup as="div" class="mt-2">
                                <div class="border-b border-gray-200">
                                    <TabList class="-mb-px flex space-x-8 px-4">
                                        <Tab as="template" v-for="category in categories" :key="category.name" v-slot="{ selected }">
                                            <button v-show="auth.isUserLoggedIn == category.islogged"
                                                    :class="[selected ? 'border-kbgreen text-kbgreen' : 'border-transparent text-gray-900', 'flex-1 whitespace-nowrap border-b-2 pb-1 px-1 text-base font-medium']">
                                                {{ category.name }}
                                            </button>
                                        </Tab>
                                    </TabList>
                                </div>
                                <TabPanels as="template">
                                    <TabPanel v-for="category in categories" :key="category.name" class="space-y-0 px-4 pt-4">
                                        <div v-for="(column, columnIdx) in category.sections" :key="columnIdx">
                                            <div v-for="section in column" :key="section.name" class="my-3">
                                                <Disclosure v-slot="{ open, close }">
                                                    <Link v-if="section.href == '/kategoriler/tum-urunler'" :href="section.href" @click="closeAfterSecond" class="flex w-full justify-between items-center main-category-item">
                                                        <div class="flex items-center space-x-2" :class="category.id != 'profile' ? 'w-full' : 'w-3/4'">
                                                            <div class="rounded-full bg-bordergray p-1.5 w-[40px] h-[40px]">
                                                                <img src="/images/tum-urunler.png" alt="" class="w-full">
                                                            </div>
                                                            <span class="font-santralregular font-semibold leading-relaxed text-gray-900 text-sm md:text-base max-w-[240px]">
                                                                Tüm Ürünler
                                                            </span>
                                                        </div>
                                                    </Link>
                                                    <DisclosureButton v-else class="flex w-full justify-between items-center main-category-item" @click="toggleMenuItem(category.id)">
                                                        <div class="flex items-center space-x-2" :class="category.id != 'profile' ? 'w-full' : 'w-3/4'">
                                                            <div class="rounded-full bg-bordergray p-1.5 w-[40px] h-[40px]" v-if="category.id != 'profile'">
                                                                <img :src="section.icon" alt="" class="w-full" v-if="section.id != 'edcf4cb7-d8a3-462d-9659-2f5da5f42ae6' && section.id !== null">
                                                                <img src="/images/tum-urunler.png" alt="" class="w-full" v-if="section.href == '/kategoriler/tum-urunler'">
                                                                <img src="https://kiralabunu.fra1.cdn.digitaloceanspaces.com/collections/90-kiralaminipng.png" alt="" class="w-full" v-if="section.id === 'edcf4cb7-d8a3-462d-9659-2f5da5f42ae6'">
                                                                <img v-if="section.id === 'ae8494ef-9ecd-4015-96de-8fba185ecb64'" src="/images/kiralamotor.png" alt="" class="w-full">
                                                            </div>
                                                            <!-- <pre>{{ section.id }}</pre> -->
                                                            <span v-if="category.id != 'profile' && section.id != 'edcf4cb7-d8a3-462d-9659-2f5da5f42ae6' && section.id != 'ae8494ef-9ecd-4015-96de-8fba185ecb64'"
                                                                  class="font-santralregular font-semibold leading-relaxed text-gray-900 text-sm md:text-base max-w-[240px]">
                                                                {{ section.name }}
                                                            </span>
                                                            <a target="_blank" href="https://kiralamini.com/" v-if="category.id != 'profile' && section.id === 'edcf4cb7-d8a3-462d-9659-2f5da5f42ae6'"
                                                               class="font-santralregular font-semibold leading-relaxed text-gray-900 text-sm md:text-base max-w-[240px]">
                                                                {{ section.name }}
                                                            </a>
                                                            <a target="_self" href="https://kiralabunu.com/kiralamotor" v-if="category.id != 'profile' && section.id === 'ae8494ef-9ecd-4015-96de-8fba185ecb64'"
                                                               class="font-santralregular font-semibold leading-relaxed text-gray-900 text-sm md:text-base max-w-[240px]">
                                                                {{ section.name }}
                                                            </a>
                                                            <Link v-if="category.id == 'profile'" :href="`${section.href}`" @click="closeAfterSecond" :id="`${category.id}-${section.id}-heading-mobile`"
                                                                  class="font-santralregular font-semibold leading-relaxed text-gray-900 text-sm md:text-base max-w-[240px]">
                                                                {{ section.name }}
                                                            </Link>
                                                        </div>
                                                        <span class="flex justify-end" :class="category.id != 'profile' ? 'hidden ' : 'w-1/4'" v-if="section.items">
                                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="h-5 w-5 text-black" :class="!open ? 'rotate-180 transform' : ''">
                                                                <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd">
                                                                </path>
                                                            </svg>
                                                        </span>
                                                    </DisclosureButton>
                                                    <DisclosurePanel :class="category.id != 'profile' ? 'w-full h-full bg-white ' : ''">
                                                        <ul role="list" :aria-labelledby="`${category.id}-${section.id}-heading-mobile`" class="flex flex-col space-y-1" v-if="category.id == 'profile'">
                                                            <li v-for="item in section.items" :key="item.name" class="flow-root ml-4">
                                                                <Link :href="item.href" @click="closeAfterSecond" class="-m-2 block p-2 text-black font-santralregular inline">{{ item.name }}</Link>
                                                            </li>
                                                        </ul>
                                                        <div :aria-labelledby="`${category.id}-${section.id}-heading-mobile`" class="flex w-full justify-between flex-col" v-if="category.id != 'profile'">
                                                            <div @click="close(); toggleMenuItem()" class="flex items-center space-x-2 mb-2 cursor-pointer">
                                                                <div class=" p-1.5 w-[40px] h-[40px]">
                                                                    <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                                                                        <g id="Icon">
                                                                            <path clip-rule="evenodd"
                                                                                  d="M10.7208 19.7071C11.1113 19.3166 11.1113 18.6834 10.7208 18.2929L5.42789 13L20.9995 13C21.5517 13 21.9995 12.5523 21.9995 12C21.9995 11.4477 21.5517 11 20.9995 11L5.42789 11L10.7208 5.70711C11.1113 5.31658 11.1113 4.68342 10.7208 4.29289C10.3303 3.90237 9.69709 3.90237 9.30656 4.29289L2.30657 11.2929C1.91604 11.6834 1.91604 12.3166 2.30657 12.7071L9.30657 19.7071C9.69709 20.0976 10.3303 20.0976 10.7208 19.7071Z"
                                                                                  fill="#333333" fill-rule="evenodd" id="Vector"></path>
                                                                        </g>
                                                                    </svg>
                                                                </div>
                                                                <div class="font-santralregular font-semibold leading-relaxed text-gray-900 text-sm md:text-base max-w-[240px]">
                                                                    Geri Dön
                                                                </div>
                                                            </div>
                                                            <div class="flex items-center space-x-2 my-2" v-if="section.id != 'edcf4cb7-d8a3-462d-9659-2f5da5f42ae6'">
                                                                <div class="rounded-full bg-bordergray p-1.5 w-[40px] h-[40px]">
                                                                    <svg fill="none" height="100%" viewBox="0 0 24 24" width="100%" xmlns="http://www.w3.org/2000/svg">
                                                                        <path clip-rule="evenodd"
                                                                              d="M13.5859 7.21875C13.2207 7.69336 12.6465 8 12 8C11.5488 8 11.1328 7.85156 10.7969 7.59961C10.3125 7.23438 10 6.6543 10 6C10 4.89453 10.8945 4 12 4C13.1055 4 14 4.89453 14 6C14 6.45898 13.8457 6.88281 13.5859 7.21875Z"
                                                                              fill="#333333" fill-rule="evenodd"></path>
                                                                        <path clip-rule="evenodd"
                                                                              d="M14 12C14 13.1055 13.1055 14 12 14C10.8945 14 10 13.1055 10 12C10 10.8945 10.8945 10 12 10C12.6406 10 13.2109 10.3008 13.5762 10.7695C13.8418 11.1094 14 11.5352 14 12Z"
                                                                              fill="#333333" fill-rule="evenodd"></path>
                                                                        <path clip-rule="evenodd"
                                                                              d="M13.7012 19.0527C13.3496 19.6211 12.7188 20 12 20C10.8945 20 10 19.1055 10 18C10 16.8945 10.8945 16 12 16C13.1055 16 14 16.8945 14 18C14 18.3867 13.8906 18.748 13.7012 19.0527Z"
                                                                              fill="#333333" fill-rule="evenodd"></path>
                                                                        <path clip-rule="evenodd"
                                                                              d="M7.58594 7.21875C7.2207 7.69336 6.64648 8 6 8C5.54883 8 5.13281 7.85156 4.79688 7.59961C4.3125 7.23438 4 6.6543 4 6C4 4.89453 4.89453 4 6 4C7.10547 4 8 4.89453 8 6C8 6.45898 7.8457 6.88281 7.58594 7.21875Z"
                                                                              fill="#333333" fill-rule="evenodd"></path>
                                                                        <path clip-rule="evenodd"
                                                                              d="M8 12C8 13.1055 7.10547 14 6 14C4.89453 14 4 13.1055 4 12C4 10.8945 4.89453 10 6 10C6.64062 10 7.21094 10.3008 7.57617 10.7695C7.8418 11.1094 8 11.5352 8 12Z"
                                                                              fill="#333333" fill-rule="evenodd"></path>
                                                                        <path clip-rule="evenodd"
                                                                              d="M7.70117 19.0527C7.34961 19.6211 6.71875 20 6 20C4.89453 20 4 19.1055 4 18C4 16.8945 4.89453 16 6 16C7.10547 16 8 16.8945 8 18C8 18.3867 7.89062 18.748 7.70117 19.0527Z"
                                                                              fill="#333333" fill-rule="evenodd"></path>
                                                                        <path clip-rule="evenodd"
                                                                              d="M19.5859 7.21875C19.2207 7.69336 18.6465 8 18 8C17.5488 8 17.1328 7.85156 16.7969 7.59961C16.3125 7.23438 16 6.6543 16 6C16 4.89453 16.8945 4 18 4C19.1055 4 20 4.89453 20 6C20 6.45898 19.8457 6.88281 19.5859 7.21875Z"
                                                                              fill="#333333" fill-rule="evenodd"></path>
                                                                        <path clip-rule="evenodd"
                                                                              d="M20 12C20 13.1055 19.1055 14 18 14C16.8945 14 16 13.1055 16 12C16 10.8945 16.8945 10 18 10C18.6406 10 19.2109 10.3008 19.5762 10.7695C19.8418 11.1094 20 11.5352 20 12Z"
                                                                              fill="#333333" fill-rule="evenodd"></path>
                                                                        <path clip-rule="evenodd"
                                                                              d="M19.7012 19.0527C19.3496 19.6211 18.7188 20 18 20C16.8945 20 16 19.1055 16 18C16 16.8945 16.8945 16 18 16C19.1055 16 20 16.8945 20 18C20 18.3867 19.8906 18.748 19.7012 19.0527Z"
                                                                              fill="#333333" fill-rule="evenodd"></path>
                                                                    </svg>
                                                                </div>
                                                                <!-- <pre>{{ section }}</pre> -->
                                                                <Link :href="`${section.href}`" @click="closeAfterSecond" :id="`${category.id}-${section.id}-heading-mobile`"
                                                                      class="font-santralregular font-semibold leading-relaxed text-gray-900 text-sm md:text-base max-w-[240px]">
                                                                    Tümünü Görüntüle
                                                                </Link>
                                                            </div>
                                                            <div class="flex items-center space-x-2 my-2" v-for="item in section.items.filter(subitem => subitem.is_visible == 1)" :key="item.name">
                                                                <div class="rounded-full bg-bordergray p-1.5 w-[40px] h-[40px]">
                                                                    <!-- <img :src="item.icon" alt="" class="w-full"> -->
                                                                    <img :src="item.icon" alt="" class="w-full">
                                                                </div>
                                                                <!-- <pre>{{ section }}</pre> -->
                                                                <Link :href="`${item.href}`" @click="closeAfterSecond" class="font-santralregular font-semibold leading-relaxed text-gray-900 text-sm md:text-base max-w-[240px]">
                                                                    {{ item.name }}
                                                                </Link>
                                                            </div>
                                                        </div>
                                                    </DisclosurePanel>
                                                </Disclosure>
                                            </div>
                                        </div>
                                    </TabPanel>
                                    <div class="mt-3 border-t-2 border-bordergray">
                                        <ul class="flex flex-col text-xl leading-6.5 font-medium">
                                            <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                                                <Link class="px-4 py-2 flex items-center space-x-2 hover:bg-icon-gray transition-all ease-in-out" href="/kategoriler/tum-urunler" @click="closeAfterSecond">
                                                <span class="p-2 bg-icon-gray rounded-full">
                                                    <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                                                        <g id="Icon">
                                                            <g id="Vector">
                                                                <path
                                                                    d="M16.7071 8.70714C17.0976 8.31661 17.0976 7.68345 16.7071 7.29292C16.3166 6.9024 15.6834 6.9024 15.2929 7.29292L7.29289 15.2929C6.90237 15.6834 6.90237 16.3166 7.29289 16.7071C7.68342 17.0977 8.31658 17.0977 8.70711 16.7071L16.7071 8.70714Z"
                                                                    fill="#333333"></path>
                                                                <path d="M11 9.00003C11 10.1046 10.1046 11 9 11C7.89543 11 7 10.1046 7 9.00003C7 7.89546 7.89543 7.00003 9 7.00003C10.1046 7.00003 11 7.89546 11 9.00003Z" fill="#333333"></path>
                                                                <path d="M15 17C16.1046 17 17 16.1046 17 15C17 13.8955 16.1046 13 15 13C13.8954 13 13 13.8955 13 15C13 16.1046 13.8954 17 15 17Z" fill="#333333"></path>
                                                                <path clip-rule="evenodd"
                                                                      d="M2 7.00003C2 4.23861 4.23858 2.00003 7 2.00003H17C19.7614 2.00003 22 4.23861 22 7.00003V17C22 19.7615 19.7614 22 17 22H7C4.23858 22 2 19.7615 2 17V7.00003ZM7 4.00003H17C18.6569 4.00003 20 5.34318 20 7.00003V17C20 18.6569 18.6569 20 17 20H7C5.34315 20 4 18.6569 4 17V7.00003C4 5.34318 5.34315 4.00003 7 4.00003Z"
                                                                      fill="#333333" fill-rule="evenodd"></path>
                                                            </g>
                                                        </g>
                                                    </svg>
                                                </span>
                                                    <span class="font-santralregular font-semibold"> İndirimli Ürünler</span>
                                                </Link>
                                            </li>
                                            <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                                                <Link class="px-4 py-2 flex items-center space-x-2 hover:bg-icon-gray transition-all ease-in-out"
                                                      href="/kategoriler/tum-urunler?filter[brand]=&filter[price]=&filter[collections]=&orderBy=yeniden-eskiye" @click="closeAfterSecond">
                                                <span class="p-2 bg-icon-gray rounded-full">
                                                    <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                                                        <g id="Star Outline">
                                                            <path clip-rule="evenodd"
                                                                  d="M14.9397 7.95492L12.0008 2L9.06187 7.95492L2.49023 8.90983L7.24552 13.5451L6.12295 20.0902L12.0008 17L17.8787 20.0902L16.7561 13.5451L21.5114 8.90983L14.9397 7.95492ZM17.2134 10.3063L13.6116 9.78293L12.0008 6.5191L10.39 9.78293L6.78816 10.3063L9.39448 12.8468L8.77921 16.4341L12.0008 14.7404L15.2224 16.4341L14.6071 12.8468L17.2134 10.3063Z"
                                                                  fill="#333333" fill-rule="evenodd" id="Vector"></path>
                                                        </g>
                                                    </svg>
                                                </span>
                                                    <span class="font-santralregular font-semibold">Yeni Ürünler</span>
                                                </Link>
                                            </li>
                                            <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                                                <Link class="px-4 py-2 flex items-center space-x-2 hover:bg-icon-gray transition-all ease-in-out" href="/kategoriler/tum-urunler?filter[brand]=&filter[price]=&filter[collections]=&orderBy=artan-fiyat"
                                                      @click="closeAfterSecond">
                                                <span class="p-2 bg-icon-gray rounded-full">
                                                    <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                                                        <g id="Trending">
                                                            <path
                                                                d="M14 8C14 7.44772 14.4477 7 15 7H21C21.5523 7 22 7.44771 22 8V14.2308C22 14.7831 21.5523 15.2308 21 15.2308C20.4477 15.2308 20 14.7831 20 14.2308V10.4801L14.387 16.309C14.1985 16.5048 13.9384 16.6154 13.6667 16.6154C13.3949 16.6154 13.1349 16.5048 12.9463 16.309L9 12.2109L3.72032 17.6936C3.33723 18.0915 2.70418 18.1034 2.30636 17.7203C1.90854 17.3372 1.89659 16.7042 2.27968 16.3064L8.27968 10.0756C8.46818 9.87983 8.72824 9.76923 9 9.76923C9.27176 9.76923 9.53182 9.87983 9.72032 10.0756L13.6667 14.1737L18.6488 9H15C14.4477 9 14 8.55228 14 8Z"
                                                                fill="#333333" id="Vector">
                                                            </path>
                                                        </g>
                                                    </svg>
                                                </span>
                                                    <span class="font-santralregular font-semibold">Popüler Ürünler</span>
                                                </Link>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class=" border-t-2 border-bordergray">
                                        <ul class="flex flex-col text-xl leading-6.5 font-medium">

                                            <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                                                <Link href="/nasil-calisir" @click="closeAfterSecond" class="px-4 py-3 flex items-center space-x-2 hover:bg-icon-gray transition-all ease-in-out"> Nasıl Çalışır</Link>
                                            </li>
                                            <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                                                <Link href="/iletisim" @click="closeAfterSecond" class="px-4 py-3 flex items-center space-x-2 hover:bg-icon-gray transition-all ease-in-out">İletişim</Link>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class=" border-t-2 border-bordergray">
                                        <ul class="flex flex-col text-xl leading-6.5 font-medium">
                                            <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                                                <Link href="/kurumsal" @click="closeAfterSecond" class="px-4 py-3 flex items-center space-x-2 hover:bg-icon-gray transition-all ease-in-out"> Kiralabunu Kurumsal</Link>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class=" border-t-2 border-bordergray">
                                        <ul class="flex flex-col text-xl leading-6.5 font-medium">
                                            <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                                                <Link href="/kampanyalar" @click="closeAfterSecond" class="px-4 py-3 flex items-center space-x-2 hover:bg-icon-gray transition-all ease-in-out"> Kampanyalar</Link>
                                            </li>
                                            <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                                                <Link href="/blog" @click="closeAfterSecond" class="px-4 py-3 flex items-center space-x-2 hover:bg-icon-gray transition-all ease-in-out"> Blog</Link>
                                            </li>
                                            <!--                                            <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">-->
                                            <!--                                                <Link target="_blank" href="https://kiralamini.com/" class="px-4 py-2 flex items-center space-x-2 hover:bg-icon-gray transition-all ease-in-out">-->
                                            <!--                                                    <div class="flex items-center">-->
                                            <!--                                                        <svg width="30" height="30" viewBox="0 0 31.013 29.461">-->
                                            <!--                                                            <g id="bxs-baby-carriage" fill="#FF6C37" stroke-linejoin="round">-->
                                            <!--                                                                <path-->
                                            <!--                                                                    d="M30.494,15.935q.156-.51.266-1.032c.005-.027.006-.052.012-.078.031-.152.045-.308.07-.462.039-.243.086-.486.109-.73A12.4,12.4,0,0,0,18.607,0V12.4H7L5.674,9.3H0v3.1H3.63l2.834,6.615a5.421,5.421,0,1,0,7.411,5.79h3.259a5.412,5.412,0,1,0,10.04-3.451c.065-.063.139-.119.2-.184a12.819,12.819,0,0,0,1.515-1.836,12.66,12.66,0,0,0,1.141-2.107c.025-.057.04-.118.063-.176h0l.023-.065c.136-.341.26-.683.364-1.036l.005-.02ZM8.528,26.36a2.326,2.326,0,1,1,2.326-2.326A2.329,2.329,0,0,1,8.528,26.36Zm13.955,0a2.326,2.326,0,1,1,2.326-2.326A2.329,2.329,0,0,1,22.483,26.36Z"-->
                                            <!--                                                                    stroke="none" />-->
                                            <!--                                                                <path-->
                                            <!--                                                                    d="M 18.2486457824707 23.14793968200684 C 18.6585578918457 21.18612480163574 20.40156555175781 19.70803451538086 22.48330879211426 19.70803451538086 C 23.54667282104492 19.70803451538086 24.52171325683594 20.09375 25.27578544616699 20.73255157470703 C 25.37454986572266 20.43253707885742 25.54524040222168 20.15317153930664 25.78392791748047 19.92199516296387 C 25.85468864440918 19.85347557067871 25.9190788269043 19.79775619506836 25.96168899536133 19.76089477539062 C 25.96789932250977 19.75552177429199 25.97414779663086 19.75018119812012 25.98035049438477 19.74477195739746 C 26.43758964538574 19.28454971313477 26.86028671264648 18.77144432067871 27.23709297180176 18.2192325592041 C 27.60735511779785 17.66555023193359 27.92375755310059 17.08392715454102 28.17801475524902 16.48962783813477 C 28.1911792755127 16.45002174377441 28.21095085144043 16.39178657531738 28.23727798461914 16.32480621337891 C 28.23892974853516 16.32060241699219 28.24061393737793 16.31636238098145 28.2422924041748 16.31217575073242 C 28.25001335144043 16.29079055786133 28.25810050964355 16.2695369720459 28.26653861999512 16.2484245300293 C 28.39206886291504 15.93458652496338 28.4859790802002 15.66946887969971 28.56107902526855 15.41667366027832 C 28.571044921875 15.37959861755371 28.58206558227539 15.34288120269775 28.59412574768066 15.30654048919678 C 28.66897964477539 15.05740737915039 28.73470687866211 14.80287647247314 28.78985977172852 14.54851150512695 C 28.795654296875 14.51037311553955 28.80318450927734 14.46676158905029 28.81347846984863 14.41811084747314 C 28.81916809082031 14.38687133789062 28.82753562927246 14.32476902008057 28.8343677520752 14.27405548095703 C 28.84464836120605 14.19775581359863 28.85514831542969 14.12141513824463 28.86757850646973 14.04423522949219 C 28.87741851806641 13.98342514038086 28.88765907287598 13.92261505126953 28.89790916442871 13.86179542541504 C 28.9239387512207 13.70741558074951 28.94852828979492 13.56159496307373 28.95910835266113 13.44806575775146 C 28.9595890045166 13.44294548034668 28.9600887298584 13.43783569335938 28.96059799194336 13.43271541595459 C 29.27531814575195 10.31655502319336 28.1843090057373 7.260515213012695 25.96441841125488 5.045305252075195 C 24.99607849121094 4.075055599212646 23.86300849914551 3.316765308380127 22.59668922424316 2.791525363922119 C 21.95019721984863 2.523374557495117 21.28533363342285 2.322727680206299 20.60687828063965 2.190538167953491 L 20.60687828063965 12.40459537506104 C 20.60687828063965 13.50916576385498 19.71144866943359 14.40459537506104 18.60687828063965 14.40459537506104 L 7.004458427429199 14.40459537506104 C 6.884079933166504 14.40459537506104 6.765507221221924 14.39381313323975 6.649740695953369 14.37296772003174 L 8.303188323974609 18.23161506652832 C 8.505120277404785 18.70283317565918 8.518071174621582 19.23270797729492 8.341076850891113 19.71202659606934 C 8.403039932250977 19.70938110351562 8.465546607971191 19.70803451538086 8.528148651123047 19.70803451538086 C 10.60974502563477 19.70803451538086 12.35262203216553 21.18572425842285 12.76269340515137 23.14733505249023 C 13.08244514465332 22.93301582336426 13.46625137329102 22.80918502807617 13.87556838989258 22.80918502807617 L 17.13486862182617 22.80918502807617 C 17.54460906982422 22.80918502807617 17.92869567871094 22.93321228027344 18.2486457824707 23.14793968200684 M 22.48227882385254 29.46090507507324 C 19.75533866882324 29.46090507507324 17.51423835754395 27.43171501159668 17.13486862182617 24.80918502807617 L 13.87556838989258 24.80918502807617 C 13.49618816375732 27.43171501159668 11.25509834289551 29.46090507507324 8.528148651123047 29.46090507507324 C 5.535548210144043 29.46090507507324 3.101148366928101 27.02650451660156 3.101148366928101 24.03389549255371 C 3.104818344116211 21.83585548400879 4.43250846862793 19.85657501220703 6.464858531951904 19.01934623718262 L 3.630408525466919 12.40459537506104 L -1.551513719277864e-06 12.40459537506104 L -1.551513719277864e-06 9.303455352783203 L 5.674068450927734 9.303455352783203 L 7.004458427429199 12.40459537506104 L 18.60687828063965 12.40459537506104 L 18.60687828063965 1.540771518193651e-05 C 21.89919471740723 -0.004194794222712517 25.05516052246094 1.30301558971405 27.38002777099609 3.632495403289795 C 30.01308822631836 6.259995460510254 31.32425880432129 9.932724952697754 30.95047760009766 13.63368511199951 C 30.92773818969727 13.87764549255371 30.88121795654297 14.12056541442871 30.84193801879883 14.36348533630371 C 30.81712913513184 14.51751518249512 30.80264854431152 14.67360496520996 30.77163887023926 14.82556533813477 C 30.76543807983398 14.85140514373779 30.76440811157227 14.8762149810791 30.75923919677734 14.90309524536133 C 30.68602752685547 15.25074577331543 30.59738922119141 15.5949649810791 30.49356842041016 15.93473529815674 L 30.49253845214844 15.93576526641846 L 30.48736763000488 15.95540523529053 C 30.38398933410645 16.30790519714355 30.25994873046875 16.65006446838379 30.12349891662598 16.99119567871094 L 30.09869766235352 17.05631446838379 C 30.07594871520996 17.11420631408691 30.06044769287109 17.17519569396973 30.0356388092041 17.23204612731934 C 29.72551918029785 17.96495628356934 29.34097862243652 18.67201614379883 28.89441871643066 19.33876609802246 C 28.4457893371582 19.99723625183105 27.93719863891602 20.61539459228516 27.37898826599121 21.17463493347168 C 27.31386756896973 21.23976516723633 27.24046897888184 21.29557609558105 27.17534828186035 21.35864448547363 C 27.62914848327637 22.15253448486328 27.90928840637207 23.05703544616699 27.90928840637207 24.03389549255371 C 27.90928840637207 27.02650451660156 25.47488784790039 29.46090507507324 22.48227882385254 29.46090507507324 Z M 22.48330879211426 21.70803451538086 C 21.20150756835938 21.70803451538086 20.15744781494141 22.75208473205566 20.15744781494141 24.03389549255371 C 20.15744781494141 25.31570625305176 21.20150756835938 26.35975456237793 22.48330879211426 26.35975456237793 C 23.76511764526367 26.35975456237793 24.80917930603027 25.31570625305176 24.80917930603027 24.03389549255371 C 24.80917930603027 22.75208473205566 23.76511764526367 21.70803451538086 22.48330879211426 21.70803451538086 Z M 8.528148651123047 21.70803451538086 C 7.245308399200439 21.70803451538086 6.202288627624512 22.75208473205566 6.202288627624512 24.03389549255371 C 6.202288627624512 25.31570625305176 7.24634838104248 26.35975456237793 8.528148651123047 26.35975456237793 C 9.810998916625977 26.35975456237793 10.85401821136475 25.31570625305176 10.85401821136475 24.03389549255371 C 10.85401821136475 22.75208473205566 9.810998916625977 21.70803451538086 8.528148651123047 21.70803451538086 Z"-->
                                            <!--                                                                    stroke="none" fill="#231f20" />-->
                                            <!--                                                            </g>-->
                                            <!--                                                        </svg>-->
                                            <!--                                                        <span class="text-[#FF6C37] text-sm ml-1"> ANNE&BEBEK </span>-->
                                            <!--                                                    </div>-->
                                            <!--                                                </Link>-->
                                            <!--                                            </li>-->
                                        </ul>
                                    </div>
                                    <div v-if="auth.isUserLoggedIn" class="flex items-center my-3 border-t-2 border-bordergray p-4">
                                        <Link href="/cikis-yap">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="22.401" height="25.546" viewBox="0 0 22.401 25.546">
                                                <g id="noun-exit-119208" transform="translate(-128.547 -26.727)">
                                                    <path id="Path_3738" data-name="Path 3738"
                                                          d="M130.729,212.969H141.32a.642.642,0,0,0,0-1.284H130.729l1.861-1.861a.635.635,0,1,0-.9-.9l-2.953,2.953c-.064.064-.128.128-.128.192a.545.545,0,0,0,0,.513c.064.064.064.128.128.192l2.953,2.953a.621.621,0,0,0,.9,0,.62.62,0,0,0,0-.9Z"
                                                          transform="translate(0 -172.827)" fill="#231f20" />
                                                    <path id="Path_3739" data-name="Path 3739"
                                                          d="M263.757,26.727h-9.885a3.15,3.15,0,0,0-3.145,3.145v5.841a.642.642,0,0,0,1.284,0V29.872a1.863,1.863,0,0,1,1.861-1.861h9.949a1.863,1.863,0,0,1,1.861,1.861V49.128a1.863,1.863,0,0,1-1.861,1.861h-9.949a1.863,1.863,0,0,1-1.861-1.861v-5.52a.642.642,0,0,0-1.284,0v5.52a3.15,3.15,0,0,0,3.145,3.145h9.949a3.15,3.15,0,0,0,3.145-3.145V29.872a3.165,3.165,0,0,0-3.209-3.145Z"
                                                          transform="translate(-116.018 0)" fill="#231f20" />
                                                </g>
                                            </svg>
                                        </Link>
                                        <Link href="/cikis-yap" class="ml-3 whitespace-nowrap text-base font-bold">Çıkış Yap</Link>
                                    </div>
                                </TabPanels>
                            </TabGroup>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </Dialog>
        </TransitionRoot>
        <div class="relative bg-white">
            <nav aria-label="Top" class="mx-auto max-w-7xl">
                <div class="border-b border-gray-200 flex items-center">
                    <div class="flex h-16 items-center justify-between">
                        <div class="flex items-center lg:hidden">
                            <button type="button" class="-ml-2 rounded-md bg-white p-2 text-gray-400" @click="openMenu">
                                <span class="sr-only">Open menu</span>
                                <svg fill="none" height="36" viewBox="0 0 24 24" width="36" xmlns="http://www.w3.org/2000/svg">
                                    <path clip-rule="evenodd" d="M4 7C4 6.44772 4.44772 6 5 6H19C19.5523 6 20 6.44772 20 7C20 7.55228 19.5523 8 19 8H5C4.44772 8 4 7.55228 4 7Z" fill="#333333" fill-rule="evenodd"></path>
                                    <path clip-rule="evenodd" d="M4 12C4 11.4477 4.44772 11 5 11H19C19.5523 11 20 11.4477 20 12C20 12.5523 19.5523 13 19 13H5C4.44772 13 4 12.5523 4 12Z" fill="#333333" fill-rule="evenodd"></path>
                                    <path clip-rule="evenodd" d="M4 17C4 16.4477 4.44772 16 5 16H19C19.5523 16 20 16.4477 20 17C20 17.5523 19.5523 18 19 18H5C4.44772 18 4 17.5523 4 17Z" fill="#333333" fill-rule="evenodd"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="group border rounded-xl flex bg-white items-center relative w-full" v-if="institutionalLayout != 'demo'">
                        <input type="text" class="peer w-full border-none rounded-xl p-2 pl-12 text-base placeholder:text-xs" v-model="searchForm.term" @keydown.enter="submitSearchForm" id="searchform" placeholder=" Marka, ürün veya kategori ara" />
                        <div class="ml-3 md:ml-3 absolute top-2 left-0" @click="submitSearchForm">
                            <img src="../../../images/svg/search-black.svg" alt="" />
                        </div>
                    </div>
                    <ul class="flex space-x-3 ts:space-x-4 2xl:space-x-6 items-center justify-center" v-if="institutionalLayout == 'demo'">
                        <li>
                            <Link href="/is-ortagi-cozumlerimiz">
                                <span class=" whitespace-nowrap hover:text-kbgreen font-semibold text-2xs font-santralregular">İş Ortağı Çözümlerimiz</span>
                            </Link>
                        </li>
                        <li>
                            <Link href="/musteri-cozumlerimiz">
                                <span class=" whitespace-nowrap hover:text-kbgreen font-semibold text-2xs font-santralregular">Müşteri Çözümlerimiz</span>
                            </Link>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
    </div>
</template>

<script>
import { Dialog, DialogPanel, Popover, PopoverButton, PopoverGroup, PopoverPanel, Tab, TabGroup, TabList, TabPanel, TabPanels, TransitionChild, TransitionRoot, Disclosure, DisclosureButton, DisclosurePanel } from "@headlessui/vue";
import { Link } from "@inertiajs/inertia-vue3";

export default {
    emits: ["update:mobileMenuIsOpen"],
    components: {
        Link,
        Dialog,
        DialogPanel,
        Popover,
        PopoverButton,
        PopoverGroup,
        PopoverPanel,
        Tab,
        TabGroup,
        TabList,
        TabPanel,
        TabPanels,
        TransitionChild,
        TransitionRoot,
        Disclosure,
        DisclosureButton,
        DisclosurePanel
    },
    props: {
        show: {
            type: Boolean,
            required: true,
            default: false
        },
        mobileMenuIsOpen: {
            type: Boolean,
            required: true,
            default: false
        },
        auth: Object,
        logoUrl: String,
        wishlist: Number,
        cart: Number,
        menuItems: Object,
        searchForm: Object,
        institutionalLayout: ""
    },
    methods: {
        closeAfterSecond() {
            setTimeout(() => {
                this.$emit("update:mobileMenuIsOpen", false);
            }, 500);
        },
        openMenu() {
            console.log(this.$emit("update:mobileMenuIsOpen", true));
        },
        submitSearchForm() {
            // Arama metni boşsa arama yapma
            if (this.searchForm.term == null || this.searchForm.term == "") return;
            this.searchForm.get(route("searchResults"));
        },
        toggleMenuItem(category) {
            if (category != "profile") {
                document.querySelectorAll(".main-category-item").forEach((el) => {
                    el.style.display = el.style.display === "none" ? "block" : "none";
                });
            }
        }
    },
    computed: {
        mobileMenuJson() {
            let firstJson = this.menuItems;
            // İkinci JSON yapısı oluşturulacak boş dizi
            let secondJson = [];

            // İlk JSON yapısını döngü ile işleyerek ikinci JSON yapısını oluşturma
            for (const key in firstJson) {
                const mainCategory = firstJson[key];
                const mainCategoryId = key;

                // Ana kategori için obje oluştur
                const mainCategoryObj = {
                    id: mainCategoryId,
                    name: mainCategory.label,
                    href: `/kategoriler/${mainCategory.data.category_slug}`,
                    icon: mainCategory.data.icon,
                    is_visible: mainCategory.data.is_visible,
                    items: []
                };

                // Alt kategorileri işle
                for (const childKey in mainCategory.children) {
                    const childCategory = mainCategory.children[childKey];

                    // Alt kategori objesi oluştur
                    const childCategoryObj = {
                        name: childCategory.label,
                        href: `/kategoriler/${childCategory.data.category_slug}`,
                        icon: childCategory.data.icon,
                        is_visible: childCategory.data.is_visible
                    };

                    // Ana kategoriye alt kategoriyi ekle
                    mainCategoryObj.items.push(childCategoryObj);
                }

                // Ana kategoriyi ikinci JSON yapısına ekle
                secondJson.push(mainCategoryObj);
            }

            return secondJson;
        },
        categories() {
            let category = [
                {
                    id: "categories",
                    islogged: true,
                    name: "Kategoriler",
                    sections: [
                        [
                            {
                                id: "tum-urunler",
                                name: "Tüm Ürünler",
                                href: "/kategoriler/tum-urunler"
                            }
                        ]
                    ]
                },
                {
                    id: "profile",
                    islogged: true,
                    name: "Hesabım",
                    sections: [
                        [
                            {
                                id: "edit-profile",
                                name: "Profilim",
                                href: "/profili-duzenle",
                                items: [
                                    { name: "Hesap Bilgilerim", href: "/profili-duzenle" },
                                    { name: "Şifre Değişikliği", href: "/sifremi-yenile" },
                                    { name: "İletişim Bilgilerim", href: "/iletisim-bilgileri" }
                                ]
                            },
                            {
                                id: "hirings",
                                name: "Kiralamalarım",
                                href: "/kiralamalar"
                            },
                            {
                                id: "requests",
                                name: "Destek Taleplerim",
                                href: "/destek-taleplerim",
                                items: [
                                    { name: "Geçmiş Taleplerim", href: "/gecmis-taleplerim" },
                                    { name: "Talep Oluştur", href: "/talep-olustur" }
                                ]
                            },
                            {
                                id: "addresses",
                                name: "Adreslerim",
                                href: "/adreslerim"
                            },
                            // {
                            //     id: "advice",
                            //     name: "Tavsiye Davetleri",
                            //     href: "/istek-listem-tavsiye"
                            // },
                            {
                                id: "wishlist",
                                name: "İstek Listem",
                                href: "/istek-listem"
                            },
                            {
                                id: "billing-type",
                                name: "Ödeme Yöntemlerim",
                                href: "/odeme-yontemlerim"
                            }
                        ]
                    ]
                }
            ];

            category[0].sections.push(this.mobileMenuJson);
            return category;
        }
    },
    data() {
        return {};
    }
};

// const open = ref(false);
</script>
