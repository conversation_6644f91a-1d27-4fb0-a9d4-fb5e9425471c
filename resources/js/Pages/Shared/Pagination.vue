<template>
    <div v-if="links.length > 3">
        <div class="flex flex-wrap -mb-1">
            <template v-for="(link, key) in links">
                <div v-if="link.url === null || link.url.startsWith('&') || link.url === '&'" :key="key" class="mb-1 mr-1 px-4 py-3 text-gray-400 text-sm leading-4 border rounded font-santralregular font-bold" v-html="link.label" />
                <Link v-else :key="`link-${key}`" class="mb-1 mr-1 px-4 py-3 focus:text-indigo-500 text-sm leading-4 hover:bg-white border rounded font-santralregular font-bold" :class="{ 'border-kbgreen border-2': link.active }" :href="addGridTypeToUrl(link.url)" v-html="link.label" />
            </template>
        </div>
    </div>
</template>

<script>
import { Link } from "@inertiajs/inertia-vue3";

export default {
    components: {
        Link,
    },
    props: {
        links: Array,
        gridType: {
            type: String,
            default: "category-box",
        },
    },
    methods: {
        addGridTypeToUrl(url) {
            // Eğer gridType default değer (category-box) değilse URL'e ekle
            if (!this.gridType || this.gridType === "category-box") {
                return url;
            }

            try {
                // URL'yi parse et
                const urlObj = new URL(url, window.location.origin);

                // GridType parametresini ekle
                urlObj.searchParams.set("gridType", this.gridType);

                return urlObj.toString();
            } catch (error) {
                console.warn("URL parse hatası:", error);
                return url;
            }
        },
    },
};
</script>

<style scoped></style>
