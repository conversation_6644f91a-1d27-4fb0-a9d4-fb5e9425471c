<script setup>
import { useForm } from "@inertiajs/inertia-vue3";
import { vMaska } from "maska";

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false
    },
    pegasusAuth: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(["update:isOpen"]);

const form = useForm({
    userPhone: props.pegasusAuth?.phone || "",
    userTravelCount: props.pegasusAuth?.travelCount || "",
    userCampaignSource: props.pegasusAuth?.campaignSource || ""
});

const submit = () => {
    form.clearErrors();
    form.post("pegasus-user-login", {
        onSuccess: () => {
            emit("update:isOpen", false);
        }
    });
};
</script>

<template>

    <!-- Main modal -->
    <div v-if="isOpen" id="default-modal" tabindex="-1" :aria-hidden="!isOpen"
        class="flex justify-center items-center fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-black/30">
        <div class="relative w-full max-w-2xl max-h-full">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow">
                <!-- Modal header -->
                <div class="flex items-start justify-between rounded-t">
                    <button @click="$emit('update:isOpen', false)" type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        data-modal-hide="default-modal">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div class="px-6 pb-6 flex flex-wrap">
                    <div class="w-full mt-8 lg:mt-0">
                        <img class="mx-auto max-w-[150px]" src="../../../images/brands/pegasus-airlines-logo.jpeg" />
                        <p class="text-center lg:text-lg">BolBol Puan kazanmak için aşağıdaki bilgileri doldur</p>

                        <form @submit.prevent="submit">
                            <div class="w-full flex items-center flex-col pb-1 space-y-3 md:w-2/3 lg:w-1/2 mt-6 mx-auto">
                                <input type="text" v-maska data-maska="(5##) ### ## ##" class="w-full placeholder:text-sm placeholder:text-textgray" placeholder="Telefon numaranı gir" v-model="form.userPhone" required />
                                <input type="text" class="w-full placeholder:text-sm placeholder:text-textgray" placeholder="Yılda kaç kere seyahat edersin ?" v-model="form.userTravelCount" required />
                                <input type="text" class="w-full placeholder:text-sm placeholder:text-textgray" placeholder="Kampanyamızı nereden duydunuz ?" v-model="form.userCampaignSource" required />
                                <button class="bg-orange-500 text-white w-64 rounded-full py-2 self-center text-lg font-bold" type="submit" :disabled="form.processing">
                                    <span class="h-5 w-5 border-t-transparent border-solid animate-spin rounded-full border-white border-4" v-if="form.processing"></span>
                                    <span class="ml-2" v-if="!form.processing">Devam</span>
                                </button>
                            </div>
                            <div class="flex-wrap flex w-full text-red-600 justify-center" v-if="Object.keys(form.errors).length > 0">
                                <div v-for="(error, key) in form.errors" :key="key">{{ error }}</div>
                            </div>
                            <p class="font-santralregular text-xs mt-3">Eklediğin telefon numarası ile sipariş oluşturduğun kişi bilgilerinin uyumlu olmaması veya siparişinin onaylanmaması durumunda herhangi bir BolBol Puan kazanımı sağlanmayacaktır.
                            </p>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped></style>
