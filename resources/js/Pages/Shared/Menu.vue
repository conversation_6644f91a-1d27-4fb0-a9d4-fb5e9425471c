<template>
    <div class=" max-h-[400px] w-full absolute z-60 bg-white hidden md:flex" v-if="active"
         @mouseenter="menuShown(true)" @mouseleave="menuShown(false)">
        <div class="flex flex-col flex-wrap h-full max-h-[400px] m-4 w-[calc(100%-230px)]">
            <div class="mb-4">
                <Link @click="menuShown(false)" :href="`/kategoriler/tum-urunler`"><h3 class="font-semibold">Tüm
                    <PERSON>rünler</h3></Link>
            </div>
            <template v-for="(menu, index) in menuItems" :key="index">
                <div class="mb-4 px-1">
                    <h3 class="font-semibold text-lg hover:text-gray-700 transition-all">
                        <Link v-if="menu.data.category_slug" @click="menuShown(false)" :href="`/kategoriler/${menu.data.category_slug}`">
                            {{ menu.label }}
                        </Link>
                        <a target="_blank" v-if="menu.data.url" @click="menuShown(false)" :href="`${menu.data.url}`">
                            {{ menu.label }}
                        </a>
                    </h3>
                    <ul class="mt-2 text-xs">
                        <li class="text-gray-600 hover:text-gray-800 transition-all" v-for="(menuSubItem, subIndex) in menu.children" :key="subIndex">
                            <Link v-if="menuSubItem.data.category_slug" @click="menuShown(false)" :href="`/kategoriler/${menuSubItem.data.category_slug}`">
                                {{ menuSubItem.label }}
                            </Link>
                            <a target="_blank" v-if="menuSubItem.data.url" @click="menuShown(false)" :href="`${menuSubItem.data.url}`">
                                {{ menuSubItem.label }}
                            </a>
                        </li>
                    </ul>
                </div>
            </template>
            <!--            <div>-->
            <!--                <Link href="https://kiralamini.com/" target="_blank">-->
            <!--                    <img class="w-full max-w-[150px]"-->
            <!--                         src="https://kiralamini.com/wp-content/uploads/2023/02/cropped-logo-2048x344.png" alt="">-->
            <!--                </Link>-->
            <!--            </div>-->
        </div>
        <div class=" hidden ts:block w-[230px]">
            <Link href="/urun/iphone-se-64-gb-2022">
                <picture class="flex justify-end">
                    <source srcset="../../../images/welcome-webp/kb-menu-iphone-se.webp" type="image/webp" />
                    <source srcset="../../../images/welcome-webp/kb-menu-iphone-se.png" type="image/png" />
                    <img class="w-full max-w-[230px]" src="../../../images/welcome-webp/kb-menu-iphone-se.png" alt="Apple İphone" />
                </picture>
            </Link>
        </div>
    </div>
</template>

<script>
import { Link } from "@inertiajs/inertia-vue3";

export default {
    name: "Menu",
    props: {
        active: {
            type: Boolean,
            default: true
        },
        menuItems: Object
    },
    emits: ["menuchange"],
    methods: {
        menuShown(val) {
            //console.log('close');
            this.$emit("menuchange", val);
        }
    },
    components: {
        Link
    }
};
</script>

<style scoped></style>
