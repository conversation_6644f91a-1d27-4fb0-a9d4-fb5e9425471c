<script setup>
import { useForm } from "@inertiajs/inertia-vue3";

const form = useForm({
    userToken: "",
});

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false,
    },
});

defineEmits(["update:isOpen"]);

const submit = () => {
    form.clearErrors();
    form.post("hopi-user-login", {});
};
</script>

<template>
    <!-- Main modal -->
    <div v-if="isOpen" id="default-modal" tabindex="-1" aria-hidden="true" class="flex justify-center items-center fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-black/30">
        <div class="relative w-full max-w-2xl max-h-full">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow">
                <!-- Modal header -->
                <div class="flex items-start justify-between rounded-t">
                    <button @click="$emit('update:isOpen', false)" type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="default-modal">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div class="p-6 flex flex-wrap">
                    <div class="w-full md:w-8/12 p-3 lg:p-8">
                        <div class="flex flex-col justify-between h-full">
                            <div class="flex justify-center">
                                <img src="/images/hopi/logo.png" />
                            </div>
                            <div class="flex justify-center">
                                <p class="text-center">Hopi'n varsa, kampanyalardan yararlanmak ve Paracık'larınla alışveriş yapmak için aşağıya Hopi QR Kodunu gir.</p>
                            </div>
                            <div class="items-center">
                                <form class="w-full flex items-center flex-col pb-1" @submit.prevent="submit">
                                    <input type="text" class="w-full" placeholder="Hopi QR Kodunu Gir" v-model="form.userToken" required />
                                    <button class="bg-hopi-pink text-white w-64 rounded-full my-2 py-2 self-center text-lg font-bold" type="submit" :disabled="form.processing">
                                        <div class="flex items-center justify-center m-[10px]">
                                            <div class="h-5 w-5 border-t-transparent border-solid animate-spin rounded-full border-white border-4" v-if="form.processing"></div>
                                            <div class="ml-2">Devam</div>
                                        </div>
                                    </button>
                                </form>
                                <div class="flex-wrap flex w-full text-red-600 justify-center" v-if="form.errors.userToken">{{ form.errors.userToken[0] }}</div>
                                <div class="flex-wrap flex w-full justify-center text-sm mt-3">Hopi'n yok mu? <a href="http://www.hopi.com.tr/" class="ml-1" target="_blank"> Hemen tıkla</a>, Hopi'nle tanış.</div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full md:w-4/12 text-sm mt-8 lg:mt-0"><img class="mx-auto" src="/images/hopi/phone.jpg" /></div>
                </div>
                <!-- Modal footer -->
                <!--                        <div class="flex items-center p-6 space-x-2 border-t border-gray-200 rounded-b dark:border-gray-600">-->
                <!--                            <button data-modal-hide="default-modal" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">I accept</button>-->
                <!--                            <button-->
                <!--                                data-modal-hide="default-modal"-->
                <!--                                type="button"-->
                <!--                                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600"-->
                <!--                            >-->
                <!--                                Decline-->
                <!--                            </button>-->
                <!--                        </div>-->
            </div>
        </div>
    </div>
</template>

<style scoped></style>
