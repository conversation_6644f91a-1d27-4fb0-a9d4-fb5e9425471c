<template>
<!--    <div class="fixed inset-0 flex items-center justify-center">-->
<!--        <button-->
<!--            type="button"-->
<!--            @click="openModal"-->
<!--            class="rounded-md bg-black bg-opacity-20 px-4 py-2 text-sm font-medium text-white hover:bg-opacity-30 focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75"-->
<!--        >-->
<!--            Open dialog-->
<!--        </button>-->
<!--    </div>-->
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild
                as="template"
                enter="duration-300 ease-out"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100"
                leave-to="opacity-0"
            >
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div
                    class="flex min-h-full items-center justify-center p-4 text-center"
                >
                    <TransitionChild
                        as="template"
                        enter="duration-300 ease-out"
                        enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100"
                        leave="duration-200 ease-in"
                        leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95"
                    >
                        <DialogPanel
                            class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white px-5 py-5 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 62.051 62.051">
                                <g id="Group_4884" data-name="Group 4884" transform="translate(-73.036 -250.295)">
                                    <circle id="Ellipse_199" data-name="Ellipse 199" cx="31.025" cy="31.025" r="31.025" transform="translate(73.036 250.295)" fill="#70d44b"/>
                                    <path id="Path_3299" data-name="Path 3299" d="M351.649,476.232a3.45,3.45,0,0,1-2.761-1.381l-8.464-11.286a3.451,3.451,0,0,1,5.522-4.141l5.538,7.383,14.223-21.334a3.451,3.451,0,1,1,5.743,3.829l-16.928,25.393a3.453,3.453,0,0,1-2.776,1.536Z" transform="translate(-251.82 -178.764)" fill="#fff"/>
                                </g>
                            </svg>
                            <DialogTitle>
                                <h2 class="text-base font-semibold text-center mb-2 mt-2">Teşekkürler</h2>
                            </DialogTitle>
                            <p class="text-xs font-kiralabunuthin ts:text-sm text-center ">Mesajınız/talebiniz başarıyla oluşturuldu</p>
<!--                            <div class="mt-4">-->
<!--                                <button-->
<!--                                    type="button"-->
<!--                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"-->
<!--                                    @click="closeModal"-->
<!--                                >-->
<!--                                    Got it, thanks!-->
<!--                                </button>-->
<!--                            </div>-->
                            <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal">x</div>

                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script >
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue';

export default {

    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
    },
    data:() => ({
        isOpen: true
    }),
    methods:{
        closeModal() {
            console.log(this.isOpen)
            this.isOpen =false
         },
        openModal() {
            this.isOpen = true
        },
    }
}
</script>
