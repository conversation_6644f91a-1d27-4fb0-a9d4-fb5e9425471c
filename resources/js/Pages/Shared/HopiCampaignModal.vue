<script setup>
import { useForm } from "@inertiajs/inertia-vue3";
import { ref } from "vue";

const form = useForm({
    userToken: "",
});

let selectedHopiCampaign = ref(localStorage.getItem("selectedHopiCampaign") || false);
let enteredBalance = ref(localStorage.getItem("enteredHopiBalance") || 0);

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false,
    },
    campaigns: {
        type: Array,
        default: () => {},
    },
    balance: {
        type: Number,
        default: 0,
    },
    selectedHopiCampaign: {
        type: String,
        default: "",
    },
});

defineEmits(["update:isOpenCampaignModal", "update:selectedCampaign", "update:enteredBalance"]);

const submit = () => {
    form.clearErrors();
    // form.post("hopi-user-login", {});
};
</script>

<template>
    <!-- Main modal -->
    <div v-if="isOpen" id="default-modal" tabindex="-1" aria-hidden="true" class="flex justify-center items-center fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-black/30">
        <div class="relative w-full max-w-2xl max-h-full">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow">
                <!-- Modal header -->
                <!--                <div class="flex items-start justify-between rounded-t">-->
                <!--                    <button @click="$emit('update:isOpenCampaignModal', false)" type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="default-modal">-->
                <!--                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">-->
                <!--                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />-->
                <!--                        </svg>-->
                <!--                        <span class="sr-only">Close modal</span>-->
                <!--                    </button>-->
                <!--                </div>-->
                <!-- Modal body -->
                <div class="p-6 flex flex-wrap">
                    <div class="w-full md:w-8/12 border-2 p-3 lg:p-5 rounded-lg">
                        <h2 class="text-xl font-semibold text-hopi-pink">Hopi Kampanyaları</h2>
                        <!--                    <p class="mt-1 text-sm text-gray-500">Transfer your balance to your bank account.</p>-->
                        <fieldset class="mt-2">
                            <!--                        <legend class="sr-only">Bank account</legend>-->
                            <div class="divide-y divide-gray-200 h-44 lg:h-96 overflow-y-scroll scroll-smooth scroll-ml-1">
                                <div v-for="(account, accountIdx) in campaigns" :key="accountIdx" class="relative flex items-start py-1 mr-3">
                                    <label :for="`account-${accountIdx}-description`" class="flex font-medium text-gray-900 w-full">
                                        <div class="min-w-0 flex-1 text-sm leading-6 cursor-pointer">
                                            <p>{{ account.code }}</p>
                                            <p :id="`account-${accountIdx}-explain`" class="text-gray-500">{{ account.name }}</p>
                                            <!--                                            <p :id="`account-${accountIdx}`" class="text-gray-500 text-2xs">{{ account.type }}</p>-->
                                        </div>
                                        <div class="ml-3 flex h-6 items-center">
                                            <input
                                                v-model="selectedHopiCampaign"
                                                @click="$emit('update:selectedCampaign', $event.target.value)"
                                                :value="account.code"
                                                :id="`account-${accountIdx}-description`"
                                                :aria-describedby="`account-${accountIdx}-description`"
                                                name="account"
                                                type="radio"
                                                class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                                            />
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                    <div class="w-full md:w-4/12 text-sm p-3 lg:p-5 mt-8 lg:mt-0">
                        <div>
                            <p>Merhaba,</p>
                            <p>{{ balance }} TL Paracık'ınız bulunmaktadır.</p>
                            <p class="text-xs mt-7">Kampanya ya da Paracık kullanmak istemiyorsan seçip "Devam" butonuna basman yeterli.</p>
                        </div>
                        <div class="mt-4">
                            <div>Paracık Kullanımı:</div>
                            <div>
                                <input type="number" placeholder="0" class="p-1 mt-1 w-full" v-model="enteredBalance" :max="balance" @input="$emit('update:enteredBalance', $event.target.value)" :class="{ 'focus:border-red-400 border-3': enteredBalance > balance }" />
                            </div>
                            <p class="mt-5 text-xs">TL Karşılığı: <span v-html="enteredBalance <= balance ? enteredBalance + ' TL' : balance + ' TL\'den büyük olamaz'"></span></p>
                            <p class="mt-2 text-xs">Kalan Paracık: <span v-html="Math.ceil((balance - enteredBalance) * 100) / 100 > 0 ? Math.ceil((balance - enteredBalance) * 100) / 100 : 0"></span> Paracık</p>
                        </div>
                        <div class="mt-16">
                            <button :disabled="enteredBalance > balance" @click="$emit('update:isOpenCampaignModal', false)" class="disabled:opacity-30 bg-hopi-pink text-white w-full rounded-full my-2 py-2 self-center text-lg font-bold">Devam</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped></style>
