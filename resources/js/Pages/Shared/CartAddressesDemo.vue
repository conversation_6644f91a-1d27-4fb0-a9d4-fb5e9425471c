<script>
import { <PERSON> } from "@inertiajs/inertia-vue3";
import { vMaska } from "maska";
import { Dialog, DialogPanel, DialogTitle, Disclosure, DisclosureButton, DisclosurePanel, TransitionChild, TransitionRoot } from "@headlessui/vue";

export default {
    components: {
        Link,
        Disclosure,
        DisclosureButton,
        DisclosurePanel,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle
    },
    props: {
        user: Object,
        cities: Array,
        errors: { type: Object, default: false }
    },
    directives: { maska: vMaska },
    data() {
        return {
            isInvoiceSame: true,
            addressModalIsOpen: false,
            invoiceAddressModalIsOpen: false,
            selectedAddress: this.user.address,
            selectedInvoiceAddress: this.user.address,
            form: this.$inertia.form({
                id: this.user.address?.id ?? null,
                first_name: this.user.address?.first_name ?? null,
                last_name: this.user.address?.last_name ?? null,
                city: this.user.address?.city ?? null,
                city_id: this.user.address?.city_id ?? null,
                district: this.user.address?.district ?? null,
                county_id: this.user.address?.county_id ?? null,
                // birtdate: false,
                tckn: this.user.address?.tckn ?? null,
                phone: this.user.address?.phone ?? null,
                name: this.user.address?.name ?? null,
                address: this.user.address?.address ?? null,
                bill_type: this.user.address?.bill_type ?? "",
                firn_name: this.user.address?.firn_name ?? null,
                tax_no: this.user.address?.tax_no ?? null,
                tax_office: this.user.address?.tax_office ?? null
            }),
            newAddressForm: this.$inertia.form({
                id: null,
                first_name: null,
                last_name: null,
                city: null,
                city_id: null,
                district: null,
                county_id: null,
                // birtdate: false,
                tckn: null,
                phone: null,
                name: null,
                address: null,
                bill_type: "Bireysel",
                firn_name: null,
                tax_no: null,
                tax_office: null
            })
        };
    },
    methods: {
        formattedNumber(phoneNumber) {
            // "### ### ## ##" formatına göre işlem yap
            return phoneNumber.replace(/(\d{3})\s(\d{3})\s(\d{2})\s(\d{2})/, "$1 ### $3 $4");
        },
        closeAddressModal() {
            this.addressModalIsOpen = false;
        },
        selectOrderAddress(address) {
            this.selectedAddress = address;
        },
        selectOrderInvoiceAddress(address) {
            this.selectedInvoiceAddress = address;
        },
        openAddressModal() {
            this.addressModalIsOpen = true;
        },
        closeInvoiceAddressModal() {
            this.invoiceAddressModalIsOpen = false;
        },
        openInvoiceAddressModal() {
            this.invoiceAddressModalIsOpen = true;
        },
        submit() {
            this.form.post("/adres-duzenle-sepet", {
                preserveScroll: true,
                onSuccess: () => {
                    this.addressModalIsOpen = false;
                    this.invoiceAddressModalIsOpen = false;
                }
            });
        },
        addNewAddress() {
            this.newAddressForm.post("/adres-duzenle-sepet", {
                preserveScroll: true,
                onSuccess: () => {
                    this.addressModalIsOpen = false;
                    this.invoiceAddressModalIsOpen = false;
                }
            });
        },
        cityChanged() {
            this.form.county_id = null;
        },
        preventNumericInput($event) {
            //console.log($event.keyCode); will display the keyCode value
            //console.log($event.key); will show the key value

            var charCode = $event.keyCode ? $event.keyCode : $event.which;
            if (
                (charCode <= 93 && charCode >= 65) ||
                (charCode <= 122 && charCode >= 97) ||
                charCode == 8 ||
                charCode == 350 ||
                charCode == 351 ||
                charCode == 304 ||
                charCode == 286 ||
                charCode == 287 ||
                charCode == 231 ||
                charCode == 199 ||
                charCode == 305 ||
                charCode == 214 ||
                charCode == 246 ||
                charCode == 220 ||
                charCode == 252 ||
                charCode == 32
            ) {
                if (charCode != 33 || charCode != 43) {
                    return true;
                } else {
                    $event.preventDefault();
                }
            } else {
                $event.preventDefault();
            }
        }
    }
};
</script>

<template>
    <div v-if="false" class="w-full flex justify-between items-center border-2 border-bordergray rounded-lg p-3 mb-5">
        <div class="text-center w-full">
            <h3 class="mt-2 text-lg font-semibold text-gray-900 leading-tight">Kayıtlı Adresiniz bulunmamaktadır</h3>
            <div class="mt-2">
                <a @click="openAddressModal" class="cursor-pointer flex flex-col items-center justify-center h-full border-2 border-bordergray rounded-lg bg-[#f8f8f8] py-3.5 px-4 hover:bg-orange-100 hover:border-kbgreen"
                   :class="user.addresses.length != 0 ? 'w-full md:w-1/2' : 'w-full'">
                    <span class="text-kbgreen text-5xl">+</span>
                    <span class="text-xl text-black">Yeni Adres Ekle</span>
                </a>
            </div>
        </div>
    </div>
    <div class="flex md:flex-row flex-col md:items-center justify-between mb-3">
        <p class="text-base lg:text-2xl mb-1 text-black whitespace-nowrap">Teslimat Bilgisi</p>

        <div class="md:px-4 flex items-center">
            <input v-model="isInvoiceSame" :checked="isInvoiceSame" id="ayni" class="w-6 h-6 border-3 rounded-md focus:outline-none checked:bg-orange-500 text-orange-500 border-gray-300 focus:ring-orange-500" type="checkbox" />
            <label class="text-xs lg:text-base text-gray-600 pl-2 font-bold font-santralregular" for="ayni">Faturamı aynı adrese gönder</label>
        </div>
    </div>
    <!-- Teslimat Adresi Seçenekleri -->
    <div class="flex flex-col md:flex-row gap-4 items-stretch">
        <!-- Yeni Adres Ekle -->
        <a @click="openAddressModal" class="cursor-pointer flex flex-col items-center justify-center h-full border-2 border-bordergray rounded-lg bg-[#f8f8f8] py-3.5 px-4 md:mt-8 hover:bg-orange-100 hover:border-kbgreen"
           :class="user.addresses.length != 0 ? 'w-full md:w-1/2' : 'w-full'">
            <span class="text-kbgreen text-5xl">+</span>
            <span class="text-xl text-black">Yeni Adres Ekle</span>
        </a>

        <!-- Seçili Adres -->
        <div v-if="user.addresses.length != 0" class="flex flex-col w-full md:w-1/2 cursor-default" :class="user.addresses.length != 0 ? 'flex' : 'hidden'">
            <div class="w-full flex justify-between">
                <div class="flex justify-start items-center w-full mb-3 bg-white">
                    <input class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" id="secili-adres" name="secili-adres" checked />
                    <label class="pl-3 text-sm text-black font-bold cursor-pointer font-santralregular" for="secili-adres">Adres Adı</label>
                </div>
                <a href="#" class="w-1/2 text-black hover:underline text-base font-santralregular font-bold text-end mb-0 pb-0" @click="openInvoiceAddressModal">Düzenle</a>
            </div>
            <!-- Adres Kartı -->
            <div class="relative border-2 border-bordergray rounded-lg px-4 py-3 bg-white">
                <div class="flex items-start w-full">
                    <!-- Onay İşareti -->
                    <div class="w-1/2 flex items-end space-x-2">
                        <img src="../../../images/svg/user-red.svg" class="w-4 h-4" alt="" />
                        <h3 class="text-sm font-semibold font-santralregular text-gray-700">{{ selectedAddress.first_name }} {{ selectedAddress.last_name }}</h3>
                    </div>
                    <div class="flex items-center justify-end text-gray-700 w-1/2 space-x-1">
                        <img src="../../../images/svg/phone.svg" class="w-4 h-4" alt="" />

                        <span class="text-xs font-santralregular font-semibold" v-maska data-maska="### ### ## ##">{{ selectedAddress.phone }}</span>
                    </div>
                </div>
                <p class="text-sm font-santralregular font-bold text-black mt-3 w-full leading-6 h-[48px] overflow-y-hidden overflow-ellipsis">
                    {{ selectedAddress.address }}
                </p>
                <!-- Düzenle Bağlantısı -->
            </div>
        </div>
    </div>
    <Disclosure as="div" v-slot="{ open }" v-if="user.addresses.length != 0">
        <DisclosureButton class="w-full flex justify-center mt-6" v-if="user.addresses.length > 1">
            <span class="px-8 py-2 md:text-xl font-santralextrabold text-black border-2 border-kbgreen rounded-lg hover:bg-kbgreen hover:text-white flex space-x-2 items-center">
                <span class="whitespace-nowrap">Diğer Teslimat Adreslerim</span>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="h-8 w-8 text-black transition-all duration-300" :class="!open ? 'rotate-180 transform' : ''">
                    <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd"></path>
                </svg>
            </span>
        </DisclosureButton>
        <DisclosurePanel class="flex flex-wrap items-stretch">
            <template v-for="item in user.addresses">
                <div class="flex flex-col w-full md:w-1/2 cursor-default p-4 pl-0">
                    <div class="w-full flex justify-between">
                        <div class="flex justify-start items-center w-full mb-3 bg-white">
                            <input :checked="selectedAddress.id == item.id" class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="checkbox" :id="'secili-adres-' + item.id" name="secili-adres" />
                            <label class="pl-3 text-sm text-black font-bold cursor-pointer font-santralregular" :for="'secili-adres-' + item.id">{{ item.name }}</label>
                        </div>
                        <a @click="openInvoiceAddressModal" class="w-1/2 text-black hover:underline text-base font-santralregular font-bold text-end mb-0 pb-0">Düzenle</a>
                    </div>
                    <!-- Adres Kartı -->
                    <div class="relative border-2 hover:border-kbgreen rounded-lg px-4 py-5 bg-white cursor-pointer min-h-[130px]" @click="selectOrderAddress(item)"
                         :class="selectedAddress.id == item.id ? 'border-kbgreen bg-orange-100' : 'border-bordergray'">
                        <div class="flex items-start w-full">
                            <!-- Onay İşareti -->
                            <div class="w-2/3 flex items-end space-x-2">
                                <img src="../../../images/svg/user-red.svg" class="w-4 h-4" />
                                <h3 class="text-sm font-semibold font-santralregular text-gray-700">{{ item.first_name + " " + item.last_name }}</h3>
                            </div>
                            <div class="flex items-center justify-end text-gray-700 w-1/3 space-x-1">
                                <img src="../../../images/svg/phone.svg" class="w-4 h-4" alt="" />
                                <span class="text-xs font-santralregular font-semibold">{{ item.phone }}</span>
                            </div>
                        </div>
                        <p class="text-base font-santralregular font-semibold text-black mt-4 w-full leading-6 max-h-[48px] overflow-y-hidden overflow-ellipsis">
                            {{ item.address }}
                        </p>
                        <!-- Düzenle Bağlantısı -->
                    </div>
                </div>
            </template>
        </DisclosurePanel>
    </Disclosure>

    <!--    <div v-if="user.addresses.length == 0" class="w-full flex justify-between items-center border-2 border-bordergray rounded-lg p-3 mb-5">-->
    <!--        <div class="text-center w-full">-->
    <!--            <h3 class="mt-2 text-sm font-semibold text-gray-900">Adresiniz bulunmamaktadır</h3>-->
    <!--            <div class="mt-2">-->
    <!--                <button type="button" @click="openAddressModal" class="bg-black text-white text-center rounded-full py-1 text-lg font-bold hover:bg-kbgreen inline-flex items-center rounded-md px-3 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2">Adres Ekle</button>-->
    <!--            </div>-->
    <!--        </div>-->
    <!--    </div>-->

    <div class="flex items-center justify-between lg:mb-3 mt-5 lg:mt-3" v-if="!isInvoiceSame">
        <p class="text-2xl mb-1 text-black">Fatura Adres Bilgisi</p>
    </div>
    <div class="flex flex-wrap items-stretch" v-if="!isInvoiceSame">
        <template v-for="item in user.addresses">
            <div class="flex flex-col w-full md:w-1/2 cursor-default p-4 pl-0">
                <div class="w-full flex justify-between">
                    <div class="flex justify-start items-center w-full mb-3 bg-white">
                        <input :checked="selectedInvoiceAddress.id == item.id" class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="checkbox" :id="'secili-fatura-adres-' + item.id"
                               name="secili-fatura-adres" />
                        <label class="pl-3 text-sm text-black font-bold cursor-pointer font-santralregular" :for="'secili-fatura-adres-' + item.id">{{ item.name }}</label>
                    </div>
                    <a @click="openInvoiceAddressModal" class="w-1/2 text-black hover:underline text-base font-santralregular font-bold text-end mb-0 pb-0">Düzenle</a>
                </div>
                <div class="relative border-2 hover:border-kbgreen rounded-lg px-4 py-5 bg-white cursor-pointer min-h-[130px]" @click="selectOrderInvoiceAddress(item)"
                     :class="selectedInvoiceAddress.id == item.id ? 'border-kbgreen bg-orange-100' : 'border-bordergray'">
                    <div class="flex items-start w-full">
                        <div class="w-2/3 flex items-end space-x-2">
                            <img src="../../../images/svg/user-red.svg" class="w-4 h-4" />
                            <h3 class="text-sm font-semibold font-santralregular text-gray-700">{{ item.first_name + " " + item.last_name }}</h3>
                        </div>
                        <div class="flex items-center justify-end text-gray-700 w-1/3 space-x-1">
                            <img src="../../../images/svg/phone.svg" class="w-4 h-4" alt="" />

                            <span class="text-xs font-santralregular font-semibold">{{ item.phone }}</span>
                        </div>
                    </div>
                    <p class="text-base font-santralregular font-semibold text-black mt-4 w-full leading-6 max-h-[48px] overflow-y-hidden overflow-ellipsis">
                        {{ item.address }}
                    </p>
                </div>
            </div>
        </template>
    </div>

    <TransitionRoot appear :show="addressModalIsOpen" as="template">
        <Dialog as="div" @close="closeAddressModal" class="relative z-50">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>
            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="max-w-[350px] lg:max-w-[750px] transform overflow-hidden rounded-sm bg-white px-1 py-1 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                            <div class="p-4 mx-auto bg-white rounded-lg shadow-md">
                                <div class="flex justify-center items-center mb-6">
                                    <h2 class="text-2xl font-semibold text-black text-center">Yeni Adres Ekle</h2>
                                    <button class="absolute right-4 top-4 text-black hover:text-gray-700 focus:outline-none px-2 py-1 bg-bordergray rounded transition-all hover:bg-white hover:shadow-aboutshadow" @click="closeAddressModal">
                                        <span class="font-santralregular font-bold">X</span>
                                    </button>
                                </div>
                                <form @submit.prevent="addNewAddress">

                                    <!-- <pre>{{ newAddressForm }}</pre> -->
                                    <div class="flex flex-row justify-center items-start w-full">
                                        <div class="hidden md:block w-6/12 text-center px-3">
                                            <div class="relative group">
                                                <input v-model="newAddressForm.first_name" id="name" @keypress="preventNumericInput"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="" name="first_name" autofocus />
                                                <label for="first_name"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adın*
                                                </label>
                                            </div>
                                            <div class="relative mt-2">
                                                <label for="cityselect" class="transform transition-all absolute -top-4 left-1 px-1 text-xs text-black">İl* </label>
                                                <select v-model="newAddressForm.city_id" @change="cityChanged"
                                                        class="w-full px-2 py-2 mb-5 rounded-lg border border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="cityselect"
                                                        id="cityselect">
                                                    <template v-for="(city, index) in [...new Map(cities.map((m) => [m.city_id, m])).values()]" :key="index">
                                                        <option :value="city.city_id">{{ city.city }}</option>
                                                    </template>
                                                </select>
                                            </div>

                                            <div class="group relative">
                                                <input id="addressname" v-model="newAddressForm.name"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="" name="addressname" />
                                                <label for="addressname"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adres
                                                    Başlığı*
                                                </label>
                                            </div>
                                        </div>
                                        <div class="hidden md:block w-6/12 text-center px-3 relative group">
                                            <input id="last_name" v-model="newAddressForm.last_name" @keypress="preventNumericInput"
                                                   class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                   required type="text" placeholder="" name="last_name" />
                                            <label for="last_name"
                                                   class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Soyadın*
                                            </label>
                                            <div class="relative mt-2">
                                                <label for="countryselect" class="transform transition-all absolute -top-4 left-1 px-1 text-xs text-black">İlçe* </label>
                                                <select v-model="newAddressForm.county_id"
                                                        class="w-full px-2 py-2 mb-5 rounded-lg border border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="countryselect"
                                                        id="countryselect" required>
                                                    <template v-for="(county, index) in cities.filter((c) => c.city_id == this.newAddressForm.city_id)" :key="index">
                                                        <option :value="county.county_id">{{ county.county }}</option>
                                                    </template>
                                                </select>
                                            </div>
                                            <div class="relative group">
                                                <input v-model="newAddressForm.phone" v-maska data-maska="(5##) ### ## ##" id="phone"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="" name="phone" />
                                                <label for="phone"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Telefon*
                                                </label>
                                            </div>
                                        </div>

                                        <div class="block md:hidden w-full text-center px-3">
                                            <div class="group relative">
                                                <input v-model="newAddressForm.first_name"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="" id="first_name_2" name="first_name_2" autofocus />
                                                <label for="first_name_2"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adın*
                                                </label>
                                            </div>
                                            <div class="group relative">
                                                <input v-model="newAddressForm.last_name"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="" id="last_name_2" name="last_name_2" />
                                                <label for="last_name_2"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Soyadın*</label>
                                            </div>
                                            <div class="relative group">
                                                <input v-model="newAddressForm.phone" v-maska data-maska="(5##) ### ## ##" id="phone_2"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="" name="phone_2" />
                                                <label for="phone_2"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Telefon*</label>
                                            </div>
                                            <div class="relative mt-2">
                                                <label for="city_2" class="transform transition-all absolute -top-4 left-1 px-1 text-xs text-black">İl* </label>
                                                <select v-model="newAddressForm.city_id"
                                                        class="w-full px-2 py-2 mb-5 rounded-lg border border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider" name="city_2"
                                                        id="city_2">
                                                    <option>İl Seç*</option>
                                                    <template v-for="(city, index) in [...new Map(cities.map((m) => [m.city_id, m])).values()]" :key="index">
                                                        <option :value="city.city_id">{{ city.city }}</option>
                                                    </template>
                                                </select>
                                            </div>
                                            <div class="relative mt-2">
                                                <label for="country_2" class="transform transition-all absolute -top-4 left-1 px-1 text-xs text-black">İlçe* </label>
                                                <select v-model="newAddressForm.county_id"
                                                        class="w-full px-2 py-2 mb-5 rounded-lg border border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider"
                                                        name="country_2" id="country_2">
                                                    <option value="">İlçe seç*</option>
                                                    <template v-for="(county, index) in cities.filter((c) => c.city_id == this.newAddressForm.city_id)" :key="index">
                                                        <option :value="county.county_id">{{ county.county }}</option>
                                                    </template>
                                                </select>
                                            </div>
                                            <div class="group relative">
                                                <input v-model="newAddressForm.name"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" id="addressname_2" placeholder="" name="addressname_2" />
                                                <label for="addressname_2"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adres
                                                    Başlığı*</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full px-3">
                                        <div class="group relative">
                                            <textarea id="address"
                                                      class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-lg tracking-wider placeholder:mt-2 h-32"
                                                      required name="Adres" placeholder="" cols="20" rows="10" v-model="newAddressForm.address">
                                            </textarea>
                                            <label for="address"
                                                   class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">
                                                Adresin*
                                            </label>
                                        </div>
                                        <div class="p-4">
                                            <!-- Fatura Türü -->
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Fatura Türü*</label>
                                            <div class="flex overflow-hidden">
                                                <!-- Bireysel Butonu -->
                                                <div class="border border-bordergray w-1/2 rounded-l-lg py-2 text-center text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:z-10"
                                                     :class="newAddressForm.bill_type == 'Bireysel' ? ' border-kbgreen' : ''" @click="newAddressForm.bill_type = 'Bireysel'">
                                                    Bireysel
                                                </div>
                                                <!-- Kurumsal Butonu -->
                                                <div class="border border-bordegray w-1/2 rounded-r-lg py-2 text-center text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:z-10"
                                                     :class="newAddressForm.bill_type == 'Kurumsal' ? ' border-kbgreen' : ''" @click="newAddressForm.bill_type = 'Kurumsal'">
                                                    Kurumsal
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex flex-wrap lg:flex-nowrap justify-between">
                                            <!--                                            <select v-model="form.bill_type" class="w-full lg:w-3/12 mr-2 w-full px-2 py-2 mb-5 rounded-lg border border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="sirala" id="sirala" required>-->
                                            <!--                                                <option>Fatura Türü*</option>-->
                                            <!--                                                <option value="Bireysel">Bireysel</option>-->
                                            <!--                                                <option value="Kurumsal">Kurumsal</option>-->
                                            <!--                                            </select>-->
                                            <div class="w-full flex flex-wrap md:px-4" v-if="newAddressForm.bill_type == 'Kurumsal'">
                                                <div class="group relative w-full">
                                                    <input v-model="newAddressForm.firn_name" id="companyname"
                                                           class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                           required type="text" placeholder="" name="Firma" />
                                                    <label for="companyname"
                                                           class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Firma
                                                        Adı</label>
                                                </div>
                                                <div class="group relative w-full">
                                                    <input v-model="newAddressForm.tax_no" id="taxno"
                                                           class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                           required type="text" placeholder="" name="Numarası" />
                                                    <label for="taxno"
                                                           class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Vergi
                                                        Numarası
                                                    </label>
                                                </div>
                                                <div class="group relative w-full">
                                                    <input id="taxname" v-model="newAddressForm.tax_office"
                                                           class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                           required type="text" placeholder="" name="Dairesi" />
                                                    <label for="taxname"
                                                           class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Vergi
                                                        Dairesi</label>
                                                </div>
                                            </div>
                                            <div class="w-full flex md:px-3" v-if="newAddressForm.bill_type == 'Bireysel'">
                                                <input v-maska data-maska="###########" v-model="newAddressForm.tckn"
                                                       class="w-full mb-5 mr-2 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="Tc Kimlik Numaran *" name="tckn" />
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <ul v-for="error in errors">
                                            <li>{{ error }}</li>
                                        </ul>
                                    </div>
                                    <div class="relative group w-full flex justify-end my-2">
                                        <button type="submit" class="bg-black text-white text-center rounded-full py-2 text-lg px-2 self-center font-bold w-full md:w-[200px;] hover:bg-kbgreen" :disabled="newAddressForm.processing">Kaydet</button>
                                    </div>
                                </form>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
    <TransitionRoot appear :show="invoiceAddressModalIsOpen" as="template">
        <Dialog as="div" @close="closeInvoiceAddressModal" class="relative z-50">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>
            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="max-w-[350px] lg:max-w-[750px] transform overflow-hidden rounded-sm bg-white px-1 py-1 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                            <div class="p-4 mx-auto bg-white rounded-lg shadow-md">
                                <div class="flex justify-center items-center mb-6">
                                    <h2 class="text-2xl font-semibold text-black text-center">Adres Düzenle</h2>
                                    <button class="absolute right-4 top-4 text-black hover:text-gray-700 focus:outline-none px-2 py-1 bg-bordergray rounded transition-all hover:bg-white hover:shadow-aboutshadow" @click="closeInvoiceAddressModal">
                                        <span class="font-santralregular font-bold">X</span>
                                    </button>
                                </div>
                                <form @submit.prevent="submit">
                                    <div class="flex flex-row justify-center items-start w-full">
                                        <div class="hidden md:block w-6/12 text-center px-3">
                                            <div class="relative group">
                                                <input v-model="form.first_name" id="name" @keypress="preventNumericInput"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="" name="AdSoyad" autofocus />
                                                <label for="name"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adın*</label>
                                            </div>
                                            <select v-model="form.city_id" @change="cityChanged"
                                                    class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="sirala" id="ilsec">
                                                <option value="">İl Seç*</option>
                                                <template v-for="(city, index) in [...new Map(cities.map((m) => [m.city_id, m])).values()]" :key="index">
                                                    <option :value="city.city_id">{{ city.city }}</option>
                                                </template>
                                            </select>

                                            <div class="group relative">
                                                <input id="addressname" v-model="form.name"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="" name="adresadi" />
                                                <label for="addressname"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adres
                                                    Başlığı*</label>
                                            </div>
                                        </div>
                                        <div class="hidden md:block w-6/12 text-center px-3 relative group">
                                            <input id="surname" v-model="form.last_name" @keypress="preventNumericInput"
                                                   class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                   required type="text" placeholder="" name="Soyad" />
                                            <label for="surname"
                                                   class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Soyadın*</label>
                                            <select v-model="form.county_id" class="w-full px-2 py-2 mb-5 rounded-lg border border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider"
                                                    name="sirala" id="sirala" required>
                                                <option value="" selected>İlçe seç*</option>
                                                <template v-for="(county, index) in cities.filter((c) => c.city_id == this.form.city_id)" :key="index">
                                                    <option :value="county.county_id">{{ county.county }}</option>
                                                </template>
                                            </select>
                                            <div class="relative group">
                                                <input v-model="form.phone" v-maska data-maska="(5##) ### ## ##" id="telephone"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="" name="telno" />
                                                <label for="telephone"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Telefon*</label>
                                            </div>
                                        </div>

                                        <div class="block md:hidden w-full text-center px-3">
                                            <div class="group relative">
                                                <input v-model="form.first_name"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="" id="namemobile2" name="AdSoyad" autofocus />
                                                <label for="namemobile2"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adın*</label>
                                            </div>
                                            <div class="group relative">
                                                <input v-model="form.last_name"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="" id="surnamemobile2" name="Soyad" />
                                                <label for="surnamemobile2"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Soyadın*</label>
                                            </div>
                                            <div class="relative group">
                                                <input v-model="form.phone" v-maska data-maska="(5##) ### ## ##" id="telephonemobile2"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="" name="telno" />
                                                <label for="telephonemobile2"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Telefon*</label>
                                            </div>
                                            <select v-model="form.city_id"
                                                    class="w-full px-2 py-2 mb-5 rounded-lg border border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider" name="citymobile2"
                                                    id="citymobile2">
                                                <option>İl Seç*</option>
                                                <template v-for="(city, index) in [...new Map(cities.map((m) => [m.city_id, m])).values()]" :key="index">
                                                    <option :value="city.city_id">{{ city.city }}</option>
                                                </template>
                                            </select>
                                            <select v-model="form.county_id"
                                                    class="w-full px-2 py-2 mb-5 rounded-lg border border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider"
                                                    name="countrymobile2" id="countrymobile2">
                                                <option value="">İlçe seç*</option>
                                                <template v-for="(county, index) in cities.filter((c) => c.city_id == this.form.city_id)" :key="index">
                                                    <option :value="county.county_id">{{ county.county }}</option>
                                                </template>
                                            </select>

                                            <div class="group relative">
                                                <input v-model="form.name"
                                                       class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" id="addressnamemobile2" placeholder="" name="adresadi" />
                                                <label for="addressnamemobile2"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adres
                                                    Başlığı*</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-full px-3">
                                        <div class="group relative">
                                            <textarea id="address"
                                                      class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-lg tracking-wider placeholder:mt-2 h-32"
                                                      required name="Adres" placeholder="" cols="20" rows="10" v-model="form.address"></textarea>
                                            <label for="address"
                                                   class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adresin*</label>
                                        </div>
                                        <div class="p-4">
                                            <!-- Fatura Türü -->
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Fatura Türü*</label>
                                            <div class="flex overflow-hidden">
                                                <!-- Bireysel Butonu -->
                                                <div class="border border-bordergray w-1/2 rounded-l-lg py-2 text-center text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:z-10"
                                                     :class="form.bill_type == 'Bireysel' ? ' border-kbgreen' : ''" @click="form.bill_type = 'Bireysel'">
                                                    Bireysel
                                                </div>

                                                <!-- Kurumsal Butonu -->
                                                <div class="border border-bordegray w-1/2 rounded-r-lg py-2 text-center text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:z-10"
                                                     :class="form.bill_type == 'Kurumsal' ? ' border-kbgreen' : ''" @click="form.bill_type = 'Kurumsal'">Kurumsal
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex flex-wrap lg:flex-nowrap justify-between">
                                            <!--                                            <select v-model="form.bill_type" class="w-full lg:w-3/12 mr-2 w-full px-2 py-2 mb-5 rounded-lg border border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="sirala" id="sirala" required>-->
                                            <!--                                                <option>Fatura Türü*</option>-->
                                            <!--                                                <option value="Bireysel">Bireysel</option>-->
                                            <!--                                                <option value="Kurumsal">Kurumsal</option>-->
                                            <!--                                            </select>-->
                                            <div class="w-full flex flex-wrap md:px-4" v-if="form.bill_type == 'Kurumsal'">
                                                <div class="group relative w-full">
                                                    <input v-model="form.firn_name" id="companyname"
                                                           class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                           required type="text" placeholder="" name="Firma" />
                                                    <label for="companyname"
                                                           class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Firma
                                                        Adı</label>
                                                </div>
                                                <div class="group relative w-full">
                                                    <input v-model="form.tax_no" id="taxno"
                                                           class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                           required type="text" placeholder="" name="Numarası" />
                                                    <label for="taxno"
                                                           class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Vergi
                                                        Numarası
                                                    </label>
                                                </div>
                                                <div class="group relative w-full">
                                                    <input id="taxname" v-model="form.tax_office"
                                                           class="peer w-full mb-5 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                           required type="text" placeholder="" name="Dairesi" />
                                                    <label for="taxname"
                                                           class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Vergi
                                                        Dairesi</label>
                                                </div>
                                            </div>
                                            <div class="w-full flex md:px-3" v-if="form.bill_type == 'Bireysel'">
                                                <input v-maska data-maska="###########" v-model="form.tckn"
                                                       class="w-full mb-5 mr-2 rounded-lg border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                       required type="text" placeholder="Tc Kimlik Numaran *" name="tckn" />
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <ul v-for="error in errors">
                                            <li>{{ error }}</li>
                                        </ul>
                                    </div>
                                    <div class="relative group w-full flex justify-end my-2">
                                        <button type="submit" class="bg-black text-white text-center rounded-full py-2 text-lg px-2 self-center font-bold w-full md:w-[200px;] hover:bg-kbgreen" :disabled="form.processing">Kaydet</button>
                                    </div>
                                </form>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>
