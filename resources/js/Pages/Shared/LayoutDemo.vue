<template>
    <div class="promotion-bar w-full px-5 py-1 bg-[#b7e8a3] flex justify-center border-1 border-[#b7e8a3] mb-3" v-if="topBannerShow">
        <div class="font-bold text-sm hidden lg:block">Yeni üyelere özel 6 ay ve üzeri kiralamaların ilk ayında 500 tl
            indirim Kiralabunu’dan hediye! İndirim kodu: merhaba500
        </div>
        <div class="font-bold text-xs font-santralregular block lg:hidden text-center">Yeni üyelere özel 6 ay ve üzeri
            kiralamaların ilk ayında 500 tl indirim Kiralabunu’dan hediye! İndirim kodu: merhaba500
        </div>
    </div>
    <BasketNotification :cart="auth?.cart.items" :show="$page.props.flash.basketStatus?.type == 'added' ? true : false // setTimeout(() => { // basketNotificationShown = false; // }, 2000)
        " />
    <div class="">
        <header class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-[100%] ts:px-[2%] 2xl:px-0 2xl:max-w-7xl mt-8 md:mt-6 ts:mt-3 2xl:mt-8">
            <div class="flex lg:hidden">
                <div class="mr-4 w-1/2">
                    <div v-if="logoLink == '/'">
                        <a :href="logoLink"><img class="max-h-[45px] mx-auto w-full" :id="this.$inertia.page.url" :src="logoUrl" alt="" /></a>
                        <img v-if="isAynet" class="max-h-[35px] ml-2" src="https://static.ticimax.cloud/54992/uploads/editoruploads/3aynet.png" alt="Aynet Logo" />
                    </div>
                    <div v-else>
                        <a :href="logoLink"><img class="max-h-[45px] mx-auto w-full" :id="this.$inertia.page.url" :src="logoUrl" alt="" /></a>
                        <img v-if="isAynet" class="max-h-[35px] ml-2" src="https://static.ticimax.cloud/54992/uploads/editoruploads/3aynet.png" alt="Aynet Logo" />
                    </div>
                </div>
                <div class="w-1/2 flex justify-end">
                    <ul class="flex relative z-50" v-if="!auth.isUserLoggedIn && institutionalLayout != 'demo'">
                        <li class="text-sm md:text-base block">
                            <Menu as="div" class="relative inline-block text-left h-[35px] w-[35px] md:h-[45px] md:w-[45px]">
                                <MenuButton>
                                    <img src="../../../images/svg/user.svg">
                                </MenuButton>

                                <transition enter-active-class="transition duration-100 ease-out" enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100" leave-active-class="transition duration-75 ease-in"
                                    leave-from-class="transform scale-100 opacity-100" leave-to-class="transform scale-95 opacity-0">
                                    <MenuItems class="absolute -right-20 mt-2 bg-white rounded-lg shadow-lg p-4 w-52 origin-top-right ring-1 ring-black/5 focus:outline-none">
                                        <h2 class="text-lg font-bold text-gray-800 text-center">
                                            Hoş Geldin 👋
                                        </h2>
                                        <!-- Giriş Yap Butonu -->
                                        <div class="mt-2">
                                            <a href="/giris-yap" class="block text-center w-full border-2 border-transparent bg-kbgreen text-white font-santralextrabold py-1 rounded-2lg hover:bg-white hover:text-black hover:border-black transition">
                                                Giriş Yap
                                            </a>
                                        </div>
                                        <!-- Üye Ol Butonu -->
                                        <div class="mt-2">
                                            <a href="/hesap-olustur" class="block text-center w-full border-2 border-black text-gray-800 font-santralextrabold py-1 rounded-2lg hover:bg-kbgreen hover:text-white hover:border-transparent transition">
                                                Üye Ol
                                            </a>
                                        </div>
                                        <!-- Menü Alt Bilgisi -->
                                        <div class="mt-2 border-t border-gray-200 pt-4 text-xs text-gray-600">
                                            <ul class="space-y-1">
                                                <li><a href="/nasil-calisir" class="hover:text-gray-800 transition">Nasıl Çalışır</a></li>
                                                <li><a href="/sikca-sorulan-sorular" class="hover:text-gray-800 transition">Sıkça Sorulan Sorular</a>
                                                </li>
                                                <li><a href="/hakkimizda" class="hover:text-gray-800 transition">Hakkımızda</a></li>
                                            </ul>
                                        </div>
                                    </MenuItems>
                                </transition>
                            </Menu>
                        </li>
                        <li class="text-sm md:text-base relative flex justify-center items-center h-[35px] w-[35px] md:h-[45px] md:w-[45px]">
                            <Link href="/sepetim">
                            <img class="w-6 h-6" src="../../../images/svg/cart.svg">
                            </Link>
                        </li>
                    </ul>
                    <ul class="flex" v-if="auth.isUserLoggedIn && institutionalLayout != 'demo'">
                        <li class="text-sm md:text-base relative h-[35px] w-[35px] md:h-[45px] md:w-[45px]">
                            <Link href="/profili-duzenle" class="flex">
                            <img src="../../../images/svg/user.svg">
                            </Link>
                        </li>
                        <li class="text-sm md:text-base flex justify-center items-center relative h-[35px] w-[35px] md:h-[45px] md:w-[45px]">
                            <Link href="/istek-listem" class="flex">
                            <!--                                <span class="font-santralextrabold text-xs lg:text-2xs 2xl:text-xs text-white bg-kbgreen rounded-full absolute w-4 h-4 font-normal bottom-1 right-1 flex justify-center items-center leading-none">{{ wishlist }}</span>-->
                            <svg class="w-6 h-6 ml-1 stroke-[#231f20] overflow-visible" viewBox="0 0 30.669 28.092">
                                <g id="heart" transform="translate(0 -1.511)" fill="none">
                                    <path
                                        d="M30.071,6.622A13.219,13.219,0,0,1,27.842,18.6,33.873,33.873,0,0,1,22.3,24.456c-1.823,1.7-5.9,5.054-6.983,5.147-.956-.182-2.028-1.263-2.787-1.82-4.264-3.24-8.853-7.181-11.18-11.49C-.6,12.156-.6,7.039,2.433,3.867a8.977,8.977,0,0,1,12.885.853A8.726,8.726,0,0,1,18.3,2.245a8.977,8.977,0,0,1,11.77,4.377Z"
                                        stroke="none" />
                                    <path
                                        d="M 22.02387619018555 4.511138916015625 C 21.19887924194336 4.511205673217773 20.36529541015625 4.668869018554688 19.54391098022461 4.980022430419922 C 18.81119155883789 5.373607635498047 18.20499801635742 5.888843536376953 17.69393539428711 6.551767349243164 L 15.37918663024902 9.55436897277832 L 12.98918724060059 6.611328125 C 11.93668746948242 5.315288543701172 10.24892616271973 4.541538238525391 8.474416732788086 4.541547775268555 C 7.442102432250977 4.541547775268555 5.927898406982422 4.800741195678711 4.52613639831543 6.020847320556641 C 3.402303695678711 7.246023178100586 3.083917617797852 8.801969528198242 3.016265869140625 9.90241813659668 C 2.916469573974609 11.52574920654297 3.276287078857422 13.31153869628906 4.030189514160156 14.94011974334717 C 4.942806243896484 16.60567092895508 6.383369445800781 18.40690231323242 8.313325881958008 20.29546737670898 C 9.916957855224609 21.86470985412598 11.82877540588379 23.48118019104004 14.32891082763672 25.38162803649902 C 14.57612991333008 25.56426811218262 14.81914901733398 25.76386070251465 15.05441665649414 25.95709800720215 C 15.1480712890625 26.03402137756348 15.26153087615967 26.12721252441406 15.37966251373291 26.22183418273926 C 16.63759994506836 25.36079597473145 18.77116775512695 23.64326858520508 20.25698661804199 22.26020812988281 L 20.30599594116211 22.2145881652832 L 20.35699653625488 22.17119789123535 C 22.4555778503418 20.38569831848145 24.10916709899902 18.6432991027832 25.41224670410156 16.84445953369141 L 25.43560600280762 16.81265830993652 C 27.5122241973877 14.02390670776367 28.17683982849121 10.72176933288574 27.27249336242676 7.72297477722168 C 26.7924976348877 6.758964538574219 26.07523345947266 5.959152221679688 25.18952560424805 5.402528762817383 C 24.26146697998047 4.819278717041016 23.16680717468262 4.511037826538086 22.02387619018555 4.511138916015625 M 22.02363014221191 1.511137008666992 C 25.47772216796875 1.510845184326172 28.58621597290039 3.41154670715332 30.07120704650879 6.62156867980957 L 30.07119750976562 6.62156867980957 C 31.46405601501465 10.92536735534668 30.29865646362305 15.30498790740967 27.84177589416504 18.60439872741699 C 26.22061729431152 20.84233856201172 24.28254699707031 22.77018737792969 22.30101776123047 24.45609855651855 C 20.47795677185059 26.15307807922363 16.39905548095703 29.51041793823242 15.31799697875977 29.60327911376953 C 14.36215591430664 29.42096900939941 13.28961753845215 28.33990859985352 12.53142738342285 27.78361892700195 C 8.267665863037109 24.54384803771973 3.678485870361328 20.60295867919922 1.351097106933594 16.29318809509277 C -0.5997524261474609 12.15636825561523 -0.6031627655029297 7.038997650146484 2.433006286621094 3.867378234863281 C 6.3704833984375 0.318359375 12.3065242767334 1.011802673339844 15.31799697875977 4.720129013061523 C 16.12644577026367 3.671438217163086 17.12146759033203 2.845949172973633 18.30135726928711 2.244508743286133 C 19.5473575592041 1.74708366394043 20.80727195739746 1.511240005493164 22.02363014221191 1.511137008666992 Z"
                                        stroke="none" fill="#231f20" />
                                </g>
                            </svg>
                            </Link>
                        </li>
                        <li class="text-sm md:text-base relative flex justify-center items-center h-[35px] w-[35px] md:h-[45px] md:w-[45px]">
                            <Link href="/sepetim" class="">
                            <!--                                <span class="font-santralextrabold text-xs lg:text-2xs 2xl:text-xs text-white bg-kbgreen rounded-full absolute w-4 h-4 font-normal bottom-1 right-1 flex justify-center items-center leading-none">{{ auth.cart.items.length }}</span>-->
                            <img class="w-6 h-6" src="../../../images/svg/cart.svg">
                            </Link>
                        </li>
                    </ul>
                </div>
            </div>
            <section class="flex flex-row justify-between relative" v-if="institutionalLayout != 'demo'">
                <div class="hidden ts:flex items-end justify-between">
                    <div class="block md:hidden cursor-pointer py-2" @mouseenter="menu = true" @mouseleave="menu = false">
                        <svg id="menu-right" class="mx-2 md:mx-0" xmlns="http://www.w3.org/2000/svg" width="32.216" height="22.55" viewBox="0 0 32.216 22.55">
                            <path id="Path_3735" data-name="Path 3735" d="M18,27.118a1.6,1.6,0,0,0,1.6,1.6H32.5a1.6,1.6,0,0,0,0-3.205H19.6a1.6,1.6,0,0,0-1.6,1.6Z" transform="translate(-18 -6.17)" fill="#000000" />
                            <path id="Path_3736" data-name="Path 3736" d="M3,18.11a1.6,1.6,0,0,0,1.6,1.6h29.01a1.6,1.6,0,1,0,0-3.205H4.6a1.6,1.6,0,0,0-1.6,1.6Z" transform="translate(-2.999 -6.835)" fill="#000000" />
                            <path id="Path_3737" data-name="Path 3737" d="M13.6,10.705a1.6,1.6,0,0,1,0-3.205H32.948a1.6,1.6,0,0,1,0,3.205H13.6Z" transform="translate(-11.999 -7.5)" fill="#000000" />
                        </svg>
                    </div>

                    <div class="mr-4 hidden md:block">
                        <div v-if="logoLink == '/'" class="flex items-center">
                            <a :href="logoLink"><img class="max-h-[45px] mx-auto w-full" :id="this.$inertia.page.url" :src="logoUrl" alt="" /></a>
                            <img v-if="isAynet" class="max-h-[35px] ml-2" src="https://static.ticimax.cloud/54992/uploads/editoruploads/3aynet.png" alt="Aynet Logo" />
                        </div>
                        <div v-else class="flex items-center">
                            <a :href="logoLink"><img class="max-h-[45px] mx-auto w-full" :id="this.$inertia.page.url" :src="logoUrl" alt="" /></a>
                            <img v-if="isAynet" class="max-h-[35px] ml-2" src="https://static.ticimax.cloud/54992/uploads/editoruploads/3aynet.png" alt="Aynet Logo" />
                        </div>
                    </div>
                </div>
                <div class="hidden ts:flex items-center" v-if="institutionalLayout != 'demo'">
                    <div class="group border rounded-xl flex bg-white items-center relative min-w-[330px] mr-6">
                        <input type="text" class="peer placeholder-transparent w-full border-none rounded-xl p-2 pl-10 text-xs font-santralregular font-semibold" v-model="searchForm.term" @keydown.enter="submitSearchForm" id="searchform"
                            placeholder="Marka, ürün adı veya kategori yaz..." />
                        <label for="searchform"
                            class=" whitespace-nowrap font-santralregular leading-none absolute left-12 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-transparent text-xs peer-placeholder-shown:text-[11px] md:peer-placeholder-shown:text-sm peer-placeholder-shown:text-checkoutgray peer-placeholder-shown:top-3 md:peer-placeholder-shown:top-2 peer-placeholder-shown:left-12 peer-focus:top-0 peer-focus:left-4 peer-focus:text-transparent peer-focus:text-xs">
                            Marka, ürün veya kategori ara
                        </label>
                        <div class="ml-3 md:ml-3 absolute top-1.5 left-0" @click="submitSearchForm">
                            <img src="../../../images/svg/search-black.svg" class="w-[20px] h-[20px]" alt="" />
                        </div>
                    </div>
                    <ul class="flex h-[45px] relative z-50" v-if="!auth.isUserLoggedIn">
                        <li class="text-sm md:text-base hidden md:block">
                            <Menu as="div" class="relative inline-block text-left h-[40px] w-[40px]">
                                <MenuButton>
                                    <img src="../../../images/svg/user.svg">
                                </MenuButton>

                                <transition enter-active-class="transition duration-100 ease-out" enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100" leave-active-class="transition duration-75 ease-in"
                                    leave-from-class="transform scale-100 opacity-100" leave-to-class="transform scale-95 opacity-0">
                                    <MenuItems class="absolute right-0 mt-0 bg-white rounded-lg shadow-lg p-4 w-52 origin-top-right ring-1 ring-black/5 focus:outline-none">
                                        <h2 class="text-lg font-bold text-gray-800 text-center">
                                            Hoş Geldin 👋
                                        </h2>
                                        <!-- Giriş Yap Butonu -->
                                        <div class="mt-2">
                                            <a href="/giris-yap"
                                                class="block text-center w-full border-2 border-transparent bg-kbgreen text-white font-semibold py-1 font-santralextrabold rounded-2lg hover:bg-white hover:text-black hover:border-black transition">
                                                Giriş Yap
                                            </a>
                                        </div>
                                        <!-- Üye Ol Butonu -->
                                        <div class="mt-2">
                                            <a href="/hesap-olustur"
                                                class="block text-center w-full border-2 border-black text-gray-800 font-semibold py-1 font-santralextrabold rounded-2lg hover:bg-kbgreen hover:text-white hover:border-transparent transition">
                                                Üye Ol
                                            </a>
                                        </div>
                                        <!-- Menü Alt Bilgisi -->
                                        <div class="mt-1 border-t border-gray-200 pt-4 text-xs text-gray-600">
                                            <ul class="space-y-1">
                                                <li><a href="/nasil-calisir" class="hover:text-gray-800 transition">Nasıl Çalışır</a></li>
                                                <li><a href="/sikca-sorulan-sorular" class="hover:text-gray-800 transition">Sıkça Sorulan Sorular</a>
                                                </li>
                                                <li><a href="/hakkimizda" class="hover:text-gray-800 transition">Hakkımızda</a></li>
                                            </ul>
                                        </div>
                                    </MenuItems>
                                </transition>
                            </Menu>
                        </li>
                        <li class="text-sm md:text-base hidden md:flex justify-center items-center relative h-[40px] w-[40px]">
                            <Link href="/giris-yap" class="flex">
                            <img class="w-4 md:w-6 h-4 md:h-6" src="../../../images/svg/heart.svg">
                            </Link>
                        </li>
                        <li class="text-sm md:text-base relative flex justify-center items-center h-[40px] w-[40px]">
                            <Link href="/sepetim">
                            <img src="../../../images/svg/cart.svg" class="p-1.5">
                            </Link>
                        </li>
                    </ul>
                    <ul class="flex" v-if="auth.isUserLoggedIn">
                        <li class="text-sm md:text-base hidden md:block relative">
                            <Link href="/profili-duzenle" class="flex">
                            <img src="../../../images/svg/user.svg">
                            </Link>
                        </li>
                        <li class="text-sm md:text-base hidden md:flex justify-center items-center relative h-[45px] w-[45px]">
                            <Link href="/istek-listem" class="flex">
                            <span class="font-santralextrabold text-xs lg:text-2xs 2xl:text-xs text-white bg-kbgreen rounded-full absolute w-4 h-4 font-normal bottom-1 right-1 flex justify-center items-center leading-none">{{
                                wishlist }}</span>
                            <svg class="w-4 md:w-6 h-4 md:h-6 ml-1 stroke-[#231f20] overflow-visible" viewBox="0 0 30.669 28.092">
                                <g id="heart" transform="translate(0 -1.511)" fill="none">
                                    <path
                                        d="M30.071,6.622A13.219,13.219,0,0,1,27.842,18.6,33.873,33.873,0,0,1,22.3,24.456c-1.823,1.7-5.9,5.054-6.983,5.147-.956-.182-2.028-1.263-2.787-1.82-4.264-3.24-8.853-7.181-11.18-11.49C-.6,12.156-.6,7.039,2.433,3.867a8.977,8.977,0,0,1,12.885.853A8.726,8.726,0,0,1,18.3,2.245a8.977,8.977,0,0,1,11.77,4.377Z"
                                        stroke="none" />
                                    <path
                                        d="M 22.02387619018555 4.511138916015625 C 21.19887924194336 4.511205673217773 20.36529541015625 4.668869018554688 19.54391098022461 4.980022430419922 C 18.81119155883789 5.373607635498047 18.20499801635742 5.888843536376953 17.69393539428711 6.551767349243164 L 15.37918663024902 9.55436897277832 L 12.98918724060059 6.611328125 C 11.93668746948242 5.315288543701172 10.24892616271973 4.541538238525391 8.474416732788086 4.541547775268555 C 7.442102432250977 4.541547775268555 5.927898406982422 4.800741195678711 4.52613639831543 6.020847320556641 C 3.402303695678711 7.246023178100586 3.083917617797852 8.801969528198242 3.016265869140625 9.90241813659668 C 2.916469573974609 11.52574920654297 3.276287078857422 13.31153869628906 4.030189514160156 14.94011974334717 C 4.942806243896484 16.60567092895508 6.383369445800781 18.40690231323242 8.313325881958008 20.29546737670898 C 9.916957855224609 21.86470985412598 11.82877540588379 23.48118019104004 14.32891082763672 25.38162803649902 C 14.57612991333008 25.56426811218262 14.81914901733398 25.76386070251465 15.05441665649414 25.95709800720215 C 15.1480712890625 26.03402137756348 15.26153087615967 26.12721252441406 15.37966251373291 26.22183418273926 C 16.63759994506836 25.36079597473145 18.77116775512695 23.64326858520508 20.25698661804199 22.26020812988281 L 20.30599594116211 22.2145881652832 L 20.35699653625488 22.17119789123535 C 22.4555778503418 20.38569831848145 24.10916709899902 18.6432991027832 25.41224670410156 16.84445953369141 L 25.43560600280762 16.81265830993652 C 27.5122241973877 14.02390670776367 28.17683982849121 10.72176933288574 27.27249336242676 7.72297477722168 C 26.7924976348877 6.758964538574219 26.07523345947266 5.959152221679688 25.18952560424805 5.402528762817383 C 24.26146697998047 4.819278717041016 23.16680717468262 4.511037826538086 22.02387619018555 4.511138916015625 M 22.02363014221191 1.511137008666992 C 25.47772216796875 1.510845184326172 28.58621597290039 3.41154670715332 30.07120704650879 6.62156867980957 L 30.07119750976562 6.62156867980957 C 31.46405601501465 10.92536735534668 30.29865646362305 15.30498790740967 27.84177589416504 18.60439872741699 C 26.22061729431152 20.84233856201172 24.28254699707031 22.77018737792969 22.30101776123047 24.45609855651855 C 20.47795677185059 26.15307807922363 16.39905548095703 29.51041793823242 15.31799697875977 29.60327911376953 C 14.36215591430664 29.42096900939941 13.28961753845215 28.33990859985352 12.53142738342285 27.78361892700195 C 8.267665863037109 24.54384803771973 3.678485870361328 20.60295867919922 1.351097106933594 16.29318809509277 C -0.5997524261474609 12.15636825561523 -0.6031627655029297 7.038997650146484 2.433006286621094 3.867378234863281 C 6.3704833984375 0.318359375 12.3065242767334 1.011802673339844 15.31799697875977 4.720129013061523 C 16.12644577026367 3.671438217163086 17.12146759033203 2.845949172973633 18.30135726928711 2.244508743286133 C 19.5473575592041 1.74708366394043 20.80727195739746 1.511240005493164 22.02363014221191 1.511137008666992 Z"
                                        stroke="none" fill="#231f20" />
                                </g>
                            </svg>
                            </Link>
                        </li>
                        <li class="text-sm md:text-base relative flex justify-center items-center h-[45px] w-[45px]">
                            <Link href="/sepetim" class="">
                            <span class="font-santralextrabold text-xs lg:text-2xs 2xl:text-xs text-white bg-kbgreen rounded-full absolute w-4 h-4 font-normal bottom-1 right-1 flex justify-center items-center leading-none">{{
                                auth.cart.items.length
                                }}</span>
                            <img src="../../../images/svg/cart.svg">
                            </Link>
                        </li>
                    </ul>
                </div>
            </section>
            <NewMenu :institutionalLayout="institutionalLayout" :searchForm="searchForm" :auth="auth" :logoUrl="logoUrl" :wishlist="wishlist" :cart="cart" :mobileMenuIsOpen="mobileMenuIsOpen" :show="true" @update:mobileMenuIsOpen="closeMenu"
                :menuItems="menuItems"></NewMenu>
            <!-- search -->

            <section class="hidden lg:flex flex-col">
                <div class="overflow-x-scroll lg:overflow-x-visible mt-5">
                    <nav class="pb-4 mx-auto flex" :class="institutionalLayout == 'demo' ? 'justify-between' : 'justify-between'">
                        <div v-if="institutionalLayout != 'demo'" class="pr-2" @mouseenter="menu = true" @mouseleave="menu = false">
                            <div class="bg-white rounded-xl flex cursor-pointer items-center px-3 md:px-5 py-2">
                                <img src="../../../images/svg/all-categories.svg" alt="" class="w-full max-w-[20px]" />
                                <span class="ml-1 whitespace-nowrap hover:text-kbgreen text-xs">TÜM KATEGORİLER</span>
                            </div>
                        </div>
                        <div class="mr-4 hidden md:block" v-if="institutionalLayout == 'demo'">
                            <div v-if="logoLink == '/'">
                                <a :href="logoLink"><img class="max-h-[45px] mx-auto w-full" :id="this.$inertia.page.url" :src="logoUrl" alt="" /></a>
                            </div>
                            <div v-else>
                                <a :href="logoLink"><img class="max-h-[45px] mx-auto w-full" :id="this.$inertia.page.url" :src="logoUrl" alt="" /></a>
                            </div>
                        </div>
                        <ul class="flex space-x-3 ts:space-x-4 2xl:space-x-6 items-center justify-center" v-if="institutionalLayout == 'demo'">
                            <li>
                                <Link href="/hakkimizda">
                                <div class="bg-white flex items-center">
                                    <span class="ml-1 whitespace-nowrap hover:text-kbgreen font-semibold text-sm font-santralregular">Hakkımızda</span>
                                </div>
                                </Link>
                            </li>
                            <li>
                                <Link href="/is-ortagi-cozumlerimiz">
                                <div class="bg-white flex items-center">
                                    <span class="ml-1 whitespace-nowrap hover:text-kbgreen font-semibold text-sm font-santralregular">İş Ortağı Çözümlerimiz</span>
                                </div>
                                </Link>
                            </li>
                            <li>
                                <Link href="/musteri-cozumlerimiz">
                                <div class="bg-white flex items-center">
                                    <span class="ml-1 whitespace-nowrap hover:text-kbgreen font-semibold text-sm font-santralregular">Müşteri Çözümlerimiz</span>
                                </div>
                                </Link>
                            </li>
                            <li class="border-l-2 border-black h-2/3"></li>

                            <li>
                                <a href="/">
                                    <div class="bg-white rounded-xl flex items-center">
                                        <span class="ml-1 whitespace-nowrap text-kbgreen font-bold text-base font-santralextrabold">Kiralabunu</span>
                                    </div>
                                </a>
                            </li>
                        </ul>
                        <ul class="flex space-x-3 ts:space-x-4 2xl:space-x-6 items-center justify-center" v-if="institutionalLayout != 'demo'">
                            <li>
                                <Link href="/kategoriler/telefon-ve-aksesuarlari">
                                <div class="bg-white flex items-center">
                                    <span class="ml-1 whitespace-nowrap hover:text-kbgreen font-semibold text-sm font-santralregular">Telefon
                                        & Aksesuarları</span>
                                </div>
                                </Link>
                            </li>
                            <li>
                                <Link href="/kategoriler/bilgisayar-tablet">
                                <div class="bg-white rounded-xl flex items-center">
                                    <span class="ml-1 whitespace-nowrap hover:text-kbgreen font-semibold text-sm font-santralregular">Bilgisayar
                                        & Tablet</span>
                                </div>
                                </Link>
                            </li>
                            <li>
                                <Link href="/kategoriler/ev-eglence">
                                <div class="bg-white rounded-xl flex items-center">
                                    <span class="ml-1 whitespace-nowrap hover:text-kbgreen font-semibold text-sm font-santralregular">Ev
                                        & Ofis</span>
                                </div>
                                </Link>
                            </li>
                            <li>
                                <Link href="/kategoriler/kiralamobil">
                                <div class="bg-white rounded-xl flex items-center">
                                    <span class="ml-1 whitespace-nowrap hover:text-kbgreen font-semibold text-sm font-santralregular">Mobilite</span>
                                </div>
                                </Link>
                            </li>
                            <li class="border-l-2 border-black h-2/3"></li>
                            <li>
                                <Link href="/kategoriler/tum-urunler">
                                <div class="bg-white rounded-xl flex items-center">
                                    <span class="ml-1 whitespace-nowrap hover:text-kbgreen font-semibold text-sm font-santralregular">İndirimli Ürünler</span>
                                </div>
                                </Link>
                            </li>
                            <li>
                                <Link href="/kampanyalar">
                                <div class="bg-white rounded-xl flex items-center">
                                    <span class="ml-1 whitespace-nowrap hover:text-kbgreen font-semibold text-sm font-santralregular">Kampanyalar</span>
                                </div>
                                </Link>
                            </li>
                            <li>
                                <Link href="/nasil-calisir">
                                <div class="bg-white rounded-xl flex items-center">
                                    <span class="ml-1 whitespace-nowrap hover:text-kbgreen font-semibold text-sm font-santralregular">Nasıl
                                        Çalışır?</span>
                                </div>
                                </Link>
                            </li>
                            <li>
                                <a href="/kurumsal">
                                    <div class="bg-white rounded-xl flex items-center">
                                        <span class="ml-1 whitespace-nowrap text-kbgreen font-bold text-base font-santralextrabold">Kurumsal</span>
                                    </div>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
                <!-- for mobile, from Burak -->
            </section>
            <div class="relative">
                <MegaMenu :active="menu" @menuchange="(val) => (menu = val) " :menuItems="menuItems"></MegaMenu>
            </div>
        </header>
        <slot />
        <footer>
            <section class="bg-checkoutgray" v-if="!auth.isUserLoggedIn">
                <div class="flex flex-col mx-auto max-w-kbmobile mts:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
                    <div class="text-white text-base md:text-lg lg:text-xl text-center mt-4 max-w-[242px] md:max-w-none mx-auto">
                        Yenilikler ve indirimler için bültenimize abone ol.
                    </div>
                    <form class="text-center mb-6 mt-6 flex justify-center">
                        <input type="email" v-model="subscribeForm.email"
                            class="rounded-l-2lg mts:w-8/12 lg:w-4/12 leading-[70px] h-10 placeholder:text-sm md:placeholder:text-lg placeholder:text-placeholdergray py-6 md:px-6 text-base focus:outline-none" placeholder="E-Posta Adresin" required />
                        <button type="submit" @click="openSubscribeModal" class="bg-black text-base font-santralextrabold text-white px-8 py-3 rounded-r-2lg border-0 focus:outline-none whitespace-nowrap">Abone
                            Ol
                        </button>
                    </form>
                </div>
            </section>
            <section class="my-3 md:my-4 md:mt-4">
                <div class="flex flex-wrap lg:flex-nowrap mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl lg:space-x-3">
                    <div class="w-6/12 md:w-6/12 lg:w-3/12 my-2 lg:my-0">
                        <div class="text-sm mts:text-base mt-3">KİRALABUNU</div>
                        <div class="flex flex-col mt-4 md:mt-8 leading-4">
                            <Link href="/" class="text-kbgray text-xs mts:text-sm mb-2">Anasayfa</Link>
                            <Link href="/hakkimizda" class="text-kbgray text-xs mts:text-sm mb-2">Hakkımızda</Link>
                            <Link href="/kampanyalar" class="text-kbgray text-xs mts:text-sm mb-2">Kampanyalar</Link>
                            <Link href="/nasil-calisir" class="text-kbgray text-xs mts:text-sm mb-2">Nasıl Çalışır
                            </Link>
                            <Link href="/sikca-sorulan-sorular" class="text-kbgray text-xs mts:text-sm mb-2">SSS</Link>
                            <!--                            <Link href="/iletisim" class="text-kbgray text-xs mts:text-sm mb-2">İletişim</Link>-->
                            <Link href="/blog" class="text-kbgray text-xs mts:text-sm mb-2">Blog</Link>
                        </div>
                    </div>
                    <div class="w-6/12 md:w-6/12 lg:w-3/12 my-2 lg:my-0 pl-2 md:pl-0">
                        <div class="text-sm mts:text-base mt-3">BİLGİLENDİRME</div>
                        <div class="flex flex-col mt-4 md:mt-8 leading-4">
                            <Link href="/uyelik-sozlesmesi" class="text-kbgray text-xs mts:text-sm mb-2">Üyelik
                            Sözleşmesi
                            </Link>
                            <Link href="/aydinlatma-metni" class="text-kbgray text-xs mts:text-sm mb-2">Aydınlatma Metni
                            </Link>
                            <Link href="/cayma-fesih-ve-iade-kosullari" class="text-kbgray text-xs mts:text-sm mb-2">
                            Cayma, Fesih ve İade Koşulları
                            </Link>
                            <Link href="/cerez-politikasi" class="text-kbgray text-xs mts:text-sm mb-2">Çerez (Cookie)
                            Politikası
                            </Link>
                            <Link href="/kimlik-findeks-raporu" class="text-kbgray text-xs mts:text-sm mb-2">Kimlik ve
                            Findeks Raporu
                            </Link>
                            <Link href="/odeme-ve-teslimat" class="text-kbgray text-xs mts:text-sm mb-2">Ödeme ve
                            Teslimat
                            </Link>
                            <Link href="/surdurulebilirlik" class="text-kbgray text-xs mts:text-sm mb-2">
                            Sürdürülebilirlik
                            </Link>
                        </div>
                    </div>
                    <div class="w-6/12 md:w-6/12 lg:w-3/12 my-2 lg:my-0">
                        <div class="text-sm mts:text-base mt-3">KURUMSAL</div>
                        <div class="flex flex-col mt-4 md:mt-8 leading-4">
                            <Link href="/kurumsal" class="text-kbgray text-xs mts:text-sm mb-2">Kurumsal</Link>
                            <Link href="/hakkimizda" class="text-kbgray text-xs mts:text-sm mb-2">Hakkımızda</Link>
                            <Link href="/musteri-cozumlerimiz" class="text-kbgray text-xs mts:text-sm mb-2">Müşteri Çözümlerimiz</Link>
                            <Link href="/is-ortagi-cozumlerimiz" class="text-kbgray text-xs mts:text-sm mb-2">İş Ortağı Çözümlerimiz</Link>
                        </div>
                    </div>
                    <div class="w-full mts:w-6/12 lg:w-3/12 flex flex-col justify-center lg:justify-start lg:my-0">
                        <div class="text-sm mts:text-base mt-3">İLETİŞİM</div>
                        <div class="flex flex-col mt-4 lg:mt-8 leading-4">
                            <!--                            <p class="text-kbgray text-sm lg:text-sm">Hızlı destek için </p>-->
                            <!--                            <a href="tel:+908502551552" class="text-kbgreen text-3xl lg:text-3xl lg:mt-2 font-semibold">0850 255 1552</a>-->
                            <Link href="/iletisim" class="text-kbgray text-xs mts:text-sm mb-2">İletişim</Link>
                            <a href="https://wa.me/+905304001412" target="_blank" v-if="true">
                                <button class="bg-kbgreen text-white text-xs lg:text-sm py-2 w-[200px] rounded-full mt-2 hover:opacity-[90%] whitespace-nowrap leading-tight font-santralextrabold">Whatsapp
                                    ile Bağlan
                                </button>
                            </a>
                        </div>
                        <ul class="flex flex-row mt-2 leading-4 space-x-3">
                            <li class="pl-2 mt-2 mts:mt-4">
                                <a href="https://www.facebook.com/kiralabunu" target="_blank" class="flex justify-center items-center bg-kb-light-grey rounded-full w-8 h-8">
                                    <svg width="7.385" height="14" viewBox="0 0 7.385 16" class="">
                                        <path id="" data-name="Facebook Icon"
                                            d="M6.981,2.83A4.442,4.442,0,0,0,5.69,2.617c-.525,0-1.654.362-1.654,1.064V5.362H6.719v2.83H4.035V16h-2.7V8.191H0V5.362H1.332V3.936C1.332,1.787,2.26,0,4.5,0A10.187,10.187,0,0,1,7.385.319Z" fill="#70d44b" />
                                    </svg>
                                </a>
                            </li>
                            <li class="pl-2 mt-2 mts:mt-4">
                                <a href="https://www.instagram.com/kiralabunu/" target="_blank" class="flex justify-center items-center bg-kb-light-grey rounded-full w-8 h-8">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="16" height="116">
                                        <path fill="#70d44b"
                                            d="M 9.9980469 3 C 6.1390469 3 3 6.1419531 3 10.001953 L 3 20.001953 C 3 23.860953 6.1419531 27 10.001953 27 L 20.001953 27 C 23.860953 27 27 23.858047 27 19.998047 L 27 9.9980469 C 27 6.1390469 23.858047 3 19.998047 3 L 9.9980469 3 z M 22 7 C 22.552 7 23 7.448 23 8 C 23 8.552 22.552 9 22 9 C 21.448 9 21 8.552 21 8 C 21 7.448 21.448 7 22 7 z M 15 9 C 18.309 9 21 11.691 21 15 C 21 18.309 18.309 21 15 21 C 11.691 21 9 18.309 9 15 C 9 11.691 11.691 9 15 9 z M 15 11 A 4 4 0 0 0 11 15 A 4 4 0 0 0 15 19 A 4 4 0 0 0 19 15 A 4 4 0 0 0 15 11 z" />
                                    </svg>
                                </a>
                            </li>
                            <li class="pl-2 mt-2 mts:mt-4">
                                <a href="https://www.linkedin.com/company/kiralabunu" target="_blank" class="flex justify-center items-center bg-kb-light-grey rounded-full w-8 h-8">
                                    <svg width="16" height="16" viewBox="0 0 16 16" class="scale-75">
                                        <path id="LinkedIn_Icon" data-name="LinkedIn Icon"
                                            d="M8.96,16H5.547q0-.937,0-1.872v-.006h0c0-3.042.008-6.189-.082-9.295h2.96L8.587,6.4H8.64a3.988,3.988,0,0,1,3.414-1.813,3.507,3.507,0,0,1,3.033,1.453A5.785,5.785,0,0,1,16,9.412V16H12.56V9.813c0-1.611-.583-2.427-1.733-2.427a1.909,1.909,0,0,0-1.76,1.307,2.518,2.518,0,0,0-.106.88V16ZM3.493,16H.08V4.826H3.493V16ZM1.76,3.467A1.69,1.69,0,0,1,0,1.733,1.716,1.716,0,0,1,1.813,0,1.7,1.7,0,0,1,3.6,1.733,1.711,1.711,0,0,1,1.76,3.467Z"
                                            fill="#70d44b" />
                                    </svg>
                                </a>
                            </li>
                            <li class="pl-2 mt-2 mts:mt-4">
                                <a href="https://www.youtube.com/channel/UCdUwTA8Y5EptffUoY3fU0OQ" target="_blank" class="flex justify-center items-center bg-kb-light-grey rounded-full w-8 h-8">
                                    <svg width="20.415" height="15.315" viewBox="0 0 20.415 15.315" class="scale-75">
                                        <path id="Path_2998" data-name="Path 2998"
                                            d="M10.566,5.776c2.6,0,4.733.044,6.7.132h.056A1.836,1.836,0,0,1,19,7.865v.044l0,.044c.092,1.356.136,2.763.136,4.183S19.1,14.962,19,16.318l0,.044v.044a2.112,2.112,0,0,1-.534,1.439,1.542,1.542,0,0,1-1.14.534h-.064c-2.11.1-4.338.151-6.635.151H10.2c-2.3,0-4.534-.052-6.627-.147H3.512a1.535,1.535,0,0,1-1.136-.534,2.131,2.131,0,0,1-.534-1.439v-.044l0-.044c-.1-1.36-.14-2.767-.132-4.175v-.008c0-1.407.04-2.811.132-4.171l0-.044V7.881A1.838,1.838,0,0,1,3.512,5.915h.056c1.973-.092,4.1-.132,6.707-.132h.291m0-1.284H9.848c-2.3,0-4.554.032-6.762.132A3.1,3.1,0,0,0,.139,7.873C.039,9.3,0,10.72,0,12.144s.036,2.843.136,4.266a3.113,3.113,0,0,0,2.947,3.253q3.277.156,6.69.151h.861q3.415,0,6.694-.151a3.114,3.114,0,0,0,2.951-3.253q.144-2.135.136-4.27c0-1.423-.04-2.843-.136-4.27a3.1,3.1,0,0,0-2.951-3.233C15.119,4.532,12.867,4.5,10.566,4.5Z"
                                            transform="translate(0 -4.5)" fill="#70d44b" />
                                        <path id="Path_2999" data-name="Path 2999" d="M14.555,18.9V11.067l5.781,3.916Z" transform="translate(-6.301 -7.343)" fill="#70d44b" />
                                    </svg>
                                </a>
                            </li>
                        </ul>

                    </div>
                    <div class="w-full mts:w-6/12 lg:w-3/12 my-2 lg:my-0">
                        <div id="ETBIS" class="mt-3 md:mt-0">
                            <div id="1136182002653428"><a href="https://etbis.eticaret.gov.tr/sitedogrulama/1136182002653428" target="_blank"><img class="w-[130px] h-[150px]"
                                        src="data:image/jpeg;base64, 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"></a>
                            </div>
                        </div>
                        <!--                        <div class="text-sm mts:text-base mt-3">BİZİ TAKİP ET</div>-->
                        <!--                        <ul class="flex flex-row flex-wrap mts:flex-col mt-2 leading-4 space-y-2 mts:space-y-4">-->
                        <!--                            <li class="flex w-1/2 mts:w-full items-center mt-2 mts:mt-4">-->
                        <!--                                <a href="https://www.facebook.com/kiralabunu" target="_blank" class="flex justify-center items-center bg-kb-light-grey rounded-full w-8 h-8">-->
                        <!--                                    <svg width="7.385" height="14" viewBox="0 0 7.385 16" class="">-->
                        <!--                                        <path id="" data-name="Facebook Icon" d="M6.981,2.83A4.442,4.442,0,0,0,5.69,2.617c-.525,0-1.654.362-1.654,1.064V5.362H6.719v2.83H4.035V16h-2.7V8.191H0V5.362H1.332V3.936C1.332,1.787,2.26,0,4.5,0A10.187,10.187,0,0,1,7.385.319Z" fill="#70d44b" />-->
                        <!--                                    </svg>-->
                        <!--                                </a>-->
                        <!--                                <a href="https://www.facebook.com/kiralabunu" target="_blank">-->
                        <!--                                    <span class="text-kbgray text-xs mts:text-sm ml-2">Facebook</span>-->
                        <!--                                </a>-->
                        <!--                            </li>-->
                        <!--                            <li class="flex w-1/2 mts:w-full items-center pl-2 mts:pl-0">-->
                        <!--                                <a href="https://www.instagram.com/kiralabunu/" target="_blank" class="flex justify-center items-center bg-kb-light-grey rounded-full w-8 h-8">-->
                        <!--                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="16" height="116">-->
                        <!--                                        <path-->
                        <!--                                            fill="#70d44b"-->
                        <!--                                            d="M 9.9980469 3 C 6.1390469 3 3 6.1419531 3 10.001953 L 3 20.001953 C 3 23.860953 6.1419531 27 10.001953 27 L 20.001953 27 C 23.860953 27 27 23.858047 27 19.998047 L 27 9.9980469 C 27 6.1390469 23.858047 3 19.998047 3 L 9.9980469 3 z M 22 7 C 22.552 7 23 7.448 23 8 C 23 8.552 22.552 9 22 9 C 21.448 9 21 8.552 21 8 C 21 7.448 21.448 7 22 7 z M 15 9 C 18.309 9 21 11.691 21 15 C 21 18.309 18.309 21 15 21 C 11.691 21 9 18.309 9 15 C 9 11.691 11.691 9 15 9 z M 15 11 A 4 4 0 0 0 11 15 A 4 4 0 0 0 15 19 A 4 4 0 0 0 19 15 A 4 4 0 0 0 15 11 z"-->
                        <!--                                        />-->
                        <!--                                    </svg>-->
                        <!--                                </a>-->
                        <!--                                <a href="https://www.instagram.com/kiralabunu/" target="_blank">-->
                        <!--                                    <span class="text-kbgray text-xs mts:text-sm ml-2">Instagram</span>-->
                        <!--                                </a>-->
                        <!--                            </li>-->
                        <!--                            <li class="flex w-1/2 mts:w-full items-center">-->
                        <!--                                <a href="https://www.linkedin.com/company/kiralabunu" target="_blank" class="flex justify-center items-center bg-kb-light-grey rounded-full w-8 h-8">-->
                        <!--                                    <svg width="16" height="16" viewBox="0 0 16 16" class="scale-75">-->
                        <!--                                        <path-->
                        <!--                                            id="LinkedIn_Icon"-->
                        <!--                                            data-name="LinkedIn Icon"-->
                        <!--                                            d="M8.96,16H5.547q0-.937,0-1.872v-.006h0c0-3.042.008-6.189-.082-9.295h2.96L8.587,6.4H8.64a3.988,3.988,0,0,1,3.414-1.813,3.507,3.507,0,0,1,3.033,1.453A5.785,5.785,0,0,1,16,9.412V16H12.56V9.813c0-1.611-.583-2.427-1.733-2.427a1.909,1.909,0,0,0-1.76,1.307,2.518,2.518,0,0,0-.106.88V16ZM3.493,16H.08V4.826H3.493V16ZM1.76,3.467A1.69,1.69,0,0,1,0,1.733,1.716,1.716,0,0,1,1.813,0,1.7,1.7,0,0,1,3.6,1.733,1.711,1.711,0,0,1,1.76,3.467Z"-->
                        <!--                                            fill="#70d44b"-->
                        <!--                                        />-->
                        <!--                                    </svg>-->
                        <!--                                </a>-->
                        <!--                                <a href="https://www.linkedin.com/company/kiralabunu" target="_blank">-->
                        <!--                                    <span class="text-kbgray text-xs mts:text-sm ml-2">Linkedin</span>-->
                        <!--                                </a>-->
                        <!--                            </li>-->
                        <!--                            <li class="flex w-1/2 md:w-full items-center pl-2 mts:pl-0">-->
                        <!--                                <a href="https://www.youtube.com/channel/UCdUwTA8Y5EptffUoY3fU0OQ" target="_blank" class="flex justify-center items-center bg-kb-light-grey rounded-full w-8 h-8">-->
                        <!--                                    <svg width="20.415" height="15.315" viewBox="0 0 20.415 15.315" class="scale-75">-->
                        <!--                                        <path-->
                        <!--                                            id="Path_2998"-->
                        <!--                                            data-name="Path 2998"-->
                        <!--                                            d="M10.566,5.776c2.6,0,4.733.044,6.7.132h.056A1.836,1.836,0,0,1,19,7.865v.044l0,.044c.092,1.356.136,2.763.136,4.183S19.1,14.962,19,16.318l0,.044v.044a2.112,2.112,0,0,1-.534,1.439,1.542,1.542,0,0,1-1.14.534h-.064c-2.11.1-4.338.151-6.635.151H10.2c-2.3,0-4.534-.052-6.627-.147H3.512a1.535,1.535,0,0,1-1.136-.534,2.131,2.131,0,0,1-.534-1.439v-.044l0-.044c-.1-1.36-.14-2.767-.132-4.175v-.008c0-1.407.04-2.811.132-4.171l0-.044V7.881A1.838,1.838,0,0,1,3.512,5.915h.056c1.973-.092,4.1-.132,6.707-.132h.291m0-1.284H9.848c-2.3,0-4.554.032-6.762.132A3.1,3.1,0,0,0,.139,7.873C.039,9.3,0,10.72,0,12.144s.036,2.843.136,4.266a3.113,3.113,0,0,0,2.947,3.253q3.277.156,6.69.151h.861q3.415,0,6.694-.151a3.114,3.114,0,0,0,2.951-3.253q.144-2.135.136-4.27c0-1.423-.04-2.843-.136-4.27a3.1,3.1,0,0,0-2.951-3.233C15.119,4.532,12.867,4.5,10.566,4.5Z"-->
                        <!--                                            transform="translate(0 -4.5)"-->
                        <!--                                            fill="#70d44b"-->
                        <!--                                        />-->
                        <!--                                        <path id="Path_2999" data-name="Path 2999" d="M14.555,18.9V11.067l5.781,3.916Z" transform="translate(-6.301 -7.343)" fill="#70d44b" />-->
                        <!--                                    </svg>-->
                        <!--                                </a>-->
                        <!--                                <a href="https://www.youtube.com/channel/UCdUwTA8Y5EptffUoY3fU0OQ" target="_blank">-->
                        <!--                                    <span class="text-kbgray text-xs mts:text-sm ml-2">Youtube</span>-->
                        <!--                                </a>-->
                        <!--                            </li>-->
                        <!--                        </ul>-->
                    </div>
                </div>
            </section>
            <section class="my-3 md:my-4 md:mt-4">
                <div class="flex flex-wrap md:justify-around lg:flex-nowrap mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl lg:space-x-8">
                    <div class="w-6/12 md:w-6/12 lg:w-3/12 my-2 lg:my-0">
                        <div class="text-sm mts:text-base mt-3">FAVORİ SAYFALAR</div>
                        <div class="flex flex-col mt-4 md:mt-8 leading-4">
                            <Link href="/kategoriler/telefon?filter[brand]=1&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> Apple Telefon
                            </Link>
                            <Link href="/kategoriler/saat/apple-saatler" class="text-kbgray text-xs mts:text-sm mb-2">
                            Apple Saat
                            </Link>
                            <Link href="/kategoriler/telefon?filter[brand]=4&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> Samsung Telefon
                            </Link>
                            <Link href="/kategoriler/kulaklik?filter[brand]=4&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> Samsung Kulaklık
                            </Link>
                        </div>
                        <Disclosure as="div" v-slot="{ open }">
                            <DisclosurePanel class="">
                                <div class="flex flex-col leading-4">
                                    <Link href="/kategoriler/oyun-aksesuarlari?filter[brand]=22&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> Logitech Aksesuar
                                    </Link>
                                    <Link href="/kategoriler/kahve-makineleri?filter[brand]=30&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> Philips Kahve Makinesi
                                    </Link>
                                    <Link href="/kategoriler/oyun-konsolu-vr/vr " class="text-kbgray text-xs mts:text-sm mb-2"> Oculus VR Gözlük
                                    </Link>
                                    <Link href="/kategoriler/kulaklik?filter[brand]=6&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> JBL Hoparlör
                                    </Link>
                                    <Link href="/kategoriler/laptoplar?filter[brand]=1&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> Macbook
                                    </Link>
                                    <Link href="/kategoriler/tabletler?filter[brand]=9&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> Huawei Tablet
                                    </Link>
                                    <Link href="/kategoriler/projeksiyon?filter[brand]=8&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> Xiaomi Projeksiyon
                                    </Link>
                                    <Link href="/kategoriler/ses-sistemleri?filter[brand]=12&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> Sony Soundbar
                                    </Link>
                                    <Link href="/kategoriler/oyun-konsollari?filter[brand]=12&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> Playstation 5
                                    </Link>
                                    <Link href="/kategoriler/hava-temizleme-ve-nem-alma-cihazlari?filter[brand]=30&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> Philips Hava Temizleyici
                                    </Link>
                                    <Link href="/kategoriler/robot-supurgeler?filter[brand]=28&filter[price]=&filter[collections]=" class="text-kbgray text-xs mts:text-sm mb-2"> Roborock Robot Süpürge
                                    </Link>
                                </div>
                            </DisclosurePanel>
                            <DisclosureButton @click="hiddenbtn1 = !hiddenbtn1" v-if="hiddenbtn1"
                                class="bg-white text-xs text-kbgray border-1 border-kbgray rounded-full py-1 px-2 self-center font-santralextrabold mt-2 hover:bg-kbgreen hover:text-white hover:border-white">
                                Daha Fazla
                            </DisclosureButton>
                        </Disclosure>
                    </div>
                    <div class="w-6/12 md:w-6/12 lg:w-3/12 my-2 lg:my-0 pl-2 md:pl-0">
                        <div class="text-sm mts:text-base mt-3">MARKALAR</div>
                        <div class="flex flex-col mt-4 md:mt-8 leading-4">
                            <Link href="https://kiralabunu.com/marka/apple" class="text-kbgray text-xs mts:text-sm mb-2"> Apple
                            </Link>
                            <Link href="https://kiralabunu.com/marka/samsung" class="text-kbgray text-xs mts:text-sm mb-2"> Samsung
                            </Link>
                            <Link href="https://kiralabunu.com/marka/xiaomi" class="text-kbgray text-xs mts:text-sm mb-2"> Xiaomi
                            </Link>
                            <Link href="https://kiralabunu.com/marka/honda" class="text-kbgray text-xs mts:text-sm mb-2"> Honda
                            </Link>
                        </div>
                        <Disclosure as="div" v-slot="{ open }">
                            <DisclosurePanel class="">
                                <div class="flex flex-col leading-4">
                                    <Link href="https://kiralabunu.com/marka/lg" class="text-kbgray text-xs mts:text-sm mb-2"> LG
                                    </Link>
                                    <Link href="https://kiralabunu.com/marka/sony" class="text-kbgray text-xs mts:text-sm mb-2"> Sony
                                    </Link>
                                    <Link href="https://kiralabunu.com/marka/monster" class="text-kbgray text-xs mts:text-sm mb-2"> Monster
                                    </Link>
                                    <Link href="https://kiralabunu.com/marka/dyson" class="text-kbgray text-xs mts:text-sm mb-2"> Dyson
                                    </Link>
                                    <Link href="https://kiralabunu.com/marka/philips" class="text-kbgray text-xs mts:text-sm mb-2"> Philips
                                    </Link>
                                    <Link href="https://kiralabunu.com/marka/logitech" class="text-kbgray text-xs mts:text-sm mb-2"> Logitech
                                    </Link>
                                    <Link href="https://kiralabunu.com/marka/lenovo" class="text-kbgray text-xs mts:text-sm mb-2"> Lenovo
                                    </Link>
                                    <Link href="https://kiralabunu.com/marka/asus" class="text-kbgray text-xs mts:text-sm mb-2"> Asus
                                    </Link>
                                    <Link href="https://kiralabunu.com/marka/jbl" class="text-kbgray text-xs mts:text-sm mb-2"> JBL
                                    </Link>
                                    <Link href="https://kiralabunu.com/marka/bosch" class="text-kbgray text-xs mts:text-sm mb-2"> Bosch
                                    </Link>
                                    <Link href="https://kiralabunu.com/marka/delonghi" class="text-kbgray text-xs mts:text-sm mb-2"> Delonghi
                                    </Link>
                                </div>
                            </DisclosurePanel>
                            <DisclosureButton @click="hiddenbtn2 = !hiddenbtn2" v-if="hiddenbtn2"
                                class="bg-white text-xs text-kbgray border-1 border-kbgray rounded-full py-1 px-2 self-center font-santralextrabold mt-2 hover:bg-kbgreen hover:text-white hover:border-white">
                                Daha Fazla
                            </DisclosureButton>
                        </Disclosure>
                    </div>
                    <div class="w-6/12 md:w-6/12 lg:w-4/12 my-2 lg:my-0">
                        <div class="text-sm mts:text-base mt-3">POPÜLER ÜRÜNLER</div>
                        <div class="flex flex-col mt-4 md:mt-8 leading-4">
                            <Link href="/urun/apple-vision-pro" class="text-kbgray text-xs mts:text-sm mb-2"> Apple
                            Vision Pro
                            </Link>
                            <Link href="/urun/apple-iphone-15-128gb" class="text-kbgray text-xs mts:text-sm mb-2"> Apple
                            iPhone 15 128GB
                            </Link>
                            <Link href="/urun/honda-pcx125" class="text-kbgray text-xs mts:text-sm mb-2"> Honda PCX 125
                            </Link>
                            <Link href="/urun/apple-iphone-16-128gb" class="text-kbgray text-xs mts:text-sm mb-2"> Apple
                            iPhone 16 128GB
                            </Link>

                        </div>
                        <Disclosure as="div" v-slot="{ open }">
                            <DisclosurePanel class="">
                                <div class="flex flex-col leading-4">
                                    <Link href="/urun/apple-iphone-15-pro-max-256gb" class="text-kbgray text-xs mts:text-sm mb-2"> Apple iPhone 15 Pro Max 256GB
                                    </Link>
                                    <Link href="/urun/apple-airpods-2-nesil-bluetooth-kulak-ici-kulaklik-ve-sarj-kutusu" class="text-kbgray text-xs mts:text-sm mb-2"> Apple AirPods 2. Nesil
                                    </Link>
                                    <Link href="/urun/oculus-quest-3-128-gb-vr" class="text-kbgray text-xs mts:text-sm mb-2"> Oculus Meta Quest 3 128GB
                                    </Link>
                                    <Link href="/urun/sony-playstation5-pro" class="text-kbgray text-xs mts:text-sm mb-2"> Sony Playstation 5 Pro
                                    </Link>
                                    <Link href="/urun/apple-watch-series-8-gps-45mm" class="text-kbgray text-xs mts:text-sm mb-2"> Apple Watch Series 8
                                    </Link>
                                    <Link href="/urun/roborock-q7-max-robot-supurge" class="text-kbgray text-xs mts:text-sm mb-2"> Roborock Q7 Max Robot Süpürge
                                    </Link>
                                    <Link href="/urun/dyson-purifier-cool-hava-temizleme-fani" class="text-kbgray text-xs mts:text-sm mb-2"> Dyson Purifier Cool Hava Temizleme
                                    Fanı
                                    </Link>
                                    <Link href="/urun/apple-airpods-max-2024" class="text-kbgray text-xs mts:text-sm mb-2"> Apple AirPods Max 2024
                                    </Link>
                                    <Link href="/urun/nespresso-essenza-mini" class="text-kbgray text-xs mts:text-sm mb-2"> Nespresso Essenza Mini D30 Kahve
                                    Makinesi
                                    </Link>
                                    <Link href="/urun/apple-ipad-9-nesil-64gb" class="text-kbgray text-xs mts:text-sm mb-2"> Apple iPad 9. Nesil Tablet
                                    </Link>
                                    <Link href="/urun/dyson-airwrap-multi-styler" class="text-kbgray text-xs mts:text-sm mb-2"> Dyson Airwrap Multi Styler
                                    </Link>
                                </div>
                            </DisclosurePanel>
                            <DisclosureButton @click="hiddenbtn3 = !hiddenbtn3" v-if="hiddenbtn3"
                                class="bg-white text-xs text-kbgray border-1 border-kbgray rounded-full py-1 px-2 self-center font-santralextrabold mt-2 hover:bg-kbgreen hover:text-white hover:border-white">
                                Daha Fazla
                            </DisclosureButton>
                        </Disclosure>
                    </div>
                    <div class="w-6/12 md:w-6/12 lg:w-3/12 my-2 lg:my-0 pl-2 md:pl-0">
                        <div class="text-sm mts:text-base mt-3">KATEGORİLER</div>
                        <div class="flex flex-col mt-4 md:mt-8 leading-4">
                            <Link href="/kategoriler/telefon" class="text-kbgray text-xs mts:text-sm mb-2">Telefon &
                            Aksesuarları
                            </Link>
                            <Link href="/kategoriler/bilgisayar-tablet" class="text-kbgray text-xs mts:text-sm mb-2">Bilgisayar & Tablet
                            </Link>
                            <Link href="/kategoriler/kiralamobil/elektrikli-motor" class="text-kbgray text-xs mts:text-sm mb-2">Elektrikli Motor
                            </Link>
                            <Link href="/kategoriler/kiralamobil/elektrikli-araba" class="text-kbgray text-xs mts:text-sm mb-2">Elektrikli Araba
                            </Link>

                            <!--                            <div class="flex flex-col md:hidden">-->
                            <!--                                <Link href="/kategoriler/tablet" class="text-kbgray text-xs mts:text-sm mb-2">-->
                            <!--                                    Tablet-->
                            <!--                                </Link>-->
                            <!--                                &lt;!&ndash;                                <Link href="/kategoriler/apple-saat" class="text-kbgray text-xs mts:text-sm mb-2">&ndash;&gt;-->
                            <!--                                &lt;!&ndash;                                    Apple Saat&ndash;&gt;-->
                            <!--                                &lt;!&ndash;                                </Link>&ndash;&gt;-->
                            <!--                                <Link href="/kategoriler/televizyon" class="text-kbgray text-xs mts:text-sm mb-2">-->
                            <!--                                    Televizyon-->
                            <!--                                </Link>-->
                            <!--                            </div>-->
                        </div>
                        <Disclosure as="div" v-slot="{ open }">
                            <DisclosurePanel class="">
                                <div class="flex flex-col leading-4">
                                    <Link href="/kategoriler/tablet" class="text-kbgray text-xs mts:text-sm mb-2">Tablet
                                    & Aksesuarları
                                    </Link>
                                    <Link href="/kategoriler/saat" class="text-kbgray text-xs mts:text-sm mb-2">Saat
                                    </Link>
                                    <Link href="/kategoriler/oyun-konsolu-vr" class="text-kbgray text-xs mts:text-sm mb-2">Oyun Konsolu & VR
                                    </Link>
                                    <Link href="/kategoriler/elektrikli-ev-aletleri" class="text-kbgray text-xs mts:text-sm mb-2">Ev Aletleri
                                    </Link>
                                    <Link href="/kategoriler/kamera" class="text-kbgray text-xs mts:text-sm mb-2">
                                    Kameralar
                                    </Link>
                                    <Link href="/kategoriler/ses-muzik" class="text-kbgray text-xs mts:text-sm mb-2">Ses
                                    & Müzik
                                    </Link>
                                    <Link href="/kategoriler/kiralamobil" class="text-kbgray text-xs mts:text-sm mb-2">
                                    Kiralamobil
                                    </Link>
                                    <Link href="/kategoriler/saglik" class="text-kbgray text-xs mts:text-sm mb-2">Sağlık
                                    </Link>
                                    <Link href="/kategoriler/spor" class="text-kbgray text-xs mts:text-sm mb-2">Spor
                                    </Link>
                                    <Link href="/kategoriler/ev-eglence" class="text-kbgray text-xs mts:text-sm mb-2">Ev
                                    & Eğlence
                                    </Link>
                                    <a href="https://kiralamini.com/" target="_blank" class="text-kbgray text-xs mts:text-sm mb-2">Anne & Bebek</a>
                                </div>
                            </DisclosurePanel>
                            <DisclosureButton @click="hiddenbtn4 = !hiddenbtn4" v-if="hiddenbtn4"
                                class="bg-white text-xs text-kbgray border-1 border-kbgray rounded-full py-1 px-2 self-center font-santralextrabold mt-2 hover:bg-kbgreen hover:text-white hover:border-white">
                                Daha Fazla
                            </DisclosureButton>
                        </Disclosure>
                    </div>
                </div>
            </section>

            <div class="mt-6 block md:hidden max-w-kbmobile mx-auto md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mb-7">
                <img src="../../../images/logos-white.png" alt="" />
            </div>
            <div class="bg-[#f5f5f5] pt-5 pb-3">
                <div class="max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <div class="flex">
                            <!--                            <div class="text-sm text-kbgray">-->
                            <!--                                <a href="">Şartlar ve Koşullar</a>-->
                            <!--                            </div>-->
                            <!--                            <div class="text-sm text-kbgray mx-1">|</div>-->
                            <div class="text-sm text-kbgray">
                                <Link href="/kvkk"> Gizlilik Politikası</Link>
                            </div>
                        </div>
                        <div class="hidden lg:block">
                            <picture>
                                <source srcset="../../../images/welcome-webp/logocard.webp" type="image/webp" />
                                <img src="../../../images/logocard.png" alt="" />
                            </picture>
                        </div>
                        <div class="text-sm text-kbgray mx-1 mt-2">Kiralabunu.com © 2023 Tüm Hakları Saklıdır</div>
                    </div>
                </div>
            </div>
        </footer>
    </div>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white px-5 py-5 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 62.051 62.051">
                                <g id="Group_4884" data-name="Group 4884" transform="translate(-73.036 -250.295)">
                                    <circle id="Ellipse_199" data-name="Ellipse 199" cx="31.025" cy="31.025" r="31.025" transform="translate(73.036 250.295)" fill="#70d44b" />
                                    <path id="Path_3299" data-name="Path 3299"
                                        d="M351.649,476.232a3.45,3.45,0,0,1-2.761-1.381l-8.464-11.286a3.451,3.451,0,0,1,5.522-4.141l5.538,7.383,14.223-21.334a3.451,3.451,0,1,1,5.743,3.829l-16.928,25.393a3.453,3.453,0,0,1-2.776,1.536Z"
                                        transform="translate(-251.82 -178.764)" fill="#fff" />
                                </g>
                            </svg>
                            <DialogTitle>
                                <h2 class="text-base font-semibold text-center mb-2 mt-2">Teşekkürler</h2>
                            </DialogTitle>
                            <p class="text-xs font-kiralabunuthin ts:text-sm text-center">Yenilikler ve indirimler için
                                aboneliğiniz oluşturuldu.</p>
                            <!--                            <div class="mt-4">-->
                            <!--                                <button-->
                            <!--                                    type="button"-->
                            <!--                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"-->
                            <!--                                    @click="closeModal"-->
                            <!--                                >-->
                            <!--                                    Got it, thanks!-->
                            <!--                                </button>-->
                            <!--                            </div>-->
                            <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal">x</div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>

</template>

<script>
import { Link } from "@inertiajs/inertia-vue3";
import MegaMenu from "@/Pages/Shared/MegaMenu.vue";
import NewMenu from "@/Pages/Shared/NewMenuDemo.vue";
// import MobileMenu from "@/Pages/MobileMenu.vue";
import BasketNotification from "@/Pages/Shared/basketNotification.vue";
import { Dialog, DialogPanel, DialogTitle, Disclosure, DisclosureButton, DisclosurePanel, TransitionChild, TransitionRoot, Menu, MenuButton, MenuItems, MenuItem } from "@headlessui/vue";

export default {
    computed: {
        basketNotification() {
            return basketNotification;
        }
    },
    components: {
        BasketNotification,
        // MobileMenu,
        Link,
        MegaMenu,
        Menu,
        MenuButton,
        MenuItems,
        MenuItem,
        Disclosure,
        DisclosureButton,
        DisclosurePanel,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        NewMenu
    },
    data() {
        return {
            modalIsOpen: true,
            menu: false,
            userCart: null,
            userCartLength: 0,
            searchForm: this.$inertia.form({
                term: null
            }),
            subscribeForm: this.$inertia.form({
                email: null
            }),
            logoUrl: "/images/logo.png",
            logoLink: "/",
            hideTopMenu: false,
            isOpen: false,
            hiddenbtn1: true,
            hiddenbtn2: true,
            hiddenbtn3: true,
            hiddenbtn4: true,
            topbar: true,
            mobileMenuIsOpen: false,
            topBannerShow: false,
            affiliateObj: {},
            institutionalLayout: null
        };
    },
    props: {
        menuItems: Object,
        menuItemsMobile: Object,
        auth: Object,
        apiEP: String,
        wishlist: Number,
        cart: Number,
        affiliate: String,
        isAynet: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        // toggleMenu() {
        //     this.menu = false;
        //     setTimeout(() => {
        //         //this.menu = false;
        //     }, 300);
        // },
        closeModal() {
            console.log(this.isOpen);
            this.isOpen = false;
        },
        closeBolBolModal() {
            console.log(this.modalIsOpen);
            this.modalIsOpen = false;
        },
        openBolBolModal() {
            this.modalIsOpen = true;
        },
        closeMenu(value) {
            // console.log("closeMenu", value);
            this.mobileMenuIsOpen = value;
        },
        toggleBar() {
            this.topbar = !this.topbar;
        },
        openSubscribeModal() {
            if (this.subscribeForm.email == null || this.subscribeForm.email == "") return;

            if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(this.subscribeForm.email)) {
                this.isOpen = true;
            }
        },
        submitSearchForm() {
            // Arama metni boşsa arama yapma
            if (this.searchForm.term == null || this.searchForm.term == "") return;
            this.searchForm.get(route("searchResults"));
        },
        newMessage(something) {
            console.log("newMessage", something);
            //this.$vueEventBus.$emit("message-in", something);
        }
    },
    mounted() {
        this.userCart = JSON.parse(localStorage.getItem("userCart")) || [];
        // this.userCartLength = this.userCart.length;
        this.affiliateObj = {
            "aktifbank": {
                logoUrl: "/images/logo_nkolay.png",
                logoLink: "/",
                hideTopMenu: true
            },
            "migros": {
                logoUrl: "/images/logo_migros.png",
                logoLink: "/",
                hideTopMenu: true
            }
        };
    },
    created() {
        this.$inertia.on("navigate", (event) => {
            this.logoUrl = "/images/logo.png";
            if (event.detail.page.url.startsWith("/kurumsal/kategori") || event.detail.page.url == "/kurumsal" || event.detail.page.url == "/kurumsal-demo" || event.detail.page.url == "/musteri-cozumlerimiz" || event.detail.page.url == "/is-ortagi-cozumlerimiz") {
                this.logoUrl = "/images/kb-kurumsal.svg";
                this.logoLink = "/kurumsal";
                this.institutionalLayout = "demo";
            }

            if (event.detail.page.url.startsWith("/urun")) this.topBannerShow = false;
            if (event.detail.page.url.endsWith("?teknosa")) {
                this.logoUrl = "https://img-teknosa-sap.mncdn.com/_ui/responsive/theme-teknosa/assets/images/logo.svg";
                this.hideTopMenu = true;
                this.logoLink = "https://teknosa.com";
            }

            if (this.$props.affiliate === "aktifbank") {
                console.log("aktifbank");
                //this.logoUrl = this.affiliateObj.aktifbank.logoUrl;
                //this.logoLink = this.affiliateObj.aktifbank.logoLink;
                //this.hideTopMenu = this.affiliateObj.aktifbank.hideTopMenu;
                //console.log(this.affiliateObj.aktifbank.logoUrl, this.logoUrl);
            }
            if (this.$props.affiliate === "migros") {
                console.log("migros");
                this.logoUrl = this.affiliateObj.migros.logoUrl;
                //this.logoLink = this.affiliateObj.aktifbank.logoLink;
                //this.hideTopMenu = this.affiliateObj.aktifbank.hideTopMenu;
                console.log(this.affiliateObj.migros.logoUrl, this.logoUrl);
            }

            dataLayer.push({
                pageView: event.detail.page.url,
                event: "virtualPageView"
            });
        });

        window.axios.defaults.headers.common["Authorization"] = `Bearer ${this.auth.token}`;

        if (this.auth.isUserLoggedIn && this.auth.user == null) {
            axios.get(`${this.apiEP}auth/user/cart/get-auth`).then((res) => {
                this.auth.user = res.data;
                window.localStorage.setItem("user", JSON.stringify(res.data));
            });
        } else if (this.auth.isUserLoggedIn && this.auth.user != null) {
            window.localStorage.setItem("user", JSON.stringify(this.auth.user));
        }
    }
// setup() {
    //     const alert = computed(() => usePage().props.value.flash.alert);
    //     return { alert };
    // },
};
</script>

<style scoped></style>
