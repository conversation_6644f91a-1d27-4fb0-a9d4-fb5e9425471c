<script>
import { <PERSON> } from "@inertiajs/inertia-vue3";
import { vMaska } from "maska";

export default {
    components: {
        Link
    },
    props: {
        user: Object,
        cities: Array,
        errors: { type: Object, default: false }
    },
    directives: { maska: vMaska },
    data() {
        return {
            isEdit: false,
            isEdit2: false,
            form: this.$inertia.form({
                id: this.user.address?.id ?? null,
                first_name: this.user.address?.first_name ?? null,
                last_name: this.user.address?.last_name ?? null,
                city: this.user.address?.city ?? null,
                city_id: this.user.address?.city_id ?? null,
                district: this.user.address?.district ?? null,
                county_id: this.user.address?.county_id ?? null,
                // birtdate: false,
                tckn: this.user.address?.tckn ?? null,
                phone: this.user.address?.phone ?? null,
                name: this.user.address?.name ?? null,
                address: this.user.address?.address ?? null,
                bill_type: this.user.address?.bill_type ?? "",
                firn_name: this.user.address?.firn_name ?? null,
                tax_no: this.user.address?.tax_no ?? null,
                tax_office: this.user.address?.tax_office ?? null
            })
        };
    },
    methods: {
        editAddress() {
            this.isEdit = true;
        },
        editAddress2() {
            this.isEdit2 = true;
        },
        completeAddress() {
            this.isEdit = false;
        },
        completeAddress2() {
            this.isEdit2 = false;
        },
        submit() {
            this.form.post("/adres-duzenle-sepet", {
                preserveScroll: true,
                onSuccess: () => {
                    this.isEdit = false;
                    this.isEdit2 = false;
                }
            });
        },
        cityChanged() {
            this.form.county_id = null;
        },
        preventNumericInput($event) {
            //console.log($event.keyCode); will display the keyCode value
            //console.log($event.key); will show the key value

            var charCode = $event.keyCode ? $event.keyCode : $event.which;
            if (
                (charCode <= 93 && charCode >= 65) ||
                (charCode <= 122 && charCode >= 97) ||
                charCode == 8 ||
                charCode == 350 ||
                charCode == 351 ||
                charCode == 304 ||
                charCode == 286 ||
                charCode == 287 ||
                charCode == 231 ||
                charCode == 199 ||
                charCode == 305 ||
                charCode == 214 ||
                charCode == 246 ||
                charCode == 220 ||
                charCode == 252 ||
                charCode == 32
            ) {
                if (charCode != 33 || charCode != 43) {
                    return true;
                } else {
                    $event.preventDefault();
                }
            } else {
                $event.preventDefault();
            }
        }
    }
};
</script>

<template>
    <p class="text-xl mb-2 text-black font-bold">Teslimat Bilgileri</p>
    <p class="text-base mb-1 text-black">Teslimat Adresi</p>

    <div v-if="!isEdit" class="w-full flex justify-between items-center border-2 border-bordergray rounded-lg p-3 mb-5">
        <div class="text-center w-full" v-if="user.addresses.length == 0">
            <h3 class="mt-2 text-sm font-semibold text-gray-900">Adresiniz bulunmamaktadır</h3>
            <div class="mt-2">
                <button type="button" @click="editAddress" class="bg-black text-white text-center rounded-full py-1 text-lg font-bold hover:bg-kbgreen inline-flex items-center rounded-md px-3 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2">Adres Ekle</button>
            </div>
        </div>
        <div v-else class="flex flex-wrap w-full">
            <div class="flex flex-col w-full lg:w-10/12 items-start">
                <p class="text-lg pb-1 text-black font-bold">
                    <a>{{ form.first_name }} {{ form.last_name }}</a>
                </p>
                <p class="text-sm lg:text-base text-textgray font-medium">
                    <a>{{ user.address?.address }}</a>
                </p>
            </div>
            <div class="w-full lg:w-2/12 pr-3 flex items-center justify-center mt-2 lg:mt-0 lg:justify-end">
                <p class="text-sm lg:text-lg text-black font-bold cursor-pointer" @click="editAddress">Düzenle</p>
                <!--            <a href="/sepet-adres" >Düzenle</a>-->
            </div>
        </div>
    </div>
    <div v-if="isEdit" class="border-2 border-bordergray rounded-2lg mb-5">
        <div class="w-full px-4 flex justify-center lg:justify-start items-center mt-4">
            <input id="ayni" class="w-4 h-4 border-3 rounded-md focus:outline-none checked:bg-black" type="checkbox" checked />
            <label class="text-sm lg:text-base text-black pl-2" for="ayni">Fatura bilgilerim, teslimat adres bilgilerim ile aynı.</label>
        </div>
        <section class="mt-6 flex flex-col lg:flex-row">
            <div class="w-full px-4">
                <form @submit.prevent="submit">
                    <div class="flex flex-row justify-center items-start w-full">
                        <div class="hidden md:block w-6/12 text-center px-3">
                            <div class="relative group">
                                <input
                                    v-model="form.first_name"
                                    id="name"
                                    @keypress="preventNumericInput"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required
                                    type="text"
                                    placeholder=""
                                    name="AdSoyad"
                                    autofocus
                                />
                                <label
                                    for="name"
                                    class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Adın*</label
                                >
                            </div>
                            <select v-model="form.city_id" @change="cityChanged" class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="sirala" id="ilsec">
                                <option value="">İl Seç*</option>
                                <template v-for="(city, index) in [...new Map(cities.map((m) => [m.city_id, m])).values()]" :key="index">
                                    <option :value="city.city_id">{{ city.city }}</option>
                                </template>
                            </select>

                            <div class="group relative">
                                <input id="addressname" v-model="form.name" class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="adresadi" />
                                <label
                                    for="addressname"
                                    class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Adres Başlığı*</label
                                >
                            </div>
                        </div>
                        <div class="hidden md:block w-6/12 text-center px-3 relative group">
                            <input
                                id="surname"
                                v-model="form.last_name"
                                @keypress="preventNumericInput"
                                class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                required
                                type="text"
                                placeholder=""
                                name="Soyad"
                            />
                            <label
                                for="surname"
                                class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                            >Soyadın*</label
                            >
                            <select v-model="form.county_id" class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="sirala" id="sirala" required>
                                <option value="">İlçe seç*</option>
                                <template v-for="(county, index) in cities.filter((c) => c.city_id == this.form.city_id)" :key="index">
                                    <option :value="county.county_id">{{ county.county }}</option>
                                </template>
                            </select>
                            <div class="relative group">
                                <input
                                    v-model="form.phone"
                                    v-maska
                                    data-maska="(5##) ### ## ##"
                                    id="telephone"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required
                                    type="text"
                                    placeholder=""
                                    name="telno"
                                />
                                <label
                                    for="telephone"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Telefon*</label
                                >
                            </div>
                        </div>

                        <div class="block md:hidden w-full text-center px-3">
                            <div class="group relative">
                                <input
                                    v-model="form.first_name"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required
                                    type="text"
                                    placeholder=""
                                    id="namemobile2"
                                    name="AdSoyad"
                                    autofocus
                                />
                                <label
                                    for="namemobile2"
                                    class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Adın*</label
                                >
                            </div>
                            <div class="group relative">
                                <input v-model="form.last_name" class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" id="surnamemobile2" name="Soyad" />
                                <label
                                    for="surnamemobile2"
                                    class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Soyadın*</label
                                >
                            </div>
                            <div class="relative group">
                                <input
                                    v-model="form.phone"
                                    v-maska
                                    data-maska="(5##) ### ## ##"
                                    id="telephonemobile2"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required
                                    type="text"
                                    placeholder=""
                                    name="telno"
                                />
                                <label
                                    for="telephonemobile2"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Telefon*</label
                                >
                            </div>
                            <select v-model="form.city_id" class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider" name="citymobile2" id="citymobile2">
                                <option>İl Seç*</option>
                                <template v-for="(city, index) in [...new Map(cities.map((m) => [m.city_id, m])).values()]" :key="index">
                                    <option :value="city.city_id">{{ city.city }}</option>
                                </template>
                            </select>
                            <select v-model="form.county_id" class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider" name="countrymobile2" id="countrymobile2">
                                <option value="">İlçe seç*</option>
                                <template v-for="(county, index) in cities.filter((c) => c.city_id == this.form.city_id)" :key="index">
                                    <option :value="county.county_id">{{ county.county }}</option>
                                </template>
                            </select>

                            <div class="group relative">
                                <input v-model="form.name" class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" id="addressnamemobile2" placeholder="" name="adresadi" />
                                <label
                                    for="addressnamemobile2"
                                    class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Adres Başlığı*</label
                                >
                            </div>
                        </div>
                    </div>
                    <div class="w-full px-3">
                        <div class="group relative">
                            <textarea
                                id="address"
                                class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-lg tracking-wider placeholder:mt-2 h-32"
                                required
                                name="Adres"
                                placeholder=""
                                cols="20"
                                rows="10"
                                v-model="form.address"
                            ></textarea>
                            <label
                                for="address"
                                class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                            >Adresin*</label
                            >
                        </div>
                        <div class="flex flex-wrap lg:flex-nowrap justify-between">
                            <select v-model="form.bill_type" class="w-full lg:w-3/12 mr-2 w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="sirala" id="sirala" required>
                                <option>Fatura Türü*</option>
                                <option value="Bireysel">Bireysel</option>
                                <option value="Kurumsal">Kurumsal</option>
                            </select>
                            <div class="w-full lg:w-8/12 flex flex-wrap md:flex-nowrap" v-if="form.bill_type == 'Kurumsal'">
                                <div class="group relative w-full md:w-1/3 mr-2">
                                    <input v-model="form.firn_name" id="companyname" class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Firma" />
                                    <label
                                        for="companyname"
                                        class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                    >Firma Adı</label
                                    >
                                </div>
                                <div class="group relative w-full md:w-1/3 mr-2">
                                    <input v-model="form.tax_no" id="taxno" class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Numarası" />
                                    <label
                                        for="taxno"
                                        class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                    >Vergi Numarası
                                    </label>
                                </div>
                                <div class="group relative w-full md:w-1/3">
                                    <input id="taxname" v-model="form.tax_office" class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Dairesi" />
                                    <label
                                        for="taxname"
                                        class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                    >Vergi Dairesi</label
                                    >
                                </div>
                            </div>
                            <div class="w-full ts:w-8/12 flex" v-if="form.bill_type == 'Bireysel'">
                                <input
                                    v-maska
                                    data-maska="###########"
                                    v-model="form.tckn"
                                    class="w-full ts:w-1/3 mb-5 mr-2 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required
                                    type="text"
                                    placeholder="Tc Kimlik Numaran *"
                                    name="tckn"
                                />
                            </div>
                        </div>
                    </div>
                    <div>
                        <ul v-for="error in errors">
                            <li>{{ error }}</li>
                        </ul>
                    </div>
                    <div class="relative group w-full flex justify-end my-2">
                        <button type="submit" class="bg-black text-white text-center rounded-full py-2 text-lg px-2 self-center font-bold w-full md:w-[200px;] hover:bg-kbgreen" :disabled="form.processing">Kaydet</button>
                    </div>
                </form>
            </div>
        </section>
    </div>

    <p class="text-base mb-1 text-black">Fatura Bilgisi</p>
    <div v-if="!isEdit2" class="w-full flex flex-wrap justify-between items-center border-2 border-bordergray rounded-lg p-3 mb-5">
        <div class="flex flex-col w-full lg:w-10/12 items-start">
            <p class="text-lg pb-1 text-black font-bold">
                <a>{{ form.first_name }} {{ form.last_name }}</a>
            </p>
            <p class="text-sm lg:text-base text-textgray font-medium">
                <a>{{ user.address?.address }}</a>
            </p>
        </div>
        <div class="w-full lg:w-2/12 pr-3 flex items-center justify-center mt-2 lg:mt-0 lg:justify-end">
            <!--            <p class="text-sm lg:text-lg  text-black font-bold"><a href="/sepet-adres">Düzenle</a> </p>-->
            <p class="text-sm lg:text-lg text-black font-bold cursor-pointer" @click="editAddress2">Düzenle</p>
        </div>
    </div>
    <div v-if="isEdit2" class="border-2 border-bordergray rounded-2lg mb-5">
        <section class="mt-10 flex flex-col lg:flex-row">
            <div class="w-full px-4">
                <form @submit.prevent="submit">
                    <div class="flex flex-row justify-center items-start w-full">
                        <div class="hidden md:block w-6/12 text-center px-3">
                            <div class="relative group">
                                <input
                                    v-model="form.first_name"
                                    id="name"
                                    @keypress="preventNumericInput"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required
                                    type="text"
                                    placeholder=""
                                    name="AdSoyad"
                                    autofocus
                                />
                                <label
                                    for="name"
                                    class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Adın*</label
                                >
                            </div>
                            <select v-model="form.city_id" @change="cityChanged" class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="sirala" id="ilsec" required>
                                <option value="">İl Seç*</option>
                                <template v-for="(city, index) in [...new Map(cities.map((m) => [m.city_id, m])).values()]" :key="index">
                                    <option :value="city.city_id">{{ city.city }}</option>
                                </template>
                            </select>

                            <div class="group relative">
                                <input id="addressname" v-model="form.name" class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="adresadi" />
                                <label
                                    for="addressname"
                                    class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Adres Başlığı*</label
                                >
                            </div>
                        </div>
                        <div class="hidden md:block w-6/12 text-center px-3 relative group">
                            <input
                                id="surname"
                                v-model="form.last_name"
                                @keypress="preventNumericInput"
                                class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                required
                                type="text"
                                placeholder=""
                                name="Soyad"
                            />
                            <label
                                for="surname"
                                class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                            >Soyadın*</label
                            >
                            <select v-model="form.county_id" class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="sirala" id="sirala" required>
                                <option value="">İlçe seç*</option>
                                <template v-for="(county, index) in cities.filter((c) => c.city_id == this.form.city_id)" :key="index">
                                    <option :value="county.county_id">{{ county.county }}</option>
                                </template>
                            </select>
                            <div class="relative group">
                                <input
                                    v-model="form.phone"
                                    v-maska
                                    data-maska="(5##) ### ## ##"
                                    id="telephone"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required
                                    type="text"
                                    placeholder=""
                                    name="telno"
                                />
                                <label
                                    for="telephone"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Telefon*</label
                                >
                            </div>
                        </div>

                        <div class="block md:hidden w-full text-center px-3">
                            <div class="group relative">
                                <input
                                    v-model="form.first_name"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required
                                    type="text"
                                    placeholder=""
                                    name="AdSoyad"
                                    id="namemobile"
                                    autofocus
                                />
                                <label
                                    for="namemobile"
                                    class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Adın*</label
                                >
                            </div>
                            <div class="group relative">
                                <input v-model="form.last_name" class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Soyad" id="surnamemobile" />
                                <label
                                    for="surnamemobile"
                                    class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Soyadın*</label
                                >
                            </div>

                            <select v-model="form.city_id" class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider" name="citymobile" id="citymobile">
                                <option>İl Seç*</option>
                                <template v-for="(city, index) in [...new Map(cities.map((m) => [m.city_id, m])).values()]" :key="index">
                                    <option :value="city.city_id">{{ city.city }}</option>
                                </template>
                            </select>
                            <select v-model="form.county_id" class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider" name="countrymobile" id="countrymobile">
                                <option value="">İlçe seç*</option>
                                <template v-for="(county, index) in cities.filter((c) => c.city_id == this.form.city_id)" :key="index">
                                    <option :value="county.county_id">{{ county.county }}</option>
                                </template>
                            </select>
                            <div class="relative group">
                                <input
                                    v-model="form.phone"
                                    v-maska
                                    data-maska="(5##) ### ## ##"
                                    id="telephonemobile"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required
                                    type="text"
                                    placeholder=""
                                    name="telno"
                                />
                                <label
                                    for="telephonemobile"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Telefon*</label
                                >
                            </div>
                            <div class="group relative">
                                <input v-model="form.name" class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="adresadi" id="addressnamemobile" />
                                <label
                                    for="addressnamemobile"
                                    class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                >Adres Başlığı*</label
                                >
                            </div>
                        </div>
                    </div>
                    <div class="w-full px-3">
                        <div class="group relative">
                            <textarea
                                id="address"
                                class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-lg tracking-wider placeholder:mt-2 h-32"
                                required
                                name="Adres"
                                placeholder=""
                                cols="20"
                                rows="10"
                                v-model="form.address"
                            ></textarea>
                            <label
                                for="address"
                                class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                            >Adresin*</label
                            >
                        </div>
                        <div class="flex flex-wrap lg:flex-nowrap justify-between">
                            <select v-model="form.bill_type" class="w-full lg:w-3/12 mr-2 w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="sirala" id="sirala">
                                <option>Fatura Türü*</option>
                                <option value="Bireysel">Bireysel</option>
                                <option value="Kurumsal">Kurumsal</option>
                            </select>
                            <div class="w-full lg:w-8/12 flex flex-wrap md:flex-nowrap" v-if="form.bill_type == 'Kurumsal'">
                                <div class="group relative w-full md:w-1/3 mr-2">
                                    <input v-model="form.firn_name" id="companyname" class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Firma" />
                                    <label
                                        for="companyname"
                                        class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                    >Firma Adı</label
                                    >
                                </div>
                                <div class="group relative w-full md:w-1/3 mr-2">
                                    <input v-model="form.tax_no" id="taxno" class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Numarası" />
                                    <label
                                        for="taxno"
                                        class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                    >Vergi Numarası
                                    </label>
                                </div>
                                <div class="group relative w-full md:w-1/3">
                                    <input id="taxname" v-model="form.tax_office" class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Dairesi" />
                                    <label
                                        for="taxname"
                                        class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black"
                                    >Vergi Dairesi</label
                                    >
                                </div>
                            </div>
                            <div class="w-full ts:w-8/12 flex" v-if="form.bill_type == 'Bireysel'">
                                <input
                                    v-maska
                                    data-maska="###########"
                                    v-model="form.tckn"
                                    class="w-full ts:w-1/3 mb-5 mr-2 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required
                                    type="text"
                                    placeholder="Tc Kimlik Numaran *"
                                    name="tckn"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="relative group w-full flex justify-end my-2">
                        <button type="submit" class="bg-black text-white text-center rounded-full py-2 text-lg px-2 self-center font-bold w-full md:w-[200px;] hover:bg-kbgreen" :disabled="form.processing">Kaydet</button>
                    </div>
                </form>
            </div>
        </section>
    </div>
</template>
