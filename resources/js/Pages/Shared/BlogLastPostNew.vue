<template>
    <div v-for="(blog, index) in all" class="overflow-hidden rounded-[7px] relative group">
        <picture>
            <source :srcset="blog.cover_image.S3Conversions.large1" type="image/webp">
            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" :src="blog.cover_image.S3URL" />
        </picture>
        <Link class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black cursor-pointer" :href="`/blog/${blog.slug}`">
            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                <div class="flex space-x-2">
                    <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2" v-for="tag in blog.tags.filter( tag => tag.slug != 'topblog' ).slice(0,3)">{{ tag.title }}</div>

                </div>
                <div class="text-base lg:text-3xl font-bold mb-5 text-white mb-2">{{ blog.menu_name }}</div>
                <div class="flex space-x-3 mb-4">
                    <div class="text-xs text-white flex space-x-2">
                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                            <path id="Path_107" data-name="Path 107" d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z" transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                        </svg>
                        <span>{{ blog.blog_date_pretty }}</span>
                    </div>
                </div>
            </div>
        </Link>
    </div>
</template>

<script>
import { Link } from "@inertiajs/inertia-vue3";

export default {
    components: {
        Link
    },
    props: {
        all: { type: Object }
    }
};
</script>

<style scoped></style>
