<template>
    <div class="w-full flex flex-row lg:flex-col space-x-3 lg:space-x-0 space-y-3 overflow-y-scroll lg:overflow-y-visible">
        <div v-for="blog in sideBlogs"
             @click="goToBlog(blog.slug)"
             class="min-w-[300px] w-full border-1 border-bordergray flex flex-wrap rounded-[7px] hover:border-kbgreen transition-all duration-300 cursor-pointer">
            <div class="w-1/3 overflow-hidden rounded-[7px]">
                <picture>
                    <source :srcset="blog.cover_image.S3Conversions.medium2" type="image/webp">
                    <img class="w-full rounded-[7px]" :src="blog.cover_image.S3URL" />
                </picture>
            </div>
            <div class="w-2/3 flex flex-col pl-5 justify-center items-start">
                <div class="text-sm lg:text-base font-bold mb-2">
                    <Link :href="'/blog/' + blog.slug">{{ blog.title }}</Link>
                </div>
                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-5">{{ blog.tags[0].title }}</div>
                <div class="flex space-x-3 justify-start">
                    <div class="text-xs text-left-menu-gray flex space-x-2">
                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                            <path id="Path_107" data-name="Path 107" d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z" transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                        </svg>
                        <span>{{ blog.blog_date_pretty }}</span>
                    </div>
                    <!--                    <div class="text-xs text-left-menu-gray flex space-x-2">-->
                    <!--                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">-->
                    <!--                            <g id="view" transform="translate(0)">-->
                    <!--                                <path id="Path_108" data-name="Path 108" d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z" transform="translate(-1.126 -5.625)" fill="#e2e2e2" />-->
                    <!--                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z" transform="translate(-6.455 -8.586)" fill="#e2e2e2" />-->
                    <!--                            </g>-->
                    <!--                        </svg>-->
                    <!--                        <span>372</span>-->
                    <!--                    </div>-->
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { Link } from "@inertiajs/inertia-vue3";

export default {
    components: {
        Link
    },
    props: {
        sideBlogs: Object
    },
    methods: {
        goToBlog(slug) {
            window.location.href = "/blog/" + slug;
        }
    }
};
</script>

<style scoped></style>
