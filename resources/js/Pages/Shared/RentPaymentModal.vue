<script setup>
import { useForm } from "@inertiajs/inertia-vue3";
import { onMounted, ref, watch } from "vue";
import Loader from "@/Pages/Shared/Loader.vue";
import { Inertia } from "@inertiajs/inertia";

const form = useForm({
    orderTransactions: [],
});

const modalOpenCount = ref(0);
const loader = ref(false);

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false,
    },
    order: {
        type: Object,
        default: () => ({}),
    },
    orderTransactions: {
        type: Object,
        default: () => ({}),
    },
    payedRentInfo: {
        type: Object,
        default: () => ({}),
    },
});

defineEmits(["update:isOpen"]);
watch(
    () => props.isOpen,
    (value, oldValue) => {
        console.log("aç kapa", value);
        if (modalOpenCount.value != props.order[0].id) {
            loader.value = true;
            Inertia.reload({
                only: ["orderTransactions"],
                data: { orderTransaction: props.order[0].id },
                onFinish: () => {
                    loader.value = false;
                },
            }); //form.orderTransaction = orderTransactions.id ?? 0;
            modalOpenCount.value = props.order[0].id;
        }
    }
);

onMounted(() => {
    modalOpenCount.value = 0;
});
const submit = () => {
    console.log("submitted");
    form.clearErrors();
    form.post("process-rent-payment", { only: ["orderTransactions", "payedRentInfo"], data: { orderTransaction: props.order[0].id } });
    form.reset();
};
</script>

<template>
    <!-- Main modal -->
    <div v-if="isOpen" id="default-modal" tabindex="-1" aria-hidden="true" class="flex justify-center items-center fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-black/30">
        <div class="relative w-full max-w-2xl max-h-full">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow">
                <!-- Modal header -->
                <div class="flex items-start justify-between rounded-t">
                    <button @click="$emit('update:isOpen', false)" type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="default-modal">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div class="p-6 flex flex-wrap">
                    <div class="w-full md:w-12/12 p-3 lg:p-8">
                        <div class="flex flex-col justify-between h-full">
                            <div class="flex justify-center mb-10 bg-yellow-300 p-5" v-if="payedRentInfo?.totalAmount > 0">
                                <p class="text-center">Toplam {{ payedRentInfo?.totalAmount }} TL tutarında {{ payedRentInfo?.count }} adet kiralama planınız başarı ile ödenmiştir</p>
                            </div>
                            <div class="flex justify-center mb-10 bg-yellow-300 p-5" v-if="payedRentInfo?.totalAmount == 0">
                                <p class="text-center">Çekim denemesi başarısız. Lütfen kayıtlı kredi kartlarınızı kontrol ediniz.</p>
                            </div>
                            <div class="flex justify-center">
                                <p class="text-center">Vadesi gelmiş ve henüz ödenmemiş kiralama planlarınız içerisinden ödeme yapabilirsiniz</p>
                            </div>
                            <div class="items-center">
                                <form class="w-full flex items-center flex-col pb-1" @submit.prevent="submit">
                                    <fieldset v-if="!loader && orderTransactions.length > 0">
                                        <legend class="sr-only">Notifications</legend>
                                        <div class="space-y-5 mt-7">
                                            <div class="relative flex items-start" v-for="item in orderTransactions">
                                                <div class="flex h-6 items-center">
                                                    <input :id="`transaction-${item.id}`" aria-describedby="comments-description" name="comments" type="checkbox" :value="item.id" v-model="form.orderTransactions" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" />
                                                </div>
                                                <div class="ml-3 text-sm leading-6">
                                                    <label :for="`transaction-${item.id}`" class="font-medium text-gray-900">{{ item.due_date }} vadeli {{ item.amount }} TL</label>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset v-if="!loader && orderTransactions.length == 0">
                                        <div class="space-y-5 mt-7">
                                            <div class="relative flex items-start">
                                                <div class="flex h-6 items-center"></div>
                                                <div class="ml-3 text-sm leading-6">
                                                    <label for="transaction-0" class="font-medium text-gray-900">Vadesi gelmiş ve ödenmemiş ödeme planınız bulunmamaktadır</label>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <loader :active="loader" class="grid h-[80px] place-items-center" message="Please wait 5 seconds" />
                                    <!--                                    {{ form.orderTransactions }}-->
                                    <button class="bg-kbgreen text-white w-64 rounded-full my-2 self-center text-lg font-bold" type="submit" :disabled="form.processing" v-if="!loader && orderTransactions.length > 0">
                                        <div class="flex items-center justify-center m-[10px]">
                                            <div class="h-5 w-5 border-t-transparent border-solid animate-spin rounded-full border-white border-4" v-if="form.processing"></div>
                                            <div class="ml-2">Devam</div>
                                        </div>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped></style>
