<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { vMaska } from "maska";

export default {
    components: {
        Link,
        Head,
        UserMenu,
    },
    directives: { maska: vMaska },
    data() {
        return {
            form: this.$inertia.form({
                holder: null,
                number: null,
                month: null,
                year: null,
                cvc: null,
                ccName: null,
                source: "profile",
            }),
        };
    },
    props: {
        errors: { type: Object, default: false },
        iyzicoFormEnabled: { type: Boolean, default: false },
        order_count: Number,
        init3D: String,
    },
    methods: {
        submit() {
            this.form.post(route("saveCC"));
        },
        preventNumericInput($event) {
            //console.log($event.keyCode);will display the keyCode value
            //console.log($event.key); //will show the key value

            var keyCode = $event.keyCode ? $event.keyCode : $event.which;
            if (keyCode > 47 && keyCode < 58) {
                $event.preventDefault();
            }
        },
    },
    watch: {
        init3D(newValue, oldValue) {
            // console.log("newValue", newValue);
            if (newValue) {
                setTimeout(function () {
                    document.getElementById("iyzico-3ds-form").submit();
                }, 1500);
            }
        },
    },
    layout: Layout,
};
</script>

<template>

    <Head title="Ödeme Yöntemi Ekle" />
    <main class="my-6 max-w-[305px] md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mb-12 flex flex-row">
            <user-menu :order_count="order_count" :active="`AddAddress`" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 pt-2">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7 pl-4">Ödeme Yöntemi Ekle</h3>
                </div>
                <form @submit.prevent="submit" v-if="init3D == null">
                    <div class="flex flex-col-reverse lg:flex-row justify-around items-start w-full">
                        <div class="w-full lg:w-6/12 text-center mt-6 lg:mt-0">
                            <div class="relative group">
                                <input v-model="form.holder" id="adi"
                                    class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="AdSoyad" autofocus />
                                <label for="adi"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Ad
                                    Soyad*</label>
                            </div>
                            <div class="relative group">
                                <input v-model="form.number" v-maska data-maska="#### #### #### ####"
                                    class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="Numarası" id="kartno" />
                                <label for="kartno"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Kart
                                    Numarası*</label>
                            </div>
                            <div class="flex md:w-2/3 ts:w-10/12 pb-1">
                                <label for="ay" class="ml-1 w-2/3 text-left font-medium text-sm"> Son Kullanma Tarihi :</label>
                                <label for="cvv" class="w-1/3 text-center font-medium text-sm hidden md:block"> CVV Kodu :</label>
                            </div>
                            <div class="w-full md:w-2/3 ts:w-10/12 flex flex-wrap md:flex-nowrap">
                                <!--                                <input-->
                                <!--                                    v-model="form.month"-->
                                <!--                                    v-maska-->
                                <!--                                    data-maska="##"-->
                                <!--                                    id="ay"-->
                                <!--                                    class="w-5/12 mb-4 mr-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"-->
                                <!--                                    required-->
                                <!--                                    type="text"-->
                                <!--                                    placeholder="Ay*"-->
                                <!--                                    name="Ay"-->
                                <!--                                />-->
                                <!--                                <input v-model="form.year" v-maska data-maska="20##"-->
                                <!--                                       class="w-5/12 mb-4 mr-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"-->
                                <!--                                       required type="text" placeholder="Yıl*" name="Yıl" />-->
                                <select name="ay" id="ay" required v-model="form.month"
                                    class="w-5/12 mb-4 mr-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider">
                                    <option value="Ay" selected disabled>Ay</option>
                                    <option value="01">01</option>
                                    <option value="02">02</option>
                                    <option value="03">03</option>
                                    <option value="04">04</option>
                                    <option value="05">05</option>
                                    <option value="06">06</option>
                                    <option value="07">07</option>
                                    <option value="08">08</option>
                                    <option value="09">09</option>
                                    <option value="10">10</option>
                                    <option value="11">11</option>
                                    <option value="12">12</option>
                                </select>
                                <select name="Yıl" id="yil" required v-model="form.year"
                                    class="w-5/12 mb-4 mr-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider">
                                    <option value="Ay" selected disabled>Yıl</option>
                                    <option value="2025">2025</option>
                                    <option value="2026">2026</option>
                                    <option value="2027">2027</option>
                                    <option value="2028">2028</option>
                                    <option value="2029">2029</option>
                                    <option value="2030">2030</option>
                                    <option value="2031">2031</option>
                                    <option value="2032">2032</option>
                                    <option value="2033">2033</option>
                                    <option value="2034">2034</option>
                                    <option value="2035">2035</option>
                                </select>
                                <div class="w-full md:w-5/12 text-left">
                                    <label for="cvv" class="w-1/3 text-center font-medium text-sm block md:hidden"> CVV Kodu :</label>
                                    <input id="cvv" maxlength="4" minlength="3" v-maska data-maska="####"
                                        class="w-1/3 md:w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                        required type="number" placeholder="CVV*" name="CVV" v-model="form.cvc" />
                                </div>
                            </div>
                            <div class="w-full mb-3 mt-1 relative group">
                                <input v-model="form.ccName"
                                    class="peer w-full rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required
                                    type="text" placeholder="" name="Numarası" id="kartad" />
                                <label for="kartad"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Kart
                                    Başlığı*</label>
                            </div>
                            <div class="flex mt-4 w-80 md:w-96 self-center flex-col" v-if="Object.entries(errors).length > 0">
                                <div class="text-xs mx-auto text-kbred my-1">{{ errors[0] }}</div>
                            </div>
                            <div class="w-full flex justify-end">
                                <button class="w-full ts:w-auto bg-black text-white rounded-full py-2 px-5 text-base font-bold whitespace-nowrap" type="submit" :disabled="form.processing">Kartı Ekle</button>
                            </div>
                        </div>
                        <div class="w-full lg:w-5/12 text-center">
                            <img class="w-full md:max-w-[360px] mx-auto" src="../../images/kart.jpg" alt="" />
                        </div>
                    </div>
                </form>
                <div v-else>
                    <div>Bankaya Yönlendiriliyor</div>
                    <div v-html="init3D"></div>
                </div>
            </div>
        </section>
    </main>
</template>
