<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenuNew from "@/Pages/Shared/BlogSideMenuNew.vue";

export default {
    components: {
        BlogSideMenuNew,
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack
    },
    props: {
        blog: Object,
        sideBlogs: Object
    },
    layout: Layout,
    data() {
        return {
            blogContent: this.blog.content
        };
    },
    setup() {

    }
};
</script>
<style>
.blog-content div a {
    text-decoration: underline;
    font-weight: bold;
    color: black;
}

.blog-content div.text-area h1 {
    font-weight: 600;
    padding-bottom: 4px;
    font-size: 1.875rem;
    line-height: 2.25rem;
    color: black;
}

.font-santralregular div {
    font-family: santralregular, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.font-santralregular li {
    list-style: disc;
    font-family: santralregular, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
</style>

<template>

    <Head :title="blog.title" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12 blog-content">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source :srcset="blog.cover_image.S3Conversions.large1" type="image/webp">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" :src="blog.cover_image.S3URL" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">{{ blog.tags[0].title }}</div>
                                <div class="text-base lg:text-3xl font-bold mb-5 text-white mb-2">{{ blog.title }}</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>{{ blog.blog_date_pretty }}</span>
                                    </div>
                                    <!--                                    <div class="text-xs text-white flex space-x-2">-->
                                    <!--                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">-->
                                    <!--                                            <g id="view" transform="translate(0)">-->
                                    <!--                                                <path id="Path_108" data-name="Path 108" d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z" transform="translate(-1.126 -5.625)" fill="#e2e2e2" />-->
                                    <!--                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z" transform="translate(-6.455 -8.586)" fill="#e2e2e2" />-->
                                    <!--                                            </g>-->
                                    <!--                                        </svg>-->
                                    <!--                                        <span>548</span>-->
                                    <!--                                    </div>-->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <div v-for="item in blog.content">
                            <div v-if="item.layout == 'text'" class="text-sm text-checkoutgray text-area font-santralregular" v-html="item.attributes.text"></div>
                            <div v-if="item.layout == 'video'" class="mx-auto">
                                <div v-html="item.attributes.video"></div>
                            </div>
                            <div v-if="item.layout == '2-photo'" class="flex flex-wrap px-0 py-4">
                                <img :src="item.attributes.image_id1" class="w-full md:w-1/2 md:pr-2 rounded-[7px]">
                                <img :src="item.attributes.image_id2" class="w-full md:w-1/2 md:pl-2 rounded-[7px]">
                            </div>
                            <div v-if="item.layout == 'image-library'">
                                <img :src="item.attributes.image_id" class="mx-auto rounded-[7px]">
                            </div>
                            <div v-if="item.layout == '3-photo'" class="flex flex-wrap px-0 py-4">
                                <img :src="item.attributes.image_id1" class="w-full md:w-1/3 md:pr-2 rounded-[7px]">
                                <img :src="item.attributes.image_id2" class="w-full md:w-1/3 md:pr-2 rounded-[7px]">
                                <img :src="item.attributes.image_id3" class="w-full md:w-1/3 md:pr-2 rounded-[7px]">
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-3xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <template v-for="tag in blog.tags.filter(tag => tag.title != 'topBlog')">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2" v-if="tag.title != 'topBlog' && tag.title != 'topblog'">{{ tag.title }}</div>
                            </template>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <BlogSideMenuNew :side-blogs="sideBlogs"></BlogSideMenuNew>
                </div>
            </div>

        </section>
    </div>
</template>
