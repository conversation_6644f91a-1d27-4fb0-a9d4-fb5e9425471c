<script>
import { Head, <PERSON> } from '@inertiajs/inertia-vue3'
import Layout from '@/Pages/Shared/Layout.vue'
import CheckoutPaymentInfo from '@/Components/CheckoutPaymentInfo.vue'
import CartProductPreview from '@/Components/CartProductPreview.vue'
import InfoCard from '@/Components/InfoCard.vue'
import PaymentOptions from '@/Components/PaymentOptions.vue'
import CartAddresses from "@/Pages/Shared/CartAddresses.vue";

export default {
  components: {
        CartAddresses,
        Link,
        Head,
        CheckoutPaymentInfo,
        CartProductPreview,
        InfoCard,
        PaymentOptions,
  },
  props: {
    products: Array,
    total: Number,
    user: Object,
  },
  layout: Layout,
}
</script>

<template>
    <Head title="Destek Talepleri" />

    <main class="my-6 max-w-[305px] md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="flex justify-center items-center mx-5">
            <p class="px-2 py-1 font-bold text-base text-textgray border-2 border-textgray rounded-full">Teslimat</p>
            <svg xmlns="http://www.w3.org/2000/svg" width="142.787" height="1" viewBox="0 0 142.787 1">
                <path id="Path_2907" data-name="Path 2907" d="M10647.213,14347.5H10790" transform="translate(-10647.213 -14347)" fill="none" stroke="#d0d0d0" stroke-width="1" stroke-dasharray="5"/>
            </svg>
            <p class="px-2 py-1 font-bold text-base border-2 border-kbgreen rounded-full">Ödeme</p>
        </section>
        <section class="flex w-full justify-center lg:justify-between mt-8 lg:mt-0">
            <div class="w-10/12 lg:w-3/12 flex justify-between items-end border-b-3 border-kbgreen pb-2">
               <div class="flex">
                   <svg id="wallet" xmlns="http://www.w3.org/2000/svg" width="28.904" height="25" viewBox="0 0 28.904 25">
                       <path id="Path_2913" data-name="Path 2913" d="M29.609,7.645a1.432,1.432,0,0,0-1.021-.424h-1.02v-2.4a1.444,1.444,0,0,0-1.443-1.443H5.369A4.244,4.244,0,0,0,1.125,7.619V24.131a4.244,4.244,0,0,0,4.244,4.244H28.53a1.445,1.445,0,0,0,1.443-1.438l.056-18.269a1.432,1.432,0,0,0-.421-1.023ZM28.051,26.452H5.369a2.321,2.321,0,0,1-2.321-2.321V7.619A2.321,2.321,0,0,1,5.369,5.3H25.645V7.221H5.452V9.144H28.105Z" transform="translate(-1.125 -3.375)" fill="#231f20"/>
                       <path id="Path_2914" data-name="Path 2914" d="M27.563,18.563h1.923v1.923H27.563Z" transform="translate(-4.966 -5.582)" fill="#231f20"/>
                   </svg>

                   <h3 class="p-0 pl-5 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap">Ödeme</h3>
               </div>
            </div>
        </section>
        <div class="w-full hidden lg:flex justify-start items-center mt-4">
            <p class="text-xl text-black font-bold">Ödeme Yöntemi </p>
        </div>
        <section class="mt-6 mb-12 flex flex-col lg:flex-row">
            <div class="w-full lg:w-9/12 pr-0 lg:pr-4 px-1 lg:px-0  ">
                <form class="mx-2 md:mx-0" action="#">
                    <InfoCard>
                        <template #header>
                            <p class="text-xl mb-2 text-black font-bold">Findeks</p>
                        </template>
                        <template #message>
                            <p class="text-sm text-black">
                                İşlemlerine hızlı bir şekilde devam edebilmek için
                                telefonuna SMS ile gelen Findeks mesajını cevapla.
                            </p>
                        </template>
                    </InfoCard>
                    <p class="text-xl mt-8 mb-8 md:mb-3 text-black font-bold">
            Ödeme Yöntemi
<!--            <span class="font-medium">({{ products.length }} ürün)</span>-->
          </p>
                    <PaymentOptions :carts="[]"/>
                </form>
                <div class="mx-2 md:mx-0">
                    <div class="border-b-2 border-bordergray my-3 "></div>
                    <CartProductPreview :products="products" />
                    <div class="border-b-2 border-bordergray my-3 "></div>

                    <CartAddresses :user="user"></CartAddresses>
<!--                    <p class="text-xl mb-2 text-black font-bold">Teslimat Bilgileri </p>-->
<!--                    <p class="text-lg mb-1 text-bordergray font-bold">Teslimat Adresi</p>-->

<!--                    <div class="w-full flex justify-between items-center border-2 border-bordergray rounded-lg p-3 mb-5">-->
<!--                        <div class="flex flex-col w-9/12 lg:w-10/12 items-start">-->
<!--                            <p class="text-lg pb-1 text-black font-bold"><a href="">Mert Aslan Atay</a> </p>-->
<!--                            <p class="text-sm lg:text-base text-textgray font-medium"><a href="">Küçükbakkalköy, Şht. Şakir Elkovan Cd No:20, 34750 Ataşehir/İstanbul</a> </p>-->

<!--                        </div>-->
<!--                        <div class="w-2/12 pr-3 flex items-center justify-end">-->
<!--                            <p class="text-base lg:text-lg  text-black font-bold"><a href="">Düzenle</a> </p>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <p class="text-lg mb-1 text-bordergray font-bold">Fatura Bilgisi</p>-->

<!--                    <div class="w-full flex justify-between items-center border-2 border-bordergray rounded-lg p-3 mb-5">-->
<!--                        <div class="flex flex-col w-9/12 lg:w-10/12 items-start">-->
<!--                            <p class="text-lg pb-1 text-black font-bold"><a href="">Mert Aslan Atay</a> </p>-->
<!--                            <p class="text-sm lg:text-base text-textgray font-medium"><a href="">Küçükbakkalköy, Şht. Şakir Elkovan Cd No:20, 34750 Ataşehir/İstanbul</a> </p>-->

<!--                        </div>-->
<!--                        <div class="w-2/12 pr-3 flex items-center justify-end">-->
<!--                            <p class="text-lg  text-black font-bold"><a href="">Düzenle</a> </p>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
            </div>
            <CheckoutPaymentInfo :total="total" :products="products">
        <template #action>
          <Link
            href="/odeme"
            class="bg-black text-white text-center rounded-full py-2 lg:py-3 text-lg px-2 lg:px-4 self-center font-bold w-full hover:bg-kbgreen"
          >
            Ödemeye Geç
          </Link>
        </template>
      </CheckoutPaymentInfo>
        </section>
    </main>
</template>
