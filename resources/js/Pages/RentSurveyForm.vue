<template>
    <Head title="Kiralama Anketi" />

    <div class="min-h-screen bg-gray-50 py-8">
        <div class="max-w-4xl mx-auto px-4">
            <!-- Header -->
            <div class="text-center mb-8 hidden">
                <h1 class="text-3xl font-bold text-gray-800 mb-6">Kiralamasını Tamamlamış Müşteriler</h1>

                <div class="bg-white p-6 rounded-lg shadow-sm mb-8 text-left">
                    <p class="text-black font-santralregular text-sm lg:text-base leading-relaxed">
                        Mer<PERSON><PERSON>,<br /><br />
                        Kiralabunu'dan / Kiralamini'den yaptığın kiralama süreci sona erdi ve ürünü iade ettiğini gördük. Umarız her şey yolunda geçmiştir! Biz de tam bu noktada senden bir ricada bulunmak istiyoruz. <br /><br />
                        <br /><br />
                        <PERSON>şağıdaki kısa anketle bize deneyimini anlatabilir misin? Toplam 2 dakikanı alacak ama bizim için çok değerli.
                    </p>
                </div>
            </div>

            <!-- Hash Status Error Modal -->
            <TransitionRoot as="template" :show="hashStatus !== 'valid'">
                <Dialog as="div" class="fixed z-50 inset-0 overflow-y-auto" @close="goToHomepage">
                    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                        <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
                            <DialogOverlay class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                        </TransitionChild>

                        <!-- This element is to trick the browser into centering the modal contents. -->
                        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                        <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" enter-to="opacity-100 translate-y-0 sm:scale-100" leave="ease-in duration-200" leave-from="opacity-100 translate-y-0 sm:scale-100" leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                            <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                                <div class="sm:flex sm:items-start">
                                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                    </div>
                                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                        <DialogTitle as="h3" class="text-lg leading-6 font-medium text-gray-900">
                                            {{ getModalTitle() }}
                                        </DialogTitle>
                                        <div class="mt-2">
                                            <p class="text-sm text-gray-500">
                                                {{ errorMessage || getDefaultErrorMessage() }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-5 sm:mt-4 sm:flex sm:justify-end">
                                    <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm" @click="goToHomepage">
                                        Anasayfaya Git
                                    </button>
                                </div>
                            </div>
                        </TransitionChild>
                    </div>
                </Dialog>
            </TransitionRoot>

            <!-- Form (only show if hash is valid) -->
            <div v-if="hashStatus === 'valid'" class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                <form @submit.prevent="submitSurvey">
                    <!-- Success Message -->
                    <TransitionRoot as="template" :show="showSuccessMessage">
                        <Dialog as="div" class="fixed z-10 inset-0 overflow-y-auto" @close="closeSuccessMessage">
                            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                                <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
                                    <DialogOverlay class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                                </TransitionChild>

                                <!-- This element is to trick the browser into centering the modal contents. -->
                                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                                <TransitionChild
                                    as="template"
                                    enter="ease-out duration-300"
                                    enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                                    enter-to="opacity-100 translate-y-0 sm:scale-100"
                                    leave="ease-in duration-200"
                                    leave-from="opacity-100 translate-y-0 sm:scale-100"
                                    leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                                >
                                    <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                                        <div class="sm:flex sm:items-start">
                                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                                                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            </div>
                                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                                <DialogTitle as="h3" class="text-lg leading-6 font-medium text-gray-900">Teşekkürler!</DialogTitle>
                                                <div class="mt-2">
                                                    <p class="text-sm text-gray-500">{{ successMessage || "Anket yanıtlarınız başarıyla gönderildi. Geri bildiriminiz bizim için çok değerli." }}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-5 sm:mt-4 sm:flex sm:justify-end">
                                            <button
                                                type="button"
                                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                                                @click="closeSuccessMessage"
                                            >
                                                Kapat
                                            </button>
                                        </div>
                                    </div>
                                </TransitionChild>
                            </div>
                        </Dialog>
                    </TransitionRoot>

                    <!-- Error Modal -->
                    <TransitionRoot as="template" :show="showErrorMessage">
                        <Dialog as="div" class="fixed z-10 inset-0 overflow-y-auto" @close="closeErrorMessage">
                            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                                <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
                                    <DialogOverlay class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                                </TransitionChild>

                                <!-- This element is to trick the browser into centering the modal contents. -->
                                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                                <TransitionChild
                                    as="template"
                                    enter="ease-out duration-300"
                                    enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                                    enter-to="opacity-100 translate-y-0 sm:scale-100"
                                    leave="ease-in duration-200"
                                    leave-from="opacity-100 translate-y-0 sm:scale-100"
                                    leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                                >
                                    <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                                        <div class="sm:flex sm:items-start">
                                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </div>
                                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                                <DialogTitle as="h3" class="text-lg leading-6 font-medium text-gray-900">Hata Oluştu</DialogTitle>
                                                <div class="mt-2">
                                                    <p class="text-sm text-gray-500">{{ errorMessage }}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-5 sm:mt-4 sm:flex sm:justify-end">
                                            <button
                                                type="button"
                                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                                                @click="closeErrorMessage"
                                            >
                                                Tamam
                                            </button>
                                        </div>
                                    </div>
                                </TransitionChild>
                            </div>
                        </Dialog>
                    </TransitionRoot>

                    <!-- Global Error Message -->
                    <div v-if="Object.entries(errors).length > 0" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Lütfen gerekli alanları doldurunuz</h3>
                            </div>
                        </div>
                    </div>

                    <!-- Soru 1: Genel Memnuniyet (Emoji) -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium mb-4" :class="errors.satisfactionRating && touched.satisfactionRating ? 'text-red-600' : 'text-gray-900'">{{ questionData.satisfactionRating.title }}</h3>

                        <div class="flex justify-center space-x-4">
                            <label v-for="(emoji, index) in questionData.satisfactionRating.options" :key="index" class="cursor-pointer flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors" :class="{ 'bg-blue-50 ring-2 ring-blue-500': form.satisfactionRating === emoji.value }">
                                <input type="radio" :value="emoji.value" v-model="form.satisfactionRating" name="satisfactionRating" class="sr-only" @change="handleInputChange('satisfactionRating')" />
                                <span class="text-4xl mb-2">{{ emoji.icon }}</span>
                                <span class="text-xs text-gray-600 text-center">{{ emoji.label }}</span>
                            </label>
                        </div>

                        <div v-if="errors.satisfactionRating && touched.satisfactionRating" class="text-red-500 text-sm mt-2 text-center">
                            {{ errors.satisfactionRating }}
                        </div>
                    </div>

                    <!-- Soru 2: Ürün Beklenti -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium mb-4" :class="errors.productExpectation && touched.productExpectation ? 'text-red-600' : 'text-gray-900'">{{ questionData.productExpectation.title }}</h3>

                        <div class="space-y-3">
                            <label v-for="option in questionData.productExpectation.options" :key="option.value" class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors" :class="{ 'bg-blue-50 border-blue-500': form.productExpectation === option.value }">
                                <input type="radio" :value="option.value" v-model="form.productExpectation" name="productExpectation" class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" @change="handleInputChange('productExpectation')" />
                                <span class="ml-3 text-gray-700">{{ option.label }}</span>
                            </label>
                        </div>

                        <div v-if="errors.productExpectation && touched.productExpectation" class="text-red-500 text-sm mt-2">
                            {{ errors.productExpectation }}
                        </div>
                    </div>

                    <!-- Soru 3: Ürün Temizliği ve Bakımı -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium mb-4" :class="errors.cleanlinessRating && touched.cleanlinessRating ? 'text-red-600' : 'text-gray-900'">{{ questionData.cleanlinessRating.title }}</h3>

                        <div class="flex flex-wrap justify-center gap-2 mb-4">
                            <button
                                v-for="score in [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]"
                                :key="score"
                                type="button"
                                @click="
                                    form.cleanlinessRating = score;
                                    handleInputChange('cleanlinessRating');
                                "
                                class="w-12 h-12 rounded-lg font-medium transition-colors text-white"
                                :class="getScoreButtonClass(score, form.cleanlinessRating)"
                                :data-field="score === 0 ? 'cleanlinessRating' : undefined"
                            >
                                {{ score }}
                            </button>
                        </div>

                        <div v-if="errors.cleanlinessRating && touched.cleanlinessRating" class="text-red-500 text-sm mt-2 text-center">
                            {{ errors.cleanlinessRating }}
                        </div>
                    </div>

                    <!-- Soru 4: İade Süreci -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium mb-4" :class="errors.returnProcessRating && touched.returnProcessRating ? 'text-red-600' : 'text-gray-900'">{{ questionData.returnProcessRating.title }}</h3>

                        <div class="flex flex-wrap justify-center gap-2 mb-4">
                            <button
                                v-for="score in [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]"
                                :key="score"
                                type="button"
                                @click="
                                    form.returnProcessRating = score;
                                    handleInputChange('returnProcessRating');
                                "
                                class="w-12 h-12 rounded-lg font-medium transition-colors text-white"
                                :class="getScoreButtonClass(score, form.returnProcessRating)"
                                :data-field="score === 0 ? 'returnProcessRating' : undefined"
                            >
                                {{ score }}
                            </button>
                        </div>

                        <div v-if="errors.returnProcessRating && touched.returnProcessRating" class="text-red-500 text-sm mt-2 text-center">
                            {{ errors.returnProcessRating }}
                        </div>
                    </div>

                    <!-- Soru 5: Müşteri Hizmetleri -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium mb-4" :class="errors.customerServiceRating && touched.customerServiceRating ? 'text-red-600' : 'text-gray-900'">{{ questionData.customerServiceRating.title }}</h3>

                        <div class="flex flex-wrap justify-center gap-2 mb-4">
                            <button
                                v-for="score in [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]"
                                :key="score"
                                type="button"
                                @click="
                                    form.customerServiceRating = score;
                                    handleInputChange('customerServiceRating');
                                "
                                class="w-12 h-12 rounded-lg font-medium transition-colors text-white"
                                :class="getScoreButtonClass(score, form.customerServiceRating)"
                                :data-field="score === 0 ? 'customerServiceRating' : undefined"
                            >
                                {{ score }}
                            </button>
                        </div>

                        <div v-if="errors.customerServiceRating && touched.customerServiceRating" class="text-red-500 text-sm mt-2 text-center">
                            {{ errors.customerServiceRating }}
                        </div>
                    </div>

                    <!-- Soru 6: Fiyat Değerlendirmesi -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium mb-4" :class="errors.pricingRating && touched.pricingRating ? 'text-red-600' : 'text-gray-900'">{{ questionData.pricingRating.title }}</h3>

                        <div class="flex flex-wrap justify-center gap-2 mb-4">
                            <button
                                v-for="score in [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]"
                                :key="score"
                                type="button"
                                @click="
                                    form.pricingRating = score;
                                    handleInputChange('pricingRating');
                                "
                                class="w-12 h-12 rounded-lg font-medium transition-colors text-white"
                                :class="getScoreButtonClass(score, form.pricingRating)"
                                :data-field="score === 0 ? 'pricingRating' : undefined"
                            >
                                {{ score }}
                            </button>
                        </div>

                        <div v-if="errors.pricingRating && touched.pricingRating" class="text-red-500 text-sm mt-2 text-center">
                            {{ errors.pricingRating }}
                        </div>
                    </div>

                    <!-- Soru 7: Tavsiye -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium mb-4" :class="errors.wouldRecommend && touched.wouldRecommend ? 'text-red-600' : 'text-gray-900'">{{ questionData.wouldRecommend.title }}</h3>

                        <div class="space-y-3">
                            <label v-for="option in questionData.wouldRecommend.options" :key="option.value" class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors" :class="{ 'bg-blue-50 border-blue-500': form.wouldRecommend === option.value }">
                                <input type="radio" :value="option.value" v-model="form.wouldRecommend" name="wouldRecommend" class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" @change="handleInputChange('wouldRecommend')" />
                                <span class="ml-3 text-gray-700">{{ option.label }}</span>
                            </label>
                        </div>

                        <div v-if="errors.wouldRecommend && touched.wouldRecommend" class="text-red-500 text-sm mt-2">
                            {{ errors.wouldRecommend }}
                        </div>
                    </div>

                    <!-- Soru 8: Ürün Önerileri -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            {{ questionData.productSuggestions.title }}
                            <span class="text-xs text-gray-600 mb-3">(isteğe bağlı)</span>
                        </h3>
                        <textarea v-model="form.productSuggestions" rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none" placeholder="Görmek istediğiniz ürünleri yazabilirsiniz..."></textarea>
                    </div>

                    <!-- Soru 9: İlgi Alanları -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium mb-4" :class="errors.interestedServices && touched.interestedServices ? 'text-red-600' : 'text-gray-900'">
                            {{ questionData.interestedServices.title }}
                            <span class="text-xs text-gray-600 mb-4">(Çoklu seçim yapabilirsin)</span>
                        </h3>

                        <div class="space-y-3">
                            <label v-for="service in questionData.interestedServices.options" :key="service.value" class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors" :class="{ 'bg-blue-50 border-blue-500': form.interestedServices.includes(service.value) }">
                                <input type="checkbox" :value="service.value" v-model="form.interestedServices" name="interestedServices" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" @change="handleInputChange('interestedServices')" />
                                <span class="ml-3 text-gray-700">{{ service.label }}</span>
                            </label>
                        </div>

                        <div v-if="errors.interestedServices && touched.interestedServices" class="text-red-500 text-sm mt-2">
                            {{ errors.interestedServices }}
                        </div>
                    </div>

                    <!-- Ek Yorum Alanı -->
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">{{ questionData.additionalComments.title }}</h3>

                        <textarea v-model="form.additionalComments" rows="4" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none" placeholder="Deneyimini bizimle paylaş..."></textarea>
                    </div>

                    <!-- Özel Teklifler Checkbox -->
                    <div class="mb-8">
                        <label class="flex items-start cursor-pointer p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                            <input type="checkbox" v-model="form.wantsSpecialOffers" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1 mr-3" @change="handleInputChange('wantsSpecialOffers')" />
                            <span class="text-sm text-gray-700 leading-relaxed"> Kiralabunu'nun / Kiralamini'nin müşterilerine özel tekliflerini duymak isterim, yeniden bir kiralama yapmayı düşünüyorum. </span>
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" :disabled="form.processing" :class="form.processing ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'" class="px-8 py-3 text-white font-medium rounded-lg transition-colors duration-200 min-w-[200px]">
                            <span v-if="form.processing" class="flex items-center justify-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Gönderiliyor...
                            </span>
                            <span v-else>Anketi Gönder</span>
                        </button>
                    </div>

                    <!-- Teşekkür Mesajı -->
                    <div class="text-center mt-8">
                        <p class="text-gray-600 text-sm leading-relaxed">
                            Teşekkür ederiz,<br />
                            <span class="font-medium">Kiralabunu Ekibi 💚 / Kiralamini Ekibi 🧡</span>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
import { Head } from "@inertiajs/inertia-vue3";
import { useForm } from "@inertiajs/inertia-vue3";
import { vMaska } from "maska";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import { Dialog, DialogPanel, DialogTitle, DialogOverlay, TransitionChild, TransitionRoot } from "@headlessui/vue";

export default {
    name: "RentSurveyForm",
    components: {
        Head,
        Dialog,
        DialogPanel,
        DialogTitle,
        DialogOverlay,
        TransitionChild,
        TransitionRoot
    },
    layout: Layout,

    directives: {
        maska: vMaska
    },

    props: {
        hash: {
            type: String,
            default: null
        },
        surveyData: {
            type: Object,
            default: () => ({})
        },
        hashStatus: {
            type: String,
            default: "valid" // 'valid', 'completed', 'invalid', 'error'
        },
        errorMessage: {
            type: String,
            default: ""
        }
    },
    setup(props) {
        const form = useForm({
            satisfactionRating: null,
            productExpectation: "",
            cleanlinessRating: null,
            returnProcessRating: null,
            customerServiceRating: null,
            pricingRating: null,
            wouldRecommend: "",
            productSuggestions: "",
            interestedServices: [],
            additionalComments: "",
            wantsSpecialOffers: false,
            hash: props.hash
        });

        return {
            form
        };
    },
    data() {
        return {
            showSuccessMessage: false,
            showErrorMessage: false,
            errorMessage: "",
            successMessage: "",
            errors: {},
            touched: {
                satisfactionRating: false,
                productExpectation: false,
                cleanlinessRating: false,
                returnProcessRating: false,
                customerServiceRating: false,
                pricingRating: false,
                wouldRecommend: false,
                productSuggestions: false,
                interestedServices: false,
                additionalComments: false,
                wantsSpecialOffers: false
            }
        };
    },
    computed: {
        questionData() {
            // Varsayılan sorular (frontend güvenliği için)
            const defaultQuestions = {
                satisfactionRating: {
                    title: "Default - Genel olarak kiralama sürecinden ne kadar memnun kaldın?",
                    type: "emoji",
                    options: [
                        { value: 1, icon: "😠", label: "Çok kötü" },
                        { value: 2, icon: "😞", label: "Kötü" },
                        { value: 3, icon: "😐", label: "Orta" },
                        { value: 4, icon: "🙂", label: "İyi" },
                        { value: 5, icon: "😊", label: "Çok iyi" }
                    ]
                },
                productExpectation: {
                    title: "Default - Kiraladığın ürün beklentini karşıladı mı?",
                    type: "radio",
                    options: [
                        { value: "Evet", label: "Evet, karşıladı" },
                        { value: "Kısmen", label: "Kısmen karşıladı" },
                        { value: "Hayır", label: "Hayır, beklentimin altındaydı" }
                    ]
                },
                cleanlinessRating: {
                    title: "Default - Ürünün ulaştığında temizliği ve bakımıyla ilgili memnuniyet seviyenin nedir?",
                    type: "score",
                    min: 0,
                    max: 10
                },
                returnProcessRating: {
                    title: "Default - Ürünü iade etme sürecini nasıl değerlendirirsin?",
                    type: "score",
                    min: 0,
                    max: 10
                },
                customerServiceRating: {
                    title: "Default - Müşteri hizmetlerinin destek ve iletişimini nasıl değerlendirirsin?",
                    type: "score",
                    min: 0,
                    max: 10
                },
                pricingRating: {
                    title: "Default - Kiraladığın ürünün fiyatı, sunduğumuz deneyime göre nasıldı?",
                    type: "score",
                    min: 0,
                    max: 10
                },
                wouldRecommend: {
                    title: "Default - Kiralabunu'yu bir arkadaşına önerir misiniz?",
                    type: "radio",
                    options: [
                        { value: "Evet", label: "Evet" },
                        { value: "Hayır", label: "Hayır" }
                    ]
                },
                productSuggestions: {
                    title: "Default - Kiralabunu'da / Kiralamini'de görmek istediğiniz ürünler var mı?",
                    type: "textarea"
                },
                interestedServices: {
                    title: "Default - Aşağıdaki hizmetlerden hangileri ilgini çeker?",
                    type: "checkbox",
                    options: [
                        { value: "rent_to_own", label: "Kira öder gibi satın alma (ürün sonunda sizin olur)" },
                        { value: "short_term_campaign", label: "Kısa dönemli kampanyalı kiralama" },
                        { value: "corporate_solutions", label: "Kurumsal çözümler (ofisimiz için)" },
                        { value: "not_interested", label: "Şu an ilgilenmiyorum" }
                    ]
                },
                additionalComments: {
                    title: "Default - Son olarak eklemek istediğin öneri veya tavsiyen var mı?",
                    type: "textarea"
                }
            };

            // API'den gelen soru yapısı
            const apiQuestions = this.surveyData?.questions || {};

            // Derin kopya & birleştirme (API değerleri öncelikli)
            const merged = JSON.parse(JSON.stringify(defaultQuestions));
            Object.keys(apiQuestions).forEach((key) => {
                merged[key] = { ...merged[key], ...apiQuestions[key] };
            });

            return merged;
        }
    },
    mounted() {
        // Check for flash messages on component mount
        const flashSuccess = this.$page.props.flash?.success;
        const flashError = this.$page.props.flash?.error;

        if (flashSuccess) {
            this.successMessage = flashSuccess;
            this.showSuccessMessage = true;
        }

        if (flashError) {
            this.showError(flashError);
        }
    },
    methods: {
        validateForm() {
            const newErrors = {};

            if (!this.form.satisfactionRating) {
                newErrors.satisfactionRating = "Memnuniyet puanı zorunludur";
            }

            if (!this.form.productExpectation) {
                newErrors.productExpectation = "Bu seçim zorunludur";
            }

            if (this.form.cleanlinessRating === null) {
                newErrors.cleanlinessRating = "Temizlik puanı zorunludur";
            }

            if (this.form.returnProcessRating === null) {
                newErrors.returnProcessRating = "İade süreci puanı zorunludur";
            }

            if (this.form.customerServiceRating === null) {
                newErrors.customerServiceRating = "Müşteri hizmetleri puanı zorunludur";
            }

            if (this.form.pricingRating === null) {
                newErrors.pricingRating = "Fiyat değerlendirme puanı zorunludur";
            }

            if (!this.form.wouldRecommend) {
                newErrors.wouldRecommend = "Bu seçim zorunludur";
            }

            if (!this.form.interestedServices || this.form.interestedServices.length === 0) {
                newErrors.interestedServices = "En az bir hizmet seçimi yapınız";
            }

            // Clear old errors and set new ones
            this.errors = newErrors;

            return Object.keys(newErrors).length === 0;
        },
        handleInputBlur(field) {
            this.touched[field] = true;
            this.validateForm();
        },
        handleInputChange(field) {
            if (this.touched[field]) {
                this.validateForm();
            }
        },
        submitSurvey() {
            // Hash geçerli değilse form submit etme
            if (this.hashStatus !== "valid") {
                this.showError("Bu anket doldurulabilir durumda değil.");
                return;
            }

            // Mark all fields as touched
            Object.keys(this.touched).forEach((key) => {
                this.touched[key] = true;
            });

            if (!this.validateForm()) {
                // Focus on first error field
                this.focusFirstErrorField();
                return;
            }

            if (!this.form.hash) {
                this.showError("Anket hash değeri bulunamadı. Lütfen geçerli bir anket linki kullanın.");
                return;
            }

            const submitUrl = `/kiralama-anketi/${this.form.hash}`;
            this.form.post(submitUrl, {
                preserveScroll: true,
                onSuccess: (page) => {
                    // Check for success flash message
                    const successMsg = page.props.flash?.success;

                    if (successMsg) {
                        this.successMessage = successMsg;
                        this.showSuccessMessage = true;
                        this.form.reset();
                        this.resetForm();
                    }
                },
                onError: (errors) => {
                    // Handle validation errors from backend
                    if (errors) {
                        this.errors = {};
                        Object.keys(errors).forEach((key) => {
                            this.errors[key] = Array.isArray(errors[key]) ? errors[key][0] : errors[key];
                        });

                        // Focus on first error field
                        this.$nextTick(() => {
                            this.focusFirstErrorField();
                        });
                    }
                },
                onFinish: () => {
                    // Check for flash messages in different locations
                    this.$nextTick(() => {
                        const flashError = this.$page.props.flash?.error;
                        const flashSuccess = this.$page.props.flash?.success;
                        const directSuccess = this.$page.props.success;

                        // Handle success message (can be in flash.success or direct success prop)
                        const successMsg = flashSuccess || (directSuccess?.success ? directSuccess.success : directSuccess);

                        if (flashError && !this.showErrorMessage) {
                            this.showError(flashError);
                        } else if (successMsg && !this.showSuccessMessage) {
                            this.successMessage = successMsg;
                            this.showSuccessMessage = true;
                            this.form.reset();
                            this.resetForm();
                        }
                    });
                }
            });
        },

        resetForm() {
            // Reset touched state
            this.touched = {
                satisfactionRating: false,
                productExpectation: false,
                cleanlinessRating: false,
                returnProcessRating: false,
                customerServiceRating: false,
                pricingRating: false,
                wouldRecommend: false,
                productSuggestions: false,
                interestedServices: false,
                additionalComments: false,
                wantsSpecialOffers: false
            };

            // Reset errors
            this.errors = {};
        },
        closeSuccessMessage() {
            this.showSuccessMessage = false;
            this.successMessage = "";
        },

        closeErrorMessage() {
            this.showErrorMessage = false;
            this.errorMessage = "";
        },

        showError(message) {
            this.errorMessage = message;
            this.showErrorMessage = true;
        },

        getScoreButtonClass(score, currentScore) {
            if (score === currentScore) {
                return "bg-blue-500 border-blue-500";
            }
            if (score <= 3) {
                return "bg-red-500 hover:bg-red-600";
            } else if (score <= 6) {
                return "bg-yellow-500 hover:bg-yellow-600";
            } else {
                return "bg-green-500 hover:bg-green-600";
            }
        },
        focusFirstErrorField() {
            // Define the order of fields to check for errors
            const fieldOrder = ["satisfactionRating", "productExpectation", "cleanlinessRating", "returnProcessRating", "customerServiceRating", "pricingRating", "wouldRecommend", "interestedServices"];

            for (const fieldName of fieldOrder) {
                if (this.errors[fieldName]) {
                    // Scroll to the error field
                    this.$nextTick(() => {
                        // Find the first element with the error field (radio, checkbox, or button)
                        let element = null;

                        if (fieldName === "satisfactionRating") {
                            element = document.querySelector("input[name=\"satisfactionRating\"]");
                        } else if (fieldName === "productExpectation") {
                            element = document.querySelector("input[name=\"productExpectation\"]");
                        } else if (fieldName === "wouldRecommend") {
                            element = document.querySelector("input[name=\"wouldRecommend\"]");
                        } else if (fieldName === "interestedServices") {
                            element = document.querySelector("input[name=\"interestedServices\"]");
                        } else {
                            // For rating buttons, focus on the first button
                            element = document.querySelector(`button[data-field="${fieldName}"]`);
                        }

                        if (element) {
                            element.scrollIntoView({ behavior: "smooth", block: "center" });
                            element.focus();
                        }
                    });
                    break; // Focus on first error only
                }
            }
        },

        getModalTitle() {
            switch (this.hashStatus) {
                case "completed":
                    return "Anket Daha Önce Doldurulmuş";
                case "invalid":
                    return "Geçersiz Anket Bağlantısı";
                case "error":
                    return "Bir Hata Oluştu";
                default:
                    return "Anket Erişim Hatası";
            }
        },

        getDefaultErrorMessage() {
            switch (this.hashStatus) {
                case "completed":
                    return "Bu anket daha önce doldurulmuş. Aynı anket sadece bir kez doldurulabilir. Teşekkürler!";
                case "invalid":
                    return "Anket bağlantısı geçersiz veya bulunamadı. Lütfen doğru bağlantıyı kullandığınızdan emin olun.";
                case "error":
                    return "Anket yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.";
                default:
                    return "Ankete erişimde bir sorun oluştu.";
            }
        },

        goToHomepage() {
            window.location.href = "/";
        }
    }
};
</script>

<style scoped>
/* Custom animations */
@keyframes slide-down {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.animate-slide-down {
    animation: slide-down 0.3s ease-out;
}

.animate-fade-in {
    animation: fade-in 0.3s ease-out;
}

/* Improve hover effects */
.hover\:scale-105:hover {
    transform: scale(1.05);
}

.active\:scale-95:active {
    transform: scale(0.95);
}

/* Focus styles for better accessibility */
input:focus,
select:focus,
textarea:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Custom checkbox and radio styles */
input[type="checkbox"]:checked,
input[type="radio"]:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .grid-cols-2 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .grid-cols-3 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .grid-cols-4 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}
</style>
