<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";

export default {
    components: {
        UserMenu,
        <PERSON>,
        Head,
    },
    data() {
        return {};
    },
    props: {
        errors: { type: Object, default: false },
        init3D: String,
    },
    methods: {},
    mounted() {
        console.log("init3D", this.init3D);
        console.log("mounted");
        setTimeout(function () {
            document.getElementById("iyzico-3ds-form").submit();
        }, 1500);
    },
    watch: {
        init3D(newValue, oldValue) {
            // console.log("newValue", newValue);
            if (newValue) {
                setTimeout(function () {
                    document.getElementById("iyzico-3ds-form").submit();
                }, 1500);
            }
        },
    },
    layout: Layout,
};
</script>

<template>

    <Head title="Ödeme Yöntemi Ekle" />
    <main class="my-6 max-w-[305px] md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mb-12 flex flex-row">
            <div class="w-full lg:w-9/12 pt-2">
                <div class="flex justify-between">
                    <!--                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7 pl-4">Ödeme Yöntemi Ekle</h3>-->
                </div>
                <div>Bankaya Yönlendiriliyor</div>
                <div v-html="init3D"></div>
            </div>
        </section>
    </main>
</template>

<style scoped></style>
