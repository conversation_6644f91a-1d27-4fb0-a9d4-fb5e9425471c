<script setup>
import { Head } from '@inertiajs/inertia-vue3'
import PaymentLayout from '@/Layouts/PaymentLayout.vue'
import { computed, onMounted, ref } from 'vue'

// Props
const props = defineProps({
    token: String,
    payment_data: Object,
    transaction_data: Object,
    success_timestamp: String,
})

// Accessibility state
const skipLinkVisible = ref(false)

// Screen reader announcements
const announceToScreenReader = (message, priority = 'polite') => {
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', priority)
    announcement.setAttribute('aria-atomic', 'true')
    announcement.className = 'sr-only'
    announcement.textContent = message

    document.body.appendChild(announcement)

    setTimeout(() => {
        if (document.body.contains(announcement)) {
            document.body.removeChild(announcement)
        }
    }, 1000)
}

// Skip link handlers
const handleSkipLinkFocus = () => {
    skipLinkVisible.value = true
}

const handleSkipLinkBlur = () => {
    skipLinkVisible.value = false
}

// Enhanced print function
const handlePrint = () => {
    announceToScreenReader('<PERSON><PERSON> yazdırılıyor', 'assertive')
    window.print()
}

// Computed properties
const formatAmount = (amount) => {
    if (amount === null || amount === undefined) return '0,00'
    const numAmount = Number(amount)
    if (isNaN(numAmount)) return '0,00'
    return new Intl.NumberFormat('tr-TR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(numAmount)
}

const formatDateTime = (dateString) => {
    if (!dateString) return 'Bilinmiyor'
    try {
        const date = new Date(dateString)
        if (isNaN(date.getTime())) return 'Bilinmiyor'
        return date.toLocaleString('tr-TR', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        })
    } catch (error) {
        return 'Bilinmiyor'
    }
}

// Accessibility setup
onMounted(() => {
    // Announce successful payment
    setTimeout(() => {
        announceToScreenReader('Ödeme başarıyla tamamlandı. İşlem detayları yüklendi.', 'assertive')
    }, 500)

    // Set focus to main content for screen readers
    const mainContent = document.getElementById('main-content')
    if (mainContent) {
        mainContent.focus()
    }
})
</script>

<template>

    <Head title="Ödeme Başarılı" />

    <PaymentLayout>
        <!-- Skip link for accessibility -->
        <a id="skip-to-main" href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 bg-blue-600 text-white px-4 py-2 rounded z-50 transition-all duration-200"
            :class="{ 'opacity-100': skipLinkVisible, 'opacity-0': !skipLinkVisible }" @focus="handleSkipLinkFocus" @blur="handleSkipLinkBlur">
            Ana içeriğe geç
        </a>

        <div id="main-content" class="max-w-2xl mx-auto p-4 sm:p-6" tabindex="-1">
            <div class="text-center">
                <!-- Success Icon and Message -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-6 sm:p-8 mb-4 sm:mb-6" role="status" aria-live="polite">
                    <div class="text-green-600 text-6xl sm:text-8xl mb-4 sm:mb-6" aria-hidden="true">✅</div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-green-800 mb-3 sm:mb-4">Ödeme Başarılı!</h1>
                    <p class="text-green-700 text-base sm:text-lg mb-4 sm:mb-6">
                        Kira ödemeniz başarıyla tamamlandı. Şimdi sözleşme işlemlerinizi tamamlayarak kiralamanızı tamamlayabilirsiniz.
                    </p>
                </div>

                <!-- Transaction Details -->
                <section class="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 mb-4 sm:mb-6 text-left" aria-labelledby="transaction-details-title" role="region">
                    <h2 id="transaction-details-title" class="text-lg sm:text-xl font-semibold text-gray-800 mb-4 sm:mb-6 text-center">İşlem Detayları</h2>

                    <dl class="space-y-3 sm:space-y-4">
                        <!-- Amount -->
                        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 sm:py-3 border-b border-gray-100">
                            <dt class="text-gray-600 font-medium text-sm sm:text-base mb-1 sm:mb-0">Ödenen Tutar: {{ payment_data?.rent_amount }}</dt>
                            <dd class="text-xl sm:text-2xl font-bold text-green-600" aria-label="Ödenen tutar {{ formatAmount(transaction_data?.amount || payment_data?.amount) }} {{ transaction_data?.currency || payment_data?.currency || 'TRY' }}">
                                {{ formatAmount(transaction_data?.rent_amount || payment_data?.rent_amount) }} {{ transaction_data?.currency || payment_data?.currency || 'TRY' }}
                            </dd>
                        </div>

                        <!-- Description -->
                        <div v-if="payment_data?.description" class="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 sm:py-3 border-b border-gray-100">
                            <dt class="text-gray-600 font-medium text-sm sm:text-base mb-1 sm:mb-0">Açıklama:</dt>
                            <dd class="text-gray-800 font-semibold text-sm sm:text-base break-words">{{ payment_data.description }}</dd>
                        </div>

                        <!-- Transaction ID -->
                        <div v-if="transaction_data?.transaction_id" class="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 sm:py-3 border-b border-gray-100">
                            <dt class="text-gray-600 font-medium text-sm sm:text-base mb-1 sm:mb-0">İşlem No:</dt>
                            <dd class="text-gray-800 font-mono text-xs sm:text-sm break-all" aria-label="İşlem numarası {{ transaction_data.transaction_id }}">CC_{{ transaction_data.transaction_id }}</dd>
                        </div>

                        <!-- Payment Date -->
                        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 sm:py-3 border-b border-gray-100">
                            <dt class="text-gray-600 font-medium text-sm sm:text-base mb-1 sm:mb-0">İşlem Tarihi:</dt>
                            <dd class="text-gray-800 text-sm sm:text-base">{{ formatDateTime(transaction_data?.completed_at || success_timestamp) }}</dd>
                        </div>

                        <!-- Status -->
                        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center py-2 sm:py-3">
                            <dt class="text-gray-600 font-medium text-sm sm:text-base mb-2 sm:mb-0">Durum:</dt>
                            <dd class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold inline-block" role="status">
                                Ödeme Tamamlandı
                            </dd>
                        </div>
                    </dl>
                </section>

                <!-- Additional Information -->
                <section class="bg-blue-50 border border-blue-200 rounded-lg p-4 sm:p-6 mb-4 sm:mb-6" aria-labelledby="important-info-title" role="region">
                    <h3 id="important-info-title" class="text-base sm:text-lg font-semibold text-blue-800 mb-3">Önemli Bilgiler</h3>
                    <ul class="text-blue-700 text-sm space-y-2 text-left list-disc list-inside" role="list">
                        <li>Bu sayfayı ileride referans olması için kaydedebilirsiniz.</li>
                        <li>Ödemenizin alındığına dair e-posta bildirimi alacaksınız.</li>
                        <li>Herhangi bir sorun yaşamanız durumunda işlem numaranızı not alınız.</li>
                    </ul>
                </section>

                <!-- Action Buttons -->
                <nav class="space-y-3 sm:space-y-4" aria-label="Sayfa eylemleri">
                    <!-- Print Button -->
                    <button @click="handlePrint"
                        class="w-full bg-blue-600 text-white py-3 px-4 sm:px-6 rounded-lg font-semibold hover:bg-blue-700 active:bg-blue-800 transition-colors touch-manipulation text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        style="min-height: 44px;" aria-label="Bu sayfayı yazdır">
                        <span aria-hidden="true">🖨️</span> Bu Sayfayı Yazdır
                    </button>

                    <!-- Home Button -->
                    <a href="/"
                        class="block w-full bg-gray-600 text-white py-3 px-4 sm:px-6 rounded-lg font-semibold hover:bg-gray-700 active:bg-gray-800 transition-colors text-center touch-manipulation text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                        style="min-height: 44px;" aria-label="Ana sayfaya dön">
                        <span aria-hidden="true">🏠</span> Ana Sayfaya Dön
                    </a>
                </nav>

                <!-- Security Badge -->
                <div class="mt-6 sm:mt-8 text-center" role="contentinfo">
                    <div class="inline-flex items-center bg-gray-100 rounded-lg px-3 sm:px-4 py-2">
                        <span class="text-green-600 mr-2" aria-hidden="true">🔒</span>
                        <span class="text-xs sm:text-sm text-gray-600">256-bit Şifreleme ile güvende</span>
                    </div>
                </div>
            </div>
        </div>
    </PaymentLayout>
</template>

<style scoped>
@media print {

    /* Print styles */
    .no-print {
        display: none !important;
    }

    body {
        background: white !important;
    }
}

/* Mobile responsive styles */
@media (max-width: 640px) {

    /* Better mobile spacing */
    .space-y-3>*+* {
        margin-top: 0.75rem !important;
    }

    /* Touch-friendly buttons */
    button,
    a {
        font-size: 16px !important;
        /* Prevents zoom on iOS */
        min-height: 44px;
        /* Apple's minimum touch target */
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    /* Better text wrapping */
    .break-all {
        word-break: break-all;
        overflow-wrap: break-word;
    }

    .break-words {
        word-break: break-words;
        overflow-wrap: break-word;
    }

    /* Mobile typography adjustments */
    .text-2xl {
        font-size: 1.5rem !important;
    }

    .text-6xl {
        font-size: 3rem !important;
    }

    /* Better mobile layout for transaction details */
    .flex.flex-col .font-mono {
        font-size: 0.75rem;
        line-height: 1.2;
    }
}

/* Very small screens */
@media (max-width: 480px) {
    .max-w-2xl {
        max-width: 100%;
        margin-left: 0.5rem;
        margin-right: 0.5rem;
    }

    /* Tighter spacing */
    .p-4 {
        padding: 0.75rem !important;
    }

    .mb-4 {
        margin-bottom: 0.75rem !important;
    }

    /* Smaller icons */
    .text-6xl {
        font-size: 2.5rem !important;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {

    /* Remove hover effects on touch devices */
    .hover\:bg-blue-700:hover,
    .hover\:bg-gray-700:hover {
        background-color: inherit;
    }

    /* Enhanced active states for touch */
    .active\:bg-blue-800:active {
        background-color: #1e40af !important;
    }

    .active\:bg-gray-800:active {
        background-color: #1f2937 !important;
    }
}

/* Landscape orientation optimizations */
@media (max-width: 896px) and (orientation: landscape) {
    .max-w-2xl {
        max-width: 90%;
    }

    /* Reduce vertical spacing in landscape */
    .space-y-3>*+* {
        margin-top: 0.5rem !important;
    }

    .mb-4 {
        margin-bottom: 0.5rem !important;
    }

    /* Smaller text in landscape */
    .text-6xl {
        font-size: 2rem !important;
    }

    .text-2xl {
        font-size: 1.25rem !important;
    }
}

/* High contrast support */
@media (prefers-contrast: high) {
    .border-gray-200 {
        border-color: #000;
        border-width: 2px;
    }

    .text-gray-600 {
        color: #000;
    }

    .bg-green-600 {
        background-color: #047857;
    }

    .bg-blue-600 {
        background-color: #1d4ed8;
    }
}

/* Focus improvements for accessibility */
button:focus-visible,
a:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Success page specific mobile improvements */
.transaction-detail-item {
    transition: background-color 0.2s ease;
}

@media (max-width: 640px) {
    .transaction-detail-item:active {
        background-color: #f9fafb;
    }
}
</style>