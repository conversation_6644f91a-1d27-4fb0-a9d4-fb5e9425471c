<script setup>
import { Head } from "@inertiajs/inertia-vue3";
import { useForm } from "@inertiajs/inertia-vue3";
import PaymentLayout from "@/Layouts/PaymentLayout.vue";
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue";
import { vMaska } from "maska";

// Props
const props = defineProps({
    token: String,
    payment_data: Object,
    error_type: String,
    error_data: Object,
    errors: Object
});

// Form setup
const form = useForm({
    card_number: "",
    expiry_month: "",
    expiry_year: "",
    cvv: "",
    cardholder_name: "",
    email: "",
    invoice_address: "",
    privacy_policy_accepted: false,
    membership_agreement_accepted: false
});

// Local state
const isProcessing = ref(false);
const showCardForm = ref(true);
const isInitialLoading = ref(true);
const isFormSubmitting = ref(false);
const networkError = ref(null);
const systemError = ref(null);
const retryCount = ref(0);
const maxRetries = 3;

// 3DS State
const show3DSModal = ref(false);
const threeDSHtml = ref('');
const iyzToken = ref('');
const is3DSProcessing = ref(false);

// Client-side validation state
const validationErrors = ref({
    cardholder_name: null,
    card_number: null,
    expiry_month: null,
    expiry_year: null,
    cvv: null,
    email: null,
    invoice_address: null,
    privacy_policy_accepted: null,
    membership_agreement_accepted: null
});

const touched = ref({
    cardholder_name: false,
    card_number: false,
    expiry_month: false,
    expiry_year: false,
    cvv: false,
    email: false,
    invoice_address: false,
    privacy_policy_accepted: false,
    membership_agreement_accepted: false
});

// Error handling computed
const hasNetworkError = computed(() => {
    return networkError.value !== null;
});

const hasSystemError = computed(() => {
    return systemError.value !== null;
});

const canRetry = computed(() => {
    return retryCount.value < maxRetries && (hasNetworkError.value || hasSystemError.value);
});

const errorMessage = computed(() => {
    if (networkError.value) {
        return networkError.value;
    }
    if (systemError.value) {
        return systemError.value;
    }
    return null;
});

// Loading states computed
const showSkeleton = computed(() => {
    return isInitialLoading.value && !props.error_type;
});

const isAnyFormProcessing = computed(() => {
    return isProcessing.value || isFormSubmitting.value || form.processing;
});

// Computed properties
const pageTitle = computed(() => {
    if (props.error_type) return "Ödeme Hatası";
    return "SMS Ödeme";
});

const isPaymentFormVisible = computed(() => {
    // Payment form should be visible if there's payment data and not initial loading
    // Even if there's a payment_error, we want to keep the form visible to show the error
    return props.payment_data?.is_payable && !isInitialLoading.value && (!props.error_type || props.error_type === 'payment_error');
});

// Client-side validation computed
const isFormValid = computed(() => {
    return Object.values(validationErrors.value).every(error => error === null) &&
        form.cardholder_name.trim() !== "" &&
        form.card_number.replace(/\s/g, "").length >= 13 &&
        form.expiry_month !== "" &&
        form.expiry_year !== "" &&
        form.cvv.length === 3 &&
        form.email.trim() !== "" &&
        form.invoice_address.trim() !== "" &&
        form.privacy_policy_accepted === true &&
        form.membership_agreement_accepted === true;
});

const cardNumberIsValid = computed(() => {
    const cardNumber = form.card_number.replace(/\s/g, "");
    return cardNumber.length >= 13 && cardNumber.length <= 19 && isValidLuhn(cardNumber);
});

const isExpiryValid = computed(() => {
    if (!form.expiry_month || !form.expiry_year) return false;

    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    const selectedYear = parseInt(form.expiry_year);
    const selectedMonth = parseInt(form.expiry_month);

    if (selectedYear > currentYear) return true;
    if (selectedYear === currentYear && selectedMonth >= currentMonth) return true;

    return false;
});

// Add new reactive state for enhanced transitions
const pageTransition = ref("fade-in");
const cardEnterAnimation = ref(false);
const formFieldAnimations = ref({
    cardholder_name: false,
    card_number: false,
    expiry_month: false,
    expiry_year: false,
    cvv: false
});

// Enhanced form field focus tracking
const focusedField = ref(null);

// Add staggered animation for form fields
const animateFormFields = () => {
    const fields = ["cardholder_name", "card_number", "expiry_month", "expiry_year", "cvv"];
    fields.forEach((field, index) => {
        setTimeout(() => {
            formFieldAnimations.value[field] = true;
        }, index * 100);
    });
};

// Enhanced focus handlers with animation
const handleFieldFocus = (fieldName) => {
    focusedField.value = fieldName;
};

const handleFieldBlur = () => {
    focusedField.value = null;
};

// Enhanced form field focus/blur handlers
const enhancedHandleCardholderNameBlur = () => {
    handleFieldBlur();
    handleCardholderNameBlur();
};

const enhancedHandleCardholderNameFocus = () => {
    handleFieldFocus("cardholder_name");
    announceToScreenReader("Kart sahibi adı alanı aktif. Adınızı ve soyadınızı girin.");
};

const enhancedHandleCardNumberBlur = () => {
    handleFieldBlur();
    handleCardNumberBlur();
};

const enhancedHandleCardNumberFocus = () => {
    handleFieldFocus("card_number");
    announceToScreenReader("Kart numarası alanı aktif. 16 haneli kart numaranızı girin.");
};

const enhancedHandleExpiryMonthFocus = () => {
    handleFieldFocus("expiry_month");
    announceToScreenReader("Son kullanma ayı alanı aktif. Kartınızın son kullanma ayını seçin.");
};

const enhancedHandleExpiryYearFocus = () => {
    handleFieldFocus("expiry_year");
    announceToScreenReader("Son kullanma yılı alanı aktif. Kartınızın son kullanma yılını seçin.");
};

const enhancedHandleCVVBlur = () => {
    handleFieldBlur();
    handleCVVBlur();
};

const enhancedHandleCVVFocus = () => {
    handleFieldFocus("cvv");
    announceToScreenReader("CVV güvenlik kodu alanı aktif. Kartın arkasındaki 3 haneli kodu girin.");
};

// Enhanced submit with animations
const enhancedSubmitPayment = () => {
    pageTransition.value = "fade-out";
    setTimeout(() => {
        submitPayment();
    }, 200);
};

// Keyboard navigation support
const handleKeyboardNavigation = (event) => {
    // ESC key to clear focus and announce
    if (event.key === "Escape") {
        handleFieldBlur();
        if (document.activeElement) {
            document.activeElement.blur();
        }
        announceToScreenReader("Form alanı odaktan çıkarıldı");
    }

    // Tab navigation enhancement with announcements
    if (event.key === "Tab") {
        // Let browser handle natural tab navigation
        // We track focus through our focus handlers
    }

    // Enter key on form fields should move to next field
    if (event.key === "Enter" && event.target.type !== "submit") {
        event.preventDefault();
        const currentFieldIndex = formFieldOrder.indexOf(event.target.id);
        if (currentFieldIndex > -1 && currentFieldIndex < formFieldOrder.length - 1) {
            const nextField = document.getElementById(formFieldOrder[currentFieldIndex + 1]);
            if (nextField) {
                nextField.focus();
                announceToScreenReader(`${getFieldLabel(formFieldOrder[currentFieldIndex + 1])} alanına geçildi`);
            }
        } else {
            // Focus submit button if on last field
            const submitButton = document.querySelector("button[type=\"submit\"]");
            if (submitButton && !submitButton.disabled) {
                submitButton.focus();
                announceToScreenReader("Ödeme butonuna geçildi");
            }
        }
    }

    // Arrow key navigation for form fields
    if (event.key === "ArrowDown" && event.target.type !== "submit") {
        event.preventDefault();
        const currentFieldIndex = formFieldOrder.indexOf(event.target.id);
        if (currentFieldIndex > -1 && currentFieldIndex < formFieldOrder.length - 1) {
            const nextField = document.getElementById(formFieldOrder[currentFieldIndex + 1]);
            if (nextField) {
                nextField.focus();
                announceToScreenReader(`${getFieldLabel(formFieldOrder[currentFieldIndex + 1])} alanına geçildi`);
            }
        }
    }

    if (event.key === "ArrowUp" && event.target.type !== "submit") {
        event.preventDefault();
        const currentFieldIndex = formFieldOrder.indexOf(event.target.id);
        if (currentFieldIndex > 0) {
            const prevField = document.getElementById(formFieldOrder[currentFieldIndex - 1]);
            if (prevField) {
                prevField.focus();
                announceToScreenReader(`${getFieldLabel(formFieldOrder[currentFieldIndex - 1])} alanına geçildi`);
            }
        }
    }

    // Skip link activation
    if (event.key === "Enter" && event.target.id === "skip-to-main") {
        event.preventDefault();
        const mainContent = document.getElementById("main-content");
        if (mainContent) {
            mainContent.focus();
            announceToScreenReader("Ana içeriğe geçildi");
        }
    }
};

// Screen reader announcements
const announceToScreenReader = (message, priority = "polite") => {
    const announcement = document.createElement("div");
    announcement.setAttribute("aria-live", priority);
    announcement.setAttribute("aria-atomic", "true");
    announcement.className = "sr-only";
    announcement.textContent = message;

    document.body.appendChild(announcement);

    // Remove after announcement
    setTimeout(() => {
        if (document.body.contains(announcement)) {
            document.body.removeChild(announcement);
        }
    }, 1000);
};

// Enhanced focus management
const manageFocus = () => {
    // Announce form state changes to screen readers
    watch(isFormValid, (newValue, oldValue) => {
        if (oldValue !== undefined) {
            if (newValue) {
                announceToScreenReader("Form geçerli, ödeme yapabilirsiniz", "polite");
            } else {
                announceToScreenReader("Form geçersiz, lütfen hataları düzeltin", "assertive");
            }
        }
    });

    // Announce validation errors with specific field information
    watch([() => props.errors, validationErrors], () => {
        const serverErrors = Object.keys(props.errors || {});
        const clientErrors = Object.keys(validationErrors.value).filter(key => validationErrors.value[key]);

        if (serverErrors.length > 0 || clientErrors.length > 0) {
            const errorFields = [...new Set([...serverErrors, ...clientErrors])];
            const fieldLabels = errorFields.map(field => getFieldLabel(field)).join(", ");
            announceToScreenReader(`Form doğrulama hataları: ${fieldLabels}`, "assertive");
        }
    }, { deep: true });

    // Announce processing state changes
    watch(isAnyFormProcessing, (newValue) => {
        if (newValue) {
            announceToScreenReader("Ödeme işlemi başlatıldı, lütfen bekleyin", "assertive");
        }
    });
};

// Methods
const formatAmount = (amount) => {
    if (!amount) return "0,00";
    return new Intl.NumberFormat("tr-TR", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
};

const preventNumericInput = (event) => {
    const keyCode = event.keyCode || event.which;
    if (keyCode > 47 && keyCode < 58) {
        event.preventDefault();
    }
};

// Luhn Algorithm for card validation
const isValidLuhn = (cardNumber) => {
    let sum = 0;
    let isEven = false;

    for (let i = cardNumber.length - 1; i >= 0; i--) {
        let digit = parseInt(cardNumber.charAt(i));

        if (isEven) {
            digit *= 2;
            if (digit > 9) {
                digit -= 9;
            }
        }

        sum += digit;
        isEven = !isEven;
    }

    return sum % 10 === 0;
};

// Real-time validation functions
const validateCardholderName = () => {
    const name = form.cardholder_name.trim();

    if (!name) {
        validationErrors.value.cardholder_name = "Kart sahibi adı gereklidir";
        return false;
    }

    if (name.length < 2) {
        validationErrors.value.cardholder_name = "En az 2 karakter olmalıdır";
        return false;
    }

    if (!/^[a-zA-ZçÇğĞıİöÖşŞüÜ\s]+$/.test(name)) {
        validationErrors.value.cardholder_name = "Sadece harf karakterleri kullanılabilir";
        return false;
    }

    validationErrors.value.cardholder_name = null;
    return true;
};

const validateCardNumber = () => {
    const cardNumber = form.card_number.replace(/\s/g, "");

    if (!cardNumber) {
        validationErrors.value.card_number = "Kart numarası gereklidir";
        return false;
    }

    if (cardNumber.length < 13) {
        validationErrors.value.card_number = "Kart numarası en az 13 karakter olmalıdır";
        return false;
    }

    if (cardNumber.length > 19) {
        validationErrors.value.card_number = "Kart numarası çok uzun";
        return false;
    }

    if (!/^\d+$/.test(cardNumber)) {
        validationErrors.value.card_number = "Sadece rakam kullanılabilir";
        return false;
    }

    if (!isValidLuhn(cardNumber)) {
        validationErrors.value.card_number = "Geçersiz kart numarası";
        return false;
    }

    validationErrors.value.card_number = null;
    return true;
};

const validateExpiry = () => {
    if (!form.expiry_month) {
        validationErrors.value.expiry_month = "Ay seçiniz";
        return false;
    }

    if (!form.expiry_year) {
        validationErrors.value.expiry_year = "Yıl seçiniz";
        return false;
    }

    if (!isExpiryValid.value) {
        validationErrors.value.expiry_month = "Geçersiz tarih";
        validationErrors.value.expiry_year = "Geçersiz tarih";
        return false;
    }

    validationErrors.value.expiry_month = null;
    validationErrors.value.expiry_year = null;
    return true;
};

const validateCVV = () => {
    const cvv = form.cvv.trim();

    if (!cvv) {
        validationErrors.value.cvv = "CVV gereklidir";
        return false;
    }

    if (cvv.length !== 3) {
        validationErrors.value.cvv = "CVV 3 karakter olmalıdır";
        return false;
    }

    if (!/^\d{3}$/.test(cvv)) {
        validationErrors.value.cvv = "Sadece rakam kullanılabilir";
        return false;
    }

    validationErrors.value.cvv = null;
    return true;
};

const validateEmail = () => {
    const email = form.email.trim();

    if (!email) {
        validationErrors.value.email = "E-posta adresi gereklidir";
        return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        validationErrors.value.email = "Geçerli bir e-posta adresi girin";
        return false;
    }

    validationErrors.value.email = null;
    return true;
};

const validateInvoiceAddress = () => {
    const address = form.invoice_address.trim();

    if (!address) {
        validationErrors.value.invoice_address = "Fatura adresi gereklidir";
        return false;
    }

    if (address.length < 30) {
        validationErrors.value.invoice_address = "Fatura adresi en az 30 karakter olmalıdır";
        return false;
    }

    validationErrors.value.invoice_address = null;
    return true;
};

const validatePrivacyPolicyAccepted = () => {
    if (!form.privacy_policy_accepted) {
        validationErrors.value.privacy_policy_accepted = "Aydınlatma metni kabul edilmelidir";
        return false;
    }

    validationErrors.value.privacy_policy_accepted = null;
    return true;
};

const validateMembershipAgreementAccepted = () => {
    if (!form.membership_agreement_accepted) {
        validationErrors.value.membership_agreement_accepted = "Üyelik sözleşmesi kabul edilmelidir";
        return false;
    }

    validationErrors.value.membership_agreement_accepted = null;
    return true;
};

// Form field handlers
const handleCardholderNameBlur = () => {
    touched.value.cardholder_name = true;
    validateCardholderName();
};

const handleCardholderNameInput = () => {
    if (touched.value.cardholder_name) {
        validateCardholderName();
    }
};

const handleCardNumberBlur = () => {
    touched.value.card_number = true;
    validateCardNumber();
};

const handleCardNumberInput = () => {
    if (touched.value.card_number) {
        validateCardNumber();
    }
};

const handleExpiryMonthChange = () => {
    touched.value.expiry_month = true;
    validateExpiry();
};

const handleExpiryYearChange = () => {
    touched.value.expiry_year = true;
    validateExpiry();
};

const handleCVVBlur = () => {
    touched.value.cvv = true;
    validateCVV();
};

const handleCVVInput = () => {
    if (touched.value.cvv) {
        validateCVV();
    }
};

const handleEmailBlur = () => {
    touched.value.email = true;
    validateEmail();
};

const handleEmailInput = () => {
    if (touched.value.email) {
        validateEmail();
    }
};

const handleInvoiceAddressBlur = () => {
    touched.value.invoice_address = true;
    validateInvoiceAddress();
};

const handleInvoiceAddressInput = () => {
    if (touched.value.invoice_address) {
        validateInvoiceAddress();
    }
};

const handlePrivacyPolicyAcceptedBlur = () => {
    touched.value.privacy_policy_accepted = true;
    validatePrivacyPolicyAccepted();
};

const handlePrivacyPolicyAcceptedInput = () => {
    if (touched.value.privacy_policy_accepted) {
        validatePrivacyPolicyAccepted();
    }
};

const handleMembershipAgreementAcceptedBlur = () => {
    touched.value.membership_agreement_accepted = true;
    validateMembershipAgreementAccepted();
};

const handleMembershipAgreementAcceptedInput = () => {
    if (touched.value.membership_agreement_accepted) {
        validateMembershipAgreementAccepted();
    }
};

const submitPayment = () => {
    if (isAnyFormProcessing.value) return;

    // Clear previous errors
    clearErrors();

    // Validate all fields before submission
    touched.value = {
        cardholder_name: true,
        card_number: true,
        expiry_month: true,
        expiry_year: true,
        cvv: true,
        email: true,
        invoice_address: true,
        privacy_policy_accepted: true,
        membership_agreement_accepted: true
    };

    const isNameValid = validateCardholderName();
    const isCardValid = validateCardNumber();
    const isExpiryValidResult = validateExpiry();
    const isCVVValid = validateCVV();
    const isEmailValid = validateEmail();
    const isInvoiceAddressValid = validateInvoiceAddress();
    const isPrivacyPolicyAcceptedValid = validatePrivacyPolicyAccepted();
    const isMembershipAgreementAcceptedValid = validateMembershipAgreementAccepted();

    if (!isNameValid || !isCardValid || !isExpiryValidResult || !isCVVValid || !isEmailValid || !isInvoiceAddressValid || !isPrivacyPolicyAcceptedValid || !isMembershipAgreementAcceptedValid) {
        return; // Don't submit if validation fails
    }

    isProcessing.value = true;
    isFormSubmitting.value = true;

    console.log('🔄 Payment submission started', {
        token: props.token,
        timestamp: new Date().toISOString(),
        form_data: {
            cardholder_name: form.cardholder_name,
            card_last4: form.card_number.slice(-4),
            email: form.email
        }
    });

    form.post(route("payment.process", { token: props.token }), {
        onSuccess: (page) => {
            console.log('✅ Payment submission successful', {
                token: props.token,
                timestamp: new Date().toISOString(),
                response_type: typeof page.props,
                page_props: page.props,
                has_payment_response: !!(page.props && page.props.payment_response),
                payment_response: page.props?.payment_response
            });

            // Reset retry count on successful submission
            resetRetryCount();
            clearErrors();

            // Check if response contains 3DS data
            if (page.props && page.props.payment_response) {
                console.log('🔄 Calling handle3DSResponse with:', page.props.payment_response);
                handle3DSResponse(page.props.payment_response);
            } else {
                console.log('⚠️ No payment_response found in page.props');
                console.log('📋 Full page.props structure:', page.props);
                console.log('📋 All keys in page.props:', Object.keys(page.props || {}));
                console.log('📋 error_type:', page.props?.error_type);
                console.log('📋 error_data:', page.props?.error_data);
                console.log('📋 payment_data:', page.props?.payment_data);
            }
        },
        onError: (errors) => {
            console.error('❌ Payment submission error', {
                token: props.token,
                timestamp: new Date().toISOString(),
                errors: errors,
                error_keys: Object.keys(errors)
            });

            // Payment errors are automatically handled by Inertia through errors.payment_error
            // Only handle network/system errors here
            if (Object.keys(errors).length === 0) {
                // No specific errors, might be network issue
                handleNetworkError({ request: true });
            }

            // Clear any existing system/network errors since we have a specific payment error
            if (errors.payment_error) {
                clearErrors();
            }
        },
        onFinish: () => {
            console.log('🏁 Payment submission finished', {
                token: props.token,
                timestamp: new Date().toISOString(),
                processing: false
            });

            isProcessing.value = false;
            isFormSubmitting.value = false;
        },
        onCancelToken: (cancelToken) => {
            // Handle cancellation if needed
        },
        onCancel: () => {
            console.warn('⚠️ Payment request was cancelled', {
                token: props.token,
                timestamp: new Date().toISOString()
            });

            isProcessing.value = false;
            isFormSubmitting.value = false;
        }
    });
};

// Current year for year options
const currentYear = new Date().getFullYear();
const yearOptions = Array.from({ length: 15 }, (_, i) => currentYear + i);

// Check for callback parameters on page load
const checkForCallback = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const source = urlParams.get('source');
    const status = urlParams.get('status');

    console.log('🔍 Checking for callback parameters', {
        token: props.token,
        timestamp: new Date().toISOString(),
        source: source,
        status: status,
        full_url: window.location.href
    });

    if (source === 'payment_sms') {
        console.log('📞 Payment SMS callback detected', {
            token: props.token,
            timestamp: new Date().toISOString(),
            status: status
        });

        if (status === 'success') {
            handle3DSCallback('success');
        } else {
            handle3DSCallback('failed');
        }

        // Clean URL
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
    }

    // Check if we're coming from a 3DS callback (SaveCreditCardController)
    // This happens when user is redirected from the SaveCreditCardController after 3DS completion
    if (window.location.pathname.includes('/success')) {
        console.log('🎉 Direct success page callback detected', {
            token: props.token,
            timestamp: new Date().toISOString(),
            pathname: window.location.pathname
        });

        // We're already on success page, so this is handled automatically by the route
        return;
    }
};

// Setup message listener for 3DS iframe communication
const setup3DSMessageListener = () => {
    const handleMessage = (event) => {
        console.log('📧 Message received from iframe', {
            token: props.token,
            timestamp: new Date().toISOString(),
            origin: event.origin,
            data: event.data
        });

        // Handle 3DS completion messages
        if (event.data && typeof event.data === 'object') {
            if (event.data.type === '3ds_complete') {
                handle3DSCallback(event.data.status || 'success');
            } else if (event.data.type === '3ds_error') {
                handle3DSCallback('failed');
            }
        }

        // Handle SaveCreditCardController callback messages
        if (event.data && typeof event.data === 'string') {
            if (event.data === '3ds_payment_success') {
                console.log('🎉 3DS Payment success message received from SaveCreditCardController');
                handle3DSCallback('success');
            } else if (event.data === '3ds_payment_failed') {
                console.log('❌ 3DS Payment failed message received from SaveCreditCardController');
                handle3DSCallback('failed');
            }
        }
    };

    window.addEventListener('message', handleMessage);

    return () => {
        window.removeEventListener('message', handleMessage);
    };
};

// Simulate initial loading
onMounted(() => {
    // Check for callback parameters first
    checkForCallback();

    // Initialize email from payment data if available
    if (props.payment_data?.scoring_request?.email) {
        form.email = props.payment_data.scoring_request.email;
    }

    // Setup 3DS message listener
    const cleanup3DSListener = setup3DSMessageListener();

    // Simulate loading time for payment data processing
    setTimeout(() => {
        isInitialLoading.value = false;
        cardEnterAnimation.value = true;

        // Animate form fields after card appears
        setTimeout(() => {
            animateFormFields();
        }, 300);
    }, 800);

    // Setup keyboard navigation
    document.addEventListener("keydown", handleKeyboardNavigation);

    // Setup focus management for accessibility
    manageFocus();

    // Announce page load to screen readers
    setTimeout(() => {
        announceToScreenReader("SMS ödeme sayfası yüklendi. Kart bilgilerinizi girin.");
    }, 1000);

    // Cleanup function will be called when component unmounts
    onUnmounted(() => {
        cleanup3DSListener();
    });
});

// Cleanup on unmount
onUnmounted(() => {
    document.removeEventListener("keydown", handleKeyboardNavigation);
});

// Watch for form changes to provide real-time validation
watch(() => form.cardholder_name, handleCardholderNameInput);
watch(() => form.card_number, handleCardNumberInput);
watch(() => form.expiry_month, handleExpiryMonthChange);
watch(() => form.expiry_year, handleExpiryYearChange);
watch(() => form.cvv, handleCVVInput);
watch(() => form.email, handleEmailInput);
watch(() => form.invoice_address, handleInvoiceAddressInput);
watch(() => form.privacy_policy_accepted, handlePrivacyPolicyAcceptedInput);
watch(() => form.membership_agreement_accepted, handleMembershipAgreementAcceptedInput);

// Error handling methods
const clearErrors = () => {
    networkError.value = null;
    systemError.value = null;
};

const handleNetworkError = (error) => {
    console.error("Network error:", error);

    if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        switch (status) {
            case 422:
                // Validation errors - handled by Inertia
                systemError.value = null;
                networkError.value = null;
                break;
            case 429:
                networkError.value = "Çok fazla istek gönderildi. Lütfen bir süre bekleyiniz.";
                break;
            case 500:
                systemError.value = "Sunucu hatası oluştu. Lütfen daha sonra tekrar deneyiniz.";
                break;
            case 503:
                systemError.value = "Servis geçici olarak kullanılamıyor. Lütfen daha sonra tekrar deneyiniz.";
                break;
            default:
                systemError.value = "Beklenmeyen bir hata oluştu. Lütfen daha sonra tekrar deneyiniz.";
        }
    } else if (error.request) {
        // Network error
        networkError.value = "İnternet bağlantınızı kontrol ediniz ve tekrar deneyiniz.";
    } else {
        // Other error
        systemError.value = "Bir hata oluştu. Lütfen sayfayı yenileyip tekrar deneyiniz.";
    }
};

const retryPayment = () => {
    if (!canRetry.value) return;

    retryCount.value++;
    clearErrors();

    // Wait a bit before retrying
    setTimeout(() => {
        submitPayment();
    }, 1000);
};

const resetRetryCount = () => {
    retryCount.value = 0;
};

// 3DS Handling Methods
const handle3DSResponse = (response) => {
    console.log('🔐 3DS Response received', {
        token: props.token,
        timestamp: new Date().toISOString(),
        response_status: response?.status,
        has_html: !!response?.html_content,
        has_token: !!response?.iyz_token,
        full_response: response
    });

    console.log('🔍 3DS Condition check:', {
        status_is_3ds: response?.status === '3ds',
        has_html_content: !!response?.html_content,
        both_conditions: response?.status === '3ds' && response?.html_content,
        modal_state_before: show3DSModal.value
    });

    if (response?.status === '3ds' && response?.html_content) {
        // Store 3DS data
        threeDSHtml.value = response.html_content;
        iyzToken.value = response.iyz_token || '';

        // Show 3DS modal
        show3DSModal.value = true;
        is3DSProcessing.value = true;

        console.log('🎭 3DS Modal should be opened', {
            token: props.token,
            timestamp: new Date().toISOString(),
            iyz_token: iyzToken.value ? 'present' : 'missing',
            modal_state_after: show3DSModal.value,
            threeDSHtml_length: threeDSHtml.value.length
        });

        // Announce to screen readers
        announceToScreenReader('3D Secure doğrulaması açılıyor. Lütfen bankacılık uygulamanızı kontrol edin.', 'assertive');
    } else {
        console.warn('⚠️ Invalid 3DS response or condition not met', {
            token: props.token,
            timestamp: new Date().toISOString(),
            response: response,
            status: response?.status,
            html_content: response?.html_content ? 'present' : 'missing',
            html_length: response?.html_content?.length || 0
        });

        if (!response?.status) {
            console.error('❌ No status in response');
        } else if (response?.status !== '3ds') {
            console.error('❌ Status is not "3ds", it is:', response.status);
        }

        if (!response?.html_content) {
            console.error('❌ No html_content in response');
        }

        systemError.value = 'Ödeme işleminde beklenmeyen bir hata oluştu. Lütfen tekrar deneyiniz.';
    }
};

const close3DSModal = () => {
    console.log('❌ 3DS Modal closed manually', {
        token: props.token,
        timestamp: new Date().toISOString(),
        was_processing: is3DSProcessing.value
    });

    show3DSModal.value = false;
    threeDSHtml.value = '';
    iyzToken.value = '';
    is3DSProcessing.value = false;

    // Re-enable form
    isProcessing.value = false;
    isFormSubmitting.value = false;

    announceToScreenReader('3D Secure doğrulaması iptal edildi.');
};

const handle3DSCallback = (result) => {
    console.log('🔄 3DS Callback received', {
        token: props.token,
        timestamp: new Date().toISOString(),
        result: result,
        iyz_token: iyzToken.value
    });

    close3DSModal();

    if (result === 'success') {
        console.log('✅ 3DS Success - redirecting to success page', {
            token: props.token,
            timestamp: new Date().toISOString()
        });

        // Redirect to success page
        window.location.href = route('payment.success', { token: props.token });
    } else {
        console.log('❌ 3DS Failed - showing error', {
            token: props.token,
            timestamp: new Date().toISOString(),
            result: result
        });

        // Show error message
        systemError.value = '3D Secure doğrulaması başarısız oldu. Lütfen tekrar deneyiniz.';
        announceToScreenReader('3D Secure doğrulaması başarısız oldu. Lütfen tekrar deneyiniz.', 'assertive');
    }
};

// Enhanced accessibility state
const formFieldOrder = ["cardholder_name", "card_number", "expiry_month", "expiry_year", "cvv"];
const focusTrapped = ref(false);
const skipLinkVisible = ref(false);

// Focus trap for modal/overlay states
const trapFocus = (element) => {
    const focusableElements = element.querySelectorAll(
        "button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])"
    );
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e) => {
        if (e.key === "Tab") {
            if (e.shiftKey) {
                if (document.activeElement === firstElement) {
                    lastElement.focus();
                    e.preventDefault();
                }
            } else {
                if (document.activeElement === lastElement) {
                    firstElement.focus();
                    e.preventDefault();
                }
            }
        }
    };

    element.addEventListener("keydown", handleTabKey);
    return () => element.removeEventListener("keydown", handleTabKey);
};

// Get field labels for screen reader announcements
const getFieldLabel = (fieldId) => {
    const labels = {
        cardholder_name: "Kart sahibi adı",
        card_number: "Kart numarası",
        expiry_month: "Son kullanma ayı",
        expiry_year: "Son kullanma yılı",
        cvv: "CVV güvenlik kodu"
    };
    return labels[fieldId] || fieldId;
};

// Skip link visibility management
const handleSkipLinkFocus = () => {
    skipLinkVisible.value = true;
};

const handleSkipLinkBlur = () => {
    skipLinkVisible.value = false;
};

</script>

<template>

    <Head :title="pageTitle">
        <!-- CSP temporarily disabled for 3DS integration -->
        <!-- <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://kbtest.test https://cdn.jsdelivr.net http://localhost:5173 https://sandbox-api.iyzipay.com https://api.iyzipay.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https://kbtest.test ws://localhost:5173 https://sandbox-api.iyzipay.com https://api.iyzipay.com https://*.bkm.com.tr https://goguvenliodeme.bkm.com.tr; frame-ancestors 'none'; base-uri 'self'; form-action 'self' https://sandbox-api.iyzipay.com https://api.iyzipay.com https://*.bkm.com.tr https://goguvenliodeme.bkm.com.tr https://*.iyzipay.com https://3dsecure.bkm.com.tr https://secure.bkm.com.tr; frame-src 'self' data: blob: https://sandbox-api.iyzipay.com https://api.iyzipay.com https://*.bkm.com.tr https://goguvenliodeme.bkm.com.tr https://*.iyzipay.com https://3dsecure.bkm.com.tr https://secure.bkm.com.tr"> -->
    </Head>

    <PaymentLayout>
        <!-- Skip link for accessibility -->
        <a id="skip-to-main" href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 bg-blue-600 text-white px-4 py-2 rounded z-50 transition-all duration-200"
            :class="{ 'opacity-100': skipLinkVisible, 'opacity-0': !skipLinkVisible }" @focus="handleSkipLinkFocus" @blur="handleSkipLinkBlur">
            Ana içeriğe geç
        </a>

        <div id="main-content" class=" mx-auto lg:p-4" tabindex="-1">

            <!-- Skeleton Loading State -->
            <div v-if="showSkeleton" class="space-y-4 sm:space-y-6">
                <!-- Payment Info Skeleton -->
                <div class="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 animate-pulse">
                    <div class="h-6 sm:h-8 bg-gray-200 rounded w-1/3 mb-3 sm:mb-4"></div>
                    <div class="space-y-2 sm:space-y-3">
                        <div class="flex justify-between">
                            <div class="h-3 sm:h-4 bg-gray-200 rounded w-16 sm:w-20"></div>
                            <div class="h-3 sm:h-4 bg-gray-200 rounded w-24 sm:w-32"></div>
                        </div>
                        <div class="flex justify-between">
                            <div class="h-3 sm:h-4 bg-gray-200 rounded w-12 sm:w-16"></div>
                            <div class="h-5 sm:h-6 bg-gray-200 rounded w-20 sm:w-24"></div>
                        </div>
                        <div class="flex justify-between">
                            <div class="h-3 sm:h-4 bg-gray-200 rounded w-20 sm:w-24"></div>
                            <div class="h-3 sm:h-4 bg-gray-200 rounded w-24 sm:w-28"></div>
                        </div>
                    </div>
                </div>

                <!-- Card Form Skeleton -->
                <div class="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 animate-pulse">
                    <div class="h-5 sm:h-6 bg-gray-200 rounded w-1/4 mb-4 sm:mb-6"></div>

                    <div class="space-y-3 sm:space-y-4">
                        <!-- Cardholder Name Skeleton -->
                        <div>
                            <div class="h-3 sm:h-4 bg-gray-200 rounded w-20 sm:w-24 mb-2"></div>
                            <div class="h-12 sm:h-12 bg-gray-200 rounded"></div>
                        </div>

                        <!-- Card Number Skeleton -->
                        <div>
                            <div class="h-3 sm:h-4 bg-gray-200 rounded w-16 sm:w-20 mb-2"></div>
                            <div class="h-12 sm:h-12 bg-gray-200 rounded"></div>
                        </div>

                        <!-- Expiry and CVV Skeleton -->
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
                            <div>
                                <div class="h-3 sm:h-4 bg-gray-200 rounded w-12 sm:w-16 mb-2"></div>
                                <div class="h-12 sm:h-12 bg-gray-200 rounded"></div>
                            </div>
                            <div>
                                <div class="h-3 sm:h-4 bg-gray-200 rounded w-8 sm:w-12 mb-2"></div>
                                <div class="h-12 sm:h-12 bg-gray-200 rounded"></div>
                            </div>
                            <div>
                                <div class="h-3 sm:h-4 bg-gray-200 rounded w-6 sm:w-8 mb-2"></div>
                                <div class="h-12 sm:h-12 bg-gray-200 rounded"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button Skeleton -->
                    <div class="mt-6 sm:mt-8">
                        <div class="h-12 sm:h-14 bg-gray-200 rounded w-full"></div>
                    </div>
                </div>
            </div>

            <!-- Error States (excluding payment_error which is handled in the form) -->
            <div v-else-if="error_type && error_type !== 'payment_error'" class="text-center transition-all duration-500 ease-in-out">

                <!-- Invalid Token -->
                <div v-if="error_type === 'invalid_token'" class="bg-red-50 border border-red-200 rounded-lg p-4 sm:p-6 transform transition-all duration-300 hover:scale-105">
                    <div class="text-red-600 text-4xl sm:text-6xl mb-3 sm:mb-4 animate-bounce">⚠️</div>
                    <h2 class="text-xl sm:text-2xl font-bold text-red-800 mb-3 sm:mb-4">Geçersiz Ödeme Bağlantısı</h2>
                    <p class="text-red-700 mb-4 sm:mb-6 text-sm sm:text-base">{{ error_data?.error_message || "Bu ödeme bağlantısı geçersiz veya süresi dolmuş." }}</p>
                    <a href="/" class="inline-block bg-red-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg hover:bg-red-700 transition-all duration-200 transform hover:scale-105 text-sm sm:text-base">
                        Ana Sayfa
                    </a>
                </div>

                <!-- Already Paid -->
                <div v-else-if="error_type === 'already_paid'" class="bg-green-50 border border-green-200 rounded-lg p-4 sm:p-6 transform transition-all duration-300 hover:scale-105">
                    <div class="text-green-600 text-4xl sm:text-6xl mb-3 sm:mb-4 animate-pulse">✅</div>
                    <h2 class="text-xl sm:text-2xl font-bold text-green-800 mb-3 sm:mb-4">Ödeme Tamamlanmış</h2>
                    <p class="text-green-700 mb-3 sm:mb-4 text-sm sm:text-base">Bu ödeme daha önce başarıyla tamamlanmış.</p>
                    <div v-if="error_data?.amount" class="text-kbgreen font-semibold mb-4 sm:mb-6 text-sm sm:text-base">
                        Tutar: {{ formatAmount(error_data.amount) }} {{ error_data?.currency || "TRY" }}
                    </div>
                    <a href="/" class="inline-block bg-green-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg hover:bg-green-700 transition-all duration-200 transform hover:scale-105 text-sm sm:text-base">
                        Ana Sayfa
                    </a>
                </div>

                <!-- Expired -->
                <div v-else-if="error_type === 'expired'" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 sm:p-6 transform transition-all duration-300 hover:scale-105">
                    <div class="text-yellow-600 text-4xl sm:text-6xl mb-3 sm:mb-4 animate-pulse">⏰</div>
                    <h2 class="text-xl sm:text-2xl font-bold text-yellow-800 mb-3 sm:mb-4">Süre Dolmuş</h2>
                    <p class="text-yellow-700 mb-3 sm:mb-4 text-sm sm:text-base">Bu ödeme bağlantısının süresi dolmuş.</p>
                    <div v-if="error_data?.expired_at" class="text-yellow-800 mb-4 sm:mb-6 text-sm sm:text-base">
                        Son geçerlilik: {{ error_data.expired_at }}
                    </div>
                    <a href="/" class="inline-block bg-yellow-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg hover:bg-yellow-700 transition-all duration-200 transform hover:scale-105 text-sm sm:text-base">
                        Ana Sayfa
                    </a>
                </div>

                <!-- Rate Limited -->
                <div v-else-if="error_type === 'rate_limited'" class="bg-orange-50 border border-orange-200 rounded-lg p-4 sm:p-6 transform transition-all duration-300 hover:scale-105">
                    <div class="text-orange-600 text-4xl sm:text-6xl mb-3 sm:mb-4 animate-bounce">🚫</div>
                    <h2 class="text-xl sm:text-2xl font-bold text-orange-800 mb-3 sm:mb-4">Çok Fazla Deneme</h2>
                    <p class="text-orange-700 mb-4 sm:mb-6 text-sm sm:text-base">{{ error_data?.error_message || "Çok fazla deneme yaptınız. Lütfen bir süre bekleyiniz." }}</p>
                    <a href="/" class="inline-block bg-orange-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg hover:bg-orange-700 transition-all duration-200 transform hover:scale-105 text-sm sm:text-base">
                        Ana Sayfa
                    </a>
                </div>

                <!-- System Error -->
                <div v-else class="bg-gray-50 border border-gray-200 rounded-lg p-4 sm:p-6 transform transition-all duration-300 hover:scale-105">
                    <div class="text-gray-600 text-4xl sm:text-6xl mb-3 sm:mb-4 animate-pulse">❌</div>
                    <h2 class="text-xl sm:text-2xl font-bold text-gray-800 mb-3 sm:mb-4">Sistem Hatası</h2>
                    <p class="text-gray-700 mb-4 sm:mb-6 text-sm sm:text-base">{{ error_data?.error_message || "Bir hata oluştu. Lütfen daha sonra tekrar deneyiniz." }}</p>
                    <a href="/" class="inline-block bg-gray-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg hover:bg-gray-700 transition-all duration-200 transform hover:scale-105 text-sm sm:text-base">
                        Ana Sayfa
                    </a>
                </div>
            </div>

            <!-- Payment Form -->
            <div v-else-if="isPaymentFormVisible" class="transition-all duration-500 ease-in-out transform" :class="{
                'animate-fade-in': cardEnterAnimation,
                'opacity-0 translate-y-4': !cardEnterAnimation
            }">
                <!-- Payment Info -->
                <div class="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 mb-4 sm:mb-6 transform transition-all duration-300 hover:shadow-lg" :class="{
                    'animate-slide-in-from-top': cardEnterAnimation
                }">
                    <h1 class="text-xl sm:text-2xl font-bold text-gray-800 mb-3 sm:mb-4">Kira Ödemesi</h1>

                    <div class="space-y-2 sm:space-y-3">
                        <div class="flex flex-col md:flex-row justify-between animate-fade-in-delayed" style="animation-delay: 0.1s;">
                            <span class="text-gray-600 text-sm sm:text-base">Açıklama:</span>
                            <span class="font-semibold text-sm sm:text-base">{{ payment_data?.description || "Ödeme" }}</span>
                        </div>
                        <div class="flex items-center justify-between animate-fade-in-delayed" style="animation-delay: 0.2s;">
                            <span class="text-gray-600 text-sm sm:text-base">Kira Tutarı:</span>
                            <span class="text-xl sm:text-2xl font-bold text-kbgreen ">
                                {{ formatAmount(payment_data?.rent_amount) }} {{ payment_data?.currency || "TRY" }}
                            </span>
                        </div>
                        <div v-if="payment_data?.expires_at" class="flex items-center justify-between animate-fade-in-delayed" style="animation-delay: 0.3s;">
                            <span class="text-gray-600 text-sm sm:text-base">Son geçerlilik:</span>
                            <span class="text-xs sm:text-sm text-black">{{ new Date(payment_data.expires_at).toLocaleString("tr-TR") }}</span>
                        </div>
                    </div>
                </div>

                <!-- Customer Information Form -->
                <div class="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 mb-4 sm:mb-6 transform transition-all duration-300 hover:shadow-lg" :class="{
                    'animate-slide-in-from-bottom': cardEnterAnimation
                }">
                    <h2 class="text-lg sm:text-xl font-semibold text-gray-800 mb-4 sm:mb-6">Müşteri Bilgileri</h2>

                    <div class="space-y-3 sm:space-y-4">
                        <!-- Full Name (Disabled) -->
                        <div class="relative group transform transition-all duration-300">
                            <input id="full_name" :value="payment_data?.scoring_request?.full_name" type="text" disabled class="peer w-full p-3 sm:p-3 border-2 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed border-gray-200" placeholder=" " />
                            <label for="full_name"
                                class="absolute left-3 -top-2.5 bg-white px-1 text-sm text-gray-500 transition-all peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-focus:-top-2.5 peer-focus:text-sm">
                                Ad Soyad
                            </label>
                        </div>

                        <!-- TCKN (Disabled) -->
                        <div class="relative group transform transition-all duration-300">
                            <input id="tckn" :value="payment_data?.scoring_request?.tckn" type="text" disabled class="peer w-full p-3 sm:p-3 border-2 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed border-gray-200" placeholder=" " />
                            <label for="tckn"
                                class="absolute left-3 -top-2.5 bg-white px-1 text-sm text-gray-500 transition-all peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-focus:-top-2.5 peer-focus:text-sm">
                                TC Kimlik No
                            </label>
                        </div>

                        <!-- Phone (Disabled) -->
                        <div class="relative group transform transition-all duration-300">
                            <input id="phone" :value="payment_data?.scoring_request?.additional_data?.phone" type="text" disabled class="peer w-full p-3 sm:p-3 border-2 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed border-gray-200"
                                placeholder=" " />
                            <label for="phone"
                                class="absolute left-3 -top-2.5 bg-white px-1 text-sm text-gray-500 transition-all peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-focus:-top-2.5 peer-focus:text-sm">
                                Telefon
                            </label>
                        </div>

                        <!-- Email (Editable) -->
                        <div class="relative group transform transition-all duration-300">
                            <input id="email" v-model="form.email" type="email" required @blur="handleEmailBlur" @input="handleEmailInput"
                                class="peer w-full p-3 sm:p-3 border-2 rounded-lg focus:outline-none transition-all duration-200 text-base touch-manipulation transform" :class="{
                                    'border-red-500 focus:border-red-600 animate-error-glow': (errors.email || validationErrors.email) && touched.email,
                                    'border-green-500 focus:border-green-600': !validationErrors.email && touched.email && form.email.trim(),
                                    'border-gray-300 focus:border-blue-500 focus:shadow-glow': !touched.email
                                }" placeholder=" " autocomplete="email" :aria-invalid="(errors.email || validationErrors.email) && touched.email ? 'true' : 'false'"
                                :aria-describedby="(errors.email || validationErrors.email) && touched.email ? 'email-error' : !validationErrors.email && touched.email && form.email.trim() ? 'email-success' : null"
                                aria-label="E-posta adresinizi girin" />
                            <label for="email"
                                class="absolute left-3 -top-2.5 bg-white px-1 text-sm transition-all peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-focus:-top-2.5 peer-focus:text-sm" :class="{
                                    'text-red-600': (errors.email || validationErrors.email) && touched.email,
                                    'text-green-600': !validationErrors.email && touched.email && form.email.trim(),
                                    'text-gray-600 peer-focus:text-blue-600': !touched.email
                                }">
                                E-posta Adresi *
                            </label>
                            <div v-if="(errors.email || validationErrors.email) && touched.email" id="email-error" class="text-red-500 text-sm mt-1 transition-all duration-200 animate-slide-down" role="alert" aria-live="polite">
                                {{ validationErrors.email || errors.email }}
                            </div>
                            <div v-else-if="!validationErrors.email && touched.email && form.email.trim()" id="email-success" class="absolute right-3 top-3 text-green-500 text-sm mt-1 transition-all duration-200 animate-success-bounce" role="status"
                                aria-live="polite">
                                <svg fill="#15A559" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" class="w-[20px]">
                                    <g id="SVGRepo_iconCarrier">
                                        <path
                                            d="M16 0c-8.836 0-16 7.163-16 16s7.163 16 16 16c8.837 0 16-7.163 16-16s-7.163-16-16-16zM16 30.032c-7.72 0-14-6.312-14-14.032s6.28-14 14-14 14 6.28 14 14-6.28 14.032-14 14.032zM22.386 10.146l-9.388 9.446-4.228-4.227c-0.39-0.39-1.024-0.39-1.415 0s-0.391 1.023 0 1.414l4.95 4.95c0.39 0.39 1.024 0.39 1.415 0 0.045-0.045 0.084-0.094 0.119-0.145l9.962-10.024c0.39-0.39 0.39-1.024 0-1.415s-1.024-0.39-1.415 0z">
                                        </path>
                                    </g>
                                </svg>
                            </div>
                        </div>

                        <!-- Invoice Address (Editable) -->
                        <div class="relative group transform transition-all duration-300">
                            <textarea id="invoice_address" v-model="form.invoice_address" required rows="3" @blur="handleInvoiceAddressBlur" @input="handleInvoiceAddressInput"
                                class="peer w-full p-3 sm:p-3 border-2 rounded-lg focus:outline-none transition-all duration-200 text-base touch-manipulation resize-none" :class="{
                                    'border-red-500 focus:border-red-600 animate-error-glow': (errors.invoice_address || validationErrors.invoice_address) && touched.invoice_address,
                                    'border-green-500 focus:border-green-600': !validationErrors.invoice_address && touched.invoice_address && form.invoice_address.trim(),
                                    'border-gray-300 focus:border-blue-500 focus:shadow-glow': !touched.invoice_address
                                }" placeholder=" " :aria-invalid="(errors.invoice_address || validationErrors.invoice_address) && touched.invoice_address ? 'true' : 'false'"
                                :aria-describedby="(errors.invoice_address || validationErrors.invoice_address) && touched.invoice_address ? 'invoice-address-error' : !validationErrors.invoice_address && touched.invoice_address && form.invoice_address.trim() ? 'invoice-address-success' : null"
                                aria-label="Fatura adresinizi girin"></textarea>
                            <label for="invoice_address"
                                class="absolute left-3 -top-2.5 bg-white px-1 text-sm transition-all peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-focus:-top-2.5 peer-focus:text-sm" :class="{
                                    'text-red-600': (errors.invoice_address || validationErrors.invoice_address) && touched.invoice_address,
                                    'text-green-600': !validationErrors.invoice_address && touched.invoice_address && form.invoice_address.trim(),
                                    'text-gray-600 peer-focus:text-blue-600': !touched.invoice_address
                                }">
                                Fatura Adresi *
                            </label>
                            <div v-if="(errors.invoice_address || validationErrors.invoice_address) && touched.invoice_address" id="invoice-address-error" class="text-red-500 text-sm mt-1 transition-all duration-200 animate-slide-down" role="alert"
                                aria-live="polite">
                                {{ validationErrors.invoice_address || errors.invoice_address }}
                            </div>
                            <div v-else-if="!validationErrors.invoice_address && touched.invoice_address && form.invoice_address.trim()" id="invoice-address-success"
                                class="absolute right-3 top-3 text-green-500 text-sm mt-1 transition-all duration-200 animate-success-bounce" role="status" aria-live="polite">
                                <svg fill="#15A559" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" class="w-[20px]">
                                    <g id="SVGRepo_iconCarrier">
                                        <path
                                            d="M16 0c-8.836 0-16 7.163-16 16s7.163 16 16 16c8.837 0 16-7.163 16-16s-7.163-16-16-16zM16 30.032c-7.72 0-14-6.312-14-14.032s6.28-14 14-14 14 6.28 14 14-6.28 14.032-14 14.032zM22.386 10.146l-9.388 9.446-4.228-4.227c-0.39-0.39-1.024-0.39-1.415 0s-0.391 1.023 0 1.414l4.95 4.95c0.39 0.39 1.024 0.39 1.415 0 0.045-0.045 0.084-0.094 0.119-0.145l9.962-10.024c0.39-0.39 0.39-1.024 0-1.415s-1.024-0.39-1.415 0z">
                                        </path>
                                    </g>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card Form -->
                <form id="cc-form" @submit.prevent="enhancedSubmitPayment" class="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 transform transition-all duration-300 hover:shadow-lg" :class="{
                    'animate-slide-in-from-bottom': cardEnterAnimation
                }" role="form" aria-labelledby="form-title" aria-describedby="form-description" novalidate>
                    <h2 id="form-title" class="text-lg sm:text-xl font-semibold text-gray-800 mb-4 sm:mb-6">Kart Bilgileri</h2>

                    <!-- Form Description for Screen Readers -->
                    <div id="form-description" class="sr-only">
                        Kredi kartı bilgilerinizi güvenli bir şekilde girin. Tüm alanlar zorunludur ve gerçek zamanlı doğrulama yapılır.
                    </div>

                    <!-- Form Status for Screen Readers -->
                    <div id="form-status-invalid" class="sr-only" v-if="!isFormValid">
                        Form geçersiz. Lütfen tüm alanları doğru şekilde doldurun.
                    </div>
                    <div id="form-status-valid" class="sr-only" v-if="isFormValid">
                        Form geçerli. Ödeme işlemine devam edebilirsiniz.
                    </div>

                    <!-- Global Error Message -->
                    <div v-if="errors.payment_error" class="bg-red-50 border border-red-200 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6 transition-all duration-300 animate-shake" role="alert" aria-live="assertive">
                        <p class="text-red-800 text-sm sm:text-base">{{ errors.payment_error }}</p>
                    </div>

                    <!-- Enhanced Error Handling -->
                    <div v-if="hasNetworkError || hasSystemError" class="bg-red-50 border border-red-200 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6 transition-all duration-300 animate-slide-down">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-4 w-4 sm:h-5 sm:w-5 text-red-400 animate-wiggle" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-2 sm:ml-3 flex-1">
                                <h3 class="text-sm font-medium text-red-800">
                                    {{ hasNetworkError ? "Bağlantı Hatası" : "Sistem Hatası" }}
                                </h3>
                                <p class="mt-1 text-sm text-red-700">{{ errorMessage }}</p>

                                <!-- Retry Section -->
                                <div v-if="canRetry" class="mt-3 sm:mt-4 flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                                    <button @click="retryPayment" :disabled="isAnyFormProcessing"
                                        class="inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 touch-manipulation transform hover:scale-105 active:scale-95"
                                        :class="{ 'opacity-50 cursor-not-allowed': isAnyFormProcessing }">
                                        <svg v-if="isAnyFormProcessing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-red-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <svg v-else class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                        {{ isAnyFormProcessing ? "Deneniyor..." : "Tekrar Dene" }}
                                    </button>

                                    <span class="text-sm text-red-600 animate-fade-in" style="animation-delay: 0.5s;">
                                        ({{ retryCount }}/{{ maxRetries }})
                                    </span>

                                    <button @click="clearErrors" class="text-sm text-red-600 hover:text-red-800 underline transition-colors duration-200 touch-manipulation">
                                        Kapat
                                    </button>
                                </div>

                                <!-- No more retries -->
                                <div v-else-if="retryCount >= maxRetries" class="mt-3 sm:mt-4">
                                    <p class="text-sm text-red-600">
                                        Maksimum deneme sayısına ulaşıldı. Lütfen sayfayı yenileyiniz.
                                    </p>
                                    <button @click="() => { window.location.reload() }"
                                        class="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 touch-manipulation transform hover:scale-105 active:scale-95">
                                        <svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                        Sayfayı Yenile
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Processing Overlay -->
                    <div v-if="isAnyFormProcessing" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg z-10 animate-fade-in">
                        <div class="text-center">
                            <div class="relative">
                                <!-- Enhanced Loading Spinner -->
                                <div class="w-12 sm:w-16 h-12 sm:h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-3 sm:mb-4"></div>
                                <!-- Progress Ring -->
                                <div class="absolute inset-0 w-12 sm:w-16 h-12 sm:h-16 border-4 border-transparent border-t-green-500 rounded-full animate-pulse mx-auto"></div>
                                <!-- Inner pulse -->
                                <div class="absolute inset-2 bg-blue-50 rounded-full animate-ping opacity-20"></div>
                            </div>
                            <p class="text-gray-600 font-medium animate-pulse text-sm sm:text-base">
                                {{ isFormSubmitting ? "Ödeme işleniyor..." : "Hazırlanıyor..." }}
                            </p>
                            <div class="mt-2 flex justify-center space-x-1">
                                <div class="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-600 rounded-full animate-bounce" style="animation-delay: 0ms"></div>
                                <div class="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-600 rounded-full animate-bounce" style="animation-delay: 150ms"></div>
                                <div class="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-600 rounded-full animate-bounce" style="animation-delay: 300ms"></div>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3 sm:space-y-4 relative" :class="{ 'opacity-50 pointer-events-none': isAnyFormProcessing }">
                        <!-- Cardholder Name -->
                        <div class="relative group transform transition-all duration-300" :class="{
                            'animate-field-enter': formFieldAnimations.cardholder_name,
                            'opacity-0 translate-y-2': !formFieldAnimations.cardholder_name,
                            'scale-105': focusedField === 'cardholder_name'
                        }">
                            <input id="cardholder_name" v-model="form.cardholder_name" type="text" required @keypress="preventNumericInput" @blur="enhancedHandleCardholderNameBlur" @focus="enhancedHandleCardholderNameFocus"
                                @input="handleCardholderNameInput" class="peer w-full p-3 sm:p-3 border-2 rounded-lg focus:outline-none focus-visible:outline-none transition-all duration-200 text-base touch-manipulation transform" :class="{
                                    'border-red-500 focus:border-red-600 focus:outline-none animate-error-glow ': (errors.cardholder_name || validationErrors.cardholder_name) && touched.cardholder_name,
                                    'border-green-500 focus:border-green-600 focus:outline-none': !validationErrors.cardholder_name && touched.cardholder_name && form.cardholder_name.trim(),
                                    'border-gray-300 focus:border-kbgreen focus:shadow-none': !touched.cardholder_name,
                                    'focus:scale-102': focusedField === 'cardholder_name'
                                }" placeholder=" " autocomplete="cc-name" :aria-invalid="(errors.cardholder_name || validationErrors.cardholder_name) && touched.cardholder_name ? 'true' : 'false'"
                                :aria-describedby="(errors.cardholder_name || validationErrors.cardholder_name) && touched.cardholder_name ? 'cardholder-name-error' : !validationErrors.cardholder_name && touched.cardholder_name && form.cardholder_name.trim() ? 'cardholder-name-success' : null"
                                aria-label="Kart sahibinin adını girin" />
                            <label for="cardholder_name"
                                class="absolute left-3 -top-2.5 bg-white px-1 text-sm transition-all peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-focus:-top-2.5 peer-focus:text-sm" :class="{
                                    'text-red-600': (errors.cardholder_name || validationErrors.cardholder_name) && touched.cardholder_name,
                                    'text-green-600': !validationErrors.cardholder_name && touched.cardholder_name && form.cardholder_name.trim(),
                                    'text-gray-600 peer-focus:text-blue-600': !touched.cardholder_name
                                }">
                                Kart Sahibi Adı *
                            </label>
                            <div v-if="(errors.cardholder_name || validationErrors.cardholder_name) && touched.cardholder_name" id="cardholder-name-error" class="text-red-500 text-sm mt-1 transition-all duration-200 animate-slide-down" role="alert"
                                aria-live="polite">
                                {{ validationErrors.cardholder_name || errors.cardholder_name }}
                            </div>
                            <div v-else-if="!validationErrors.cardholder_name && touched.cardholder_name && form.cardholder_name.trim()" id="cardholder-name-success"
                                class="absolute right-3 top-3 text-green-500 text-sm mt-1 transition-all duration-200 animate-success-bounce" role="status" aria-live="polite">
                                <svg fill="#15A559" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
                                    <g id="SVGRepo_iconCarrier">
                                        <path
                                            d="M16 0c-8.836 0-16 7.163-16 16s7.163 16 16 16c8.837 0 16-7.163 16-16s-7.163-16-16-16zM16 30.032c-7.72 0-14-6.312-14-14.032s6.28-14 14-14 14 6.28 14 14-6.28 14.032-14 14.032zM22.386 10.146l-9.388 9.446-4.228-4.227c-0.39-0.39-1.024-0.39-1.415 0s-0.391 1.023 0 1.414l4.95 4.95c0.39 0.39 1.024 0.39 1.415 0 0.045-0.045 0.084-0.094 0.119-0.145l9.962-10.024c0.39-0.39 0.39-1.024 0-1.415s-1.024-0.39-1.415 0z">
                                        </path>
                                    </g>
                                </svg>

                            </div>
                        </div>

                        <!-- Card Number -->
                        <div class="relative group transform transition-all duration-300" :class="{
                            'animate-field-enter': formFieldAnimations.card_number,
                            'opacity-0 translate-y-2': !formFieldAnimations.card_number,
                            'scale-105': focusedField === 'card_number'
                        }" style="animation-delay: 0.1s;">
                            <input id="card_number" v-model="form.card_number" v-maska data-maska="#### #### #### ####" type="tel" required @blur="enhancedHandleCardNumberBlur" @focus="enhancedHandleCardNumberFocus" @input="handleCardNumberInput"
                                class="peer w-full p-3 sm:p-3 border-2 rounded-lg focus:outline-none transition-all duration-200 text-base touch-manipulation transform" :class="{
                                    'border-red-500 focus:border-red-600 animate-error-glow': (errors.card_number || validationErrors.card_number) && touched.card_number,
                                    'border-green-500 focus:border-green-600': cardNumberIsValid && touched.card_number,
                                    'border-gray-300 focus:border-blue-500 focus:shadow-glow': !touched.card_number,
                                    'focus:scale-102': focusedField === 'card_number'
                                }" placeholder=" " autocomplete="cc-number" inputmode="numeric" :aria-invalid="(errors.card_number || validationErrors.card_number) && touched.card_number ? 'true' : 'false'"
                                :aria-describedby="(errors.card_number || validationErrors.card_number) && touched.card_number ? 'card-number-error' : cardNumberIsValid && touched.card_number ? 'card-number-success' : null"
                                aria-label="16 haneli kart numaranızı girin" />
                            <label for="card_number"
                                class="absolute left-3 -top-2.5 bg-white px-1 text-sm transition-all peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-focus:-top-2.5 peer-focus:text-sm" :class="{
                                    'text-red-600': (errors.card_number || validationErrors.card_number) && touched.card_number,
                                    'text-green-600': cardNumberIsValid && touched.card_number,
                                    'text-gray-600 peer-focus:text-blue-600': !touched.card_number
                                }">
                                Kart Numarası *
                            </label>
                            <div v-if="(errors.card_number || validationErrors.card_number) && touched.card_number" id="card-number-error" class="text-red-500 text-sm mt-1 transition-all duration-200 animate-slide-down" role="alert"
                                aria-live="polite">
                                {{ validationErrors.card_number || errors.card_number }}
                            </div>
                            <div v-else-if="cardNumberIsValid && touched.card_number" id="card-number-success" class="absolute right-3 top-3 text-green-500 text-sm mt-1 transition-all duration-200 animate-success-bounce" role="status"
                                aria-live="polite">
                                <svg fill="#15A559" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" class="w-[20px]">
                                    <g id="SVGRepo_iconCarrier">
                                        <path
                                            d="M16 0c-8.836 0-16 7.163-16 16s7.163 16 16 16c8.837 0 16-7.163 16-16s-7.163-16-16-16zM16 30.032c-7.72 0-14-6.312-14-14.032s6.28-14 14-14 14 6.28 14 14-6.28 14.032-14 14.032zM22.386 10.146l-9.388 9.446-4.228-4.227c-0.39-0.39-1.024-0.39-1.415 0s-0.391 1.023 0 1.414l4.95 4.95c0.39 0.39 1.024 0.39 1.415 0 0.045-0.045 0.084-0.094 0.119-0.145l9.962-10.024c0.39-0.39 0.39-1.024 0-1.415s-1.024-0.39-1.415 0z">
                                        </path>
                                    </g>
                                </svg>
                            </div>
                        </div>

                        <!-- Expiry and CVV -->
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
                            <!-- Expiry Month -->
                            <div class="transform transition-all duration-300" :class="{
                                'animate-field-enter': formFieldAnimations.expiry_month,
                                'opacity-0 translate-y-2': !formFieldAnimations.expiry_month
                            }" style="animation-delay: 0.2s;">
                                <label for="expiry_month" class="block text-sm font-medium mb-2 transition-all duration-200" :class="{
                                    'text-red-600': (errors.expiry_month || validationErrors.expiry_month) && touched.expiry_month,
                                    'text-green-600': !validationErrors.expiry_month && touched.expiry_month && form.expiry_month,
                                    'text-gray-600': !touched.expiry_month
                                }">
                                    Son Kullanma Ayı *
                                </label>
                                <select id="expiry_month" v-model="form.expiry_month" required @change="handleExpiryMonthChange" class="w-full p-2 border-2 rounded-lg focus:outline-none transition-all duration-200 text-base touch-manipulation"
                                    :class="{
                                        'border-red-500 focus:border-red-600 animate-error-glow': (errors.expiry_month || validationErrors.expiry_month) && touched.expiry_month,
                                        'border-green-500 focus:border-green-600': !validationErrors.expiry_month && touched.expiry_month && form.expiry_month,
                                        'border-gray-300 focus:border-blue-500': !touched.expiry_month
                                    }" autocomplete="cc-exp-month" :aria-invalid="(errors.expiry_month || validationErrors.expiry_month) && touched.expiry_month ? 'true' : 'false'"
                                    :aria-describedby="(errors.expiry_month || validationErrors.expiry_month) && touched.expiry_month ? 'expiry-month-error' : null" aria-label="Kartın son kullanma ayını seçin">
                                    <option value="" disabled>Ay</option>
                                    <option v-for="month in 12" :key="month" :value="month">
                                        {{ month.toString().padStart(2, "0") }}
                                    </option>
                                </select>
                                <div v-if="(errors.expiry_month || validationErrors.expiry_month) && touched.expiry_month" id="expiry-month-error" class="text-red-500 text-sm mt-1 transition-all duration-200 animate-slide-down" role="alert"
                                    aria-live="polite">
                                    {{ validationErrors.expiry_month || errors.expiry_month }}
                                </div>
                            </div>

                            <!-- Expiry Year -->
                            <div class="transform transition-all duration-300" :class="{
                                'animate-field-enter': formFieldAnimations.expiry_year,
                                'opacity-0 translate-y-2': !formFieldAnimations.expiry_year
                            }" style="animation-delay: 0.3s;">
                                <label for="expiry_year" class="block text-sm font-medium mb-2 transition-all duration-200" :class="{
                                    'text-red-600': (errors.expiry_year || validationErrors.expiry_year) && touched.expiry_year,
                                    'text-green-600': !validationErrors.expiry_year && touched.expiry_year && form.expiry_year,
                                    'text-gray-600': !touched.expiry_year
                                }">
                                    Son Kullanma Yılı *
                                </label>
                                <select id="expiry_year" v-model="form.expiry_year" required @change="handleExpiryYearChange" class="w-full p-2 border-2 rounded-lg focus:outline-none transition-all duration-200 text-base touch-manipulation" :class="{
                                    'border-red-500 focus:border-red-600 animate-error-glow': (errors.expiry_year || validationErrors.expiry_year) && touched.expiry_year,
                                    'border-green-500 focus:border-green-600': !validationErrors.expiry_year && touched.expiry_year && form.expiry_year,
                                    'border-gray-300 focus:border-blue-500': !touched.expiry_year
                                }" autocomplete="cc-exp-year" :aria-invalid="(errors.expiry_year || validationErrors.expiry_year) && touched.expiry_year ? 'true' : 'false'"
                                    :aria-describedby="(errors.expiry_year || validationErrors.expiry_year) && touched.expiry_year ? 'expiry-year-error' : null" aria-label="Kartın son kullanma yılını seçin">
                                    <option value="" disabled>Yıl</option>
                                    <option v-for="year in yearOptions" :key="year" :value="year">
                                        {{ year }}
                                    </option>
                                </select>
                                <div v-if="(errors.expiry_year || validationErrors.expiry_year) && touched.expiry_year" id="expiry-year-error" class="text-red-500 text-sm mt-1 transition-all duration-200 animate-slide-down" role="alert"
                                    aria-live="polite">
                                    {{ validationErrors.expiry_year || errors.expiry_year }}
                                </div>
                            </div>

                            <!-- CVV -->
                            <div class="relative transform transition-all duration-300" :class="{
                                'animate-field-enter': formFieldAnimations.cvv,
                                'opacity-0 translate-y-2': !formFieldAnimations.cvv,
                                'scale-105': focusedField === 'cvv'
                            }" style="animation-delay: 0.4s;">
                                <label for="cvv" class="block text-sm font-medium mb-2 transition-all duration-200" :class="{
                                    'text-red-600': (errors.cvv || validationErrors.cvv) && touched.cvv,
                                    'text-green-600': !validationErrors.cvv && touched.cvv && form.cvv.length === 3,
                                    'text-gray-600': !touched.cvv
                                }">
                                    CVV *
                                </label>
                                <input id="cvv" v-model="form.cvv" v-maska data-maska="###" type="tel" required maxlength="3" @blur="enhancedHandleCVVBlur" @focus="enhancedHandleCVVFocus" @input="handleCVVInput"
                                    class="w-full p-2 border-2 rounded-lg focus:outline-none transition-all duration-200 text-base touch-manipulation transform" :class="{
                                        'border-red-500 focus:border-red-600 animate-error-glow': (errors.cvv || validationErrors.cvv) && touched.cvv,
                                        'border-green-500 focus:border-green-600': !validationErrors.cvv && touched.cvv && form.cvv.length === 3,
                                        'border-gray-300 focus:border-blue-500 focus:shadow-glow': !touched.cvv,
                                        'focus:scale-102': focusedField === 'cvv'
                                    }" placeholder="CVV" autocomplete="cc-csc" inputmode="numeric" :aria-invalid="(errors.cvv || validationErrors.cvv) && touched.cvv ? 'true' : 'false'"
                                    :aria-describedby="(errors.cvv || validationErrors.cvv) && touched.cvv ? 'cvv-error' : !validationErrors.cvv && touched.cvv && form.cvv.length === 3 ? 'cvv-success' : 'cvv-help'"
                                    aria-label="Kartın arkasındaki 3 haneli CVV kodunu girin" />
                                <div v-if="(errors.cvv || validationErrors.cvv) && touched.cvv" id="cvv-error" class="text-red-500 text-sm mt-1 transition-all duration-200 animate-slide-down" role="alert" aria-live="polite">
                                    {{ validationErrors.cvv || errors.cvv }}
                                </div>
                                <div v-else-if="!validationErrors.cvv && touched.cvv && form.cvv.length === 3" id="cvv-success" class="absolute top-9 right-3 text-green-500 text-sm mt-1 transition-all duration-200 animate-success-bounce"
                                    role="status" aria-live="polite">
                                    <svg fill="#15A559" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" class="w-[20px]">
                                        <g id="SVGRepo_iconCarrier">
                                            <path
                                                d="M16 0c-8.836 0-16 7.163-16 16s7.163 16 16 16c8.837 0 16-7.163 16-16s-7.163-16-16-16zM16 30.032c-7.72 0-14-6.312-14-14.032s6.28-14 14-14 14 6.28 14 14-6.28 14.032-14 14.032zM22.386 10.146l-9.388 9.446-4.228-4.227c-0.39-0.39-1.024-0.39-1.415 0s-0.391 1.023 0 1.414l4.95 4.95c0.39 0.39 1.024 0.39 1.415 0 0.045-0.045 0.084-0.094 0.119-0.145l9.962-10.024c0.39-0.39 0.39-1.024 0-1.415s-1.024-0.39-1.415 0z">
                                            </path>
                                        </g>
                                    </svg>
                                </div>
                                <div v-else id="cvv-help" class="text-gray-500 text-xs mt-1" role="note">
                                    Kartın arkasındaki 3 haneli kod
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Privacy Policy and Membership Agreement -->
                    <div class="mt-6 sm:mt-8 space-y-4">
                        <!-- Privacy Policy Checkbox -->
                        <div class="flex items-start">
                            <input id="privacy_policy_accepted" v-model="form.privacy_policy_accepted" type="checkbox" required @blur="handlePrivacyPolicyAcceptedBlur" @change="handlePrivacyPolicyAcceptedInput"
                                class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-all duration-200" :class="{
                                    'border-red-500': (errors.privacy_policy_accepted || validationErrors.privacy_policy_accepted) && touched.privacy_policy_accepted,
                                    'border-green-500': !validationErrors.privacy_policy_accepted && touched.privacy_policy_accepted && form.privacy_policy_accepted,
                                    'border-gray-300': !touched.privacy_policy_accepted
                                }" :aria-invalid="(errors.privacy_policy_accepted || validationErrors.privacy_policy_accepted) && touched.privacy_policy_accepted ? 'true' : 'false'"
                                :aria-describedby="(errors.privacy_policy_accepted || validationErrors.privacy_policy_accepted) && touched.privacy_policy_accepted ? 'privacy-policy-error' : null" aria-label="Aydınlatma metnini kabul et" />
                            <label for="privacy_policy_accepted" class="ml-3 text-sm text-gray-600 cursor-pointer">
                                <a href="/aydinlatma-metni" target="_blank" class="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200">
                                    <strong>Aydınlatma Metnini</strong>
                                </a>
                                okudum ve
                                <strong>Açık Rıza Beyanına</strong>
                                onay veriyorum. *
                            </label>
                        </div>
                        <div v-if="(errors.privacy_policy_accepted || validationErrors.privacy_policy_accepted) && touched.privacy_policy_accepted" id="privacy-policy-error" class="text-red-500 text-sm transition-all duration-200 animate-slide-down"
                            role="alert" aria-live="polite">
                            {{ validationErrors.privacy_policy_accepted || errors.privacy_policy_accepted }}
                        </div>

                        <!-- Membership Agreement Checkbox -->
                        <div class="flex items-start">
                            <input id="membership_agreement_accepted" v-model="form.membership_agreement_accepted" type="checkbox" required @blur="handleMembershipAgreementAcceptedBlur" @change="handleMembershipAgreementAcceptedInput"
                                class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-all duration-200" :class="{
                                    'border-red-500': (errors.membership_agreement_accepted || validationErrors.membership_agreement_accepted) && touched.membership_agreement_accepted,
                                    'border-green-500': !validationErrors.membership_agreement_accepted && touched.membership_agreement_accepted && form.membership_agreement_accepted,
                                    'border-gray-300': !touched.membership_agreement_accepted
                                }" :aria-invalid="(errors.membership_agreement_accepted || validationErrors.membership_agreement_accepted) && touched.membership_agreement_accepted ? 'true' : 'false'"
                                :aria-describedby="(errors.membership_agreement_accepted || validationErrors.membership_agreement_accepted) && touched.membership_agreement_accepted ? 'membership-agreement-error' : null"
                                aria-label="Üyelik sözleşmesini kabul et" />
                            <label for="membership_agreement_accepted" class="ml-3 text-sm text-gray-600 cursor-pointer">
                                <a href="/uyelik-sozlesmesi" target="_blank" class="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200">
                                    <strong>Üyelik Sözleşmesini</strong>
                                </a>
                                okudum ve kabul ediyorum. *
                            </label>
                        </div>
                        <div v-if="(errors.membership_agreement_accepted || validationErrors.membership_agreement_accepted) && touched.membership_agreement_accepted" id="membership-agreement-error"
                            class="text-red-500 text-sm transition-all duration-200 animate-slide-down" role="alert" aria-live="polite">
                            {{ validationErrors.membership_agreement_accepted || errors.membership_agreement_accepted }}
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="mt-6 sm:mt-8">
                        <button type="submit" :disabled="form.processing || isAnyFormProcessing || !isFormValid"
                            class="w-full py-3 sm:py-4 px-4 sm:px-6 rounded-lg font-semibold text-base sm:text-lg transition-all duration-200 transform touch-manipulation animate-button-ready" :class="{
                                'bg-green-600 text-white hover:bg-kbgreen active:bg-kbgreen hover:scale-105 active:scale-95 animate-button-pulse': isFormValid && !isAnyFormProcessing,
                                'bg-gray-400 text-gray-200 cursor-not-allowed': !isFormValid || isAnyFormProcessing
                            }" style="min-height: 44px;" :aria-disabled="form.processing || isAnyFormProcessing || !isFormValid ? 'true' : 'false'" :aria-describedby="!isFormValid ? 'form-status-invalid' : isFormValid ? 'form-status-valid' : null"
                            aria-label="Ödeme işlemini tamamla">
                            <span v-if="isAnyFormProcessing" class="flex items-center justify-center animate-fade-in">
                                <svg class="animate-spin -ml-1 mr-3 h-4 w-4 sm:h-5 sm:w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {{ isFormSubmitting ? "İşleniyor..." : "Hazırlanıyor..." }}
                            </span>
                            <span v-else class="transition-all duration-200">
                                {{ isFormValid ?
                                    `Ödemeyi Tamamla (${formatAmount(payment_data?.rent_amount)} ${payment_data?.currency || "TRY"})` :
                                    "Lütfen tüm alanları doğru doldurunuz"
                                }}
                            </span>
                        </button>
                    </div>

                    <!-- Form Status Indicator -->
                    <div v-if="!isFormValid" id="form-status-invalid" class="mt-3 sm:mt-4 text-center transition-all duration-300 animate-fade-in" role="status" aria-live="polite">
                        <p class="text-sm text-gray-500">
                            <span class="inline-block w-1.5 h-1.5 sm:w-2 sm:h-2 bg-orange-400 rounded-full mr-2 animate-pulse" aria-hidden="true"></span>
                            Form tamamlanmadı - eksik veya hatalı alanlar var
                        </p>
                    </div>
                    <div v-else id="form-status-valid" class="mt-3 sm:mt-4 text-center transition-all duration-300 animate-success-bounce" role="status" aria-live="polite">
                        <p class="text-sm text-green-600">
                            <span class="inline-block w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-500 rounded-full mr-2 animate-pulse" aria-hidden="true"></span>
                            Form hazır - ödemeyi tamamlayabilirsiniz
                        </p>
                    </div>

                    <!-- Security Info -->
                    <div class="mt-4 sm:mt-6 text-center animate-fade-in" style="animation-delay: 1s;">
                        <p class="text-xs sm:text-sm text-gray-500">
                            <span aria-hidden="true">🔒</span>
                            <span class="sr-only">Güvenlik: </span>
                            Ödemeniz SSL sertifikası ile güvence altındadır
                        </p>
                    </div>
                </form>
            </div>

            <!-- Fallback for unknown state -->
            <div v-else class="text-center transition-all duration-500 ease-in-out">
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 sm:p-6 transform transition-all duration-300 hover:scale-105">
                    <div class="text-gray-600 text-4xl sm:text-6xl mb-3 sm:mb-4 animate-pulse">❓</div>
                    <h2 class="text-xl sm:text-2xl font-bold text-gray-800 mb-3 sm:mb-4">Durum Belirlenemiyor</h2>
                    <p class="text-gray-700 mb-4 sm:mb-6 text-sm sm:text-base">Ödeme durumu belirlenemiyor. Lütfen daha sonra tekrar deneyiniz.</p>
                    <a href="/" class="inline-block bg-gray-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg hover:bg-gray-700 transition-all duration-200 transform hover:scale-105 text-sm sm:text-base">
                        Ana Sayfa
                    </a>
                </div>
            </div>
        </div>

        <!-- 3DS Modal -->
        <div v-if="show3DSModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4" role="dialog" aria-labelledby="threeds-modal-title" aria-modal="true">
            <div class="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col animate-fade-in">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-4 border-b border-gray-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <h3 id="threeds-modal-title" class="text-lg font-semibold text-gray-900">3D Secure Doğrulaması</h3>
                    </div>

                    <button @click="close3DSModal" class="text-gray-400 hover:text-gray-600 transition-colors p-2 rounded-lg hover:bg-gray-100" aria-label="3D Secure doğrulamasını kapat">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Modal Content -->
                <div class="flex-1 p-4">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-5 h-5 text-blue-600 mt-0.5">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-blue-800 mb-1">Güvenlik Doğrulaması</h4>
                                <p class="text-sm text-blue-700">
                                    Ödemenizi güvenli bir şekilde tamamlamak için bankanızın 3D Secure doğrulamasını tamamlayın.
                                    Bu işlem birkaç dakika sürebilir.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- 3DS iframe Container -->
                    <div class="border border-gray-300 rounded-lg overflow-hidden" style="height: 500px;">
                        <iframe v-if="threeDSHtml" :srcdoc="threeDSHtml" class="w-full h-full border-0"
                            sandbox="allow-scripts allow-forms allow-top-navigation allow-top-navigation-by-user-activation allow-popups allow-popups-to-escape-sandbox allow-same-origin" allow="payment; publickey-credentials-get"
                            title="3D Secure Doğrulaması" aria-label="3D Secure doğrulama formu">
                        </iframe>

                        <!-- Loading state -->
                        <div v-else class="flex items-center justify-center h-full bg-gray-50">
                            <div class="text-center">
                                <div class="w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
                                <p class="text-gray-600">3D Secure doğrulaması yükleniyor...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="p-4 border-t border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            <span>256-bit SSL ile şifrelenmiş</span>
                        </div>

                        <button @click="close3DSModal"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                            İptal Et
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </PaymentLayout>
</template>

<style scoped>
/* Enhanced animations and keyframes */
@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fade-in-delayed {
    from {
        opacity: 0;
        transform: translateY(5px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slide-in-from-top {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slide-in-from-bottom {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slide-down {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }

    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 200px;
    }
}

@keyframes field-enter {
    from {
        opacity: 0;
        transform: translateY(8px) scale(0.98);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes shake {

    0%,
    100% {
        transform: translateX(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        transform: translateX(-2px);
    }

    20%,
    40%,
    60%,
    80% {
        transform: translateX(2px);
    }
}

@keyframes wiggle {

    0%,
    100% {
        transform: rotate(0deg);
    }

    25% {
        transform: rotate(-3deg);
    }

    75% {
        transform: rotate(3deg);
    }
}

@keyframes success-glow {

    0%,
    100% {
        box-shadow: 0 0 5px rgba(34, 197, 94, 0.3);
    }

    50% {
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.6);
    }
}

@keyframes success-bounce {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

@keyframes pulse-amount {

    0%,
    100% {
        transform: scale(1);
        color: #16a34a;
    }

    50% {
        transform: scale(1.02);
        color: #15803d;
    }
}

@keyframes button-pulse {

    0%,
    100% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
    }

    50% {
        box-shadow: 0 0 0 8px rgba(34, 197, 94, 0);
    }
}

@keyframes button-ready {
    from {
        opacity: 0.8;
        transform: translateY(4px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation classes */
.animate-fade-in {
    animation: fade-in 0.5s ease-out;
}

.animate-fade-in-delayed {
    animation: fade-in-delayed 0.6s ease-out;
}

.animate-slide-in-from-top {
    animation: slide-in-from-top 0.6s ease-out;
}

.animate-slide-in-from-bottom {
    animation: slide-in-from-bottom 0.6s ease-out;
}

.animate-slide-down {
    animation: slide-down 0.4s ease-out;
}

.animate-field-enter {
    animation: field-enter 0.4s ease-out;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-wiggle {
    animation: wiggle 1s ease-in-out infinite;
}

.animate-success-glow {
    animation: success-glow 2s ease-in-out infinite;
}

.animate-error-glow {
    animation: error-glow 2s ease-in-out infinite;
}

.animate-success-bounce {
    animation: success-bounce 0.6s ease-out;
}

.animate-pulse-amount {
    animation: pulse-amount 2s ease-in-out infinite;
}

.animate-button-pulse {
    animation: button-pulse 2s ease-in-out infinite;
}

.animate-button-ready {
    animation: button-ready 0.5s ease-out;
}

/* Enhanced focus and interaction effects */
.focus\:shadow-glow:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 0 15px rgba(59, 130, 246, 0.2);
}

.focus\:scale-102:focus {
    transform: scale(1.02);
}

/* Staggered animation delays */
.animate-fade-in-delayed:nth-child(1) {
    animation-delay: 0.1s;
}

.animate-fade-in-delayed:nth-child(2) {
    animation-delay: 0.2s;
}

.animate-fade-in-delayed:nth-child(3) {
    animation-delay: 0.3s;
}

.animate-fade-in-delayed:nth-child(4) {
    animation-delay: 0.4s;
}

.animate-fade-in-delayed:nth-child(5) {
    animation-delay: 0.5s;
}

/* Loading enhancement */
@keyframes enhanced-ping {
    0% {
        transform: scale(1);
        opacity: 1;
    }

    75%,
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

.animate-ping {
    animation: enhanced-ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {

    .animate-fade-in,
    .animate-fade-in-delayed,
    .animate-slide-in-from-top,
    .animate-slide-in-from-bottom,
    .animate-slide-down,
    .animate-field-enter,
    .animate-shake,
    .animate-wiggle,
    .animate-success-glow,
    .animate-error-glow,
    .animate-success-bounce,
    .animate-pulse-amount,
    .animate-button-pulse,
    .animate-button-ready {
        animation: none;
    }

    /* Static fallbacks for essential feedback */
    .animate-shake {
        border-color: #ef4444 !important;
    }

    .animate-success-glow {
        border-color: #ef4444 !important;
    }

    .animate-error-glow {
        border-color: #ef4444 !important;
    }
}

/* Enhanced transitions for better UX */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom focus styles for better accessibility */
input:focus,
select:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Enhanced skeleton loading animations */
@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Improved bounce animation */
@keyframes bounce {

    0%,
    100% {
        transform: translateY(0);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }

    50% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

.animate-bounce {
    animation: bounce 1s infinite;
}

/* Enhanced loading spinner */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Smooth scaling transitions */
.transform {
    transform: translateX(0) translateY(0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1);
}

.hover\:scale-105:hover {
    transform: scale(1.05);
}

.active\:scale-95:active {
    transform: scale(0.95);
}

/* Mobile responsive styles - Phase 9.1 enhancements */
@media (max-width: 640px) {
    .transform {
        transform: none !important;
    }

    .hover\:scale-105:hover {
        transform: none !important;
    }

    /* Mobile-specific input styling */
    input,
    select,
    button {
        font-size: 16px !important;
        /* Prevents zoom on iOS */
        min-height: 44px;
        /* Apple's minimum touch target */
    }

    /* Better touch targets */
    .touch-manipulation {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    /* Mobile form improvements */
    .peer {
        -webkit-appearance: none;
        appearance: none;
    }

    select.peer {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
    }

    /* Enhanced mobile spacing */
    .grid.grid-cols-1>div:not(:last-child) {
        margin-bottom: 1rem;
    }

    /* Better mobile error message spacing */
    .bg-red-50 .flex-col button,
    .bg-red-50 .flex-col span {
        width: 100%;
        text-align: center;
    }

    /* Mobile-optimized loading spinner */
    .loading-overlay {
        backdrop-filter: blur(2px);
    }

    /* Better mobile card spacing */
    .space-y-3>*+* {
        margin-top: 0.75rem !important;
    }

    /* Mobile skeleton improvements */
    .animate-pulse {
        animation-duration: 1.5s;
    }
}

/* Better viewport handling */
@media (max-width: 480px) {
    .max-w-2xl {
        max-width: 100%;
        margin-left: 0.5rem;
        margin-right: 0.5rem;
    }

    /* Tighter mobile spacing */
    .p-4 {
        padding: 0.75rem !important;
    }

    .mb-4 {
        margin-bottom: 0.75rem !important;
    }

    /* Mobile typography adjustments */
    .text-xl {
        font-size: 1.125rem !important;
    }

    .text-4xl {
        font-size: 2rem !important;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {

    /* Remove hover effects on touch devices */
    .hover\:bg-green-700:hover,
    .hover\:bg-red-700:hover,
    .hover\:bg-yellow-700:hover,
    .hover\:bg-orange-700:hover,
    .hover\:bg-gray-700:hover {
        background-color: inherit;
    }

    /* Enhanced active states for touch */
    .active\:bg-green-800:active {
        background-color: #166534 !important;
    }

    /* Better focus indicators for touch navigation */
    input:focus,
    select:focus,
    button:focus {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
    }
}

/* Landscape mobile optimizations */
@media (max-width: 896px) and (orientation: landscape) {
    .max-w-2xl {
        max-width: 90%;
    }

    /* Reduce vertical spacing in landscape */
    .space-y-4>*+* {
        margin-top: 0.5rem !important;
    }

    .mb-4 {
        margin-bottom: 0.5rem !important;
    }

    /* Smaller text in landscape */
    .text-4xl {
        font-size: 2.5rem !important;
    }

    .text-xl {
        font-size: 1.25rem !important;
    }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {

    /* Sharper borders on high DPI */
    .border-2 {
        border-width: 1px;
    }

    /* Better icon rendering */
    svg {
        shape-rendering: geometricPrecision;
    }
}

/* Input type specific styles */
input[type="tel"] {
    letter-spacing: 0.05em;
    /* Better number readability */
}

input[autocomplete="cc-number"] {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    letter-spacing: 0.1em;
}

/* Enhanced accessibility */
@media (prefers-contrast: high) {
    .border-gray-300 {
        border-color: #000;
        border-width: 2px;
    }

    .text-gray-600 {
        color: #000;
    }

    .bg-green-600 {
        background-color: #047857;
    }

    .bg-red-600 {
        background-color: #dc2626;
    }
}

/* Focus visible improvements */
button:focus-visible,
input:focus-visible,
select:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Screen reader only class */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus\:not-sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .bg-white {
        background-color: #ffffff;
        color: #000000;
    }

    .border-gray-200 {
        border-color: #000000;
        border-width: 2px;
    }
}
</style>
