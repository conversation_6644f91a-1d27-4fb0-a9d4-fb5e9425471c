<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import CategoryBox from "@/Pages/Shared/CategoryBox.vue";
import { Disclosure, DisclosureButton, DisclosurePanel } from "@headlessui/vue";
import Comments from "@/Pages/Shared/Comments.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        CategoryBox,
        Disclosure,
        DisclosureButton,
        DisclosurePanel,
        Comments
    },
    layout: Layout,
    data() {
        return {
            hiddenbtn1: true,
            isOpen: false
        };
    },
    methods: {
        toogleBlock(block) {
            block.showBlock = !block.showBlock;
        },
        splidedArray(arr, size) {
            return arr.reduce((acc, e, i) => (i % size ? acc[acc.length - 1].push(e) : acc.push([e]), acc), []);
        }
    },
    props: {
        allProducts: Object,
        sliders: Object
    },
    computed: {
        splidedAllProducts() {
            return this.splidedArray(this.allProducts.items.data, 1);
        }
    }
};
</script>

<template>

    <Head title="Kategori Detay" />
    <div class=" max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <div class="sliderclass">
            <splide :options="{ rewind: true, gap: '1rem', perPage: 1, arrows: false }" class="block mts:hidden ts:hidden">
                <splide-slide v-for="item in sliders.filter((item) => item.type === 'mobil')">
                    <Link :href="item.url">
                    <img class="w-full" :src="item.media[0].original_url" alt="" />
                    </Link>
                </splide-slide>
            </splide>

            <splide :options="options" class="hidden mts:block ts:hidden">
                <splide-slide v-for="item in sliders.filter((item) => item.type === 'tablet')">
                    <Link :href="item.url">
                    <img class="w-full" :src="item.media[0].original_url" alt="" />
                    </Link>
                </splide-slide>
            </splide>

            <splide :options="options" class="hidden mts:hidden ts:block">
                <splide-slide v-for="item in sliders.filter((item) => item.type === 'desktop')">
                    <Link :href="item.url">
                    <img class="w-full" :src="item.media[0].original_url" alt="" />
                    </Link>
                </splide-slide>
            </splide>

            <!--                    <div>-->
            <!--                        <svg class="w-full overflow-visible h-96 bg-[#61ba3f]">-->
            <!--                            <rect rx="0" ry="0" x="0" y="0" width="1920" class="fill-current hidden md:block h-96"></rect>-->
            <!--                        </svg>-->
            <!--                        <img id="Mask_Group_70" src="../../images/Mask_Group_70.png" srcset="../../images/Mask_Group_70.png 1x, ../../images/<EMAIL> 2x">-->
            <!--                        <div id="Group_115">-->
            <!--                            <svg class="Ellipse_18">-->
            <!--                                <ellipse id="Ellipse_18" rx="200" ry="200" cx="200" cy="200">-->
            <!--                                </ellipse>-->
            <!--                            </svg>-->
            <!--                        </div>-->
            <!--                        <img id="Mask_Group_63" src="../../images/Mask_Group_63.png" srcset="../../images/Mask_Group_63.png 1x, ../../images/<EMAIL> 2x">-->
            <!--                    </div>-->
        </div>

    </div>


    <section class="mt-6 mts:mt-4 py-1 md:py-9 bg-[#f8f8f8]">
        <div class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
            <div class="flex flex-wrap mx-auto justify-center items-center relative z-40 w-full mt-[50px]">
                <div class="w-full mts:w-5/12 ">
                    <img class="" src="../../images/<EMAIL>" />
                </div>
                <div class="w-full mts:w-7/12 mts:pl-6">
                    <div class="my-4 text-4xl font-bold relative z-40 text-center">Lorem İpsum</div>
                    <div class="font-santralregular text-base text-black">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus libero nisi, porta in metus a, semper imperdiet enim. Sed efficitur felis ac est bibendum lacinia. In tristique neque dignissim, tristique sem ac, luctus diam.
                        In ut blandit quam. Etiam porta magna sem, sit amet dignissim ex eleifend ut. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Aliquam a urna ut tellus pretium faucibus quis id
                        augue.
                        <br><br>
                        Vestibulum a sem consequat, aliquam metus vitae, placerat felis. Phasellus hendrerit metus in lacus luctus sollicitudin. Sed vel leo vel libero ullamcorper viverra.
                        <br><br>
                        Duis eros metus, aliquam ac tellus sit amet, sollicitudin maximus massa. Maecenas finibus aliquam aliquam. Maecenas vestibulum, augue ac posuere placerat, neque dolor aliquam metus, ut facilisis nunc est quis lorem. Nulla
                        facilisi. Aenean tincidunt massa quis dolor bibendum tincidunt eu ut enim. Morbi accumsan posuere dapibus. Cras ullamcorper maximus pharetra.


                    </div>
                </div>
            </div>
            <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0 lg:mt-9">
                <Splide :has-track="false" aria-label="" :options="{ rewind: true, perPage: 4, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 3 } } }">
                    <SplideTrack>
                        <SplideSlide v-for="(productGroup, index) in splidedAllProducts" :key="index" class="flex">
                            <category-box v-for="(product, index) in productGroup" :product="product" :new-container-classes="`w-full`" />
                        </SplideSlide>
                    </SplideTrack>

                    <div class="splide__arrows">
                        <button class="splide__arrow splide__arrow--prev !-left-9">
                            <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                    transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                        <button class="splide__arrow splide__arrow--next !-right-9">
                            <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                    transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                    </div>
                </Splide>
            </div>
        </div>
    </section>
    <div class=" max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="top-banner relative z-40 my-6 w-full">
            <img class="hidden ts:block" src="../../images/<EMAIL>" />
            <img class="hidden mts:block ts:hidden" src="../../images/<EMAIL>" />
            <img class="block mts:hidden" src="../../images/<EMAIL>" />
        </section>
    </div>

</template>
