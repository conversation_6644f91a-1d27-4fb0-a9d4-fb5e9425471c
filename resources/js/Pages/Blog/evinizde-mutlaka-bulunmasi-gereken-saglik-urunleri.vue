<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenu from "@/Pages/Shared/BlogSideMenu.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenu
    },
    layout: Layout,
    setup() {

    }
};
</script>

<template>

    <Head title="Evinizde Mutlaka Bulunması Gereken Sağlık Ürünleri" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source srcset="../../../images/blog-webp/saglikurunleri2.webp" type="image/jpeg">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../../images/blog/saglikurunleri2.png" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Blog</div>
                                <div class="text-base lg:text-3xl font-bold mb-5 text-white mb-2">Evinizde Mutlaka Bulunması Gereken Sağlık Ürünleri</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>08 Ekim 2023</span>
                                    </div>
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">
                                            <g id="view" transform="translate(0)">
                                                <path id="Path_108" data-name="Path 108"
                                                    d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z"
                                                    transform="translate(-1.126 -5.625)" fill="#e2e2e2" />
                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z"
                                                    transform="translate(-6.455 -8.586)" fill="#e2e2e2" />
                                            </g>
                                        </svg>
                                        <span>548</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h1 class="text-base lg:text-3xl font-bold lg:font-semibold pb-1">Evinizde Mutlaka Bulunması Gereken Sağlık Ürünleri </h1>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            Daha uzun ve mutlu bir beden için sağlıklı yaşam tarzını benimsemelisiniz. Sağlıklı yaşam öncelikle dinamik bir zihinde başlamaktadır. Egzersiz yapmalı, sebze-meyve ağırlıklı beslenmeli ve zihninizi her daim güçlü
                            tutmalısınız. Daha fazla enerji ve hayata tutkulu olma sevinci için zararlı tüketimi hayatınızdan çıkarmalısınız. Her daim dinç bir vücut için düzenli yürüme alışkanlığı edinmelisiniz. Bunları devam ettirmek için gerekli
                            şey ise uyku düzenidir. Yetişkin bir
                            insan vücudu için günde 4-6 saat uyku aralığı doktorlar tarafından önerilmektedir. Çok geç yatmamalı ve uyanmamalısınız.
                            <br><br> Günde 2,5 litre su içmeniz önemli unsurlardandır. Aynı zamanda su içmek vücut sağlığı kadar organ ve cilt sağlığına da oldukça yararlıdır. Güne pozitif başlamak ne kadar yararlı olsa da kış aylarında bu durum
                            zorlaşabilir. Grip, nezle ve soğuk algınlığı gibi birtakım hastalıklara yakalanabilirsiniz. Hastalık sürecinde doktora gitmeli ve gerekli tedavi sürecine başlamalısınız. Evinizde boğazınızı yumuşatacak içecekler içebilir
                            ve kremleri kullanabilirsiniz.
                            Doktor onaylı olması da alerjik reaksiyonlarını ortadan kaldıracaktır. Aksi durumlarda evinizde bulunduracağınız sağlık ürünleri ile hastalık sürecinizi de kolaylıkla yönetebilirsiniz. Aşağıda bahsedeceğimiz 5 sağlık ürünü
                            sizler için yararlı olabilir.
                            <br><br>
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Evinizde Bulunması Gereken 5 Sağlık Ürünü </h3>
                            <br>
                            <ul class="ml-1 md:ml-3 list-decimal font-santralregular">
                                <li class=" font-santralregular"><b> Ateş Ölçer </b><br>
                                    Ateş Ölçerler, vücut sıcaklığını ölçmek için kullanılmaktadır. Ateşli hastalıkların takibini yapmak, herhangi bir risk durumunu en aza indirecektir. Hassas ve güvenilir sonuçlar için dijital ölçerlere göz
                                    gezdirebilirsiniz. Sağlığınızı korumak ve erken teşhisler için evinizin bir köşesinde mutlaka bulunmalı
                                </li>
                                <li class=" font-santralregular"><b> Tansiyon Ölçer– Aleti</b><br>
                                    Kalp krizi riskini erken teşhis ile tansiyon ölçerler ile engelleyebilirsiniz. Kan basıncını ölçerek sağlık durumunu izlemek ve hipertansiyon gibi sorunları tespit etmek için gerekli sağlık ürünlerindendir.
                                    Tansiyon hastalığı 40-70 yaş aralığında daha sık görülse de erken yaşlarda da başlayabilir. Düzenli tansiyon ölçümü, her yaşta gerekli olabilmektedir.
                                </li>
                                <!--                                <li class=" font-santralregular"><b> Tekerlekli Sandalye</b><br>-->
                                <!--                                    Yorgun hissettiğiniz dönemlerde yürümek de sizin için zorlu bir hal alıyorsa sandalyeler rahatlık kazandırabilir. Hareket kabiliyeti kısıtlı olan kişilerin bağımsızlığını ve yaşam kalitesini arttırmada önemli rol oynamaktadır.-->
                                <!--                                </li>-->
                                <li class=" font-santralregular"><b> Masaj Aletleri- Kemeri</b><br>
                                    Hastalık sürecine doğrudan bir katkısı bulunmasa da dolaylı yoldan ağrılarınızı dindirmek için evinizde bulundurmanız gereken ürünlerdendir. Ağrılı günleri hafifleten masaj aletleri, kas ve kemik sağlığınız için de
                                    oldukça yararlıdır. Gerekli ilaçları kullandıktan sonra hızlı bir etki için aletlerden yararlanabilirsiniz.
                                </li>
                                <li class=" font-santralregular"><b> Nabız Ölçer</b><br>
                                    Kalp atış hızınızı takip etmek ve egzersiz performansınızı değerlendirmek için nabız ölçerlere şans vermekte fayda var. Hastalıklı günlerde kalp sağlığınızı izlemek ve stres seviyelerinizi kontrol etmek için
                                    oldukça yararlıdır. Kontrolsüz kalp ritmi sonrası ani durumlarda hızlı müdahale için gerekli ürünlerdendir. Çeşitli kullanıma uygunluğu ile spor faaliyetlerinde de kullanılabilir.
                                </li>

                            </ul>
                            Evinizde bulunması gereken sağlık ürünlerini kendi sağlık problemlerinize göre çeşitlendirebilirsiniz. Aynı zamanda ek vitaminler ile hastalık durumlarını azaltabilirsiniz.
                        </div>
                    </div>


                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-3xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Sağlık</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Yaşam</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Kontrol</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Düzen</div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <blog-side-menu></blog-side-menu>
                </div>
            </div>

        </section>
    </div>
</template>
