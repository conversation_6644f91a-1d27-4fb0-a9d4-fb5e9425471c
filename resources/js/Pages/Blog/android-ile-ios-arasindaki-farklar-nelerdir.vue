<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenu from "@/Pages/Shared/BlogSideMenu.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenu
    },
    layout: Layout,
    setup() {

    }
};
</script>

<template>

    <Head title="Android ile IOS Arasındaki Farklar Nelerdir?" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source srcset="../../../images/blog-webp/ios-android-1-800x500.webp" type="image/jpeg">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../../images/blog/ios-android-1-800x500.jpg" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Blog</div>
                                <div class="text-base lg:text-3xl font-bold mb-5 text-white mb-2">Android ile IOS Arasındaki Farklar Nelerdir?</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>18 Ocak 2024</span>
                                    </div>
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">
                                            <g id="view" transform="translate(0)">
                                                <path id="Path_108" data-name="Path 108"
                                                    d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z"
                                                    transform="translate(-1.126 -5.625)" fill="#e2e2e2" />
                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z"
                                                    transform="translate(-6.455 -8.586)" fill="#e2e2e2" />
                                            </g>
                                        </svg>
                                        <span>247</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h1 class="text-base lg:text-2xl font-bold lg:font-semibold pb-1">Android ile IOS Arasındaki Farklar Nelerdir?</h1>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            Telefon seçerken kullanıcıların en zorlandığı konu hangi işletim sistemine sahip olan cihazı tercih etmeliyim sorusudur. Birbirinden oldukça farklı özelliklere sahip Android ve IOS işletim sistemleri arasındaki en büyük ve
                            temel fark kişiselleştirme olanaklarıdır. Android işletim sistemi Google tarafından geliştirilen açık kaynaklı bir sistemdir. IOS işletim sistemi ise Apple tarafından geliştirilmiş kapalı bir sistemdir. Yüksek güvenlik
                            özellikleri ile öne çıkar.<br><br>
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Cihaz ve Donanım</h3>
                            Android ve IOS arasındaki temel farklardan biri de cihaz ve donanımdır. IOS işletim sistemi, Apple şirketi tarafından üretilen cihazlarda kullanılır. iPhone, iPad, iPod Touch gibi cihazlarda kullanılabildiği için sınırlı
                            bir cihaz çeşitliliği sunar. Bu cihazlara özel donanım tasarlayarak kullanıcılara yüksek performans ve kaliteli bir deneyim sunar. Android işletim sistemi ise açık kaynaklı bir sistem olduğu için birçok marka tarafından
                            kullanılır. Bu da kullanıcılara
                            geniş bir cihaz çeşitliliği sağlar. Donanım kısmında da farklı markalar tarafından kullanıldığı için cihazların kalitesi ve performansı değişkenlik gösterir.
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Güncelleme Süreci</h3>
                            IOS telefon güncellemeleri, Apple tarafından kullanıcılara sunulur. En son güncellemeleri alan IOS cihazlar ile kullanıcıların, yüksek güvenlik ve performans bakımından kaliteli bir deneyim yaşamasına olanak tanınır.
                            Android telefon güncellemeleri ise biraz yavaş olabilir. Çünkü güncellemeler Android telefon üreticilerine bağlı olarak farklılık gösterir.
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Uygulama Geliştirme</h3>
                            Uygulama geliştirmeleri, IOS telefonlarda Swift dilinde geliştirilir. Uygulamalar, Apple tarafından çok iyi bir şekilde denetlenir. Sıkı bir yönergeye tabi tutulur. Bu şekilde IOS uygulamaları daha yüksek kalitede de ve
                            güvenlikli hale getirilir. Android telefon uygulamalarında ise Java dili kullanılır. Android, açık kaynaklı sistem olduğu için daha fazla geliştiriciye sahiptir. Bu da uygulamaların daha çeşitli ve özelleştirilebilir
                            olmasına imkan tanır.
                            <picture>
                                <source srcset="../../../images/blog-webp/ios-android-2-800x500.webp" type="image/webp">
                                <img class="w-full my-4" src="../../../images/blog/ios-android-2-800x500.jpg" />
                            </picture>
                            <h3 class="text-lg font-bold lg:font-semibold pb-1">Android Avantajları Nelerdir?</h3>
                            Android, açık kaynaklı olduğu için markalara özelleştirme, kişiselleştirme ve uygulama geliştirme olanağı tanır. Ücretsiz bir sistem olduğundan bu sistemi kullanan cihazların maliyeti de daha hesaplı olur. Android telefon
                            fiyatları genellikle daha uygundur. Kullanıcı arayüzleri bakımından oldukça özgürdür. Uygulama geliştiricileri, kodlara kolay erişim sağlayarak değişiklikler yapabilir. Android telefonlardaki uygulama mağazası olan Play
                            Store, oldukça fazla uygulama
                            içerir.
                            <h3 class="text-lg font-bold lg:font-semibold pb-1">IOS Avantajları Nelerdir?</h3>
                            İOS telefonlar; güvenlik ve gizlilik açısından yüksek standarttadır. Güncellemelerini hızlı bir şekilde yayınlar. App Store, yüksek kaliteli ve güvenli uygulamalar içerir. Kullanıcılarına minimalist ve zarif tasarımları
                            ile üst düzey bir deneyim sunar. Basit ve tutarlı kullanıcı arayüzü ve deneyimi sağlar. Donanım ve yazılım entegrasyonu ile yüksek performans sergiler.
                            <h3 class="text-lg font-bold lg:font-semibold pb-1">IOS Ürünler Nelerdir?</h3>
                            Apple tarafından geliştirilen IOS, Apple cihazlar üzerinde kullanılır. Bu cihazlar; oldukça popüler akıllı telefon serisi iPhone, tablet bilgisayar serisi iPad, müzik dinleme ve video oynatmak için taşınabilir iPod Touch,
                            akıllı saat serisi Apple Watch, akıllı hoparlör HomePod ve kulaklık serisi AirPods gibi ürünlerdir.
                            <h3 class="text-lg font-bold lg:font-semibold pb-1">Android Ürünler Nelerdir?</h3>
                            Android işletim sistemini kullanan pek çok elektronik cihaz bulunur. Birçok marka tarafından kullanılan Android tabanlı akıllı telefonlar üretilir. Samsung, Xiaomi, Huawei gibi markalar Android sistemini kullanır. Android
                            tabanlı tablet bilgisayarlar, akıllı saatler, akıllı televizyonlar, oyun konsolları ve kameralar gibi geniş bir ürün çeşitliliği vardır.
                            <h3 class="text-lg font-bold lg:font-semibold pb-1">Kiralık Android ve IOS Ürünler En Uygun Fiyatlarla KiralaBunu'da!</h3>
                            Kullanıcılara oldukça fazla avantaj sağlayan Android ve İOS ürünlerini, kiralama seçenekleri ile deneyimleyebilirsiniz. Telefonunuzu değiştirmek ya da değişik bir model denemek istediğinizde kiralık telefon modellerinden
                            birini tercih edebilirsiniz. Cazip fiyatlarla aylık opsiyonlarla dilediğiniz süreyi seçebilirsiniz.
                            Birbirinden seçkin markanın ürünlerinin yer aldığı kategoriler içinden <a href="https://kiralabunu.com/kategoriler/telefon" target="_blank">telefon kiralama</a> yapmak için Kiralabunu adresini ziyaret edebilir, avantajlı
                            fiyatlardan yararlanabilirsiniz.
                        </div>
                    </div>


                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-3xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Teknoloji</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Android</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">İOS</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Aktif Yaşam</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Mobilite</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Akıllı Cihazlar</div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <blog-side-menu></blog-side-menu>
                </div>
            </div>

        </section>
    </div>
</template>
