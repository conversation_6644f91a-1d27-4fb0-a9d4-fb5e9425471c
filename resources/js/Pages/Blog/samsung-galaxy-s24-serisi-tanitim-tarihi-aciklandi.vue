<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenu from "@/Pages/Shared/BlogSideMenu.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenu
    },
    layout: Layout,
    setup() {

    }
};
</script>

<template>

    <Head title="Samsung Galaxy S24 Serisi Tanıtım Tarihi Açıklandı!" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source srcset="../../../images/blog-webp/samsung-1-800x500.webp" type="image/webp">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../../images/blog/samsung-1-800x500.png" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Blog</div>
                                <div class="text-base lg:text-2xl font-bold mb-5 text-white mb-2">Samsung Galaxy S24 Serisi Tanıtım Tarihi Açıklandı!</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>10 Ocak 2024</span>
                                    </div>
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">
                                            <g id="view" transform="translate(0)">
                                                <path id="Path_108" data-name="Path 108"
                                                    d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z"
                                                    transform="translate(-1.126 -5.625)" fill="#e2e2e2" />
                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z"
                                                    transform="translate(-6.455 -8.586)" fill="#e2e2e2" />
                                            </g>
                                        </svg>
                                        <span>95</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h1 class="text-base lg:text-2xl font-bold lg:font-semibold pb-1">Samsung Galaxy S24 Serisi Tanıtım Tarihi Açıklandı! </h1>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            Samsung’un merakla beklenen yeni teknolojisini sunacağı, Galaxy S24 serisinin tanıtımını gerçekleştireceği resmi tarih belli oldu. 2024 yılında Samsung’un yeni amiral gemisi olacak Galaxy S24 serisinin tüm özellikleri ve
                            yeni yapay zeka platformu olan Galaxy Al teknolojisi tanıtımı yapılacak. Güney Kore devi Samsung, yeni serisi Galaxy S24 modellerini Galaxy Unpacked adlı bir etkinlik düzenleyerek tüm dünyaya tanıtmayı planlıyor. Bu büyük
                            ve önemli etkinlikte
                            tanıtılacak olan modeller arasında Galaxy S24, Galaxy S24+, Galaxy S24 Ultra yer alacak. Bu yeni modellerin ekran, tasarım, pil ve sergiledikleri performans açıdan birbirinden oldukça farklı olması bekleniyor. Geleneksel
                            olarak her yılın başında tanıtım etkinliği düzenleyen Samsung, 2024 yılındaki yüz yüze gerçekleştireceği tanıtım etkinliğini Kaliforniya San Jose’de tertipleyecek.
                            <h3 class="text-base font-bold lg:font-semibold pb-1 mt-2">Galaxy Unpacked Etkinlik Tarihi Ne Zaman?</h3>
                            Sade bir açıklamayla duyurulan Galaxy Unpacked etkinliğinin tarihi, Samsung Avusturalya Twitter (X) hesabından resmi olarak açıklandı. Yapılan duyuruya göre Galaxy Unpacked etkinliği 17 Ocak 2024’te Türkiye saati ile
                            21:00’da başlayacak ve canlı olarak yayınlanacak. Etkinlik, Samsung Youtube kanalı aracılığı ile canlı olarak izlenebilecek. Samsung’un paylaştığı davetiye temasının odak noktası, ¨Galaxy Al is Coming¨ olarak belirlenmiş.
                            Galaxy Al yapay zeka
                            özelliklerine dikkat çekerek, kullanıcılara akıllı telefon deneyimini eşsiz bir noktaya ulaşmayı vaat ediyor. Bu gelişmiş teknoloji ile Samsung S24 serisi telefonlarla sesli konuşmalarda gerçek zamanlı dil çevirisi, duvar
                            kağıdı oluşturma, Samsung Notes’ta notları özetleme gibi pek çok yenilik yapılabilecek. Ayrıca Samsung Galaxy S24 ailesi kamera tarafında da yapay zeka teknolojisi ile fotoğraf ve video kalitesi de artış gösterecek.
                            <h3 class="text-base font-bold lg:font-semibold pb-1 mt-2">Galaxy Unpacked Etkinliğinde Hangi Ürünler Tanıtılacak?</h3>
                            Galaxy Unpacked etkinliğinde Galaxy S24, Galaxy S24+, Galaxy S24 Ultra modellerinin tanıtımı yapılacağı açıklandı. Bu modellerin ne gibi özelliklere ve değişikliklere sahip olduğu tanıtılacak. Samsung Galaxy S24 ailesinin
                            modellerinin belirtilen özellikleri ise şöyledir;
                            <h3 class="text-base font-bold lg:font-semibold pb-1 mt-2">Galaxy S24</h3>
                            6.2 inçlik FHD+ Dynamic AMOLED ekran HDR desteği mevcut. Kamera olarak 50 MP ana ekran, 12 MP ultra geniş açı, 10 MP telefoto lens 3x yakınlaştırmaya sahip olacak. Bellek 8 GB RAM. Depolama olarak da 128 GB ve 256 GB
                            seçenekleri bulunacak. 2600 nit ekran parlaklığı olacak. İşlemci pazara bağlı olarak Snapdragon 8 Gen 3 veya Exynos 2400 olacak. 25W hızlı şarj ve 4000 mAh bataryaya sahip olacak. Yuvarlatılmış köşe ve alüminyum çerçeveye
                            sahip olacak. Telefonun renk
                            seçenekleri gri, siyah, mor ve sarı renkte olacak. Avrupa fiyatlandırması belirlenen modellerin Türkiye’de nasıl olacağı henüz bilinmiyor. Samsung S24 fiyat olarak bakıldığında 256 GB versiyonu 128 GB’a göre biraz daha
                            yüksek olması bekleniyor.
                            <picture>
                                <source srcset="../../../images/blog-webp/samsung-2-800x500.webp" type="image/webp">
                                <img class="w-full my-4" src="../../../images/blog/samsung-2-800x500.jpeg" />
                            </picture>
                            <h3 class="text-base font-bold lg:font-semibold pb-1 mt-2">Galaxy S24+</h3>
                            6.7 inçlik FHD+ Dynamic AMOLED ekran HDR desteği mevcut. Kamera 50 MP ana ekran, 12 MP ultra geniş açı, 10 MP telefoto lens 3x zoom yapabilme özelliği ve 30x’e kadar dijital zoom yapabilme olanağı bulunacak. Bellek 12 GB
                            RAM. Depolama olarak da 256 GB ve 512 GB seçenekleri ile satışa sunulacak. 2600 nit ekran parlaklığı olacak. 45W hızlı şarj ve kablosuz şarj desteği ile 4900 mAh bir bataryaya sahip olacak. Galaxy S24’ün aynısı çerçeve ve
                            kasa tasarımına sahip olacak.
                            <h3 class="text-base font-bold lg:font-semibold pb-1 mt-2">Galaxy S24 Ultra</h3>
                            6.8 inçlik QHD+ Dynamic AMOLED ekran HDR desteği mevcut. 200 MP ana sensör, 12 MP ultra geniş açı, 10 Mp 3x zoom lens ve 50 Mp 5x optik yakınlaştırma yapabilen telefoto kamera ile geliştirilmiş olacak. Bellek 12 GB RAM.
                            256 GB ve 512 GB depolama seçenekleri bulunacak. 2600 nit ekran parlaklığı olacak. 45W hızlı şarj ve kablosuz şarj desteği ile 5000 mAh bir bataryaya sahip olacağı belirtiliyor. Samsun S24 serisinin en üst seviyesi olan
                            Galaxy S24 Ultra, köşeli kasa ve
                            titanyum çerçeveye sahip olacak. Bu özelliği ile daha dayanıklı ve sağlam bir hale gelecek. Samsung S24 Ultra fiyatı bir önceki modelinden biraz daha yüksek olması bekleniyor.
                            <br><br>
                            Samsung’un öne çıkan telefon, tablet, kulaklık, akıllı saat, televizyon, hoparlör, robot süpürge gibi teknolojik ürünlerini kiralama yaparak deneyimleyebilirsiniz. Satın almadan merak ettiğiniz ve ihtiyacınız olan ürünü
                            kiralama seçenekleri ile avantajlı bir şekilde kullanabilirsiniz. Bütçenize katkıda bulunacak cazip fiyat aralıkları ile dilediğiniz Samsung ürününü dilediğiniz kadar kiralayabilirsiniz.
                            <a href="https://kiralabunu.com/marka/samsung" target="_blank">Samsung ürünleri kiralamak</a> için Kiralabunu adresini ziyaret edebilirsiniz.
                        </div>
                    </div>


                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-2xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Telefon</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Mobilite</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Teknoloji</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Samsung</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Tanıtım</div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <blog-side-menu></blog-side-menu>
                </div>
            </div>

        </section>
    </div>
</template>
