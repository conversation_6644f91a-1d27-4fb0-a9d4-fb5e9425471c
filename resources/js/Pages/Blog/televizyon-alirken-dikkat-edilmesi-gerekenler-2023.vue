<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenu from "@/Pages/Shared/BlogSideMenu.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenu
    },
    layout: Layout,
    setup() {

    }
};
</script>

<template>

    <Head title="Televizyon Alırken Dikkat Edilmesi Gerekenler 2023" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source srcset="../../../images/blog-webp/televizyon-1440x840.webp" type="image/jpeg">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../../images/blog/televizyon-1440x840.jpg" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Blog</div>
                                <div class="text-base lg:text-3xl font-bold mb-5 text-white mb-2">Televizyon Alırken Dikkat Edilmesi Gerekenler 2023</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>01 Aralık 2023</span>
                                    </div>
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">
                                            <g id="view" transform="translate(0)">
                                                <path id="Path_108" data-name="Path 108"
                                                    d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z"
                                                    transform="translate(-1.126 -5.625)" fill="#e2e2e2" />
                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z"
                                                    transform="translate(-6.455 -8.586)" fill="#e2e2e2" />
                                            </g>
                                        </svg>
                                        <span>298</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h1 class="text-base lg:text-3xl font-bold lg:font-semibold pb-1">Televizyon Alırken Dikkat Edilmesi Gerekenler 2023 </h1>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            Günümüzde, televizyonlar her evde bulunan ve hemen herkes tarafından kullanılan teknolojik cihazlarından biri. Bu yüzden, kullanım amacımıza uygun doğru bir televizyon seçimi için dikkat etmemiz gereken birçok önemli
                            faktör bulunuyor. Ekran teknolojisi, boyut, çözünürlük, ses kalitesi, akıllı özellikler ve fiyat gibi faktörler, en iyi seçimi yapabilmeniz için göz önünde bulundurmanız gereken faktörlerden bazıları. İhtiyacınıza göre, bu
                            özellikleri önem sırasına göre
                            sıralayarak en doğru seçimi yapabilir ve tercihlerinizi o yönde daraltabilirsiniz.
                            <br><br>
                            Bu yazımızda, size televizyon alırken dikkat etmeniz gereken püf noktaları detaylı ca anlatacak ve doğru seçimi yapmanızı sağlayacak bilgileri paylaşacağız. İşte Televizyon Alırken Dikkat Edilmesi Gerekenler:
                            <br><br>
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Ekran Boyutu ve Çözünürlük</h3>
                            Televizyonların görüntü kalitesini belirleyen en temel özellik, ekran çözünürlüğüdür. Çözünürlüğü kısaca açıklamak gerekirse; televizyon ekranında her bir kareye düşen piksel sayısıdır. En basit ifadeyle; daha yüksek
                            çözünürlük, daha keskin ve detaylı görüntüler elde etmenizi sağlar. Televizyonlarda ise üç adet temel ekran çözünürlüğü türü vardır: HD (High Definition), Full HD (Full High Definition) ve 4K (Ultra High Definition).
                            <br><br>
                            <ul class="font-santralregular">
                                <li class=" font-santralregular"><b>HD (High Definition) </b><br>
                                    Günümüz televizyon teknolojisi standartlarına göre artık daha düşük bir çözünürlük olarak kabul edilse de HD televizyonlar temel görüntü kalitesini sunarlar.
                                </li>
                                <br>
                                <li class=" font-santralregular"><b>Full HD (Full High Definition) </b><br>
                                    Full HD çözünürlük, daha yüksek bir piksel yoğunluğu sunar ve daha keskin görüntüler elde etmenizi sağlar. 1080p olarak da ifade edilen bu çözünürlük türü, genellikle 40 inç ve üzeri ekran boyutları için önerilir.
                                </li>
                                <br>
                                <li class=" font-santralregular"><b>4K (Ultra High Definition) </b><br>
                                    4K çözünürlük, Full Hd televizyonlara göre dört kat daha fazla piksel içerir ve izlerken keskin ve detaylı görüntüler sunar. Burada belirtmekte fayda var ki; 4K teknolojisinin yaygınlaşmasıyla beraber, televizyon
                                    alırken 4K desteğine sahip bir model tercih etmek daha uzun süreli ve kaliteli bir kullanım açısından mantıklı bir tercih olacaktır.
                                </li>
                            </ul>
                            Ayrıca, aşağıda size ekran boyutu ve ideal izleme mesafesi ilişkisini anlatan bir tabloyu bulabilirsiniz. Elbette, kişisel tercihlerinize göre bu tablodan farklı bir seçim de yapabilirsiniz.
                            <picture>
                                <source srcset="../../../images/blog-webp/televizyon-2.webp" type="image/jpeg">
                                <img class="w-full my-4" src="../../../images/blog/televizyon-2.png" />
                            </picture>
                            <h3 class="text-base font-bold lg:font-semibold pb-1"> Ses Kalitesi </h3>
                            <picture>
                                <source srcset="../../../images/blog-webp/televizyon-4.webp" type="image/jpeg">
                                <img class="md:w-3/5 mx-auto my-4" src="../../../images/blog/televizyon-4.png" />
                            </picture>
                            İhtiyacınıza uygun bir televizyon seçerken ses kalitesine dikkat etmek oldukça önemli. Örneğin, televizyonun dahili hoparlörlerinin gücü ve sesin çıkışı, net ve yüksek kaliteli seslerin duyulmasını sağlar. Bu yüzden, yeni
                            bir televizyon alırken, ses kalitesi konusunda diğer kullanıcılardan olumlu yorumlar ve değerlendirmeler alan marka ve/veya modelleri tercih edebilirsiniz.
                            <br><br>
                            <h3 class="text-base font-bold lg:font-semibold pb-1"> Görüntü Teknolojileri </h3>
                            Televizyonlarda yer alan görüntü teknolojileri şu şekilde sıralanabilir:
                            <br>
                            <ul class="font-santralregular">
                                <li class=" font-santralregular"><b>LED: </b><br>
                                    LED ekranlar, bir dizi LED’in arka aydınlatma olarak kullanıldığı bir teknolojiye sahiptir. Bu ekranlar, yüksek parlaklık, canlı renkler ve iyi bir kontrast oranı sunar. LED televizyonlar genellikle enerji
                                    bakımından verimli olup daha uygun fiyatlıdırlar.
                                </li>
                                <li class=" font-santralregular"><b>OLED: </b><br>
                                    OLED ekranlar, organik bileşiklerden oluşan bir tabaka kullanarak her pikselin kendisini aydınlattığı bir teknolojiye sahiptir. Bu sayede, daha derin siyah seviyeleri, zengin renkler ve geniş bir görüş açısı elde
                                    edilir. OLED TV’ler, yüksek kontrast oranı ve ince tasarımıyla dikkat çeker.
                                </li>
                                <li class=" font-santralregular"><b>QLED: </b><br>
                                    QLED ekranlar, bir katman üzerindeki mikroskobik nano kristallerin ışığı filtrelemesi ve renkleri iyileştirmesi esasına dayanan bir teknolojidir. Böylece, daha geniş bir renk gamı, yüksek parlaklık ve kontrast
                                    sağlanır. QLED televizyon modelleri, canlı renkler ve ışık yansıması azaltma özelliğiyle tanınırlar.
                                </li>
                                <li class=" font-santralregular"><b>LCD: </b><br>
                                    LCD ekranlar, sıvı kristallerin ışığı filtreleyerek görüntüyü oluşturduğu bir teknolojidir. Bu ekranlar, düşük enerji tüketimi ve uygun fiyatlarıyla popülerdir.
                                </li>
                                <li class=" font-santralregular"><b>MicroLED: </b><br>
                                    MicroLED ekranlar, her pikselin kendisini aydınlattığı bir teknolojiye sahiptir. Bu ekranlar, OLED benzeri bir görüntü kalitesi sunarken daha uzun ömürlü ve daha parlak olabilir. MicroLED TV’ler, mükemmel kontrast,
                                    renk doğruluğu ve yüksek çözünürlük sunma potansiyeline sahiptir.
                                </li>
                            </ul>
                            <br>
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Enerji Verimliliği</h3>
                            Yeni bir televizyon seçimi yaparken enerji verimliliği konusunu göz önünde bulundurmak, çevresel etkiyi azaltmak ve enerji maliyetlerini düşürmek için dikkat edilmesi gereken bir faktördür. Bu bağlamda, eğer bir
                            televizyonun enerji verimliliği hakkında bilgi almak istiyorsanız, ürün etiketlerini ve enerji sınıflandırmasını inceleyebilirsiniz.
                            <br>
                            Minik bir ipucu: Enerji verimliliği konusunda sertifikalı ürünleri tercih ederek uzun vadede enerji maliyetlerinizi önemli ölçüde azaltabilirsiniz.
                            <br>
                            <picture>
                                <source srcset="../../../images/blog-webp/televizyon-3.webp" type="image/jpeg">
                                <img class="md:w-3/5 mx-auto my-4" src="../../../images/blog/televizyon-3.png" />
                            </picture>
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Bağlantı Seçenekleri</h3>
                            Bir televizyonun HDMI, USB ve diğer giriş/çıkış bağlantılarına sahip olması kullanım kolaylığı açısından önemlidir. Bu yüzden, yeni bir televizyon alırken, yeterli sayıda HDMI bağlantı noktasına sahip olduğundan ve USB
                            girişlerinin çeşitli cihazlarla (bilgisayar, tablet vb.) uyumlu çalıştığından emin olun. Bununla beraber; kablosuz bağlantı seçenekleri olan televizyonları tercih ederseniz, Wi-Fi, Ethernet ve Bluetooth gibi özelliklere de
                            erişebilir ve bu
                            teknolojilerin sunduğu imkanlardan faydalanabilirsiniz.
                            <br><br>
                            <h3 class="text-base font-bold lg:font-semibold pb-1">İhtiyacınıza Uygun Televizyon Modelleri Uygun Fiyatlarla Kiralabunu’da!</h3>
                            Yeni bir televizyon almak istiyor ama hangi modeli alacağınıza karar veremiyorsanız ya da binlerce lira para ödeyip satın almak istemiyorsanız <a class="font-bold underline"
                                href="https://kiralabunu.com/kategoriler/ev-eglence/televizyon" target="_blank">televizyon kiralama</a> seçeneğini tercih edebilirsiniz. Böylece satın almadan önce kiralayarak deneyebilir, kiralama sonunda memnun
                            kalırsanız satın alabilirsiniz.
                            <br><br>
                            <b>Seç, beğen, Kiralabunu!</b>
                        </div>
                    </div>


                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-3xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Spor</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Aktivite</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Sağlık</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Koşu</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Pilates</div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <blog-side-menu></blog-side-menu>
                </div>
            </div>

        </section>
    </div>
</template>
