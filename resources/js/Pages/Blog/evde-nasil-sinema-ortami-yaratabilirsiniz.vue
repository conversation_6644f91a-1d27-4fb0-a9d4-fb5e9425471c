<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenu from "@/Pages/Shared/BlogSideMenu.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenu
    },
    layout: Layout,
    setup() {

    }
};
</script>

<template>

    <Head title="Evde Nasıl Sinema Ortamı Yaratabilirsiniz ?" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source srcset="../../../images/blog-webp/evdesinemaortamiyaratmak2.webp" type="image/jpeg">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../../images/blog/evdesinemaortamiyaratmak2.png" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Blog</div>
                                <div class="text-base lg:text-3xl font-bold mb-5 text-white mb-2">Evde Nasıl Sinema Ortamı Yaratabilirsiniz ?</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>08 Ekim 2023</span>
                                    </div>
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">
                                            <g id="view" transform="translate(0)">
                                                <path id="Path_108" data-name="Path 108"
                                                    d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z"
                                                    transform="translate(-1.126 -5.625)" fill="#e2e2e2" />
                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z"
                                                    transform="translate(-6.455 -8.586)" fill="#e2e2e2" />
                                            </g>
                                        </svg>
                                        <span>548</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h1 class="text-base lg:text-3xl font-bold lg:font-semibold pb-1">Evde Nasıl Sinema Ortamı Yaratabilirsiniz ? </h1>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            Film-dizi izlemek herkes tarafından sevilen bir aktivitedir. Sizi farklı dünyalara götürür, duygusal bir deneyim yaşatır. Hayal gücünüzün en özgür olduğu anlardandır. Sevdiğiniz oyuncuları izlemek ve yeni hikayeler
                            keşfetmek son derece keyiflidir. Karakterlerin hikayelerini takip etmek, empati yeteneğinizi geliştirmek için oldukça faydalıdır. Ayrıca, farklı kültürleri, tarihleri ve perspektifleri keşfedebilirsiniz. İçerikler, stres
                            atmanızı sağlayarak eğlenmenize
                            yardımcı olmaktadır.
                            <br>
                            Evinizde, altı adımda sinema salonundaki heyecanla filmleri izlemek oldukça kolay. Bu basit adımlarla kendinizi filmlerin başkarakteri hissedeceksiniz.
                            <br><br>
                            <b>Evde Altı Adımda Sinema Keyfi</b>
                            <br>
                            <ul class="ml-1 md:ml-3 list-decimal font-santralregular">
                                <li class=" font-santralregular"> Öncelikle, rahat bir oturma düzeni oluşturarak kendinize bir sinema salonu hissi kazandırabilirsiniz.</li>
                                <li class=" font-santralregular"> Büyük bir TV veya projeksiyon kullanarak geniş bir ekran elde edebilirsiniz. Sinemaların en ilgi çekici hali yüksek çözünürlüklü geniş ekranlardır.</li>
                                <li class=" font-santralregular"> Ses kalitesini arttırmak için bir ses sistemi kullanabilirsiniz. Aksiyon/gerilim filmlerindeki her ses ayrıntısını duymanın, heyecanı ikiye katladığı yadsınamaz bir gerçektir.</li>
                                <li class=" font-santralregular"> Işıklandırmayı düşük tutarak loş bir ortam yaratabilirsiniz. Fazla ışık dikkatinizi dağıtabilir ve filme odaklanamayabilirsiniz.</li>
                                <li class=" font-santralregular"> Diğer teknolojik cihazlardan uzaklaşmanız da seyir keyfiniz için oldukça önemlidir. Filmin en heyecanlı yerinde gelen bildirimler bir hayli rahatsız edici olabilmektedir.</li>
                                <li class=" font-santralregular"> Son olarak önceden film seçimini yapmalı ve dolu bir atıştırmalık masası hazırlayarak sinema keyfini tamamlayabilirsiniz.</li>
                            </ul>
                            <br>
                            Birçok kişi televizyon ve projeksiyon arasında kararsız kalıyor ve en iyi seyir keyfinin hangisi olduğundan emin olamıyor. Televizyonlarda daha fazla içerik seçeneği olduğu düşünülse de projeksiyonlarda da artık her
                            içeriğe ulaşım sağlanabiliyor.
                            Hangisinin size daha uygun olduğuna karar vermeniz için televizyon ve projeksiyonu birlikte karşılaştıralım.
                            <br><br>
                            <b>Sinema Ortamı İçin Projeksiyon mu Televizyon mu?</b>
                            <br>Projeksiyon, daha büyük bir ekran sunabilir ve sinema salonu hissi yaratırken televizyonlar daha kompakt bir seçenektir. Bu durum televizyonun inç ve çözünürlük değerine göre de değişkenlik gösterebilir.
                            <br>Sizin için geniş bir görüntü mü daha önemli yoksa daha net görüntü mü olduğuna karar vermelisiniz. Projeksiyon genellikle daha geniş bir görüntü sunarken TV’ler daha kaliteli görüntüler sağlayabilir.
                            <br>Projeksiyonu bir duvara veya perdeye yansıtabilirsiniz. Böylelikle ekstra bir televizyon almanız da gerekmez. Aynı zamanda projeksiyonlar evinize estetik açıdan güzel dokunuşlar kazandırabilir.
                            <br>Televizyonlar geleneksel seyir keyfi olarak bilinse de hızlıca evirilerek akıllı bir hal almıştır. Telefonlarınızla uyumlayabilir her içeriğe anında ulaşabilirsiniz.
                            <br>Projeksiyonların avantajları gibi birtakım dezavantajları da bulunmaktadır. Örneğin, aydınlatması yüksek bir alanda kullanıldığında görüntü kalitesi düşebilir. Projeksiyon cihazının taşınması ve kurulumu biraz daha
                            zaman alabilmektedir.
                            <br>Evinizin dekoruna göre karar vermeli ve göz zevkiniz için küçük detayları es geçmemelisiniz. Tarzınızı tam olarak yansıtmanız daha keyifli anlar sağlayacaktır.
                            <br>Kararsız kaldığınızda sevdiklerinizden yardım alabilirsiniz. Kullanıcı yorumlarını okumak da kararınız için etkili olabilir. Marka ve model araştırması yapmalı, projeksiyon lamba ömrüne dikkat etmelisiniz.

                        </div>
                    </div>


                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-3xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Film</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Dizi</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Projeksiyon</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Televizyon</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Sinema</div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <blog-side-menu></blog-side-menu>
                </div>
            </div>

        </section>
    </div>
</template>
