<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import BlogSideMenu from "@/Pages/Shared/BlogSideMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenu
    },
    layout: Layout,
    setup() {

    }
};
</script>

<template>

    <Head title="Playstation 5 ile Oynanabilecek 5 Oyun" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source srcset="../../../images/blog-webp/ps5oyunlar2.webp" type="image/jpeg">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../../images/blog/ps5oyunlar2.png" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Blog</div>
                                <div class="text-base lg:text-3xl font-bold mb-5 text-white mb-2">Playstation 5 ile Oynanabilecek 5 Oyun</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>08 Ekim 2023</span>
                                    </div>
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">
                                            <g id="view" transform="translate(0)">
                                                <path id="Path_108" data-name="Path 108"
                                                    d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z"
                                                    transform="translate(-1.126 -5.625)" fill="#e2e2e2" />
                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z"
                                                    transform="translate(-6.455 -8.586)" fill="#e2e2e2" />
                                            </g>
                                        </svg>
                                        <span>548</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h1 class="text-base lg:text-3xl font-bold lg:font-semibold pb-1">Playstation 5 ile Oynanabilecek 5 Oyun </h1>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            Atarilerle hayatımıza giren oyun konsolları, günümüzde yaş sınırı tanımadan kişiler tarafından ilgi görmeye devam ediyor. Birbirinden farklı modeller piyasa sürülse de üstün oyun kalitesiyle Sony açık ara en iyisi
                            denilebilir. Oyunların aranan karakteri olmak için yeni nesil PlayStation 5 tam size göre. <br><br>
                            PS 5, son teknolojiye sahip bir oyun konsoludur. Yüksek performansı ve gelişmiş grafikleriyle oyun kalitesini ikiye katlamaktadır. Gerçekçi ses ve Haptic Feedback gibi özellikleri ile ütopik bir oyun dünyası yaratır.
                            Benzersiz bir oyun deneyimi için oyun konsoluna sanal gerçeklik gözlüğünü de dahil edebilirsiniz <br> <br>
                            Hızlı işlemci ve yüksek performansla sizi yarı yolda bırakmaz. Geriye dönük uyumluluk ile PS 4 oyunlarını oynama fırsatı sunması, oyun çeşitliliğini bir hayli arttırmaktadır. Gelişmiş grafikler ve 4K çözünürlük ile
                            etkileyici hale getirmektedir. Ayrıca, Ray Tracing teknolojisi ile gerçekten farksız. Hızlı yükleme süreleri ve akıcı oyunlarla kasma-donma sorununu en aza indirmektedir. Geniş depolama alanı ve gelişmiş denetleyici
                            özellikleriyle her oyunu
                            indirebilirsiniz. Görüntü kalitesi kadar 3D ses teknolojisiyle de ön plana çıkmayı ihmal etmiyor. Çevrim içi çok oyunculu oyunlar oynayabilir, Playstation Plus üyeliği ile her şeye ücretsiz erişebilirsiniz. <br><br>
                            Kullanıcılar tarafından tam not almayı başaran birbirinden farklı 5 oyunu birlikte inceleyelim. <br><br>
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Potansiyelini ve Gücünü Gösteren 5 Oyun </h3>
                            <br>
                            <ul class="ml-1 md:ml-3 list-decimal font-santralregular">
                                <li class=" font-santralregular"><b> God of War: Ragnarok</b><br>
                                    Aksiyon ve macerayı iliklerinize kadar hissedeceksiniz! Kratos’un mitolojik dünyada epik bir maceraya atılması ile yaratıklarla kıyasıya savaş mücadelesi vermelisin. Bulmacaları hızlıca çözmeli ve tanrıların
                                    isteklerini yerine getirmelisin.
                                </li>
                                <li class=" font-santralregular"><b> Resident Evil Village</b><br>
                                    Resident Evil serisinin bir parçası olması akıllarda yer edinen oyunlar arasındadır. Hayatta kalmak için cesurca keşfe çıkmalısın. Korkunç yaratıklarla dolu bir gizemli köyde korkuya yer yok.
                                </li>
                                <li class=" font-santralregular"><b> Marvel’s Spiderman: Miles Morales</b><br>
                                    Marvel tutkunuysanız isminden de anlayacağınız gibi tam size göre. Marvel evreninde geçen bir aksiyon-macera oyunudur. Morales’ın özel güçlerini kullanarak New York City’de suçla savaşmasını konu almaktadır.
                                </li>
                                <li class=" font-santralregular"><b> F1 2023-2022</b><br>
                                    Formula1, dünya genelinde popüler olan bir motor sporları yarış serisidir. Yüksek performanslı araçlarla, farklı ülkelerdeki pistlerde yarışabilir bitiş çizgisine ilk gelen sen olabilirsin! Hız, teknik, beceri ve
                                    strateji gerektiren bir spor oyunu olmasıyla da eğlendirirken gelişim sağlamaktadır. Yarışlar genellikle büyük bir heyecanla takip edilir ve birçok ünlü pilotun yer aldığı bir platformdur.
                                </li>
                                <li class=" font-santralregular"><b> Ratchet & Clank: Rift Apart</b><br>
                                    Çocukların büyük bir keyifle oynadığı bu oyun, macera ve aksiyon üzerine kuruludur. Her yaş için uygun olduğunu da belirtelim. Ratchet ve robot arkadaşı Clank, farklı boyutlarda geçen bir maceraya atılmasıyla
                                    evreni tehdit eden kötü karakterlerle karşı karşıyadır. Onlara yardım etmeli ve boyutlar arasında geçiş yaparak bulmacaları çözmelisin. Oyunun renkli grafikleri ve hızlı temposu, el-kol kondisyonuna da doğrudan
                                    etki sağlamaktadır.
                                </li>
                            </ul>
                        </div>
                    </div>


                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-3xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Playstation</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Oyun</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Oyun Konsolu</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Eğlence</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Teknoloji</div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <blog-side-menu></blog-side-menu>
                </div>
            </div>

        </section>
    </div>
</template>
