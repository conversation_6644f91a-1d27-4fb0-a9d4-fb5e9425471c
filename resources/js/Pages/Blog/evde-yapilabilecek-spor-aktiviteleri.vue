<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenu from "@/Pages/Shared/BlogSideMenu.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenu
    },
    layout: Layout,
    setup() {

    }
};
</script>

<template>

    <Head title="Evde Yapılabilecek Spor Aktiviteleri" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source srcset="../../../images/blog-webp/evdespor3.webp" type="image/jpeg">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../../images/blog/evdespor3.png" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Blog</div>
                                <div class="text-base lg:text-3xl font-bold mb-5 text-white mb-2">Evde Yapılabilecek Spor Aktiviteleri</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>08 Ekim 2023</span>
                                    </div>
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">
                                            <g id="view" transform="translate(0)">
                                                <path id="Path_108" data-name="Path 108"
                                                    d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z"
                                                    transform="translate(-1.126 -5.625)" fill="#e2e2e2" />
                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z"
                                                    transform="translate(-6.455 -8.586)" fill="#e2e2e2" />
                                            </g>
                                        </svg>
                                        <span>548</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h1 class="text-base lg:text-3xl font-bold lg:font-semibold pb-1">Evde Yapılabilecek Spor Aktiviteleri </h1>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            Sportif vücut sağlığı, gün içerisinde stresi azaltan ve mutluluğa giden yolun en kolay halidir. Sadece mutlu etmesiyle de sınırlı değildir. Sosyalleşmenizi, düzene girmenizi ve zinde kalmanızı sağlaması gibi pozitif etkisi
                            bulunmaktadır. Günlük sporunuzu ister evde ister spor salonlarında yapabilirsiniz. Tamamen özgürsünüz!
                            <br>
                            Kilo vermek için spor kadar düzenli beslenmek de çok önemli bir yere sahiptir. Kalori ihtiyacınızı bol lifli ve proteinli yiyecek-içeceklerden karşılamalısınız.
                            <br>
                            Evinizde yapabileceğiniz ev egzersizlerini birlikte inceleyelim.
                            <br><br>
                            <h3 class="text-base font-bold lg:font-semibold pb-1">30 Gün İçinde İstediğiniz Bedene Kavuşturan 5 Ev Egzersizi </h3>
                            <br>
                            <ul class="ml-1 md:ml-3 list-decimal font-santralregular">
                                <li class=" font-santralregular">
                                    <b>Sıcak Soğuk Demeden Her Mevsimde Koşu </b><br>
                                    Koşu bantları insanların spor ihtiyaçlarını karşılamak için kapalı mekanlarda tercih edilebilecek en iyi şey denilebilir. Kötü hava koşullarında dışarıda yürüyüş yapmak imkânsız bir hale gelebilir. Yaz-kış demeden
                                    evinizde dilediğiniz bedene sahip olabilirsiniz. Koşu bandını ne kadar süre kullanacağınız veya hangi tempoda koşacağınızı da kendinize göre planlayabilirsiniz. 1 ay kadar kısa bir sürede inceldiğinizi fark
                                    edeceksiniz.
                                </li>
                                <li class=" font-santralregular"><b>Kuvvetli Kaslar Bisikletlerle Başlar!</b> <br>
                                    Sıkı bacakların gizli hamlesi olan bisiklet sürmek sandığınızdan daha fazla işe yarıyor desek yanılmayız. Sevdiklerinizle bisiklet turları düzenleyebilir, eğlenirken spor yapabilirsiniz. El-ayak bisikleti, adı
                                    üstünde olduğu gibi el ve ayak hareketleriyle pedallanan bir egzersiz aletidir. Koordinasyonu geliştirir ve kas gücünü arttırır. Düzenli bisiklet kullanımı kalp rahatsızlığı riskini de büyük ölçüde azaltacaktır.
                                    Dış mekanlarda bisiklet sürücüsüyseniz
                                    temel koruma ekipmanlarını kullanmayı da ihmal etmemelisiniz.
                                </li>
                                <li class=" font-santralregular"><b>Esnek Vücutlar İçin Pilates</b><br>
                                    Hem esnek ve dengeli vücutlar hem de bütçe dostu. Evinizde pilates yapmak için sadece pilates matı yeterli olacaktır. Evinizde düzenli bir şekilde yapacağınız pilates, sportif bir vücutla birlikte sırt ve boyun
                                    ağrıları için birebirdir. Zayıf postürü azaltır, doğru bir şekilde yürümenizi sağlar. Daha çok kadınları yaptığı bir spor olarak görülse de sporun cinsiyeti olmaz! Her gün yapacak olmanız istediğiniz bedene bir
                                    adım daha yaklaştıracaktır.
                                </li>
                                <li class=" font-santralregular"><b>Demir Gibi Kollar İçin Tek Gerekli Şey Dambıllar </b><br>
                                    İster 3 Kg ister 30 Kg. Dambılların ağırlığını kendinize göre çeşitlendirebilirsiniz. Ağırlık kaldırmak için en düşük seviyeden başlamak sakatlanmamanız için dikkat etmeniz gereken unsurlardandır. Adım adım gitmek
                                    vücut gelişiminiz için daha etkili olacaktır. Kemik sağlığını geliştirir, güçlü kaslar kazandırır. Kaslı bir vücut için yeterli protein ihtiyacını da ihmal etmemelisiniz.
                                </li>
                                <li class=" font-santralregular"><b>İp Atlayarak Bulut Kadar Hafiflik</b><br>
                                    İp atlamak evde çok mümkün gibi görülmeyebilir. Uygun saatlerde, hafif adımlarla 30 dakika ip atlamanız diğer kişileri rahatsız etmeyecektir. Kardiyo sağlığını hem geliştirir hem de dengeler. Vücut eklem
                                    dayanıklılığını da kısa bir sürede arttıracaktır. Tam vücut sporu için idealdir ve hızlıca kalori yakımını sağlamaktadır. Mutluluğun sırrını iplerde bulabilirsiniz. Endorfin salgılayarak gün içerisindeki
                                    mutluluğunuzu ikiye katlayacaktır.
                                </li>
                            </ul>
                        </div>
                    </div>


                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-3xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Spor</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Aktivite</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Sağlık</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Koşu</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Pilates</div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <blog-side-menu></blog-side-menu>
                </div>
            </div>

        </section>
    </div>
</template>
