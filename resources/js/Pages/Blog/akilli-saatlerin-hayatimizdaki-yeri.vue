<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenu from "@/Pages/Shared/BlogSideMenu.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenu
    },
    layout: Layout,
    setup() {

    }
};
</script>

<template>

    <Head title="Akıllı Saatlerin Hayatımızdaki Yeri " />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source srcset="../../../images/blog-webp/akillisaatlerinhayatimizdakietkisi.webp" type="image/jpeg">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../../images/blog/akillisaatlerinhayatimizdakietkisi.png" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Blog</div>
                                <div class="text-base lg:text-3xl font-bold mb-5 text-white mb-2">Akıllı Saatlerin Hayatımızdaki Yeri</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>08 Ekim 2023</span>
                                    </div>
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">
                                            <g id="view" transform="translate(0)">
                                                <path id="Path_108" data-name="Path 108"
                                                    d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z"
                                                    transform="translate(-1.126 -5.625)" fill="#e2e2e2" />
                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z"
                                                    transform="translate(-6.455 -8.586)" fill="#e2e2e2" />
                                            </g>
                                        </svg>
                                        <span>548</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h1 class="text-base lg:text-3xl font-bold lg:font-semibold pb-1">Akıllı Saatlerin Hayatımızdaki Yeri </h1>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            Teknolojinin giyilebilir hali! Watchlar, hayatımıza konfor katan her an yanımızda taşıdığımız teknolojik cihazdır. Yani bir nevi kol arkadaşı da denilebilir. Kendi isteğinize göre kişiselleştirebilir ve gün içerisindeki
                            yapacağınız aktiviteleri kolay bir şekilde gerçekleştirebilirsiniz. Akıllı saatler, çağrı ve mesaj bildirimi, hava durumu ve navigasyon bilgisi gibi temel özelliklerinin yanı sıra bazı modellerde detaylı analiz edici
                            özelliklere sahiptir. Saatlerin
                            hayatımızdaki yerini anlamak 10 temel özelliğini detaylı bir şekilde birlikte inceleyelim. <br><br>
                            <b>Akıllı Saatlerde Hayat Kolaylaştırıcı 10 Temel Özellik</b>
                            <br>
                            <ul class="ml-1 md:ml-3 list-decimal font-santralregular">
                                <li class=" font-santralregular"> Müzik ruhun gıdasıdır diyorsanız akıllı saatler ile oldukça mutlu olacaksınız. Akıllı saatlerle istediğiniz her yerde müzik dinleyebilirsiniz.</li>
                                <li class=" font-santralregular"> Sesli asistan kullanımıyla gerekli olan her bilgiyi öğrenebilir, arama yapabilir ve cevaplayabilirsiniz. Kısacası akıllı telefonlarla zaman kaybetmek yerine dilediğiniz her şeye sesli
                                    asistan yardımıyla ulaşabilirsiniz.</li>
                                <li class=" font-santralregular"> Adımsayar özelliği tam anlamıyla sağlıklı yaşam dostudur. Programladığınız adımsayar uygulaması ile gün içerisinde hedeflediğiniz adım sayısını ve kalori yakımını analiz eder. Akıllı
                                    saatlerde göreceğiniz hedefe ulaşıldı yazısı tam anlamıyla tatmin edici olacaktır!</li>
                                <li class=" font-santralregular"> Uyku düzeni sağlıklı bir beden için olmazsa olmazlardandır. Akıllı saatlerde bulunan uyku takibini uyku saatlerinize göre ayarlayabilir güne pozitif bir başlangıç yapabilirsiniz.</li>
                                <li class=" font-santralregular"> Akıllı saatler ile alarm kurabilirsiniz. Sessiz modda kalan telefonlar ile sabah uyanamama derdini ortadan kaldırabilirsiniz. Aynı zamanda uyku esnasında telefonlarınızı yataktan
                                    uzaklaştırmak yüksek radyasyon zararlarını da engelleyecektir.</li>
                                <li class=" font-santralregular"> Spor ve fitness takibiyle spor koçuna da ihtiyacınız artık yok! Birbirinden farklı spor türüne uygun olması ile dilediğiniz her sporu yapabilirsiniz. Spor süresince detaylı analizler
                                    yaparak verim değerlerini kolayca öğrenebilirsiniz.</li>
                                <li class=" font-santralregular"> Kalp atış hızı takibiyle riskli durumları da en aza indirmektedir. Akıllı saatler kalp atış hızını yürüyüş, uyku ve spor esnasındaki kalp ritim durumuna göre analiz etmektedir. Riskli
                                    bir durum olduğunda size bildirim göndererek sağlık durumunuzu öğrenmektedir. Kalp krizi gibi tehlikeli sağlık durumlarını önceden öğrenmeniz hayatınızı kurtarabilir.</li>
                                <li class=" font-santralregular"> Çağrı-mesaj bildirimleri ile meşgul anlarınızda veya telefonunuz uzaktayken de gelen bildirimlerden haberdar olabilirsiniz.</li>
                                <li class=" font-santralregular"> Akıllı saatler birçok uygulamayı desteklemektedir. Uyumlu olan sosyal medyaları kullanabilirsiniz, maillerinizi kontrol edebilir ve yanıtlayabilirsiniz. Hatta banka hesaplarınızı bile
                                    kontrol edebilir temassız ödeme yapabilirsiniz.</li>
                                <li class=" font-santralregular"> Watchlar, yardımcı küçük asistanlardır. Yoğun süreçli bir işte hızlıca not alabilir, gün içerisindeki yapılması gerekenleri öğrenebilirsiniz. Anımsatıcılar, sizin için özel günleri
                                    unutmamanıza yardımcı olmaktadır.</li>
                            </ul>
                            <br>
                            Özellikler bunlarla da sınırlı olmadığı gibi tercih edeceğiniz marka ve modele göre değişiklik göstermektedir. Akıllı saatler, kişisel tarzını yansıtabilme imkânı sunmaktadır. Farklı renk ve tasarımlar ile tarzınızı
                            yansıtabilir, stilinize uygun bir aksesuar olarak kullanabilirsiniz.
                            <br><br>
                            Hangi saati almalıyım sorusunu kendinize yöneltiyorsanız öncelikle akıllı telefonunuzla uyumlu olan marka ve modellere göz gezdirmelisiniz. Telefonunuzla uyumlu olmayan modeller uygun olmayacaktır. Watchlar arasındaki
                            farklar, özelliklerine ve fiyatlarına göre şekillenmektedir. Bazı akıllı saatler EKG ve şeker ölçümü gibi daha fazla özellik sunmasıyla daha yüksek fiyata sahip olabilir. Belirli bir işletim sistemiyle çalışmaktadırlar.
                            Apple Watchlar IOS işletim
                            sistemine sahipken Samsung Watchlar, Android işletim sistemine sahiptir. Samsung akıllı saatler her telefona uyumlu olmasıyla tercih edilen modeller arasında örnek gösterilebilir. Sularla aranız iyiyse su geçirmez
                            özellikleri veya daha dayanıklı malzemelerle üretilmiş olduğuna dikkat etmelisiniz. Akıllı saatlerin özellikleri, kullanım amacınızı bilirseniz sizin için daha yararlı olacaktır.
                            <br>Son olarak, hayatınıza hem konfor hem de tarz katan teknolojik giyilebilir cihazlar ile kendinizi her zaman daha mutlu hissedeceksiniz.

                        </div>
                    </div>


                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-3xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Akıllı Saat</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Asistan</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Sağlık</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Uyku Kontrolü</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Spor</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Fitness</div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <blog-side-menu></blog-side-menu>
                </div>
            </div>

        </section>
    </div>
</template>
