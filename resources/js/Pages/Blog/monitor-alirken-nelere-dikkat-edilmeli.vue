<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenu from "@/Pages/Shared/BlogSideMenu.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenu
    },
    layout: Layout,
    setup() {

    }
};
</script>

<template>

    <Head title="Monitör Alırken Nelere Dikkat Edilmeli?" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source srcset="../../../images/blog-webp/monitor-1-800x500.webp" type="image/jpeg">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../../images/blog/monitor-1-800x500.jpg" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Blog</div>
                                <div class="text-base lg:text-3xl font-bold mb-5 text-white mb-2">Monitör Alırken Nelere Dikkat Edilmeli?</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>18 Ocak 2024</span>
                                    </div>
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">
                                            <g id="view" transform="translate(0)">
                                                <path id="Path_108" data-name="Path 108"
                                                    d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z"
                                                    transform="translate(-1.126 -5.625)" fill="#e2e2e2" />
                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z"
                                                    transform="translate(-6.455 -8.586)" fill="#e2e2e2" />
                                            </g>
                                        </svg>
                                        <span>247</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h1 class="text-base lg:text-2xl font-bold lg:font-semibold pb-1">Monitör Alırken Nelere Dikkat Edilmeli?</h1>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            Günümüzün önemli bir kısmını bilgisayar başında geçiririz. Dolayısıyla uzun saatler boyunca baktığımız ekranın kalitesi de çalışma koşullarını etkileyen faktörden biridir. Tasarım açısından farklı özellikleri bulunan
                            monitör modellerinin; ekran çözünürlükleri, ekran genişlikleri, panel türleri gibi çeşitli özellikler, kullanım deneyimini etkileyen faktörlerdir. İhtiyaçlarınızı karşılayacak monitör alırken dikkat edilmesi gereken bazı
                            noktalara değineceğiz.
                            <h3 class="text-lg font-bold lg:font-semibold pb-1">Monitörün Ana Kullanım Amacı</h3>
                            Monitör almadan önce kullanım amacınıza ve sıklığınıza göre karar vermek doğru seçim yapmanıza yardımcı olabilir. Tasarım yapma ve oyun oynama gibi işlemler için üst düzey ekran çözünürlüğü ve geniş ekranlar ideal
                            monitörler olabilir. Belirli saatlerde basit işler için bilgisayar kullanıyorsanız temel özellikleri bulunan monitör modellerini tercih edebilirsiniz.
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Monitör Çözünürlüğü</h3>
                            Yüksek çözünürlüğe sahip bir monitörle daha net ve keskin bir görüntü elde edilir. Standart olarak kabul gören Full HD (1920 x 1080p) çözünürlük, bilgisayarı basit işlemler için kullanan kullanıcılar için idealdir. 2K
                            monitörler (2560 x 1440p) ise Full HD’den daha iyi çözünürlük sunar. Ancak yaratıcı tasarımlar yapmak, oyun oynamak gibi aktiviteler için kullanacak kullanıcılar için yüksek çözünürlük sağlayan QHD, 4K monitör gibi
                            seçenekler daha detaylı ve net görüntü
                            sağlar.
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Monitör Boyutları</h3>
                            Bilgisayarınızda günlük ofis programları kullanma, sunum hazırlama, video izleme gibi işleri yapıyorsanız çoğunlukla monitörü konumlandırmak istediğiniz yere ve kişisel beğenilerinize göre istediğiniz boyutlarda monitör
                            tercih edebilirsiniz. Yazılım yapma, kod yazma ya da içerik üretmek için masaüstü bilgisayarlarda monitör olarak genellikle 22 inç ile 27 inç arasındaki modelleri tercih edebilirsiniz.
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Yenileme Hızları</h3>
                            Monitör seçiminde belirleyici özelliklerden birisi de ekran yenileme hızıdır. Oyun oynama veya video izleme gibi aksiyon gerektiren içerikler için yüksek yenileme hızına sahip monitörler gerekir. Çoğunlukla 60 Hz bazında
                            üretilen monitörler temel ihtiyaçları karşılar. Ancak daha kusursuz bir oyun deneyimi yaşamak isteyen oyuncular için 144 Hz ve üzeri LED monitör modelleri ideal seçenekleri oluşturur.
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Monitör Tepki Süreleri</h3>
                            Oyun oynama ve video oynatma gibi hareketli sahnelerin fazla olduğu içeriklerin gecikme yaşanmadan, donmadan ekran belirmesi için düşük tepki süreleri tercih edilir. Bu gibi sorunların yaşanmaması için 5 ms ve altındaki
                            tepki süreli monitör modellerinin tercih edinilmesi yerinde olacaktır.
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Panel Teknolojisi</h3>
                            Monitörlerin 3 ana panel çeşidi vardır. Bunlardan ilki uygun fiyatlı olan, yandan bakıldığında görüntü açısı düşük olsa da tepki süreleri iyi olan TN panellerdir. İkincisi IPS paneller, doğru renk üretimine ve geniş görüş
                            açılarına sahiptir. Üçüncü olan VA paneller ise en iyi kontrastı sağlayan panel seçeneğidir.
                            <h3 class="text-base font-bold lg:font-semibold pb-1">Kavisli Monitörler</h3>
                            Kavisli monitörler, ekranın bir bölümünün hafifçe kavisli olması şeklinde tanımlanır. Kavisli monitörler çoğunlukla büyük ekranlara sahiptir. Kullanıcıların, monitöre daha yakın olarak oturduğunda daha çok görüş açısı
                            sağlamak ve daha sürükleyici deneyim elde etmek amacıyla tercih edilir. Daha çok oyun ve film gibi içerikler için bu tür monitör modelleri kullanılır.
                            <picture>
                                <source srcset="../../../images/blog-webp/monitor-2-800x500.webp" type="image/webp">
                                <img class="w-full my-4" src="../../../images/blog/monitor-2-800x500.jpg" />
                            </picture>
                            <h3 class="text-lg font-bold lg:font-semibold pb-1">Monitör Panel Türleri Nelerdir?</h3>
                            Bilgisayar monitörlerinde kullanılan farklı teknolojilere göre panel türleri vardır. Bunlar; TN, VA ve IPS olmak üzere üç ana panel türleridir. Bu panel türleri, farklı özelliklere ve avantajlara sahiptir.
                            <h3 class="text-base font-bold lg:font-semibold pb-1">TN Ekran Monitörler</h3>
                            TN (Twisted Nematic) panel türleri düşük tepki sürelerine sahiptir. Bu özelliğiyle hızlı hareket eden görüntülerde daha az ghosting etkisi yaşanır. TN panelli monitör fiyatları oldukça uygundur. Görüntü kalitesi ve renkler
                            IPS ve VA paneller kadar iyi değildir. Görüş açıları daha kısıtlıdır.
                            <h3 class="text-base font-bold lg:font-semibold pb-1">VA Ekran Monitörler</h3>
                            VA (Vertical Alignment) panel türleri, yüksek kontrast oranları sunar. Böylece daha derin siyahlar ve daha parlak beyazlar sunar. Daha doğru renkler sunar. Tepki süreleri TN panellere göre daha yavaştır. Görüş açıları daha
                            kısıtlıdır. VA panele sahip monitör fiyatları, TN panellere göre biraz daha yüksektir.
                            <h3 class="text-base font-bold lg:font-semibold pb-1">IPS Ekran Monitörler</h3>
                            IPS (In Plane Switching) panel türleri, ekrana farklı açılardan bakıldığında bile renk kaybını önleyen geniş görüş açısı sağlar. Yüksek renk doğruluğu sunar. Yüksek tepki süreleri nedeniyle ghosting etkisi sorunları
                            yaşanabilir. IPS panele sahip monitör fiyatları, diğer panel türlerine göre biraz daha maliyetlidir.
                            <br> <br>
                            İhtiyaçlarınıza cevap veren ve kullanım alanına uyan bir monitör modelini kiralama seçenekleri ile deneyimleyebilirsiniz.
                            Kiralık monitör seçenekleri, maliyet açısından oldukça avantajlıdır.
                            Uygun monitör fiyatları ile aylık seçeneklerle dilediğiniz kadar kullanabilirsiniz.
                            <a href="https://kiralabunu.com/kategoriler/laptoplar/monitor" target="_blank"> Monitör kiralama </a>yapmak için Kiralabunu adresini ziyaret edebilirsiniz.


                        </div>
                    </div>


                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-3xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Teknoloji</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Temizlik</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Robot Süpürge</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">IOT</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Süpürge</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Akıllı Cihazlar</div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <blog-side-menu></blog-side-menu>
                </div>
            </div>

        </section>
    </div>
</template>
