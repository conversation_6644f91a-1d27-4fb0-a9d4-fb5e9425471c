<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenu from "@/Pages/Shared/BlogSideMenu.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenu
    },
    layout: Layout,
    setup() {

    }
};
</script>

<template>

    <Head title="Oyun Bilgisayarı Alırken Nelere Dikkat Etmelisiniz?" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source srcset="../../../images/blog-webp/oyun-bilgisayari-1-800x500.webp" type="image/webp">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../../images/blog/oyun-bilgisayari-1-800x500.jpg" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Blog</div>
                                <div class="text-base lg:text-3xl font-bold mb-5 text-white mb-2">Oyun Bilgisayarı Alırken Nelere Dikkat Etmelisiniz?</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>03 Ocak 2024</span>
                                    </div>
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">
                                            <g id="view" transform="translate(0)">
                                                <path id="Path_108" data-name="Path 108"
                                                    d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z"
                                                    transform="translate(-1.126 -5.625)" fill="#e2e2e2" />
                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z"
                                                    transform="translate(-6.455 -8.586)" fill="#e2e2e2" />
                                            </g>
                                        </svg>
                                        <span>112</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h1 class="text-base lg:text-3xl font-bold lg:font-semibold pb-1">Oyun Bilgisayarı Alırken Nelere Dikkat Etmelisiniz?</h1>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            Teknolojisinin gelişmesiyle beraber oyun tutkunları için de özel bilgisayarlar tasarlanmaya başlanmıştır. Keyifli bir oyun deneyimini yaşamak için yüksek performans sergileyen oyun bilgisayar modelleri yoğun ilgi görür.
                            Keyifli oyunlar oynamak için oyun bilgisayar modeli alırken dikkat edilmesi gereken bazı önemli faktörler vardır. Bu faktörleri şöyle sıralayabiliriz;
                        </div>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            <ul class=" list-none font-santralregular mt-2">
                                <li class="font-santralregular mb-2"><b>Ekran Kartı </b><br>
                                    Bir oyun bilgisayarı, normal bir bilgisayardan çok daha gelişmiş özellikler sahiptir. Ekran kartı ne kadar hızlı ve yüksek çözünürlüğe sahip olursa oyun deneyimi de o kadar keyifli olur.
                                </li>
                                <li class="font-santralregular mb-2"><b>İşlemci </b><br>
                                    Bilgisayar oyunları genelde işlemciyi aktif bir şekilde kullanır. Oyunların hızlı oynanması için önemli bir kriterdir. Intel i7 ya da AMD Ryzen 5 serisi oldukça tercih edilir.
                                </li>
                                <li class="font-santralregular mb-2"><b> Ram (Bellek) </b><br>
                                    Oyun bilgisayarı ya da gaming laptop modelleri için en az 16 GB RAM tavsiye edilir. Yüksek hızlı bir RAM oyun için önemli etkenlerden biridir. Daha iyi bir performans için 16 GB veya daha yüksek RAM tercih
                                    edilmelidir.
                                </li>
                                <li class="font-santralregular mb-2"><b> Depolama </b><br>
                                    Sistem ve oyun yüklenirken hızlı olmasını sağlayan ve donanım hızını etkileyen bir bileşendir. Yüksek bir oyun performansı için SSD oldukça kullanışlıdır. Daha büyük bir depolama alanına sahip olmak için ek bir HDD
                                    tercih edebilirsiniz.
                                </li>
                                <li class="font-santralregular mb-2"><b> PSU (Güç Kaynağı) </b><br>
                                    Güçlü bir ekran kartı ve işlemci için gerekli olan güce sahip bir güç kaynağı seçmek önemlidir. Kaliteli ve uzun ömürlü bir PSU için güvenilir markalardan birini tercih etmelisiniz.
                                </li>
                                <li class="font-santralregular mb-2"><b>Soğutma </b><br>
                                    Sistemi güçlü olan bir oyun bilgisayarı ya da gaming laptop modelleri için iyi bir soğutma sistemi oldukça önemlidir. Yüksek kaliteli soğutucular tercih ederek bilgisiyarınızın performansının düşmesini en aza
                                    indirgeyebilirsiniz.
                                </li>
                                <li class="font-santralregular mb-2"><b>Yüksek Çözünürlüğe Sahip Monitör </b><br>
                                    Oyun bilgisayarı seçiminde önemli olan hususlardan biri de monitördür. Yüksek çözünürlüğe, doygun ve canlı renklere sahip paneller tavsiye edilir. VA, IPS Ve TN olmak üzere üç farklı panel türünden birini tercih
                                    edebilirsiniz.
                                    <br>Bu faktörler ışığında yüksek performanslı en iyi oyun bilgisayarı modellerinden birini kolayca seçebilirsiniz.
                                </li>
                            </ul>
                        </div>
                        <picture>
                            <source srcset="../../../images/blog-webp/oyun-bilgisayari-2-800x500.webp" type="image/webp">
                            <img class="w-full my-4" src="../../../images/blog/oyun-bilgisayari-2-800x500.jpg">
                        </picture>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            <h3 class="text-base font-bold lg:font-semibold pb-1 mt-3">En İyi Oyun Bilgisayarında Olması Gereken Özellikler Nelerdir?</h3>
                            Bir oyun bilgisayarının yüksek performans sergileyen donanım ve yazılım etkenlerine sahip olması gerekir. Kişisel tercihlerin büyük oranda etkili olduğu oyun bilgisayarı modelleri arasından birini araştırma yaparak tercih
                            edebilirsiniz. En iyi oyun bilgisayarı için en az 16 GB RAM veya daha yukarısı ve en az i7 işlemciye sahip olması gerekir. Hızlı yükleme ve depolama alanı için 512 GB SSD’ler tercih edilmelidir. Yüksek kaliteli oyunlar;
                            oyun bilgisayarı veya oyun
                            laptop modellerinin aşırı ısınmasına sebep olacağı için kaliteli bir soğutma sistemi gerekir. Ve kaliteli bir görüntü için en az 1080p çözünürlüğe sahip bir ekran tavsiye edilir.
                        </div>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            <h3 class="text-base font-bold lg:font-semibold pb-1 mt-3">Öne Çıkan Gaming Laptop Modelleri ve Markaları</h3>
                            Oyun severlerin keyifli bir oyun deneyimi yaşaması için dünyaca tanınmış markanın pek çok oyun bilgisayarı bulunur. Bu markalardan bazıları şunlardır;
                            <ul class=" list-none font-santralregular mt-2">
                                <li class="font-santralregular mb-2"><b>Monster </b><br>
                                    Oyun bilgisayarı denilince hemen hemen herkesin aklına gelen markalardan biri Monster’dır. Ses ve görüntü kalitesi, hızı, ekranı ile üstün kaliteli modelleri bulunur. Monster Huma H4 V5.2.6 1255U, Monster Abra A5
                                    V16.7.5 Gaming Laptop, Monster Abra A5 V16.7 öne çıkan modelleridir.
                                </li>
                                <li class="font-santralregular mb-2"><b>Acer </b><br>
                                    Yüksek performans sergileyen yeni nesil oyunlar için tasarlanan gaming laptop modelleri ile beğeni toplar. Acer Nitro 5 AN515-57 ve Acer Nitro 5 AN515-46 modelleri oldukça dikkat çeker.
                                </li>
                                <li class="font-santralregular mb-2"><b>HP </b><br>
                                    Eğlence ve oyun odaklı çözümler sunan HP, oyun tutkunları için yüksek performans sağlayan oyun laptop modelleri ile dikkat çeker. Öne çıkan modelleri arasında HP Victus 16-E0006nt 4H0R9EA ve HP Victus 15-FA1019NT
                                    gaming laptop seçenekleri bulunur.
                                </li>
                                <li class="font-santralregular mb-2"><b>Lenovo </b><br>
                                    Mükemmel oyun deneyimi ve kaliteli video yapısıyla yenilikçi tasarımlar üreten Lenovo oldukça tercih edilir. Lenovo IdeaPad Gaming 3 82K101HRTX ve Lenovo IdeaPad Gaming 3 82K101EATX gaming laptop dikkat çeken
                                    modelleridir.
                                </li>
                            </ul>
                        </div>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            <h3 class="text-base font-bold lg:font-semibold pb-1 mt-3">Oyun Bilgisayarı Fiyatları Neye Göre Belirleniyor?</h3>
                            Oyun bilgisayarı fiyatları bazı etkenlere göre belirlenir. Gaming laptop fiyatları; donanımsal özelliklerine, kalitesine, markasına, grafik tasarımlarına, soğutma sistemine, performansına bağlı olarak değişkenlik gösterir.
                            İhtiyaçlarınız ve bütçeniz dahilinde uygun fiyatlı modellerden birini tercih edebilirsiniz.
                        </div>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            <h3 class="text-base font-bold lg:font-semibold pb-1 mt-3">Kiralık Oyun Bilgisayarı Modelleri Uygun Fiyatlar İle KiralaBunu'da!</h3>
                            Oyun bilgisayarları hızla gelişen teknolojileri ile yüksek fiyatlarda olabilir. Oyun keyfini doyasıya yaşamak için kiralık gaming laptop alternatifleri, oyun tutkunlarına ekonomik açıdan avantaj sağlayabilir. Oyun
                            bilgisayarı kiralama seçeneği çok fazla bütçe ayırmadan dilediğiniz oyun bilgisayarı modelini istediğiniz kadar deneyimle fırsatı sunar. Satın almadan önce merak ettiğiniz bir markanın oyun laptop modelinin özelliklerini
                            uygun fiyatlarla deneyimlemek
                            isterseniz, Kiralabunu adresini ziyaret edebilirsiniz. Bütçenizi yormadan, tercih ettiğiniz süre boyunca pek çok marka ve modelin bulunduğu <a target="_blank"
                                href="https://kiralabunu.com/kategoriler/laptoplar/oyun-bilgisayarlari">oyun bilgisayarlarını uygun fiyatlarla kiralayabilirsiniz.</a> Bu sayede, satın almadan önce deneyimleme şansı yakalayarak, ihtiyaçlarınıza en
                            uygun seçimi yapabilirsiniz.
                        </div>

                    </div>

                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-3xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Gaming</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Performans</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Oyun</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Bilgisayar</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Dizüstü</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Ofis</div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <blog-side-menu></blog-side-menu>
                </div>
            </div>

        </section>
    </div>
</template>
