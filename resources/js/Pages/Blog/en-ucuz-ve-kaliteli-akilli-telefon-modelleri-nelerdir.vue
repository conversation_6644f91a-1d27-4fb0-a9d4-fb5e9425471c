<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenu from "@/Pages/Shared/BlogSideMenu.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenu
    },
    layout: Layout,
    setup() {

    }
};
</script>

<template>

    <Head title="En Ucuz ve Kaliteli Akıllı Telefon Modelleri Nelerdir?" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source srcset="../../../images/blog-webp/akilli-telefon-1-800x500.webp" type="image/webp">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../../images/blog/akilli-telefon-1-800x500.jpg" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Blog</div>
                                <div class="text-base lg:text-2xl font-bold mb-5 text-white mb-2">En Ucuz ve Kaliteli Akıllı Telefon Modelleri Nelerdir?</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>10 Ocak 2024</span>
                                    </div>
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">
                                            <g id="view" transform="translate(0)">
                                                <path id="Path_108" data-name="Path 108"
                                                    d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z"
                                                    transform="translate(-1.126 -5.625)" fill="#e2e2e2" />
                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z"
                                                    transform="translate(-6.455 -8.586)" fill="#e2e2e2" />
                                            </g>
                                        </svg>
                                        <span>95</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h1 class="text-base lg:text-2xl font-bold lg:font-semibold pb-1">En Ucuz ve Kaliteli Akıllı Telefon Modelleri Nelerdir? </h1>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            Telefon satın almak için belli bir bütçe ayıranlar, pek çok ucuz akıllı telefon modeli ile karşılaşır. Uygun fiyatlı telefonlar; kaliteli tasarımları, yüksek çözünürlüklü kameraları, yeterli depolama alanları ile gerekli
                            tüm özelliklere sahiptir. Akıllı telefon fiyatları bakımından uygun ve beklentileri karşılayan modeller bulunmaktadır. Bu modellerden bazıları şunlardır;
                            <br><br>
                            Xiaomi, teknik özellikleri ve tasarımları ile oldukça uygun fiyatlı telefonlar arasında yer alır. Geniş ekranları, kaliteli kameraları ve cazip fiyatları ile öne çıkar. Realme serisi,temel özellikleri ile öne çıkan ucuz
                            akıllı telefon markalarından biridir. Günlük kullanımda uzun batarya ömrü, yüksek çözünürlüklü kamerası ve uzun ekranı ile en ucuz cep telefonları açısından dikkat çeker. Reeder serileri, uygun fiyatlı telefonlar arasında
                            oldukça önemli bir yeri
                            vardır. Gerek işletim sistemleri gerek ekran çözünürlükleri ile fiyat performans ürünüdür. Yeterli depolama alanı ve uzun pil ömrü ile tercih edebileceğiniz en ucuz cep telefonları arasındadır. Tecno markası, genelde uygun
                            fiyatlı ve orta segment sınıfa hitap eden Android işletim sistemine sahip akıllı telefonlar üretir. Geniş ekranları, uzun pil ömrü, dayanıklı, gelişmiş kamera özellikleri ile günlük kullanımlar için oldukça idealdir.
                            Akıllı telefon fiyatları
                            açısından bütçenize dost bu markalarının cazip fiyatlı ve yüksek performans sergileyen modellerinden birini tercih edebilirsiniz.
                        </div>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            <h3 class="text-base font-bold lg:font-semibold pb-1 mt-3">Uygun Fiyatları İle Öne Çıkan Akıllı Telefon Markaları</h3>
                            Teknolojinin gelişmesiyle ve üretim maliyetlerinin artması ile birlikte akıllı telefon fiyatları da artmaktadır. İhtiyaçlarınızı karşılayan, kaliteli bir telefonu daha ekonomik fiyatlarda bulabileceğiniz pek çok ünlü
                            markanın modeli bulunur. Kaliteli ve sağlam modelleri ile öne çıkan bu markaların başında Xiaomi, Realme, Reeder ve Tecno markalarının modelleri yer alır. Kriterlerinize uygun özelliklere sahip uygun fiyatlı telefonlardan
                            birini seçebilirsiniz.
                        </div>
                        <picture>
                            <source srcset="../../../images/blog-webp/akilli-telefon-2-800x500.webp" type="image/webp">
                            <img class="w-full my-4" src="../../../images/blog/akilli-telefon-2-800x500.jpg" />
                        </picture>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            <h3 class="text-base font-bold lg:font-semibold pb-1 mt-3">En Ucuz Akıllı Telefon Modelleri ve Fiyatları</h3>
                            En ucuz akıllı telefon modellerini ve fiyatlarını inceleyerek, bütçenize uygun bir modeli kolayca bulabilirsiniz. Bu modellerden bazıları şunlardır;
                            <br><br><b>Reeder P13 Blue Plus </b> <br>
                            6.55 inç ekranı ve IPS LCD ekran teknolojisine sahiptir. 4780 mAH bataryası ve 60 Hz ekran yenileme hızına sahiptir. Cep telefonu dünyasında uygun fiyatları ile her bütçeye hitap eder.
                            <br><b>Xiaomi Redmi 10S </b> <br>
                            Cazip fiyatları ile dikkat çeken Redmi Note serisinden olan 10S 6. 43 inçlik bir ekran boyutuna sahiptir. 64 MP + 8 MP + 2 MP + 2 MP kamerası, hızlı bir performans sunan RAM’i ve 5000 mAH bataryası bulunur. Oldukça uygun
                            fiyatlı telefonlar arasında yer alır.
                            <br><b>Realme 10 </b> <br>
                            6.4 inçlik ekranı ve Super AMOLED ekran teknolojisine sahiptir. 50 MP + 3 MP kamerası, 90 Hz ekran yenileme hızı, 5000 mAH bataryası ile uygun fiyatlı telefon modellerinden biridir. Günlük işlerde kullanıma uygun ve makul
                            fiyatları ile oldukça tercih edilen modeller arasındadır.
                            <br><b>Tecno Spark Go</b> <br>
                            Oldukça uygun fiyatlı Tecno Spark Go modeli, 6.6 inçlik HD ekrana sahiptir. 5000 mAH bataryası ile uzun ömürlü pil ve kolaj şarj edilir. 13 MP kameraya ve 60 Hz ekran yenileme hızına sahiptir. Genişletilmiş RAM’i ile
                            telefonunuzun kusursuz ve hızlı bir şekilde çalışması sağlanmıştır.
                        </div>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            <h3 class="text-base font-bold lg:font-semibold pb-1 mt-3">Kiralık Android Telefon Modelleri Uygun Fiyatlar İle KiralaBunu'da!</h3>
                            Akıllı telefonunuzu yenilemek ya da yeni bir modeli denemek isterseniz, kiralık akıllı telefon seçeneği tam size göre olabilir. Bütçenize hitap eden pek çok uygun akıllı telefonlar arasından birini cazip fiyat aralıkları
                            ile kiralamak mümkündür. Bütçenizi sarsmadan dilediğiniz süre kadar <a href="https://kiralabunu.com/kategoriler/telefon" target="_blank">akıllı telefon kiralama</a> yapabilirsiniz. Evinizin konforunda aradığınız kriterlere
                            hitap eden en ucuz akıllı
                            telefon modellerinden birini Kiralabunu adresinden bulabilirsiniz.
                        </div>
                    </div>


                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-2xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Spor</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Aktivite</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Sağlık</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Koşu</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Pilates</div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <blog-side-menu></blog-side-menu>
                </div>
            </div>

        </section>
    </div>
</template>
