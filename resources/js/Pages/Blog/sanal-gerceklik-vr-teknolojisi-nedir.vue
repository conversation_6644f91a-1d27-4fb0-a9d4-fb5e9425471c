<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenu from "@/Pages/Shared/BlogSideMenu.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenu
    },
    layout: Layout,
    setup() {

    }
};
</script>

<template>

    <Head title="Sanal Gerçeklik VR Teknolojisi Nedir?" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">
            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1 items-start">
                <div class="w-full lg:w-8/12">
                    <div class="overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source srcset="../../../images/blog-webp/vrgozluk3.webp" type="image/jpeg">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../../images/blog/vrgozluk3.png" />
                        </picture>
                        <div class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black">
                            <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Blog</div>
                                <div class="text-base lg:text-3xl font-bold mb-5 text-white mb-2">Sanal Gerçeklik VR Teknolojisi Nedir?</div>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                            <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                            <path id="Path_107" data-name="Path 107"
                                                d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                        </svg>
                                        <span>08 Ekim 2023</span>
                                    </div>
                                    <div class="text-xs text-white flex space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">
                                            <g id="view" transform="translate(0)">
                                                <path id="Path_108" data-name="Path 108"
                                                    d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z"
                                                    transform="translate(-1.126 -5.625)" fill="#e2e2e2" />
                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z"
                                                    transform="translate(-6.455 -8.586)" fill="#e2e2e2" />
                                            </g>
                                        </svg>
                                        <span>548</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mt-10 mb-4">
                        <h1 class="text-base lg:text-3xl font-bold lg:font-semibold pb-1">Sanal Gerçeklik VR Teknolojisi Nedir? </h1>
                        <div class="text-sm text-checkoutgray font-santralregular">
                            VR gözlük diğer adıyla sanal gerçeklik gözlükleri, sanal ortamları gerçek kılmak için tasarlanmıştır. Teknolojik araçlardan gerçekleştireceğiniz etkinlikleri görüntü olarak gerçek kılmaktadır. Her bütçeye uygun olmasıyla
                            da kolaylıkla sahip olabilirsiniz.
                            <br><br>VR teknolojisi ile oyunların içinde kaybolacaksınız! Gerçek dünyanın dışında farklı bir ortama adım atmanızı ve size yeni keşifler, eğlence ve öğrenme fırsatları sunmaktadır. Yenilikçi yapısı, size yeni deneyimler
                            sunarken artık birden fazla evrene sahip olabilirsiniz. Oyunlarda yaşayabilir, kendi ütopik dünyanızı yaratabilirsiniz. Yani bir nevi gerçek dünyadan uzaklaşmak için iyi bir seçenek denilebilir. 10 adımda neler
                            yapabileceğinizi inceleyelim.

                            <br><br>
                            <h3 class="text-base font-bold lg:font-semibold pb-1">VR Gözlüklerle Neler Yapabilirsiniz ve Özellikleri Nelerdir? </h3>
                            <br>
                            <ul class="ml-1 md:ml-3 list-decimal font-santralregular">
                                <li class=" font-santralregular"> Sanal gerçeklik oyunlarını deneyimleyebilirsiniz. Fiziksel olarak oyunların içinde hissedebilir, oyunların aranan yüzü siz olabilirsiniz.</li>
                                <li class=" font-santralregular"> Sanal turlar yaparak farklı yerleri keşfedebilirsiniz. Dünyanın her bir yerini gezmek VR teknolojisi ile oldukça kolay.</li>
                                <li class=" font-santralregular"> Eğitim amaçlı simülasyonlarla yeni beceriler kazanabilirsiniz. Sanal gerçeklik, tıp, mühendislik ve tarih gibi pek çok alanda kullanılmaktadır.</li>
                                <li class=" font-santralregular"> Sanal gerçeklik filmlerini ve 360 derece videolar izleyebilirsiniz. Filmlerin aktarılan yoğun duygusunu daha çok hissedeceksiniz.</li>
                                <li class=" font-santralregular"> Gelişmiş teknolojisi ile tam anlamıyla spor düşkünü desek yanılmayız. Birbirinden farklı spor türleri ile size ait bir spor salonuna sahip olabilirsiniz.</li>
                                <li class=" font-santralregular"> Stresli günleri de geri de bıraktıran VR gözlükler, terapi ve rehabilitasyon uygulamalarıyla psikolojiniz için de oldukça kullanışlı.</li>
                                <li class=" font-santralregular"> Sosyal etkileşimde bulunabilir ve farklı kültüre sahip insanlarla tanışabilirsiniz.</li>
                                <li class=" font-santralregular"> VR teknolojisi ile yaratıcı projeler üretebilirsiniz.</li>
                                <li class=" font-santralregular"> Çalışma rutininizi eğlenceli hale getirebilirsiniz. Sanal gerçeklik gözlükleri her alanda uzmanlaşmanız için nitelikli fırsatlar sunmaktadır.</li>
                                <li class=" font-santralregular"> Sanal gerçeklik ile uzaktaki sevdiklerinizle buluşabilirsiniz.</li>
                            </ul>
                            Ayrıca, sanal gerçeklik sadece video ve oyunlarla da sınırlı değil. Kendinize göre kişiselleştirebilir, dilediğiniz her şeyi yapabilirsiniz. Tamamen özgürsünüz. <br><br>
                            <h3 class="text-base font-bold lg:font-semibold pb-1"> En İyi 3 Sanal Gerçeklik Filmi </h3>
                            <ul class="ml-1 md:ml-3 list-decimal font-santralregular">
                                <li class=" font-santralregular"><b>Ashes to Ashes </b><br>
                                    Büyükbabalarının ölümüyle uğraşan aile hakkında kısa bir VR filmidir. Büyükbaba son dileğini yerine getirmek ister ve filmin gidişatı buna bağlıdır. Büyükbabanın dileği, vazosunu bir bombaya dönüştürmek ve
                                    gökyüzüne fırlatmaktadır. Hikâyenin tek perspektiften olması sanal gerçekliği profesyonelleştiriyor. Gerilim ve korku bir arada olan bu filmi çok seveceksiniz.
                                </li>
                                <li class=" font-santralregular"><b>Surviving 9/11 </b><br>
                                    Dünya Ticaret Merkezine yapılan saldırıdan sonra hayatta kalan son kişi siz olmak ister miydiniz? Dünya Ticaret Merkezinin özel ve daha önce hiç görülmemiş görüntülerini keşfedeceksiniz. Saldırı sırasında sıfır
                                    noktasındaki korkuyla yüzleşin ve 20 dakikalık VR belgeseli için kendinizi hazırlayın!
                                </li>
                                <li class=" font-santralregular"><b>GYMNASIA </b><br>
                                    Terk edilmiş bir okulun sessizliğine adım atın ve kayıp bir çocuğun sizi beklediği GYMNASIA’ya girmeniz ile şekillenen bu filmde korku ile yüzleşin. Kukla animasyonu ve 3D 360 derecelik kısa filmde, görüntüleri ve
                                    sesleri hatırlamanız gerektiğini unutmamalısınız. Unutulmuş günleri canlandırmak için gerilim içeren film tam puan almayı hak ediyor.
                                </li>
                            </ul>
                            Daha fazla VR içerikli filmler için FELLIX&PAUL STUDIOS’a göz atabilirsiniz.
                        </div>
                    </div>


                    <div class="w-full mt-10 mb-4">
                        <h2 class="text-3xl font-semibold pb-1">Etiketler</h2>
                        <div class="flex flex-wrap justify-start space-x-2 items-start">
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Spor</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Aktivite</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Sağlık</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Koşu</div>
                            <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2">Pilates</div>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <blog-side-menu></blog-side-menu>
                </div>
            </div>

        </section>
    </div>
</template>
