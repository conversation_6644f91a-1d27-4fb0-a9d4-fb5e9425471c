<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import BlogSideMenuNew from "@/Pages/Shared/BlogSideMenuNew.vue";
import BlogLastPostNew from "@/Pages/Shared/BlogLastPostNew.vue";

export default {
    components: {
        BlogLastPostNew,
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        BlogSideMenuNew
    },
    props: {
        topBlogs: { type: Object },
        techology: { type: Object },
        all: { type: Object },
        sideBlogs: Object
    },
    layout: Layout,
    methods: {
        // getImgUrl(imageObj) {
        //     return imageObj.id;
        //     //https://kiralabunu.fra1.digitaloceanspaces.com/kb-media/27/
        // }
    },
    setup() {

    }
};
</script>

<template>

    <Head title="Blog Haberler" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="mt-12 mb-12">

            <template v-if="Object.keys(this.topBlogs).length > 0">
                <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1">
                    <div class="w-full lg:w-8/12 overflow-hidden rounded-[7px] relative group">
                        <Link :href="`/blog/${topBlogs[0].slug}`">
                        <picture>
                            <source :srcset="topBlogs[0].cover_image.S3Conversions.large1" type="image/webp">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" :src="topBlogs[0].cover_image.S3URL" />
                        </picture>
                        </Link>
                        <Link class="rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black cursor-pointer" :href="`/blog/${topBlogs[0].slug}`">
                        <div class="absolute bottom-0 left-0 flex flex-col px-2 md:pl-5 justify-start">
                            <div class="flex space-x-2">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2" v-for="tag in topBlogs[0].tags.filter(tag => tag.title != 'topBlog').slice(0, 2)">{{ tag.title }}</div>
                            </div>
                            <div class="text-base lg:text-2xl font-bold text-white mb-2">
                                <Link :href="`/blog/${topBlogs[0].slug}`">{{ topBlogs[0].menu_name }}</Link>
                            </div>
                            <div class="flex space-x-3 mb-4">
                                <div class="text-xs text-white flex space-x-2">
                                    <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                        <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                        <path id="Path_107" data-name="Path 107"
                                            d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                            transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                    </svg>
                                    <span>{{ topBlogs[0].blog_date_pretty }}</span>
                                </div>
                                <!--                                    <div class="text-xs text-white flex space-x-2">-->
                                <!--                                        <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">-->
                                <!--                                            <g id="view" transform="translate(0)">-->
                                <!--                                                <path id="Path_108" data-name="Path 108" d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z" transform="translate(-1.126 -5.625)" fill="#e2e2e2"/>-->
                                <!--                                                <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z" transform="translate(-6.455 -8.586)" fill="#e2e2e2"/>-->
                                <!--                                            </g>-->
                                <!--                                        </svg>-->
                                <!--                                        <span>372</span>-->
                                <!--                                    </div>-->
                            </div>
                        </div>
                        </Link>
                    </div>

                    <div class="w-full lg:w-4/12 flex flex-nowrap lg:flex-wrap space-y-2 overflow-y-scroll lg:overflow-y-visible mt-4 lg:mt-0">
                        <div v-for="(blog, index) in topBlogs">
                            <div v-if="index > 0" class="lg:min-w-[200px] w-full px-1 overflow-hidden rounded-[7px] relative group">
                                <picture>
                                    <source :srcset="blog.cover_image.S3Conversions.large1" type="image/webp">
                                    <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" :src="blog.cover_image.S3URL" />
                                </picture>
                                <Link class="rounded-[7px] mx-1 absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black cursor-pointer" :href="`/blog/${blog.slug}`">
                                <div class="absolute bottom-0 left-0 flex flex-col pl-1 md:pl-5 justify-start">
                                    <div class="hidden md:flex space-x-2">
                                        <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2" v-for="tag in topBlogs[0].tags.filter(tag => tag.title != 'topBlog').slice(0, 2)">{{ tag.title }}</div>
                                    </div>
                                    <div class="text-xs px-1 lg:text-xl font-bold text-white mb-1">
                                        <Link :href="`/blog/${blog.slug}`">{{ blog.menu_name }}</Link>
                                    </div>
                                    <div class="hidden md:flex space-x-3 mb-4">
                                        <div class="text-xs text-white flex space-x-2">
                                            <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                                <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                                <path id="Path_107" data-name="Path 107"
                                                    d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                                    transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                            </svg>
                                            <span>{{ blog.blog_date_pretty }}</span>
                                        </div>
                                    </div>
                                </div>
                                </Link>
                            </div>

                        </div>
                        <!--                        <div class="min-w-[200px] w-full px-1 overflow-hidden rounded-[7px] relative group">-->
                        <!--                            <picture>-->
                        <!--                                <source srcset="../../images/blog-webp/hava-temizleme-1-1440x840.webp" type="image/webp">-->
                        <!--                                <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" src="../../images/blog/hava-temizleme-1-1440x840.jpg"/>-->
                        <!--                            </picture>-->

                        <!--                            <div class="rounded-[7px] mx-1 absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black cursor-pointer" onclick="window.location='/blog/hava-temizleme-cihazi-nedir-ne-ise-yarar';">-->
                        <!--                                <div class="absolute bottom-0 left-0 flex flex-col pl-5 justify-start">-->
                        <!--                                    <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-1">Sağlık</div>-->
                        <!--                                    <div class="text-sm lg:text-xl font-bold text-white mb-1">-->
                        <!--                                        <Link href="/blog/hava-temizleme-cihazi-nedir-ne-ise-yarar">Hava Temizleme Cihazı Nedir? Ne İşe Yarar?</Link>-->
                        <!--                                    </div>-->
                        <!--                                    <div class="flex space-x-3 mb-4">-->
                        <!--                                        <div class="text-xs text-white flex space-x-2">-->
                        <!--                                            <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">-->
                        <!--                                                <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2"/>-->
                        <!--                                                <path id="Path_107" data-name="Path 107" d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z" transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd"/>-->
                        <!--                                            </svg>-->
                        <!--                                            <span>01 Aralık 2023</span>-->
                        <!--                                        </div>-->
                        <!--                                        <div class="text-xs text-white flex space-x-2">-->
                        <!--                                            <svg xmlns="http://www.w3.org/2000/svg" width="15.984" height="11.722" viewBox="0 0 15.984 11.722">-->
                        <!--                                                <g id="view" transform="translate(0)">-->
                        <!--                                                    <path id="Path_108" data-name="Path 108" d="M17.078,11.3a8.893,8.893,0,0,0-7.961-5.68,8.893,8.893,0,0,0-7.96,5.68.533.533,0,0,0,0,.362,8.893,8.893,0,0,0,7.96,5.68,8.893,8.893,0,0,0,7.961-5.68.533.533,0,0,0,0-.362ZM9.118,16.281a7.912,7.912,0,0,1-6.889-4.8,7.912,7.912,0,0,1,6.889-4.8,7.911,7.911,0,0,1,6.889,4.8A7.912,7.912,0,0,1,9.118,16.281Z" transform="translate(-1.126 -5.625)" fill="#e2e2e2"/>-->
                        <!--                                                    <path id="Path_109" data-name="Path 109" d="M14.447,11.25a3.2,3.2,0,1,0,3.2,3.2A3.2,3.2,0,0,0,14.447,11.25Zm0,5.328a2.131,2.131,0,1,1,2.131-2.131A2.131,2.131,0,0,1,14.447,16.578Z" transform="translate(-6.455 -8.586)" fill="#e2e2e2"/>-->
                        <!--                                                </g>-->
                        <!--                                            </svg>-->
                        <!--                                            <span>160</span>-->
                        <!--                                        </div>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                        </div>-->
                    </div>
                </div>
            </template>

            <template v-if="techology.length > 0">
                <div class="w-full mt-10 mb-4">
                    <h2 class="text-3xl md:text-4xl font-semibold pb-1">Teknoloji Yazıları</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>

                    <div class="text-sm text-checkoutgray">Teknoloji’nin tüm konularında en seçkin makaleleri keşfedin.</div>
                </div>
            </template>

            <template v-if="techology.length > 0">
                <div class="w-full flex space-x-2 overflow-y-scroll lg:overflow-y-visible">
                    <div v-for="(blog, index) in techology" class="min-w-[300px] w-1/4 px-0 overflow-hidden rounded-[7px] relative group">
                        <picture>
                            <source :srcset="blog.cover_image.S3Conversions.medium4" type="image/webp">
                            <img class="w-full rounded-[7px] group-hover:scale-105 transition-all duration-300" :src="blog.cover_image.S3URL" />
                        </picture>
                        <Link class=" rounded-[7px] absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black cursor-pointer" :href="`/blog/${blog.slug}`">
                        <div class="absolute bottom-0 left-0 flex flex-col px-3 justify-start">
                            <div class="flex space-x-2">
                                <div class="bg-kbgreen text-xs text-white rounded-full px-3 py-1 self-start leading-tight mb-2" v-for="tag in blog.tags.filter(tag => tag.title != 'topBlog').slice(0, 2)">{{ tag.title }}</div>
                            </div>
                            <div class="text-xl font-bold text-white mb-1">
                                <Link :href="`/blog/${blog.slug}`">{{ blog.menu_name }}</Link>
                            </div>
                            <div class="flex space-x-3 mb-4">
                                <div class="text-xs text-white flex space-x-2">
                                    <svg id="calendar" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 12 12">
                                        <path id="Path_106" data-name="Path 106" d="M11.151,13.5a.651.651,0,0,0,0,1.3h5.21a.651.651,0,1,0,0-1.3Z" transform="translate(-7.895 -9.593)" fill="#e2e2e2" />
                                        <path id="Path_107" data-name="Path 107"
                                            d="M6.454,4.5A1.954,1.954,0,0,0,4.5,6.454v7.815a1.954,1.954,0,0,0,1.954,1.954h7.815a1.954,1.954,0,0,0,1.954-1.954V6.454A1.954,1.954,0,0,0,14.268,4.5ZM5.8,14.268V7.1H14.92v7.163a.651.651,0,0,1-.651.651H6.454A.651.651,0,0,1,5.8,14.268Z"
                                            transform="translate(-4.5 -4.5)" fill="#e2e2e2" fill-rule="evenodd" />
                                    </svg>
                                    <span>{{ blog.blog_date_pretty }}</span>
                                </div>
                            </div>
                        </div>
                        </Link>
                    </div>
                </div>
            </template>

            <div class="w-full mt-10 mb-5">
                <h2 class="text-3xl md:text-4xl font-semibold pb-1">En Son Yazılar</h2>
                <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                <!--                <div class="text-sm text-checkoutgray">Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed</div>-->
            </div>

            <div class="w-full flex flex-wrap lg:flex-nowrap space-x-1">
                <div class="w-full lg:w-8/12 relative flex flex-col space-y-3 mb-10">
                    <BlogLastPostNew :all="all"></BlogLastPostNew>
                </div>
                <div class="w-full lg:w-4/12 lg:pl-2">
                    <h2 class="text-3xl lg:text-4xl font-semibold pb-1">Öne Çıkan Yazılar</h2>
                    <div class="w-1/3 lg:w-1/6 border-t-2 border-kbgreen mb-4"></div>
                    <BlogSideMenuNew :side-blogs="sideBlogs"></BlogSideMenuNew>
                </div>

            </div>

        </section>
    </div>
</template>
