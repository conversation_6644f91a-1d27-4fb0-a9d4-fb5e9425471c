<script>
import {Head, <PERSON>} from '@inertiajs/inertia-vue3';
import Layout from '@/Pages/Shared/Layout.vue';
import UserMenu from '@/Pages/Shared/UserMenu.vue';

export default {
    components: {
        <PERSON>,
        Head,
        UserMenu,

    },
    layout: Layout,
}
</script>

<template>
    <Head title="Tav<PERSON><PERSON> " />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <user-menu :active="`Advice`" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 lg:px-4 pt-2">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7">Tav<PERSON><PERSON>  </h3>
                </div>
                <div class="flex flex-col justify-center items-center mb-10">
                    <div class="w-11/12 lg:w-6/12 mb-5 lg:mb-12">
                    <img class="w-full max-w-[336px] mx-auto" src="../../images/istek-listem.png" alt="">
                    </div>
                    <div class="w-full lg:w-8/12">
                        <p class=" leading-tight p-0 text-base text-center text-kbgray box-border mb-5 lg:mb-6 lg:px-8">Arkadaşlarını davet et, her biri kiraladığı ilk üründe ilk ay kira tutarının %25’i kadar indirim kazansın, sen de bir sonraki ödemenden arkadaşınla aynı tutarda indirim kazan!</p>
                        <div class="flex justify-center">
                            <div class="w-10/12 flex justify-between border-2 border-bordergray rounded-2lg mb-7 px-4 py-1 lg:py-2 bg-kb-mid-grey items-center">
                                <p class="text-xl text-black font-bold tracking-wider">KIRALABUNU25</p>
                                <svg width="17.785" height="21.078" viewBox="0 0 17.785 21.078">
                                    <g id="copy-line" opacity="0.9">
                                        <path id="Path_129" data-name="Path 129" d="M9.988,7H22.5a.988.988,0,0,1,.988.988V23.8a.988.988,0,0,1-.988.988H9.988A.988.988,0,0,1,9,23.8V7.988A.988.988,0,0,1,9.988,7Zm.329,16.467H22.174V8.317H10.317Z" transform="translate(-9 -3.707)" fill="#231f20"/>
                                        <path id="Path_130" data-name="Path 130" d="M4,2.988A.988.988,0,0,1,4.988,2H17.5a.988.988,0,0,1,.988.988V18.8a.988.988,0,0,1-.988.988h-.329V3.317H4Z" transform="translate(-0.707 -2)" fill="#231f20"/>
                                    </g>
                                </svg>
                            </div>
                        </div>
                        <p class="p-0 text-base text-center text-kbred box-border mb-5 lg:mb-6">Arkadaşlarına tavsiyede bulunmak için ödeme yapan bir müşteri olmalısın.</p>

                    </div>
                </div>
            </div>
        </section>
    </main>

</template>
