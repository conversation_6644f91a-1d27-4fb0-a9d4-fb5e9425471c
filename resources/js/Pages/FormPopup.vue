<template>
    <div class="w-full bg-[#0000007a] absolute top-0 z-60 h-full flex justify-center items-center">
        <header class="absolute bg-white px-5 py-5 z-70 max-w-[305px] md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl rounded-lg flex flex-col justify-center items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 62.051 62.051">
                <g id="Group_4884" data-name="Group 4884" transform="translate(-73.036 -250.295)">
                    <circle id="Ellipse_199" data-name="Ellipse 199" cx="31.025" cy="31.025" r="31.025" transform="translate(73.036 250.295)" fill="#70d44b"/>
                    <path id="Path_3299" data-name="Path 3299" d="M351.649,476.232a3.45,3.45,0,0,1-2.761-1.381l-8.464-11.286a3.451,3.451,0,0,1,5.522-4.141l5.538,7.383,14.223-21.334a3.451,3.451,0,1,1,5.743,3.829l-16.928,25.393a3.453,3.453,0,0,1-2.776,1.536Z" transform="translate(-251.82 -178.764)" fill="#fff"/>
                </g>
            </svg>
            <h2 class="text-base font-semibold text-center mb-2 mt-2">Teşekkürler</h2>
            <p class="text-xs font-kiralabunuthin ts:text-sm text-center ">Mesajınız/talebiniz başarıyla oluşturuldu</p>
            <div class="absolute top-2 right-5" @click="closePopup">x</div>
        </header>
    </div>

</template>

<script>
    import { Link } from '@inertiajs/inertia-vue3';

    export default {
        props: {
            showPopup: true
        },
        components: {
            Link,

        },
        methods: {
            closePopup() {
                this.showPopup = true

            }
        }
    }

</script>

<style scoped>

</style>
