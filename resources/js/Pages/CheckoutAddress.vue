<script>
import {Head, <PERSON>} from '@inertiajs/inertia-vue3';
import Layout from '@/Pages/Shared/Layout.vue';
import CheckoutPaymentInfo from '@/Components/CheckoutPaymentInfo.vue'

export default {
    components: {
        <PERSON>,
        Head,
        CheckoutPaymentInfo,
    },
    props: {
        products: Array,
        total: Number,
        user: Object,
    },
    layout: Layout,
}
</script>

<template>
    <Head title="Destek Talepleri" />

    <main class="my-6 max-w-max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="flex justify-center items-center">
            <p class="px-2 py-1 font-bold text-base border-2 border-kbgreen rounded-full">Teslimat</p>
            <svg xmlns="http://www.w3.org/2000/svg" width="142.787" height="1" viewBox="0 0 142.787 1">
                <path id="Path_2907" data-name="Path 2907" d="M10647.213,14347.5H10790" transform="translate(-10647.213 -14347)" fill="none" stroke="#d0d0d0" stroke-width="1" stroke-dasharray="5"/>
            </svg>
            <p class="px-2 py-1 font-bold text-base text-textgray border-2 border-textgray rounded-full">Ödeme</p>
        </section>
        <section class="flex w-full justify-center lg:justify-between mt-8 lg:mt-0">
            <div class="w-10/12 lg:w-3/12 flex justify-between items-end border-b-3 border-kbgreen pb-2">
                <div class="flex">
                    <svg id="Group_4807" data-name="Group 4807" xmlns="http://www.w3.org/2000/svg" width="37.887" height="30" viewBox="0 0 37.887 30">
                        <g id="Rectangle_106" data-name="Rectangle 106" fill="#fff" stroke="#231f20" stroke-width="2">
                            <rect width="28.5" height="25.5" rx="8" stroke="none"/>
                            <rect x="1" y="1" width="26.5" height="23.5" rx="7" fill="none"/>
                        </g>
                        <path id="Path_90" data-name="Path 90" d="M-5860-13140.214h6.176l2.963,8.068H-5860Z" transform="translate(5887.751 13145.304)" fill="none" stroke="#231f20" stroke-linejoin="round" stroke-width="2"/>
                        <path id="Path_91" data-name="Path 91" d="M-5850.767-13129.456v11.3h-8.846v-11.3Z" transform="translate(5887.653 13142.614)" fill="none" stroke="#231f20" stroke-linejoin="round" stroke-width="2"/>
                        <g id="Ellipse_39" data-name="Ellipse 39" transform="translate(23.25 19.5)" fill="#fff" stroke="#231f20" stroke-width="2">
                            <circle cx="5.25" cy="5.25" r="5.25" stroke="none"/>
                            <circle cx="5.25" cy="5.25" r="4.25" fill="none"/>
                        </g>
                        <g id="Ellipse_40" data-name="Ellipse 40" transform="translate(3 19.5)" fill="#fff" stroke="#231f20" stroke-width="2">
                            <circle cx="5.25" cy="5.25" r="5.25" stroke="none"/>
                            <circle cx="5.25" cy="5.25" r="4.25" fill="none"/>
                        </g>
                    </svg>
                    <h3 class="p-0 pl-5 text-2xl md:text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap">Kiralama Adresi</h3>
                </div>
            </div>
        </section>
        <div class="w-full flex justify-center lg:justify-start items-center mt-4">
            <input id="ayni" class="w-4 h-4 border-3 rounded-md focus:outline-none checked:bg-black" type="checkbox">
            <label class="text-sm lg:text-base text-black pl-2" for="ayni">Fatura bilgilerim, teslimat adres bilgilerim ile aynı.</label>
        </div>
        <section class="mt-6 mb-12 flex flex-col lg:flex-row mt-5">
            <div class="w-full lg:w-9/12 pr-4">
                <form  action="#">
                    <div class="flex flex-row justify-center items-start w-full ">
                        <div class="hidden md:block w-6/12 text-center px-3">
                            <div class="relative group">
                                <input id="name" class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" v-model="user.first_name" placeholder="" name="AdSoyad" autofocus>
                                <label for="name" class=" transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adın*</label>
                            </div>
                            <select class=" w-full px-2 py-2 mb-4 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider" name="sirala" id="ilsec">
                                <option value="Sırala" selected>İl Seç*</option>
                            </select>
                            <div class="group relative">
                                <input id="birthdate" class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Dogumtarihi" v-model="user.date_of_birth">
                                <label for="birthdate" class=" transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Doğum Tarihin*</label>
                            </div>
                            <div class="group relative">
                                <input id="addressname" class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="adresadi">
                                <label for="addressname" class=" transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adres Başlığı*</label>
                            </div>
                        </div>
                        <div class="hidden md:block w-6/12 text-center px-3 relative group">
                            <input id="surname" class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Soyad" v-model="user.last_name">
                            <label for="surname" class=" transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Soyadın*</label>
                            <select class=" w-full px-2 py-2 mb-4 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider" name="sirala" id="sirala">
                                <option value="Sırala" selected>İlçe seç*</option>
                            </select>
                        </div>

                        <div class="block md:hidden w-full text-center px-3">
                            <div class="group relative">
                                <input class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="AdSoyad" autofocus v-model="user.first_name">
                                <label for="name" class=" transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adın*</label>
                            </div>
                            <div class="group relative">
                                <input class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Soyad" v-model="user.last_name">
                                <label for="surname" class=" transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Soyadın*</label>
                            </div>

                            <select class=" w-full px-2 py-2 mb-4 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider" name="sirala" id="sirala">
                                <option value="Sırala" selected>İl Seç*</option>
                            </select>
                            <select class=" w-full px-2 py-2 mb-4 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider" name="sirala" id="sirala">
                                <option value="Sırala" selected>İlçe seç*</option>
                            </select>
                            <div class="group relative">
                                <input class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Dogumtarihi" v-model="user.date_of_birth">
                                <label for="birthdate" class=" transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Doğum Tarihin*</label>
                            </div>
                            <div class="group relative">
                                <input class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="adresadi" >
                                <label for="addressname" class=" transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adres Başlığı*</label>
                            </div>
                        </div>
                    </div>
                    <div class="w-full px-3">
                        <div class="group relative">
                            <textarea id="address" class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-lg tracking-wider placeholder:mt-2 h-32" required name="Adres" placeholder="" cols="20" rows="10"  v-model="user.address.address"></textarea>
                            <label for="address" class=" transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adresin*</label>
                        </div>
                        <div class="flex flex-wrap lg:flex-nowrap justify-between">
                            <select class="w-full lg:w-3/12 mr-2 w-full px-2 py-2 mb-4 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider" name="sirala" id="sirala" v-model="user.address.type">
                                <option value="default" selected> Fatura Türü*</option>
                                <option value="home" > Bireysel</option>
                                <option value="work" > Kurumsal</option>

                            </select>
                            <div class="w-full lg:w-8/12 flex flex-wrap md:flex-nowrap">
                                <div class="group relative w-full md:w-1/3 mr-2">
                                    <input id="companyname" class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Firma" >
                                    <label for="companyname" class=" transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Firma Adı</label>
                                </div>
                                <div class="group relative w-full md:w-1/3 mr-2">
                                    <input id="taxno" class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Numarası" >
                                    <label for="taxno" class=" transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Vergi Numarası </label>
                                </div>
                                <div class="group relative w-full md:w-1/3">
                                    <input id="taxname" class="peer w-full mb-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="" name="Dairesi" >
                                    <label for="taxname" class=" transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Vergi Dairesi</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

            </div>
            <CheckoutPaymentInfo :total="total" :products="products">
                <template #action>
                    <Link
                        href="/odeme"
                        class="bg-black text-white text-center rounded-full py-2 lg:py-3 text-lg px-2 lg:px-4 self-center font-bold w-full"
                    >
                        Ödemeye Geç
                    </Link>
                </template>
            </CheckoutPaymentInfo>

        </section>
    </main>

</template>
