<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import CheckoutPaymentInfo from "@/Components/CheckoutPaymentInfoDemo.vue";
import CartProductPreview from "@/Components/CartProductPreviewDemo.vue";
import CartAddresses from "@/Pages/Shared/CartAddressesDemo.vue";
import { Dialog, DialogPanel, DialogTitle, Disclosure, DisclosureButton, DisclosurePanel, TransitionChild, TransitionRoot } from "@headlessui/vue";
import Datepicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import { vMaska } from "maska";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import OnBilgilendirmeFormu from "@/Pages/Shared/OnBilgilendirmeFormu.vue";
import MesafeliKiralamaSozlesmesi from "@/Pages/Shared/MesafeliKiralamaSozlesmesi.vue";

let intervalCode = null;

export default {
    components: {
        DisclosurePanel,
        DisclosureButton,
        Disclosure,
        OnBilgilendirmeFormu,
        MesafeliKiralamaSozlesmesi,
        Link,
        Head,
        CheckoutPaymentInfo,
        CartProductPreview,
        CartAddresses,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        Splide,
        SplideSlide,
        SplideTrack,
        Datepicker
    },
    directives: { maska: vMaska },
    props: {
        saveCardStatus: Boolean,
        products: Array,
        total: Number,
        discount_amount: Number,
        sub_total: Number,
        insurance: Number,
        user: Object,
        auth: Object,
        cards: Object,
        errors: { type: Object, default: false },
        paymentType: String,
        validationMessage: String,
        cities: Array,
        coupon: { type: Object, default: false },
        hopi_campaigns: { type: Object, default: false },
        hopi_bird: { type: Number, default: 0 },
        hopi_balance: { type: Number, default: 0 },
        non_mass_payment_total: { type: Number, default: 0 },
        is_mass_payment_enabled: { type: Boolean, default: false },
        paymnentValidationMessage: { type: String, default: null },
        tosla_active: { type: Boolean, default: false }
    },
    data() {
        return {
            loading: false,
            loadingText: "",
            form: this.$inertia.form({
                // first_name: JSON.parse(window.localStorage.getItem("user")).first_name,
                // last_name: JSON.parse(window.localStorage.getItem("user")).last_name,
                // tckn: JSON.parse(window.localStorage.getItem("user")).tckn,
                // date_of_birth: JSON.parse(window.localStorage.getItem("user")).birth_date,
                first_name: this.user?.first_name ?? "",
                last_name: this.user?.last_name ?? "",
                tckn: this.user?.tckn ?? "",
                date_of_birth: this.user?.birth_date ?? ""
            }),
            ccForm: this.$inertia.form({
                holder: null,
                number: null,
                month: null,
                year: null,
                cvc: null,
                source: "cart"
            }),
            isCardCreated: false,
            isOpen: false,
            isOpen2: false,
            showCCForm: this.paymentType == "nonRegisteredCard" ? true : false,
            selectedCC: null,
            formSubmitted: false,
            errorText: "",
            onBilgilendirme: false,
            mesafeliKiralama: true,
            selectedHopiCampaign: localStorage.getItem("selectedHopiCampaign"),
            enteredHopiBalance: localStorage.getItem("enteredHopiBalance") ?? 0,
            formSubmiting: false,
            addressModalIsOpen: false,
            cartHamburger: false,
            addressHamburger: true,
            billingHamburger: true
        };
    },
    methods: {
        toggleCartHamburger() {
            this.cartHamburger = !this.cartHamburger;
        },
        toggleAddressHamburger() {
            this.addressHamburger = !this.addressHamburger;
        },
        toggleBillingHamburger() {
            this.billingHamburger = !this.billingHamburger;
        },
        submitCardSave() {
            this.ccForm
                .post(route("saveCCOnPaymentScreen"))
                .then(() => {
                    this.isCardCreated = true;
                })
                .catch((error) => {
                    console.error("Kart kaydedilirken hata oluştu:", error);
                });
        },
        closeCardModal() {
            this.isCardCreated = false;
        },
        closeModal() {
            this.isOpen = false;
        },
        closeModal2() {
            this.isOpen2 = false;
        },
        openModal() {
            this.isOpen = true;
        },
        openModal2() {
            this.isOpen2 = true;
        },
        submitTCKN() {
            console.log(this.form);
            this.form.post("/tckn-dogrula");
        },
        compeletePaymentWithRegisteredCard() {
            console.log("compeletePaymentWithRegisteredCard");

            let uyelik = document.getElementById("uyelik");
            let aydinlatma = document.getElementById("aydinlatma");

            if (uyelik.checked == false) {
                uyelik.setCustomValidity("Ön Bilgilenme Formunu ve Mesafeli Kiralama Sözleşmesini Onaylamalısınız.");
                uyelik.reportValidity();

                return;
            }

            // if (aydinlatma.checked == false) {
            //     aydinlatma.setCustomValidity("Mesafeli Kiralama Sözleşmesini Onaylamalısınız.");
            //     aydinlatma.reportValidity();
            //     return;
            // }

            if (this.selectedCC == null) {
                this.errorText = "Lütfen bir kart seçiniz.";
                return;
            }

            if (this.user.addresses.length == 0) {
                this.errorText = "Lütfen bir adres ekleyiniz.";
                return;
            }

            this.formSubmitted = true;
            this.formSubmiting = true;

            this.sendPaymentRequest();

            if (this.selectedCC.id < 0) intervalCode = setInterval(() => this.sendPaymentRequest(), 10000);
        },
        sendPaymentRequest() {
            this.$inertia.post(
                "/compelete-payment-with-registered-card",
                {
                    cc: this.selectedCC.id,
                    hopiCampaign: localStorage.getItem("selectedHopiCampaign"),
                    enteredBalance: localStorage.getItem("enteredHopiBalance"),
                    birdId: this.hopi_bird
                },
                {
                    onSuccess: () => {
                        this.formSubmiting = false;
                        //clearInterval(intervalCode);
                        console.log("sendPaymentRequest succeed so form closed");
                    },
                    onError: () => {
                        this.formSubmiting = false;
                        this.errorText = Object.values(this.errors).join(", ");
                        clearInterval(intervalCode);
                        console.error("sendPaymentRequest failed so form closed");
                    }
                }
            );
        },
        preventNumericInput($event) {
            console.log($event.keyCode); //will display the keyCode value
            //console.log($event.key); will show the key value

            var charCode = $event.keyCode ? $event.keyCode : $event.which;
            if (
                (charCode <= 93 && charCode >= 65) ||
                (charCode <= 122 && charCode >= 97) ||
                charCode == 32 ||
                charCode == 8 ||
                charCode == 350 ||
                charCode == 351 ||
                charCode == 304 ||
                charCode == 286 ||
                charCode == 287 ||
                charCode == 231 ||
                charCode == 199 ||
                charCode == 305 ||
                charCode == 214 ||
                charCode == 246 ||
                charCode == 220 ||
                charCode == 252 ||
                charCode == 32
            ) {
                if (charCode != 33 || charCode != 43) {
                    return true;
                } else {
                    $event.preventDefault();
                }
            } else {
                $event.preventDefault();
            }
        },
        creditCardSelected(card) {
            this.selectedCC = card;
            //console.log(this.selectedCC);
            let cartProducts = [];
            this.products.map((item) => {
                let product = {};
                product.item_id = item.id;
                product.item_name = item.product.attribute_data.name.tr;
                product.price = item.total;
                // product.item_brand = item.product.brand.name;
                // product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                // product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                // (product.item_color = item.variants[0]?.options[0] ?? ""), (product.index = 0);
                product.discount = 0;
                product.item_variant = item.month?.name;
                product.quantity = item.quantity;
                cartProducts.push(product);
            });

            dataLayer.push({
                event: "add_payment_info",
                ecommerce: {
                    currency: "TRY",
                    value: this.total,
                    coupon: null,
                    shipping_tier: "Address",
                    payment_type: card.card_association, // or Mastercard
                    items: cartProducts
                }
            });
        },
        initPayment() {
            let cartProducts = [];
            this.products.map((item) => {
                let product = {};
                product.item_id = item.id;
                product.item_name = item.product.attribute_data.name.tr;
                product.price = item.total;
                // product.item_brand = item.product.brand.name;
                // product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                // product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                // (product.item_color = item.variants[0]?.options[0] ?? ""), (product.index = 0);
                product.discount = 0;
                product.item_variant = item.month?.name;
                product.quantity = item.quantity;
                cartProducts.push(product);
            });

            dataLayer.push({
                event: "begin_checkout",
                ecommerce: {
                    currency: "TRY",
                    value: this.total,
                    items: cartProducts
                }
            });
        }
    },
    created() {
        this.$inertia.on("navigate", (event) => {
            console.log("ödeme navigate", event.detail.page.url);
            let cartProducts = [];
            this.products.map((item) => {
                let product = {};
                product.item_id = item.id;
                product.item_name = item.product.attribute_data.name.tr;
                product.price = item.total;
                // product.item_brand = item.product.brand.name;
                // product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                // product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                // (product.item_color = item.variants[0]?.options[0] ?? ""), (product.index = 0);
                product.discount = 0;
                product.item_variant = item.month?.name;
                product.quantity = item.quantity;
                cartProducts.push(product);
            });

            if (event.detail.page.url.startsWith("/odeme")) {
                dataLayer.push({
                    event: "add_shipping_info",
                    ecommerce: {
                        currency: "TRY",
                        value: this.total,
                        shipping_tier: "Address",
                        items: cartProducts
                    }
                });
            }

            console.log("dataLayer", dataLayer);

            // Destroy the interval before navigating to a new page. This is important!
            clearInterval(intervalCode); // this is important!

            this.isCardCreated = this.saveCardStatus;
        });
    },
    layout: Layout
};
</script>

<template class="relative">

    <Head title="Ödeme Yöntemi" />

    <div class="fixed inset-0 hidden bg-gray-500 bg-opacity-75 z-80 transition-opacity md:block" v-if="paymnentValidationMessage || formSubmiting">
        <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div class="flex min-h-full items-stretch justify-center text-center md:items-center md:px-2 lg:px-4">
                <div aria-label="Loading..." class="flex items-center space-x-2 bg-white text-3xl m-4 p-10 rounded-2lg" role="status">
                    <svg class="h-20 w-20 animate-spin stroke-gray-500" viewBox="0 0 256 256">
                        <line x1="128" y1="32" x2="128" y2="64" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                        <line x1="195.9" y1="60.1" x2="173.3" y2="82.7" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                        <line x1="224" y1="128" x2="192" y2="128" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                        <line x1="195.9" y1="195.9" x2="173.3" y2="173.3" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                        <line x1="128" y1="224" x2="128" y2="192" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                        <line x1="60.1" y1="195.9" x2="82.7" y2="173.3" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                        <line x1="32" y1="128" x2="64" y2="128" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                        <line x1="60.1" y1="60.1" x2="82.7" y2="82.7" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                    </svg>
                    <span class="text-4xl font-medium text-gray-500">{{ paymnentValidationMessage ?? "Bankadan cevap bekleniyor" }}...</span>
                </div>
            </div>
        </div>
    </div>

    <main class="my-3 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-4 mx-auto relative">
        <section class="flex w-full justify-center lg:justify-between mt-8 lg:mt-0">
            <div class="w-10/12 lg:w-3/12 flex justify-between items-end border-b-3 border-bordergray">
                <div class="flex">
                    <h3 class="p-0 pl-5 text-3xl text-left text-gray-900 box-border whitespace-no-wrap">Ödeme</h3>
                </div>
            </div>
        </section>

        <section class="mt-2 mb-12 flex flex-col lg:flex-row relative ">
            <div class="w-full lg:w-9/12 pr-0 lg:pr-4 px-1 lg:px-0">
                <Disclosure as="div" class="mt-2" v-slot="{ open }" :defaultOpen="cartHamburger">
                    <DisclosureButton @click="toggleCartHamburger"
                        class="flex w-full justify-between rounded-lg bg-kb-light-grey px-4 py-2 text-left text-sm font-medium hover:bg-kbgreen/20 focus:outline-none focus-visible:ring focus-visible:ring-purple-500/75">
                        <p class="text-xl text-black font-bold font-santralregular font-bold">
                            Sepetimdeki Ürünler
                            <span class="font-medium">({{ products.length }} ürün)</span>
                        </p>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="h-8 w-8 text-black transition-all duration-300" :class="!open ? 'rotate-180 transform' : ''">
                            <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd"></path>
                        </svg>
                    </DisclosureButton>
                    <DisclosurePanel class="mt-3">
                        <CartProductPreview :products="products">
                            <template #ourcart>
                                <div class="flex justify-between px-2 w-full">
                                    <p class="lg:text-2xl text-black font-santralextrabold whitespace-nowrap">
                                        Sepetimdeki Ürünler</p>
                                    <a href="/sepetim" class="w-1/2 text-black hover:underline text-base font-santralregular font-bold text-end mb-0 pb-0">Düzenle</a>
                                </div>
                            </template>
                        </CartProductPreview>
                    </DisclosurePanel>
                </Disclosure>
                <Disclosure as="div" class="mt-2" v-slot="{ open }" :defaultOpen="addressHamburger">
                    <DisclosureButton @click="toggleAddressHamburger"
                        class="flex w-full justify-between rounded-lg bg-kb-light-grey px-4 py-2 text-left text-sm font-medium hover:bg-kbgreen/20 focus:outline-none focus-visible:ring focus-visible:ring-purple-500/75">
                        <p class="text-xl text-black font-bold font-santralregular font-bold">Teslimat Adresi</p>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="h-8 w-8 text-black transition-all duration-300" :class="!open ? 'rotate-180 transform' : ''">
                            <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd"></path>
                        </svg>
                    </DisclosureButton>
                    <DisclosurePanel class="md:px-4 pb-2 pt-4 text-sm text-gray-500">
                        <CartAddresses :user="user" :cities="cities" :errors="errors"></CartAddresses>
                    </DisclosurePanel>
                </Disclosure>
                <Disclosure as="div" class="mt-2" v-slot="{ open }" :defaultOpen="billingHamburger">
                    <DisclosureButton @click="toggleBillingHamburger"
                        class="flex w-full justify-between rounded-lg bg-kb-light-grey px-4 py-2 text-left text-sm font-medium hover:bg-kbgreen/20 focus:outline-none focus-visible:ring focus-visible:ring-purple-500/75">
                        <p class="text-xl text-black font-bold font-santralregular font-bold">Ödeme Bilgisi</p>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="h-8 w-8 text-black transition-all duration-300" :class="!open ? 'rotate-180 transform' : ''">
                            <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd"></path>
                        </svg>
                    </DisclosureButton>
                    <DisclosurePanel class="md:px-4 pb-2 pt-4 text-sm text-gray-500">
                        <div class="flex flex-col-reverse md:flex-row justify-between my-5 items-center">
                            <p class="mt-2 md:mt-0 w-full md:w-1/2 text-lg md:text-2xl text-black font-santralextrabold whitespace-nowrap flex">
                                Kredi/Banka Kartı <span class="hidden lg:block font-santralextrabold pl-1.5">
                                    Bilgileri</span></p>

                            <span href="#" class="cursor-pointer w-full md:w-1/2 text-black hover:underline text-xs md:text-base font-santralregular lg:font-bold md:text-end mb-0 pb-0 whitespace-nowrap"
                                v-if="!showCCForm && user?.tckn_verified_at && cards?.length > 0" @click="showCCForm = true">Başka bir
                                kart ile ödeme yap</span>
                            <div class="w-full md:w-1/3 flex flex-col justify-center items-center" v-if="user?.tckn_verified_at && cards?.length == 0">
                                <img src="../../images/banka-ornek.png" class="w-full" />
                                <img src="../../images/logos-white.png" class="w-full" alt="" />
                            </div>
                            <div class="w-full md:w-1/3 flex flex-col justify-center items-center" v-if="showCCForm">
                                <img src="../../images/banka-ornek.png" class="w-full" />
                                <img src="../../images/logos-white.png" class="w-full" alt="" />
                            </div>
                        </div>
                        <div class="w-full" v-if="!showCCForm && user?.tckn_verified_at && cards?.length > 0">
                            <Splide class="w-full" :has-track="false" aria-label="" :options="{ rewind: true, perPage: 4, pagination: false, breakpoints: { 640: { perPage: 1 }, 768: { perPage: 2 }, 1100: { perPage: 3 } } }">
                                <SplideTrack>
                                    <SplideSlide class="px-1" v-for="(card, index) in cards" :key="index">
                                        <div class="transition-all w-full mr-2 mb-5 max-w-full ts:max-w-[256px] relative p-3 rounded-lg border-2 border-bordergray hover:bg-orange-200 cursor-pointer"
                                            :class="[selectedCC == card ? 'border-kbgreen bg-orange-100' : '']" @click="creditCardSelected(card)">
                                            <div class="flex flex-wrap relative z-40">
                                                <div class="w-full flex flex-wrap mb-6 justify-end">
                                                    <!--                                                    <img class="w-2/3" src="" alt="" />-->
                                                    <img class="w-1/3 pl-4" :src="`/images/${card.card_association}.png`" alt="" />
                                                </div>
                                                <div class="w-full text-right mx-auto">
                                                    <p class="font-santralregular text-xs text-right text-black mb-2 md:mb-1 lg:mb-0 2xl:mb-2 min-h-[16px]">
                                                        {{ card.holder }}
                                                    </p>
                                                    <p class="font-santralregular text-xs text-right text-black mb-2 md:mb-1 lg:mb-0 2xl:mb-2 min-h-[16px]">
                                                        {{ card.bin_number }} **** {{ card.number }}</p>
                                                    <p class="font-santralregular text-xs text-right text-black min-h-[16px]">
                                                        **/{{ card.year }}</p>
                                                </div>
                                                <!--                                                <div class="w-2/12 flex flex-col justify-around">-->
                                                <!--                                                    <div class="text-right w-full mt-0 ts:mt-3">-->
                                                <!--                                                        <img class="w-full" src="../../images/visa.png" alt="" />-->
                                                <!--                                                    </div>-->
                                                <!--                                                </div>-->
                                            </div>
                                        </div>
                                    </SplideSlide>
                                    <SplideSlide class="px-1" :key="-9999" v-if="tosla_active">
                                        <div class="transition-all w-full mr-2 max-w-[314px] ts:max-w-[256px] relative rounded-lg border-2 border-transparent cursor-pointer" :class="[selectedCC?.id == -9999 ? 'border-kbgreen' : '']"
                                            @click="creditCardSelected({ id: -9999 })">
                                            <picture>
                                                <source srcset="../../images/billing/tosla-card-desktop.png" type="image/png" />
                                                <img class="hidden lg:block w-[256px] p-0 m-0 min-h-[123px]" src="../../images/billing/tosla-card-desktop.png" />
                                            </picture>
                                            <picture>
                                                <source srcset="../../images/billing/tosla-card-mobile.png" type="image/png" />
                                                <img src="../../images/billing/tosla-card-mobile.png" class="block lg:hidden w-[256px] p-0 m-0" />
                                            </picture>
                                            <!--                                    <div class="transition-all w-full h-full mr-2 p-3 absolute top-0 left-0">-->
                                            <!--                                        <div class="flex relative z-40">-->
                                            <!--                                            <div class="w-full mx-auto">-->
                                            <!--                                                <p class="text-xs font-santralextrabold text-left text-white mt-2 md:mt-1 ts:mt-2">-->
                                            <!--                                                    TOSLA-->
                                            <!--                                                </p>-->
                                            <!--                                                <p class="text-xs text-left text-white mt-4 md:mt-3 lg:mt-3">-->
                                            <!--                                                    TOSLA HOLDER-->
                                            <!--                                                </p>-->
                                            <!--                                                <p class="text-xs text-left text-white mt-4 md:mt-3 lg:mt-3">**** **** ****</p>-->
                                            <!--                                                <p class="text-xs text-left text-white mt-7 md:mt-7 lg:mt-7">**/24</p>-->
                                            <!--                                            </div>-->
                                            <!--                                        </div>-->
                                            <!--                                    </div>-->
                                        </div>
                                    </SplideSlide>
                                </SplideTrack>
                                <div class="splide__arrows">
                                    <button class="splide__arrow splide__arrow--prev !-left-9">
                                        <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                            <path id="Path_18" data-name="Path 18"
                                                d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                                transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                        </svg>
                                    </button>
                                    <button class="splide__arrow splide__arrow--next !-right-9">
                                        <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                            <path id="Path_18" data-name="Path 18"
                                                d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                                transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                        </svg>
                                    </button>
                                </div>
                            </Splide>
                        </div>

                        <form class="mt-3" id="cc-form" v-if="(showCCForm && user?.tckn_verified_at) || (cards?.length == 0 && user?.tckn_verified_at)" @submit.prevent="submitCardSave">
                            <div class="mx-auto w-full md:w-2/3 p-4 border-2 border-bordergray rounded-2lg">
                                <div class="flex flex-col-reverse md:flex-row justify-between my-5 items-center">
                                    <p class="w-full md:w-1/2 text-xl text-black font-santralregular font-semibold mt-2 md:mt-0">Kart Bilgileri</p>
                                    <span href="#" class="cursor-pointer w-full md:w-1/2 text-black underline hover:text-kbgreen text-xs md:text-base font-santralregular md:font-bold md:text-end mb-0 pb-0 whitespace-nowrap"
                                        v-if="cards?.length != 0 && showCCForm" @click="showCCForm = false">Tanımlı kartımla devam et</span>
                                </div>
                                <div class="w-full text-center relative">
                                    <div class="relative group">
                                        <input id="fullname"
                                            class="peer w-full mb-5 rounded-lg hover:border-acordion-green group-hover:placeholder-white border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                            required type="text" placeholder="" name="holder" autofocus v-model="ccForm.holder" @keypress="preventNumericInput" />
                                        <label for="fullname"
                                            class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Ad
                                            Soyad*</label>
                                    </div>
                                    <div class="relative group">
                                        <input id="cardnumber" v-maska data-maska="#### #### #### ####"
                                            class="peer w-full mb-5 hover:border-acordion-green group-hover:placeholder-white rounded-lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                            required type="text" placeholder="" name="number" v-model="ccForm.number" />
                                        <label for="cardnumber"
                                            class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Kart
                                            Numarası*</label>
                                    </div>
                                    <div class="flex w-full justify-between pb-1">
                                        <label class="w-2/4 pl-1 text-left font-medium text-sm"> Son Kullanma Tarihi:
                                        </label>
                                        <label class="w-1/4 ml-5 text-left font-medium text-sm"> CVV : </label>
                                    </div>
                                    <div class="flex justify-between">
                                        <div class="relative group mr-4 flex space-x-3 w-2/4">
                                            <select name="month" id="month" required v-model="ccForm.month"
                                                class="hover:border-acordion-green group-hover:placeholder-white w-40 md:w-full mb-5 rounded-lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider">
                                                <option value="Ay" selected disabled>Ay</option>
                                                <option value="01">01</option>
                                                <option value="02">02</option>
                                                <option value="03">03</option>
                                                <option value="04">04</option>
                                                <option value="05">05</option>
                                                <option value="06">06</option>
                                                <option value="07">07</option>
                                                <option value="08">08</option>
                                                <option value="09">09</option>
                                                <option value="10">10</option>
                                                <option value="11">11</option>
                                                <option value="12">12</option>
                                            </select>
                                            <select name="year" id="year" required v-model="ccForm.year"
                                                class="hover:border-acordion-green group-hover:placeholder-white w-40 md:w-full mb-5 rounded-lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider">
                                                <option value="Ay" selected disabled>Yıl</option>
                                                <option value="2025">2025</option>
                                                <option value="2026">2026</option>
                                                <option value="2027">2027</option>
                                                <option value="2028">2028</option>
                                                <option value="2029">2029</option>
                                                <option value="2030">2030</option>
                                                <option value="2031">2031</option>
                                                <option value="2032">2032</option>
                                                <option value="2033">2033</option>
                                                <option value="2034">2034</option>
                                                <option value="2035">2035</option>
                                            </select>
                                        </div>
                                        <div class="relative group w-1/4">
                                            <input id="cvc" required type="number" name="cvc" v-model="ccForm.cvc" v-maska data-maska="####"
                                                class="w-full mb-5 rounded-lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white text-base tracking-wider" />
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full mt-0 relative">
                                    <div class="flex justify-center items-center w-full h-full">
                                        <button type="submit" :disabled="ccForm.processing"
                                            class="disabled:bg-checkoutgray relative block bg-kbgreen border-2 border-transparent text-white text-center rounded-2lg py-1 text-xl px-3 font-bold hover:bg-white hover:text-black hover:border-black transition-all">Kartı
                                            Kaydet
                                        </button>
                                        <!--                            <button class="w-full ts:w-auto bg-black text-white rounded-full py-2 px-5 text-base font-bold whitespace-nowrap" type="submit" :disabled="form.processing">Kartı Ekle</button>-->
                                    </div>
                                </div>
                                <div class="flex mt-4 w-full self-center justify-between" v-if="Object.entries(errors).length > 0">
                                    <div class="text-xs mx-auto text-kbdvred" v-for="error in errors">{{ error }}</div>
                                </div>
                            </div>
                        </form>

                        <form @submit.prevent="submitTCKN" v-if="user?.tckn_verified_at == null">
                            <div class="flex flex-wrap justify-start items-start w-full border border-kbred rounded-lg px-4 lg:px-8 py-4 lg:py-6">
                                <p class="w-full text-center relative text-lg font-santralregular font-semibold text-kbred pb-5 mb-5">
                                    Kiralamayı tamamlaman için kimlik doğrulama gerekli.
                                </p>
                                <div class="w-full text-center relative flex flex-wrap">
                                    <div class="relative group w-full md:w-6/12 px-1 md:px-3 mb-3">
                                        <input id="name"
                                            class="peer w-full mb-6 rounded-lg hover:border-kbgreen group-hover:placeholder-white border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                            type="text" placeholder="" name="name" v-model="form.first_name" required />
                                        <label for="name"
                                            class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-5 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Ad*</label>
                                    </div>
                                    <div class="relative group w-full md:w-6/12 px-1 md:px-3 mb-3">
                                        <input id="surname"
                                            class="peer w-full mb-6 rounded-lg hover:border-kbgreen group-hover:placeholder-white border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                            required type="text" placeholder="" name="holder" v-model="form.last_name" />
                                        <label for="surname"
                                            class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-5 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Soyad*</label>
                                    </div>
                                    <div class="relative group w-full md:w-6/12 px-1 md:px-3 mb-3">
                                        <input id="tckn" v-maska data-maska="###########"
                                            class="peer w-full mb-6 rounded-lg hover:border-kbgreen group-hover:placeholder-white border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                            required type="text" placeholder="" name="tckn" v-model="form.tckn" />
                                        <label for="tckn"
                                            class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-5 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">TC
                                            Kimlik No*</label>
                                    </div>
                                    <div class="relative group w-full md:w-6/12 px-1 md:px-3 mb-3">
                                        <Datepicker v-model="form.date_of_birth" locale="tr" :enable-time-picker="false" :format="`dd/MM/yyyy`" auto-apply required :max-date="new Date().setFullYear(new Date().getFullYear() - 18)"
                                            class="disabled:text-textgray peer w-full mb-5 rounded-lg hover:border-kbgreen group-hover:placeholder-white border border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider">
                                        </Datepicker>
                                        <label for="date_of_birth" class="transform transition-all absolute -top-5 left-5 px-1 pl-0 text-2xs text-black">Doğum Tarihi*</label>
                                    </div>
                                    <div class="mt-4 self-center justify-center flex" v-if="Object.entries(errors).length > 0">
                                        <div class="text-xs mx-auto text-kbdvred" v-for="error in errors">{{ error }}
                                        </div>
                                    </div>
                                    <div class="mt-4 self-center justify-center flex" v-if="validationMessage != ''">
                                        <div class="text-xs mx-auto text-kbdvred">{{ validationMessage }}</div>
                                    </div>

                                    <div class="relative group w-full flex justify-center">
                                        <button type="submit" class="bg-black text-white text-center rounded-full py-2 text-lg px-2 self-center font-santralextrabold w-full md:w-1/2 xl:w-1/3 hover:bg-kbgreen"
                                            :disabled="form.processing">Doğrula</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </DisclosurePanel>
                </Disclosure>
                <!--                <div class="w-full flex justify-center" v-if="Object.entries(errors).length > 0">-->
                <!--                    <div class="w-full md:w-2/4 text-xs text-center text-kbdvred font-sans" v-for="error in errors">{{ error }}</div>-->
                <!--                </div>-->
            </div>
            <div class="w-full lg:w-3/12 sticky top-4 h-fit z-10" id="checkout-payment-info" style="position: -webkit-sticky; position: sticky;">
                <CheckoutPaymentInfo :total="total" :products="products" :discount="discount_amount" :sub_total="sub_total" :insurance="insurance" :non_mass_payment_total="non_mass_payment_total" :is_mass_payment_enabled="is_mass_payment_enabled">
                    <template #checkoutButton>
                        <template v-if="showCCForm || cards?.length == 0">
                            <button :disabled="!mesafeliKiralama || !onBilgilendirme" type="submit" form="cc-form"
                                class="disabled:bg-checkoutgray w-full relative hidden lg:block bg-kbgreen border-2 border-transparent text-white text-center rounded-2lg py-2 lg:py-3 text-xl px-2 lg:px-4 font-bold hover:bg-white hover:text-black hover:border-black transition-all"
                                v-if="onBilgilendirme && mesafeliKiralama && auth.user?.address">
                                Ödeme Yap
                            </button>
                            <button :disabled="!mesafeliKiralama || !onBilgilendirme"
                                class="disabled:bg-checkoutgray w-full relative hidden lg:block bg-kbgreen border-2 border-transparent text-white text-center rounded-2lg py-2 lg:py-3 text-xl px-2 lg:px-4 font-bold hover:bg-white hover:text-black hover:border-black transition-all"
                                v-else>Ödeme Yap
                            </button>
                        </template>
                        <button :disabled="!mesafeliKiralama || !onBilgilendirme || user?.addresses.length == 0" type="button"
                            class="disabled:bg-checkoutgray w-full relative hidden lg:block bg-kbgreen border-2 border-transparent text-white text-center rounded-2lg py-2 lg:py-3 text-xl px-2 lg:px-4 font-bold hover:bg-white hover:text-black hover:border-black transition-all"
                            v-else @click="compeletePaymentWithRegisteredCard()">
                            Ödeme Yap
                        </button>
                        <div v-if="user?.addresses.length == 0 && formSubmitted" :disabled="!mesafeliKiralama || !onBilgilendirme" class="disabled:bg-checkoutgray mt-5 w-full text-center">Ödeme Yap</div>
                    </template>
                    <template #action>
                        <template v-if="showCCForm || cards?.length == 0">
                            <button :disabled="!mesafeliKiralama || !onBilgilendirme" type="submit" form="cc-form"
                                class="disabled:bg-checkoutgray w-full relative block bg-kbgreen border-2 border-transparent text-white text-center rounded-2lg py-2 lg:py-3 text-xl px-2 lg:px-4 font-bold hover:bg-white hover:text-black hover:border-black transition-all"
                                v-if="onBilgilendirme && mesafeliKiralama && auth.user?.address">
                                Ödeme Yap
                            </button>
                            <button :disabled="!mesafeliKiralama || !onBilgilendirme"
                                class="disabled:bg-checkoutgray w-full relative block bg-kbgreen border-2 border-transparent text-white text-center rounded-2lg py-2 lg:py-3 text-xl px-2 lg:px-4 font-bold hover:bg-white hover:text-black hover:border-black transition-all"
                                v-else>Ödeme Yap
                            </button>
                        </template>
                        <button :disabled="!mesafeliKiralama || !onBilgilendirme || user?.addresses.length == 0" type="button"
                            class="disabled:bg-checkoutgray w-full relative block bg-kbgreen border-2 border-transparent text-white text-center rounded-2lg py-2 lg:py-3 text-xl px-2 lg:px-4 font-bold hover:bg-white hover:text-black hover:border-black transition-all"
                            v-else @click="compeletePaymentWithRegisteredCard()">
                            Ödeme Yap
                        </button>
                        <div v-if="user?.addresses.length == 0 && formSubmitted" :disabled="!mesafeliKiralama || !onBilgilendirme" class="disabled:bg-checkoutgray mt-5 w-full text-center">Ödeme Yap</div>
                        <div v-if="errorText != ''" class="mt-5 w-full text-center">{{ errorText }}</div>

                        <div class="mt-5 border border-hopi-pink p-3 rounded text-sm w-full" v-if="hopi_bird">
                            <p>Hopi Bakiyeniz: {{ hopi_balance }} ₺</p>
                            <p v-if="selectedHopiCampaign">Hopi Seçili Kampanya: {{selectedHopiCampaign &&
                                hopi_campaigns.filter((x) =>
                                    x.code == selectedHopiCampaign)[0]?.name }}</p>
                            <p v-if="enteredHopiBalance > 0">Kullanılan Paracık: {{ enteredHopiBalance }}</p>
                        </div>
                    </template>
                    <template #aggrements>
                        <div class="border-2 rounded-lg py-2 lg:py-3 px-4 my-4">
                            <div class="w-full flex justify-start items-center mb-3">
                                <input v-model="onBilgilendirme" id="uyelik" class="cursor-pointer w-4 h-4 border-3 rounded-md focus:outline-none checked:bg-black focus:bg-black" type="checkbox" required />
                                <label class="cursor-pointer text-sm text-left text-checkoutgray pl-2">
                                    <span class="underline hover:text-gray-600 transition-all ease-in-out" @click="openModal">Ön bilgilendirme formu</span>
                                    ve <span class="underline hover:text-gray-600 transition-all ease-in-out" @click="openModal2">Mesafeli kiralama sözleşmesini</span> kabul ediyorum.
                                </label>
                            </div>
                        </div>
                    </template>
                </CheckoutPaymentInfo>
            </div>
        </section>
    </main>
    <Dialog :open="isCardCreated" @close="closeCardModal" class="relative z-50">
        <!-- The backdrop, rendered as a fixed sibling to the panel container -->
        <div class="fixed inset-0 bg-black/30" aria-hidden="true" />

        <!-- Full-screen scrollable container -->
        <div class="fixed inset-0 overflow-y-auto">
            <!-- Container to center the panel -->
            <div class="flex min-h-full items-center justify-center p-4">
                <!-- The actual dialog panel -->
                <DialogPanel class="relative w-full max-w-[350px] md:max-w-lg p-4 ts:p-6 rounded bg-white max-h-[65vh] overflow-y-scroll">
                    <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeCardModal">x</div>
                    <div class="flex flex-col justify-center items-center w-full">
                        <svg fill="#70d44b" height="80px" width="80px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 191.667 191.667" xml:space="preserve">
                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                            <g id="SVGRepo_iconCarrier">
                                <path
                                    d="M95.833,0C42.991,0,0,42.99,0,95.833s42.991,95.834,95.833,95.834s95.833-42.991,95.833-95.834S148.676,0,95.833,0z M150.862,79.646l-60.207,60.207c-2.56,2.56-5.963,3.969-9.583,3.969c-3.62,0-7.023-1.409-9.583-3.969l-30.685-30.685 c-2.56-2.56-3.97-5.963-3.97-9.583c0-3.621,1.41-7.024,3.97-9.584c2.559-2.56,5.962-3.97,9.583-3.97c3.62,0,7.024,1.41,9.583,3.971 l21.101,21.1l50.623-50.623c2.56-2.56,5.963-3.969,9.583-3.969c3.62,0,7.023,1.409,9.583,3.969 C156.146,65.765,156.146,74.362,150.862,79.646z">
                                </path>
                            </g>
                        </svg>
                        <div class="text-black font-lg leading-tight mt-5 text-center">Kartınız kaydoldu, siparişinizi tamamlayabilirsiniz.</div>
                    </div>
                </DialogPanel>
            </div>
        </div>
    </Dialog>
    <!--    Ön Bilgilendirme Formu-->
    <Dialog :open="isOpen" @close="closeModal" class="relative z-50">
        <!-- The backdrop, rendered as a fixed sibling to the panel container -->
        <div class="fixed inset-0 bg-black/30" aria-hidden="true" />

        <!-- Full-screen scrollable container -->
        <div class="fixed inset-0 overflow-y-auto">
            <!-- Container to center the panel -->
            <div class="flex min-h-full items-center justify-center p-4">
                <!-- The actual dialog panel -->
                <DialogPanel class="relative w-full max-w-[305px] md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl p-4 ts:p-6 rounded bg-white max-h-[65vh] overflow-y-scroll">
                    <DialogTitle :class="'mb-5'">ÖN BİLGİLENDİRME FORMU</DialogTitle>
                    <OnBilgilendirmeFormu :products="products" :user="user"></OnBilgilendirmeFormu>
                    <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal">x</div>
                </DialogPanel>
            </div>
        </div>
    </Dialog>
    <!--    Mesafeli Kiralama Sözleşmesi-->
    <Dialog :open="isOpen2" @close="closeModal2" class="relative z-50">
        <!-- The backdrop, rendered as a fixed sibling to the panel container -->
        <div class="fixed inset-0 bg-black/30" aria-hidden="true" />

        <!-- Full-screen scrollable container -->
        <div class="fixed inset-0 overflow-y-auto">
            <!-- Container to center the panel -->
            <div class="flex min-h-full items-center justify-center p-4">
                <!-- The actual dialog panel -->
                <DialogPanel class="relative w-full max-w-[305px] md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl p-4 ts:p-6 rounded bg-white max-h-[65vh] overflow-y-scroll">
                    <DialogTitle :class="'mb-5'">MESAFELİ KİRALAMA SÖZLEŞMESİ</DialogTitle>
                    <MesafeliKiralamaSozlesmesi :products="products" :user="user"></MesafeliKiralamaSozlesmesi>
                    <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal2">x</div>
                </DialogPanel>
            </div>
        </div>
    </Dialog>
</template>
