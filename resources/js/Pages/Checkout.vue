<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import CheckoutPaymentInfo from "@/Components/CheckoutPaymentInfo.vue";
import HopiModal from "@/Pages/Shared/HopiModal.vue";
import PegasusModal from "@/Pages/Shared/PegasusModal.vue";
import coupons from "@/Pages/Coupons.vue";
import HopiCampaignModal from "@/Pages/Shared/HopiCampaignModal.vue";
import { Popover, PopoverButton, PopoverPanel } from "@headlessui/vue";

export default {
    computed: {
        coupons() {
            return coupons;
        }
    },
    components: {
        PegasusModal,
        HopiModal,
        HopiCampaignModal,
        Link,
        Head,
        UserMenu,
        Popover,
        PopoverButton,
        PopoverPanel,
        CheckoutPaymentInfo
    },
    data() {
        return {
            selectedItems: [],
            form: this.$inertia.form({
                coupon: ""
            }),
            isHopiModelOpen: false,
            isPegasusModalOpen: false,
            isHopiCampaignModelOpen: false,
            selectedHopiCampaign: localStorage.getItem("selectedHopiCampaign"),
            enteredHopiBalance: localStorage.getItem("enteredHopiBalance") ?? 0,
            is_mass_payment_local_enable: false
        };
    },
    props: {
        products: Array,
        total: Number,
        discount_amount: Number,
        sub_total: Number,
        insurance: Number,
        errors: { type: Object, default: false },
        coupon: { type: Object, default: false },
        hopi_campaigns: { type: Object, default: false },
        hopi_bird: { type: Number, default: 0 },
        hopi_balance: { type: Number, default: 0 },
        non_mass_payment_total: { type: Number, default: 0 },
        hopi_active: { type: Boolean, default: false },
        pgs_active: { type: Boolean, default: false },
        is_mass_payment_enabled: { type: Boolean, default: false }
    },
    layout: Layout,
    mounted() {
        this.is_mass_payment_local_enable = this.is_mass_payment_enabled;
    },
    methods: {
        submitCoupon() {
            this.form.post("coupon");
        },
        itemSelected(itemId, monthId) {
            if (this.selectedItems == null) {
                this.selectedItems = [];
            }

            if (this.selectedItems.filter((item) => item.product_id == itemId && item.month_id == monthId).length > 0) this.selectedItems = this.selectedItems.filter((item) => item.product_id != itemId || item.month_id != monthId);
            else this.selectedItems.push({ product_id: itemId, month_id: monthId });

            // if (this.selectedItems.indexOf(combined_id) > -1) {
            //     this.selectedItems.splice(this.selectedItems.indexOf(combined_id), 1);
            // } else {
            //     this.selectedItems.push(combined_id: { product_id: itemId, month_id: monthId});
            // }
            // console.log(this.selectedItems);
        },
        selectAll() {
            this.products.map((item) => {
                this.itemSelected(item.product_id, item.month_id);
            });
        },
        closeHopi(value) {
            console.log("closeHopi", value);
            this.isHopiModelOpen = false;
        },
        closePegasusModal(value) {
            this.isPegasusModalOpen = false;
        },
        closeHopiCampaign(value) {
            console.log("closeHopiCampaign", value);
            this.isHopiCampaignModelOpen = false;
        },
        changeSelectedHopiCampaign(value) {
            console.log("selectedHopiCampaign", value);
            this.selectedHopiCampaign = value;
        },
        updateEnteredBalance(value) {
            console.log("enteredHopiBalance", value);
            this.enteredHopiBalance = value;
        },
        clearSelected() {
            let removedProducts = [];
            removedProducts = this.products.filter((item) => {
                return this.selectedItems.filter((item2) => {
                    return item2.product_id == item.product_id && item2.month_id == item.month_id;
                });
            });

            this.selectedItems = [];

            console.log(removedProducts);

            let removedCartProducts = [];
            removedProducts.map((item, keys) => {
                let product = {};
                product.item_id = item.id;
                product.item_name = item.product.attribute_data.name.tr;
                product.price = item.total;
                //product.item_brand = item.brand.name;
                //product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                //product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                //product.item_color = item.variants[0]?.options[0] ?? ""; // Kategori verisidne yok
                //product.item_list_id = "HOME-1";
                //product.item_list_name = "Homepage - Kampanya Ürünleri List";
                product.item_variant = item.month?.name;
                product.index = keys;
                product.quantity = item.quantity;
                removedCartProducts.push(product);
            });

            // Sum removed products price
            let removedProductsPrice = 0;
            removedProducts.map((item) => {
                removedProductsPrice += item.total;
            });

            dataLayer.push({
                event: "remove_from_cart",
                ecommerce: {
                    currency: "TRY",
                    value: removedProductsPrice,
                    items: removedCartProducts
                }
            });
        },
        getMassPaymentDiscount(subscriptionMonth) {
            let discount = 0;
            switch (subscriptionMonth) {
                case 3:
                    discount = 10;
                    break;
                case 4:
                    discount = 15;
                    break;
                case 5:
                    discount = 20;
                    break;
                default:
                    discount = 20;
            }
            return discount;
        },
        monthChanged(productId, mountId) {
            this.$inertia.post(
                "/sepetden-kaldir",
                { itemIds: [{ product_id: productId.product_id, month_id: productId.month_id }] },
                {
                    preserveState: true,
                    preserveScroll: true,
                    onSuccess: () => {
                        this.$inertia.post("/sepete-ekle", {
                            product_id: productId.product.product_id,
                            month: mountId,
                            product_variant_id: productId.product_id
                        });
                    }
                }
            );
        },
        insuranceChanged(cartLine, monthId) {
            cartLine.is_insurance_requested = !cartLine.is_insurance_requested;
            this.$inertia.post(
                "/sepet-guncelle",
                {
                    cart_item_id: cartLine.id,
                    month: monthId,
                    insurance: cartLine.is_insurance_requested
                },
                {
                    preserveState: true,
                    preserveScroll: true
                }
            );
        },
        massPaymentChanged() {
            this.$inertia.post(
                "/sepet-toplu-odeme",
                {
                    is_mass_payment_enabled: this.is_mass_payment_local_enable
                },
                {
                    preserveState: true,
                    preserveScroll: true
                }
            );
        },
        checkEligibleForInsurance(cartLine) {
            const EligibleCategories = [47, 49, 54, 84];
            let status = cartLine.categories.filter((x) => EligibleCategories.includes(x));
            return status.length > 0;
        },
        paymentStep() {
            let cartProducts = [];
            this.products.map((item) => {
                let product = {};
                product.item_id = item.id;
                product.item_name = item.product.attribute_data.name.tr;
                product.price = item.total;
                // product.item_brand = item.product.brand.name;
                // product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                // product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                // (product.item_color = item.variants[0]?.options[0] ?? ""), (product.index = 0);
                product.discount = 0;
                product.item_variant = item.month?.name;
                product.quantity = item.quantity;
                cartProducts.push(product);
            });

            dataLayer.push({
                event: "begin_checkout",
                ecommerce: {
                    currency: "TRY",
                    value: this.total,
                    items: cartProducts
                }
            });
        },
        clearHopiSelections() {
            console.log("clearHopiSelections");
            this.selectedHopiCampaign = "";
            this.enteredHopiBalance = 0;
        }
    },
    created() {
        this.$inertia.on("navigate", (event) => {
            console.log("sepet navigate", event.detail.page.url);

            let cartProducts = [];
            this.products.map((item) => {
                let product = {};
                //product.item_id = item.id; cart_item_id idi
                product.item_id = item.product_id; // varyant id yaptım
                //product.product_id = item.product_id; // varyant id idi
                product.product_id = item.product.product_id; // ürün id yaptım
                product.item_name = item.product.attribute_data.name.tr;
                product.price = item.total;
                // product.item_brand = item.product.brand.name;
                // product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                // product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                // (product.item_color = item.variants[0]?.options[0] ?? ""), (product.index = 0);
                product.discount = 0;
                product.item_variant = item.month?.name;
                product.quantity = item.quantity;
                cartProducts.push(product);
            });

            if (event.detail.page.url.startsWith("/sepetim")) {
                dataLayer.push({
                    event: "view_cart",
                    ecommerce: {
                        currency: "TRY",
                        value: this.total,
                        items: cartProducts
                    }
                });
            }
        });
    },
    watch: {
        selectedHopiCampaign: function(val) {
            localStorage.setItem("selectedHopiCampaign", val);
        },
        enteredHopiBalance: function(val) {
            localStorage.setItem("enteredHopiBalance", val);
        },
        hopi_campaigns: function(val) {
            // console.log("hopi_campaigns", val);
            if (this.hopi_campaigns.length > 0) {
                this.isHopiModelOpen = false;
                this.isHopiCampaignModelOpen = true;
            }
            //if (val) this.isHopiCampaignModelOpen = true;
            //localStorage.setItem("isHopiCampaignModelOpen", val);
        }
    }
};
</script>

<template>

    <Head title="Sepetim" />

    <main class="my-3 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-4 mx-auto">
        <section class="flex justify-center items-center">
            <p class="px-2 py-1 font-bold text-base border-2 border-kbgreen rounded-full">Teslimat</p>
            <svg xmlns="http://www.w3.org/2000/svg" width="142.787" height="1" viewBox="0 0 142.787 1">
                <path id="Path_2907" data-name="Path 2907" d="M10647.213,14347.5H10790" transform="translate(-10647.213 -14347)" fill="none" stroke="#d0d0d0" stroke-width="1" stroke-dasharray="5" />
            </svg>
            <p class="px-2 py-1 font-bold text-base text-textgray border-2 border-textgray rounded-full">Ödeme</p>
        </section>
        <section class="flex w-full justify-center md:justify-between">
            <div class="w-full md:w-12/12 lg:w-5/12 flex flex-wrap md:flex-nowrap justify-center lg:justify-between items-end mt-8 lg:mt-0 border-b-none lg:border-b-3 border-kbgreen">
                <div class="w-full md:w-8/12 lg:w-9/12 flex border-b-3 md:border-none border-kbgreen pb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="35.411" height="36.531" viewBox="0 0 35.411 36.531">
                        <g id="Group_3799" data-name="Group 3799" transform="translate(1.009 1)">
                            <path id="Path_2958" data-name="Path 2958"
                                  d="M217.281,222.6H200.824c-2.7,0-4.985-1.356-5.329-3.167L192.4,203.145c-.413-2.17,2.091-4.091,5.329-4.091h22.644c3.239,0,5.742,1.921,5.33,4.091l-3.094,16.284C222.266,221.239,219.984,222.6,217.281,222.6Z"
                                  transform="translate(-192.356 -188.064)" fill="none" stroke="#343434" stroke-miterlimit="10" stroke-width="2" />
                            <path id="Path_2959" data-name="Path 2959" d="M202.477,194.2l6.319-9.652c1.152-1.759,2.773-1.786,3.94-.063l6.581,9.715" transform="translate(-194.015 -183.208)" fill="none" stroke="#343434" stroke-miterlimit="10"
                                  stroke-width="2" />
                        </g>
                    </svg>
                    <h3 class="pl-2 text-2xl ts:text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap">Sepetim ({{ products.length }} ürün)</h3>
                </div>
                <p class="w-full md:w-4/12 lg:w-5/12 md:text-right text-sm font-medium text-black pt-4 md:pt-0 ts:pb-1">
                    <Link href="/kategoriler/tum-urunler"><u>Kiralamaya devam et</u></Link>
                </p>
            </div>
            <div class="w-5/12 ts:w-4/12 hidden lg:flex justify-between flex-col" v-if="products.length > 0">
                <form class="w-full flex items-center justify-between border-b-3 border-black pb-1" @submit.prevent="submitCoupon">
                    <input class="rounded-md border-none placeholder:text-base placeholder:text-placeholdergray focus:border-none focus:outline-none focus:ring-white text-lg tracking-wide placeholder:font-kiralabunuthin" required type="text"
                           placeholder="Kupon Kodu" name="couponCode" v-model="form.coupon" :disabled="is_mass_payment_local_enable" />
                    <button class="bg-textgray text-white rounded-full py-2 px-4 self-center text-lg font-bold" type="submit" :disabled="form.processing">Kupon Ekle</button>
                </form>
                <!--                <div class="flex mt-4 self-center justify-center" v-if="!errors.status">-->
                <!--                    <div class="text-xs mx-auto text-red-900">{{ errors.message }}</div>-->
                <!--                </div>-->

                <div class="flex mt-4 self-center justify-center flex-col" v-if="Object.entries(errors).length > 0">
                    <div class="flex text-sm text-kbred mr-[2px] tracking-normal" v-for="error in errors">{{ error }}</div>
                </div>
                <div class="flex mt-4 self-center justify-center flex-col" v-if="is_mass_payment_local_enable">
                    <div class="flex text-sm text-kbred mr-[2px] tracking-normal">Toplu ödemede kupon kullanılamaz</div>
                </div>
            </div>
        </section>
        <div class="w-full md:w-10/12 flex justify-start items-center mt-2 md:ml-8 lg:ml-0" v-if="products.length > 0">
            <label @click="selectAll()" class="text-sm lg:text-base text-kbgray pr-2" for="ayni"> Hepsini Seç </label>
            <input id="ayni" class="w-4 h-4 border-3 border-kbgray rounded-md checked:bg-black focus:ring-black dark:focus:ring-black dark:ring-black focus:ring-2 text-black" type="checkbox" @click="selectAll()" />
            <p class="text-sm lg:text-base text-kbgray pl-5">
                <Link href="/sepetden-kaldir" :data="{ itemIds: selectedItems }" method="post" as="button" @click="clearSelected()">
                    <u>Seçilenleri Sil</u>
                </Link>
            </p>
        </div>
        <section class="mt-2 mb-12 flex flex-col lg:flex-row" v-if="products.length > 0">
            <!--            <div class="w-full lg:w-9/12 pr-0 lg:pr-4 px-0 flex flex-col justify-between">-->
            <div class="w-full lg:w-9/12 pr-0 lg:pr-4 px-0">
                <div class="flex flex-wrap md:flex-nowrap lg:flex-wrap relative" v-for="product in products" :key="`${product.product.id}_${product.month_id}`">
                    <div class="w-full md:w-full flex flex-wrap md:flex-nowrap lg:flex-wrap border-3 border-bordergray rounded-lg p-2 lg:p-3 mb-5">
                        <div class="w-2/12 md:w-2/12 lg:w-1/12 flex items-center">
                            <div class="flex justify-center items-center w-full md:w-18 ts:w-18 h-full md:h-20 shadow-lg bg-white rounded-lg">
                                <picture class="w-10 md:w-16">
                                    <source :srcset="product.product.imagesWebP?.thumb_webp" type="image/webp" />
                                    <source :srcset="product.product.imagesWebP?.zoom_webp" type="image/jpeg" />
                                    <img :src="product.product.imagesWebP?.thumb" alt="Alt Text!" />
                                </picture>
                            </div>
                        </div>
                        <div class="w-10/12 md:w-11/12 flex flex-wrap md:flex-nowrap">
                            <div class="w-full md:w-3/12 lg:w-5/12 flex md:block pl-3 ts:pl-4 border-r-none md:border-r-2 border-bordergray">
                                <div class="font-kiralabunuthin text-xs lg:text-sm text-black font-normal pb-2 whitespace-nowrap">Ürün Adı:</div>
                                <div class="pl-2 md:pl-0 text-xs md:text-sm ts:text-base text-black font-bold">
                                    {{ product.product.attribute_data.name.tr }}
                                </div>
                            </div>
                            <div class="w-6/12 md:w-3/12 pl-3 pr-1 border-r-none md:border-r-2 border-bordergray pt-2 md:pt-0">
                                <div class="font-kiralabunuthin text-xs md:text-sm text-black font-normal pb-1 ts:pb-2 tracking-tight">Kiralama Süresi:</div>
                                <select
                                    class="w-full mts:w-24 px-2 py-0 mr-0 md:mr-1 lg:mr-4 rounded-2lg border-2 lg:border-3 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-xs md:text-sm ts:text-base tracking-wider"
                                    name="sirala" id="sirala" v-model="product.month.id" @change="monthChanged(product, product.month.id)">
                                    <option :value="month.id" v-for="month in product.product.monthOptions" :key="month.id">{{ month.name }}</option>
                                </select>
                            </div>
                            <div class="w-6/12 md:w-2/12 lg:w-1/12 pl-2 pr-1 border-r-none md:border-r-2 border-bordergray pt-2 md:pt-0">
                                <div class="font-kiralabunuthin text-xs md:text-sm text-black font-normal pb-2">Sigorta:</div>
                                <div class="text-xs md:text-sm ts:text-base text-black font-bold lg:text-center" v-if="checkEligibleForInsurance(product)">
                                    <input @change="insuranceChanged(product, product.month.id)" :checked="product.is_insurance_requested" class="w-5 h-5 border-3 rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                                </div>
                            </div>
                            <div class="w-6/12 md:w-3/12 lg:w-2/12 pl-3 md:pl-2 border-r-none md:border-r-2 border-bordergray mt-2 md:mt-0">
                                <div class="font-kiralabunuthin text-xs lg:text-sm text-black font-normal ts:pb-2">Tutar:</div>
                                <div class="text-xs md:text-sm ts:text-base text-black font-bold leading-tight align-middle pt-[3px] md:pt-2 ts:pt-0">{{ product.total }} TL/<span class="text-xs">Ay</span></div>
                            </div>
                            <div class="w-6/12 md:w-3/12 lg:w-2/12 items-center md:block pl-2 mt-2 md:mt-0">
                                <div class="font-kiralabunuthin text-xs lg:text-sm text-black font-normal ts:pb-2 tracking-tight">Tahmini Teslim:</div>
                                <div class="text-xs md:text-sm ts:text-base text-black font-bold leading-tight align-middle pt-[3px] md:pt-2 ts:pt-0">{{ product.product.min_delivery_time }}-{{ product.product.max_delivery_time }} İş Günü</div>
                            </div>
                            <div class="hidden lg:flex w-1/12 pl-3 items-center justify-end">
                                <input @change="itemSelected(product.product_id, product.month_id)" :checked="selectedItems.filter((i) => i.product_id === product.product_id && i.month_id === product.month_id).length > 0"
                                       class="w-5 h-5 border-3 rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            </div>
                        </div>
                    </div>
                    <div class="block lg:hidden w-1/12 absolute top-1/3 -right-7 md:w-auto px-1 flex items-center justify-end">
                        <input @change="itemSelected(product.product_id, product.month_id)" :checked="selectedItems.filter((i) => i.product_id === product.product_id && i.month_id === product.month_id).length > 0"
                               class="w-5 h-5 border-3 rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                    </div>
                </div>

                <div class="mb-4 bg-[#f4fbf1] py-2 px-2 md:px-4 rounded-lg md:rounded-2lg" v-if="false && products.length == 1 && products[0].month_id >= 3">
                    <div class="text-xs md:text-base text-black leading-none flex flex-col-reverse md:flex-row justify-between items-center">
                        <div class="w-full">
                            <input type="checkbox" id="massPayment" @change="massPaymentChanged" v-model="is_mass_payment_local_enable"
                                   class="cursor-pointer bg-green-50 border-2 w-5 h-5 md:w-6 md:h-6 text-kbgreen border-black rounded focus:ring-kbgreen" />
                            <label for="massPayment" class="cursor-pointer leading-loose ml-2">
                                Toplu öde %{{ this.getMassPaymentDiscount(products[0].month_id) }} indirimle {{ (non_mass_payment_total * products[0].month.value).toFixed(2) }} TL yerine toplam {{ ((non_mass_payment_total / 100) * (100 -
                                this.getMassPaymentDiscount(products[0].month_id)) * products[0].month.value).toFixed(2) }} TL</label>
                        </div>
                        <div class="flex items-center justify-between w-full md:w-auto">
                            <div class="text-lg whitespace-nowrap line-through mr-2">{{ non_mass_payment_total }} TL</div>
                            <div class="text-lg whitespace-nowrap">{{ ((non_mass_payment_total / 100) * (100 - this.getMassPaymentDiscount(products[0].month_id))).toFixed(2) }} TL / Ay</div>
                            <Popover v-slot="{ open }" class="relative">
                                <PopoverButton :class="open ? 'text-white' : 'text-white/90'" class="group inline-flex items-center px-3 py-2 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75">
                                    <svg id="question-circle-fill" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 md:w-6 md:h-6" viewBox="0 0 14.141 14.141">
                                        <path id="Path_92" data-name="Path 92"
                                              d="M14.141,7.07A7.07,7.07,0,1,1,7.07,0a7.07,7.07,0,0,1,7.07,7.07ZM5.807,5.332H4.64A2.168,2.168,0,0,1,7.076,3.093c1.235,0,2.362.645,2.362,1.98a2.157,2.157,0,0,1-1.1,1.818c-.651.494-.892.679-.892,1.314v.314H6.29l-.006-.409A2.014,2.014,0,0,1,7.316,6.353c.521-.392.853-.65.853-1.212A1.027,1.027,0,0,0,7.007,4.109a1.128,1.128,0,0,0-1.2,1.223Zm1.106,5.694a.828.828,0,1,1,0-1.65.829.829,0,1,1,0,1.65Z"
                                              transform="translate(0 0)" fill="#ace697" fill-rule="evenodd" />
                                    </svg>
                                </PopoverButton>

                                <transition enter-active-class="transition duration-200 ease-out" enter-from-class="translate-y-1 opacity-0" enter-to-class="translate-y-0 opacity-100" leave-active-class="transition duration-150 ease-in"
                                            leave-from-class="translate-y-0 opacity-100" leave-to-class="translate-y-1 opacity-0">
                                    <PopoverPanel class="absolute left-0 z-10 mt-3 w-screen max-w-sm -translate-x-3/4 -translate-y-full px-4 sm:px-0 z-[99999]">
                                        <div class="overflow-hidden rounded-lg shadow-lg ring-1 ring-black/5 border-2 border-kbgreen">
                                            <div class="bg-gray-50 p-4">

                                                <span class="block text-sm text-gray-500"> kiraladığınız sürenin toplam kira bedeli %{{ this.getMassPaymentDiscount(products[0].month_id) }} indirim ile tek seferde tahsil edilecektir. Böylelikle
                                                    kiralama süren bitene kadar kira bedeli tahsil edilmez. </span>
                                            </div>
                                        </div>
                                    </PopoverPanel>
                                </transition>
                            </Popover>
                        </div>
                    </div>
                </div>
                <div class="mb-4 px-4 lg:px-6 py-2 bg-[#FCF3EC] rounded-2lg mt-2" v-if="false">
                    <div class="flex justify-start items-center"><img class="w-[30px] h-[30px]" src="../../images/svg/discount.svg" alt="">
                        <div class="ml-4 text-sm mt-1 ts:mt-0 2xl:mt-1">Yeni üyelere özel 6 ay ve üzeri kiralamaların ilk ayında 500 tl indirim Kiralabunu’dan hediye! İndirim kodu: merhaba500</div>
                    </div>
                </div>

                <div class="flex flex-wrap lg:justify-start" v-if="false">
                    <div class="relative w-full md:w-6/12 lg:w-3/12 mt-3 flex pl-4">
                        <div class="rounded-lg relative w-10/12 flex flex-col items-center justify-between p-2 bg-orange-200 border-dashed border-r-2 border-red-600">
                            <p class="p-0 text-sm font-bold text-center">applekirala</p>
                            <p class="relative text-2xs text-center" title="6 ay ve üzeri Apple kiralamalarında ilk 2 ay %50 indirimli">6 ay ve üzeri Apple kiralamalarında ilk 2 ay %50 indirimli</p>
                            <button class="transition-all delay-100 bg-[#ff6c37] text-white text-sm py-1 px-2 rounded hover:opacity-[90%] whitespace-nowrap leading-tight font-santralextrabold">Sepete ekle</button>
                            <div class="absolute w-5 h-5 rounded-full -right-3 -top-3 bg-white"></div>
                            <div class="absolute w-5 h-5 rounded-full -right-3 -bottom-3 bg-white"></div>
                        </div>
                        <div class="rounded-lg relative w-2/12 flex flex-col items-center justify-center px-2 pt-5 pb-2 bg-orange-200">
                            <div class="absolute w-5 h-5 rounded-full -left-3 -top-3 bg-white"></div>
                            <div class="absolute w-5 h-5 rounded-full -left-3 -bottom-3 bg-white"></div>
                        </div>
                    </div>
                    <div class="relative w-full md:w-6/12 lg:w-3/12 mt-3 flex pl-4">
                        <div class="rounded-lg relative w-10/12 flex flex-col items-center justify-between p-2 bg-orange-200 border-dashed border-r-2 border-red-600">
                            <p class="p-0 text-sm font-bold text-center">applekirala</p>
                            <p class="relative text-2xs text-center">6 ay ve üzeri Apple kiralamalarında ilk 2 ay %50 indirimli</p>
                            <button class="transition-all delay-100 bg-[#ff6c37] text-white text-sm py-1.5 px-2 rounded hover:opacity-[90%] whitespace-nowrap leading-tight font-santralextrabold">Sepete ekle</button>
                            <div class="absolute w-5 h-5 rounded-full -right-3 -top-3 bg-white"></div>
                            <div class="absolute w-5 h-5 rounded-full -right-3 -bottom-3 bg-white"></div>
                        </div>
                        <div class="rounded-lg relative w-2/12 flex flex-col items-center justify-center px-2 pt-5 pb-2 bg-orange-200">
                            <div class="absolute w-5 h-5 rounded-full -left-3 -top-3 bg-white"></div>
                            <div class="absolute w-5 h-5 rounded-full -left-3 -bottom-3 bg-white"></div>
                        </div>
                    </div>
                    <div class="relative w-full md:w-6/12 lg:w-3/12 mt-3 flex pl-4">
                        <div class="rounded-lg relative w-10/12 flex flex-col items-center justify-between p-2 bg-orange-200 border-dashed border-r-2 border-red-600">
                            <p class="p-0 text-sm font-bold text-center">applekirala</p>
                            <p class="relative text-2xs text-center">6 ay ve üzeri Apple kiralamalarında ilk 2 ay %50 indirimli</p>
                            <button class="transition-all delay-100 bg-[#ff6c37] text-white text-sm py-1.5 px-2 rounded hover:opacity-[90%] whitespace-nowrap leading-tight font-santralextrabold">Sepete ekle</button>
                            <div class="absolute w-5 h-5 rounded-full -right-3 -top-3 bg-white"></div>
                            <div class="absolute w-5 h-5 rounded-full -right-3 -bottom-3 bg-white"></div>
                        </div>
                        <div class="rounded-lg relative w-2/12 flex flex-col items-center justify-center px-2 pt-5 pb-2 bg-orange-200">
                            <div class="absolute w-5 h-5 rounded-full -left-3 -top-3 bg-white"></div>
                            <div class="absolute w-5 h-5 rounded-full -left-3 -bottom-3 bg-white"></div>
                        </div>
                    </div>
                    <div class="relative w-full md:w-6/12 lg:w-3/12 mt-3 flex pl-4">
                        <div class="rounded-lg relative w-10/12 flex flex-col items-center justify-between p-2 bg-orange-200 border-dashed border-r-2 border-red-600">
                            <p class="p-0 text-sm font-bold text-center">applekirala</p>
                            <p class="relative text-2xs text-center">6 ay ve üzeri Apple kiralamalarında ilk 2 ay %50 indirimli</p>
                            <button class="transition-all delay-100 bg-[#ff6c37] text-white text-sm py-1.5 px-2 rounded hover:opacity-[90%] whitespace-nowrap leading-tight font-santralextrabold">Sepete ekle</button>
                            <div class="absolute w-5 h-5 rounded-full -right-3 -top-3 bg-white"></div>
                            <div class="absolute w-5 h-5 rounded-full -right-3 -bottom-3 bg-white"></div>
                        </div>
                        <div class="rounded-lg relative w-2/12 flex flex-col items-center justify-center px-2 pt-5 pb-2 bg-orange-200">
                            <div class="absolute w-5 h-5 rounded-full -left-3 -top-3 bg-white"></div>
                            <div class="absolute w-5 h-5 rounded-full -left-3 -bottom-3 bg-white"></div>
                        </div>
                    </div>
                </div>
            </div>

            <CheckoutPaymentInfo :total="total" :products="products" :discount="discount_amount" :sub_total="sub_total" :coupon="coupon" :insurance="insurance" :non_mass_payment_total="non_mass_payment_total"
                                 :is_mass_payment_enabled="is_mass_payment_enabled" :is_mass_payment_local_enable="is_mass_payment_local_enable">
                <template #action>
                    <Link href="/odeme" class="bg-black text-white text-center rounded-full py-2 lg:py-3 text-lg px-2 lg:px-4 self-center font-bold w-full hover:bg-kbgreen" @click="paymentStep"> Ödemeye Geç</Link>
                    <div class="mt-5 cursor-pointer" @click="isHopiModelOpen = true" v-if="hopi_active && hopi_bird === null && hopi_campaigns.length == 0">
                        <img src="/images/hopi/button.png" alt="" />
                    </div>
                    <div class="mt-5 border border-hopi-pink p-3 rounded text-sm w-full" v-if="hopi_active && hopi_bird && hopi_campaigns && hopi_campaigns.length > 0">
                        <p>Hopi Bakiyeniz: {{ hopi_balance }} ₺</p>
                        <!--                        <p>Hopi Seçili Kampanya: {{ selectedHopiCampaign }} ₺</p>-->
                        <p v-if="selectedHopiCampaign">Hopi Seçili Kampanya: {{ selectedHopiCampaign && hopi_campaigns.filter((x) => x.code == selectedHopiCampaign)[0]?.name }}</p>
                        <p v-if="enteredHopiBalance > 0">Kullanılan Paracık: {{ enteredHopiBalance }}</p>
                        <!--                        <p v-if="hopi_balance > 0" class="mt-2"><input class="p-1" type="text" name="balance" id="" v-model="enteredHopiBalance" /></p>-->
                        <!--                        <p v-if="hopi_balance > 0" class="text-sm mt-2">Kullanmak istediğiniz hopi bakiyenizi giriniz</p>-->
                        <div class="flex justify-around items-center mt-3">
                            <div @click="isHopiCampaignModelOpen = true"
                                 class="cursor-pointer bg-white text-xs text-hopi-pink border-1 border-hopi-pink rounded-full py-1 px-2 self-center font-santralextrabold mt-2 hover:bg-hopi-pink hover:text-white hover:border-white">Değiştir
                            </div>
                            <Link href="/hopi-user-logout"
                                  class="cursor-pointer bg-white text-xs text-hopi-pink border-1 border-hopi-pink rounded-full py-1 px-2 self-center font-santralextrabold mt-2 hover:bg-hopi-pink hover:text-white hover:border-white"
                                  @click="clearHopiSelections" as="button" method="post">Kaldır
                            </Link>
                        </div>
                    </div>
                    <div class="mt-5 cursor-pointer flex items-center space-x-2 w-full max-w-full" @click="isPegasusModalOpen = true" v-if="pgs_active">
                        <img src="../../images/brands/pegasus-airlines-logo.jpeg" alt="" class="w-12 md:w-16" />
                        <span class="whitespace-nowrap text-xs md:text-sm text-gray-500"><span class="text-orange-500">BOLBOL Puan</span> için tıkla</span>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" class="h-8 w-8 text-textgray rotate-90">
                            <path fill-rule="evenodd" d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </template>
            </CheckoutPaymentInfo>

            <HopiModal :isOpen="isHopiModelOpen" @update:isOpen="closeHopi" />
            <PegasusModal :isOpen="isPegasusModalOpen" @update:isOpen="closePegasusModal" :pegasusAuth="$page.props.auth?.params?.pegasus" />
            <HopiCampaignModal :isOpen="isHopiCampaignModelOpen" :campaigns="hopi_campaigns" :balance="hopi_balance" :selectedHopiCampaign="selectedHopiCampaign" @update:isOpenCampaignModal="closeHopiCampaign"
                               @update:selectedCampaign="changeSelectedHopiCampaign" @update:enteredBalance="updateEnteredBalance" />
        </section>
        <section class="mt-6 mb-12 flex flex-col lg:flex-row" v-else>
            <div class="w-full max-w-[90%] mx-auto border-2 border-bordergray rounded-lg text-black text-base">
                <div class="p-3 text-center">Sepetinde ürün bulunmamaktadır. Kiralamaya hemen başlamak için <a href="/kategoriler/tum-urunler" class="underline">tıkla.</a></div>
            </div>
        </section>
    </main>
</template>
