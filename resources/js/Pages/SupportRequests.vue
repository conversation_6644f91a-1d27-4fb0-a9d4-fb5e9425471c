<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";

export default {
    components: {
        <PERSON>,
        Head,
        UserMenu,
    },
    props: {
        orders: Object,
        order_count: Number,
    },
    layout: Layout,
};
</script>

<template>

    <Head title="Destek Talepleri" />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <user-menu :order_count="order_count" :active="`SupportRequests`" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 px-4 pt-2">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7">Destek <PERSON></h3>
                </div>
                <div class="w-full flex flex-row items-center border-3 border-bordergray rounded-lg p-7 mb-4">
                    <a href="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="43.128" height="43" viewBox="0 0 43.128 43">
                            <g id="Group_4083" data-name="Group 4083" transform="translate(1 1.5)">
                                <path id="Path_151" data-name="Path 151" d="M13.418,12.082l-.49-1.692L3.656,12.571,1.913,4.1,0,4.419,2.126,14.737Z" transform="translate(-1 -1.813)" fill="#70d44b" />
                                <g id="Group_954" data-name="Group 954" transform="translate(3.037)">
                                    <path id="Path_150" data-name="Path 150" d="M4.688,12.367a20,20,0,1,1,7.084,26.545" transform="translate(-4.688 -1.891)" fill="none" stroke="#70d44b" stroke-linecap="round" stroke-miterlimit="10"
                                        stroke-width="3" />
                                    <path id="Path_152" data-name="Path 152" d="M33.593,42.573V31.567l10.04-5.02" transform="translate(-16.445 -13.131)" fill="none" stroke="#70d44b" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" />
                                </g>
                            </g>
                        </svg>
                    </a>
                    <Link href="/gecmis-taleplerim" class="text-base text-black font-bold ml-7">Geçmiş Taleplerim</Link>
                </div>
                <div class="w-full flex flex-row items-center border-3 border-bordergray rounded-lg p-7 mb-4" v-if="orders.length > 0">
                    <a href="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="44.191" height="46.525" viewBox="0 0 44.191 46.525">
                            <path id="noun-customer-service-3370552"
                                d="M173.85,71.192V67.314a13.622,13.622,0,0,0-13.606-13.605h-6.6A13.621,13.621,0,0,0,140.04,67.314v3.878c-2.644.341-4.686,2.269-4.686,4.607v7.034c0,2.581,2.475,4.679,5.515,4.679s5.518-2.1,5.518-4.679V75.8c0-2.339-2.042-4.265-4.689-4.607V67.314a11.964,11.964,0,0,1,11.951-11.952h6.6A11.967,11.967,0,0,1,172.2,67.314v3.878c-2.651.341-4.693,2.267-4.693,4.607v7.034a4.859,4.859,0,0,0,3.989,4.473,11.734,11.734,0,0,1-9.187,7.726,3.173,3.173,0,0,0-2.994-2.156h-4.733a3.18,3.18,0,1,0,0,6.36h4.733a3.182,3.182,0,0,0,3.116-2.563A13.556,13.556,0,0,0,173.175,87.5c2.971-.067,5.37-2.13,5.37-4.666V75.8c-.006-2.339-2.05-4.267-4.7-4.608ZM144.73,75.8v7.034c0,1.667-1.732,3.024-3.864,3.024s-3.857-1.357-3.857-3.024V75.8c0-1.665,1.728-3.024,3.857-3.024S144.73,74.134,144.73,75.8Zm14.58,21.78h-4.733a1.524,1.524,0,0,1,0-3.047h4.733a1.524,1.524,0,0,1,0,3.047Zm17.57-14.746c0,1.667-1.732,3.024-3.86,3.024s-3.86-1.357-3.86-3.024V75.8c0-1.665,1.732-3.024,3.86-3.024s3.86,1.357,3.86,3.024Z"
                                transform="translate(-134.854 -53.209)" fill="#70d44b" stroke="#70d44b" stroke-width="1" />
                        </svg>
                    </a>
                    <Link href="/talep-olustur" class="text-base text-black font-bold ml-7">Talep Oluştur</Link>
                </div>
            </div>
        </section>
    </main>
</template>
