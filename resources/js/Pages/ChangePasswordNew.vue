<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";

import { TransitionRoot, TransitionChild, Dialog, DialogPanel, DialogTitle } from "@headlessui/vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
    },
    data() {
        return {
            passReset: this.$inertia.form({
                pass1: null,
                pass2: null,
                token: this.$page.props.token,
            }),
        };
    },
    props: {
        errors: { type: Object, default: false },
    },
    layout: Layout,
};
</script>

<template>

    <Head title="Şifremi Yenile" />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6">
            <div class="flex mx-auto flex-col max-w-[350px] md:max-w-lg py-8 rounded-2lg bg-white shadow-searchshadow">
                <img src="../../images/logo.png" class="mx-auto w-60 mb-10" alt="Logo" />
                <div class="font-bold text-xl self-center">Şifre Yenile</div>
                <div class="flex w-80 md:w-96 self-center mt-4">
                    <span class="text-xs mx-auto">Şifreniz en az 6 karakter, bir büyük harf, bir küçük harf ve rakam içermelidir.</span>
                </div>
                <div class="self-center mt-6 relative">
                    <input v-model="passReset.pass1" type="password" placeholder="Yeni Şifre" class="placeholder:text-placeholdergray border-2 rounded-2lg border-kb-light-grey w-80 md:w-96" autocomplete="off" />
                    <svg class="absolute mt-3 top-0 right-5" id="eye-slash" xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 17.749 15">
                        <path id="Path_2948" data-name="Path 2948"
                            d="M20.762,15.939a16.033,16.033,0,0,0,2.93-3.822s-3.328-6.492-8.874-6.492a7.4,7.4,0,0,0-3.095.694l.854.91a6.24,6.24,0,0,1,2.241-.424,8.088,8.088,0,0,1,5.733,2.9,15.359,15.359,0,0,1,1.841,2.412c-.065.1-.135.216-.216.34a15.4,15.4,0,0,1-1.625,2.072c-.183.195-.374.387-.573.573Z"
                            transform="translate(-5.943 -4.617)" fill="#231f20" />
                        <path id="Path_2949" data-name="Path 2949"
                            d="M17.661,15.641a4.323,4.323,0,0,0-.91-4.312A3.717,3.717,0,0,0,12.7,10.36l.913.972a2.672,2.672,0,0,1,2.354.835,3.069,3.069,0,0,1,.784,2.5l.912.97ZM14.4,17.175l.912.97a3.717,3.717,0,0,1-4.053-.969,4.323,4.323,0,0,1-.91-4.312l.913.972a3.069,3.069,0,0,0,.784,2.5,2.672,2.672,0,0,0,2.354.835Z"
                            transform="translate(-5.13 -6.753)" fill="#231f20" />
                        <path id="Path_2950" data-name="Path 2950"
                            d="M3.716,11.55q-.3.283-.574.575A15.359,15.359,0,0,0,1.3,14.537l.216.34a15.4,15.4,0,0,0,1.625,2.072,8.09,8.09,0,0,0,5.733,2.9,6.219,6.219,0,0,0,2.241-.425l.854.911a7.4,7.4,0,0,1-3.095.694C3.328,21.029,0,14.537,0,14.537a15.978,15.978,0,0,1,2.93-3.822l.785.837Z"
                            transform="translate(0 -7.036)" fill="#231f20" />
                        <path id="Path_2951" data-name="Path 2951" d="M17.015,18.7,3.7,4.539,4.489,3.7,17.8,17.867Z" transform="translate(-1.878 -3.703)" fill="#231f20" fill-rule="evenodd" />
                    </svg>
                </div>
                <div class="self-center mt-6 relative">
                    <input v-model="passReset.pass2" type="password" placeholder="Yeni Şifre Tekrar" class="placeholder:text-placeholdergray border-2 rounded-2lg border-kb-light-grey w-80 md:w-96" autocomplete="off" />
                    <svg class="absolute mt-3 top-0 right-5" id="eye-slash" xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 17.749 15">
                        <path id="Path_2948" data-name="Path 2948"
                            d="M20.762,15.939a16.033,16.033,0,0,0,2.93-3.822s-3.328-6.492-8.874-6.492a7.4,7.4,0,0,0-3.095.694l.854.91a6.24,6.24,0,0,1,2.241-.424,8.088,8.088,0,0,1,5.733,2.9,15.359,15.359,0,0,1,1.841,2.412c-.065.1-.135.216-.216.34a15.4,15.4,0,0,1-1.625,2.072c-.183.195-.374.387-.573.573Z"
                            transform="translate(-5.943 -4.617)" fill="#231f20" />
                        <path id="Path_2949" data-name="Path 2949"
                            d="M17.661,15.641a4.323,4.323,0,0,0-.91-4.312A3.717,3.717,0,0,0,12.7,10.36l.913.972a2.672,2.672,0,0,1,2.354.835,3.069,3.069,0,0,1,.784,2.5l.912.97ZM14.4,17.175l.912.97a3.717,3.717,0,0,1-4.053-.969,4.323,4.323,0,0,1-.91-4.312l.913.972a3.069,3.069,0,0,0,.784,2.5,2.672,2.672,0,0,0,2.354.835Z"
                            transform="translate(-5.13 -6.753)" fill="#231f20" />
                        <path id="Path_2950" data-name="Path 2950"
                            d="M3.716,11.55q-.3.283-.574.575A15.359,15.359,0,0,0,1.3,14.537l.216.34a15.4,15.4,0,0,0,1.625,2.072,8.09,8.09,0,0,0,5.733,2.9,6.219,6.219,0,0,0,2.241-.425l.854.911a7.4,7.4,0,0,1-3.095.694C3.328,21.029,0,14.537,0,14.537a15.978,15.978,0,0,1,2.93-3.822l.785.837Z"
                            transform="translate(0 -7.036)" fill="#231f20" />
                        <path id="Path_2951" data-name="Path 2951" d="M17.015,18.7,3.7,4.539,4.489,3.7,17.8,17.867Z" transform="translate(-1.878 -3.703)" fill="#231f20" fill-rule="evenodd" />
                    </svg>
                </div>
                <input v-model="passReset.token" type="hidden" />
                <div class="flex mt-4 w-80 md:w-96 self-center flex-col" v-if="Object.entries(errors).length > 0">
                    <div class="text-xs mx-auto text-kbred my-1">{{ errors[0] }}</div>
                </div>
                <div class="flex mt-4 w-80 md:w-96 self-center">
                    <Link :href="route('saveResetPassword')" method="post" class="text-center bg-black text-white lg:text-lg rounded-full py-2 lg:py-3 px-4 self-center font-bold w-full hover:bg-kbgreen hover:text-white" :data="passReset">Şifreyi
                    Yenile</Link>
                    <!--                    <button class="bg-black text-white rounded-full py-2 px-4 self-center font-bold w-full">Şifreyi Yenile</button>-->
                </div>
            </div>
        </section>
    </main>
</template>
