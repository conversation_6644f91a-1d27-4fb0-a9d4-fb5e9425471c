<script>
import {<PERSON>, <PERSON>} from '@inertiajs/inertia-vue3';
import Layout from '@/Pages/Shared/Layout.vue';

export default {
    components: {
        <PERSON>,
        Head,
    },
    layout: Layout,
}
</script>

<template>
    <Head title="Hesabım" />
    <main class="my-6 max-w-tablet lg:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex justify-center">
            <div class="w-10/12 lg:w-5/12 pl-3.5 pt-2">
                <div class="flex flex-col justify-center items-center mb-10">
                    <div class="mb-10">
                        <img class="" src="../../images/sifre-basarili.png" alt="">
                    </div>
                    <h1 class="p-1 text-base font-medium lg:font-bold text-center text-black box-border whitespace-no-wrap mb-3"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.</h1>
                    <p class="p-0 text-xs text-center text-kbgray box-border whitespace-no-wrap mb-3">Anasayfaya yönlendiriliyorsun…</p>
                        <Link href="/" class="w-80 text-center bg-black text-white rounded-full py-2 px-4 self-center text-lg font-bold">Anasayfaya Dön</Link>
                </div>
            </div>
        </section>
    </main>
</template>
