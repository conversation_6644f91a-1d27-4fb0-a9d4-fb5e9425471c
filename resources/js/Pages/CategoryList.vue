<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import VerticalProductList from "@/Pages/Shared/VerticalProductList.vue";
import VerticalProductBox from "@/Pages/Shared/VerticalProductBox.vue";
import ProductBox from "@/Pages/Shared/ProductBox.vue";

export default {
    components: {
        Link,
        Head,
        VerticalProductList,
        ProductBox,
        VerticalProductBox
    },
    props: {
        products: Object
    },
    layout: Layout
};
</script>

<template>

    <Head title="Kategori" />

    <main class="my-6 max-w-tablet lg:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <div class="w-4/12 lg:w-2/12 hidden md:flex flex-col justify-start  items-start rounded-lg">
                <div class="w-full bg-kb-mid-grey p-5 rounded-t-2lg">
                    <h3 class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Markalar </h3>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="Apple" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="Apple">Apple</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="Asus" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="Asus">Asus</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="Google" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="Google">Google</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="Huawei" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="Huawei">Huawei</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="Oppo" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="Oppo">Oppo</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="Samsung" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="Samsung">Samsung</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="Sony" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="Sony">Sony</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="Xiaomi" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="Xiaomi">Xiaomi</label>
                    </div>
                    <p class="w-full text-sm font-medium text-black pt-1 pl-4"><a href=""><u>Daha Fazla</u></a></p>
                </div>
                <div class="w-full border-t-4 border-white bg-kb-mid-grey p-5">
                    <h3 class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">İşletim Sistemi </h3>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="Android" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="Android">Android</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="iOS" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="iOS">iOS</label>
                    </div>
                </div>
                <div class="w-full border-t-4 border-white bg-kb-mid-grey p-5">
                    <h3 class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Fiyat Aralığı </h3>
                    <div class="-mr-4 w-full flex justify-around items-center mb-2">
                        <input id="min" class="p-2 mr-1 px-2 w-1/3 rounded-lg border-2 border-textgray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-xs tracking-wider"
                            type="text" placeholder="En Az" name="min">
                        <input id="max" class="p-2 mr-1 px-2 w-1/3 rounded-lg border-2 border-textgray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-xs tracking-wider"
                            type="text" placeholder="En Az" name="max">
                        <button class="w-1/3 flex items-center bg-kbblue text-white rounded-lg py-2 px-1 self-center text-xs font-bold">Ara
                            <svg class="ml-1" width="20" height="20" viewBox="0 0 24.61 24.611">
                                <defs>
                                    <clipPath id="clip-path">
                                        <path id="Mask"
                                            d="M8.991,0a8.991,8.991,0,1,0,5.845,15.815l.381.378v1.1l6.914,6.9,2.062-2.063-6.9-6.915h-1.1l-.382-.38A8.982,8.982,0,0,0,8.991,0Zm0,15.215a6.224,6.224,0,1,1,6.224-6.224A6.231,6.231,0,0,1,8.991,15.215Z"
                                            transform="matrix(1, -0.017, 0.017, 1, -0.209, 0.213)" fill="none" />
                                    </clipPath>
                                </defs>
                                <g id="Icon_Search_Sharp" data-name="Icon / Search / Sharp" transform="translate(0.209 0.209)">
                                    <path id="Mask-2" data-name="Mask"
                                        d="M8.991,0a8.991,8.991,0,1,0,5.845,15.815l.381.378v1.1l6.914,6.9,2.062-2.063-6.9-6.915h-1.1l-.382-.38A8.982,8.982,0,0,0,8.991,0Zm0,15.215a6.224,6.224,0,1,1,6.224-6.224A6.231,6.231,0,0,1,8.991,15.215Z"
                                        fill="none" />
                                    <g id="Icon_Search_Sharp-2" data-name="Icon / Search / Sharp" clip-path="url(#clip-path)">
                                        <g id="_Icon_Color" data-name="↳ Icon Color" transform="translate(-4.15 -3.884)">
                                            <rect id="Rectangle" width="24.197" height="23.653" fill="#fff" />
                                        </g>
                                    </g>
                                </g>
                            </svg>
                        </button>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="1100" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="1100">100 Altı</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="100250" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="100250">100 - 250</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="250500" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="250500">250 - 500</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="500750" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="500750">500 - 750</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="7501000" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="7501000">750 - 1000</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="10001500" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="10001500">1000 - 1500</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="15002000" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="15002000">1500 - 2000</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="2000uzeri" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="2000uzeri">2000 Üzeri</label>
                    </div>
                </div>
                <div class="w-full border-t-4 border-white bg-kb-mid-grey p-5">
                    <h3 class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Depolama</h3>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="32GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="32GB">32 GB</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="64GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="64GB">64 GB</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="128GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="128GB">128 GB</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="256GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="256GB">256 GB</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="512GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="512GB">512 GB</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="1TB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="1TB">1TB</label>
                    </div>
                </div>
                <div class="w-full border-t-4 border-white bg-kb-mid-grey p-5 rounded-b-2lg">
                    <h3 class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Hafıza (RAM)</h3>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="3GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="3GB">3 GB</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="4GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="4GB">4 GB</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="6GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="6GB">6 GB </label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="8GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="8GB">8 GB</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="12GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="12GB">12 GB </label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="16GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="16GB">16 GB</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="32GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox">
                        <label class="text-base text-black pl-2" for="32GB">32 GB</label>
                    </div>
                </div>

            </div>
            <div class="w-full md:w-8/12 lg:w-10/12 pl-4 lg:pt-2">
                <div class="hidden md:flex w-full justify-between items-center">
                    <div class="flex space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32.57" height="33.621" viewBox="0 0 32.57 33.621">
                            <g id="Group_753" data-name="Group 753" opacity="0.2">
                                <g id="Rectangle_165" data-name="Rectangle 165" transform="translate(0 0)" fill="#fff" stroke="#231f20" stroke-width="2">
                                    <rect width="32.57" height="33.621" rx="5" stroke="none" />
                                    <rect x="1" y="1" width="30.57" height="31.621" rx="4" fill="none" />
                                </g>
                                <g id="Group_753-2" data-name="Group 753" transform="translate(25.963 5.332) rotate(90)">
                                    <rect id="Rectangle_166" data-name="Rectangle 166" width="23.251" height="5.425" rx="2" fill="#231f20" />
                                    <rect id="Rectangle_167" data-name="Rectangle 167" width="23.251" height="5.425" rx="2" transform="translate(0 6.975)" fill="#231f20" />
                                    <rect id="Rectangle_168" data-name="Rectangle 168" width="23.251" height="5.425" rx="2" transform="translate(0 13.95)" fill="#231f20" />
                                </g>
                            </g>
                        </svg>

                        <svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 34 34">
                            <g id="Group_754" data-name="Group 754" transform="translate(0.379)">
                                <g id="Rectangle_169" data-name="Rectangle 169" transform="translate(-0.379)" fill="#fff" stroke="#231f20" stroke-width="2">
                                    <rect width="34" height="34" rx="5" stroke="none" />
                                    <rect x="1" y="1" width="32" height="32" rx="4" fill="none" />
                                </g>
                                <g id="Group_756" data-name="Group 756" transform="translate(4.803 7.037)">
                                    <g id="Group_755" data-name="Group 755" transform="translate(7.204)">
                                        <rect id="Rectangle_166" data-name="Rectangle 166" width="16.81" height="5.473" rx="2" fill="#231f20" />
                                        <rect id="Rectangle_167" data-name="Rectangle 167" width="16.81" height="5.473" rx="2" transform="translate(0 7.037)" fill="#231f20" />
                                        <rect id="Rectangle_168" data-name="Rectangle 168" width="16.81" height="5.473" rx="2" transform="translate(0 14.074)" fill="#231f20" />
                                    </g>
                                    <g id="Group_754-2" data-name="Group 754">
                                        <rect id="Rectangle_166-2" data-name="Rectangle 166" width="5.603" height="5.473" rx="2" fill="#231f20" />
                                        <rect id="Rectangle_167-2" data-name="Rectangle 167" width="5.603" height="5.473" rx="2" transform="translate(0 7.037)" fill="#231f20" />
                                        <rect id="Rectangle_168-2" data-name="Rectangle 168" width="5.603" height="5.473" rx="2" transform="translate(0 14.074)" fill="#231f20" />
                                    </g>
                                </g>
                            </g>
                        </svg>

                    </div>
                    <select class="font-bold w-44 bg-kb-mid-grey p-1 pl-2 rounded-2lg border-3 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-base tracking-wider" name="sirala" id="sirala">
                        <option value="Sırala" selected="">Sırala</option>
                    </select>
                </div>
                <div class="flex md:hidden ml-3">
                    <svg id="filter-circle" xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25">
                        <path id="Path_3751" data-name="Path 3751" d="M12.5,23.438A10.938,10.938,0,1,0,1.563,12.5,10.937,10.937,0,0,0,12.5,23.438ZM12.5,25A12.5,12.5,0,1,0,0,12.5,12.5,12.5,0,0,0,12.5,25Z" fill-rule="evenodd" />
                        <path id="Path_3752" data-name="Path 3752"
                            d="M13,21.406a.781.781,0,0,1,.781-.781h1.563a.781.781,0,1,1,0,1.563H13.781A.781.781,0,0,1,13,21.406ZM9.875,16.719a.781.781,0,0,1,.781-.781h7.813a.781.781,0,1,1,0,1.563H10.656A.781.781,0,0,1,9.875,16.719ZM6.75,12.031a.781.781,0,0,1,.781-.781H21.594a.781.781,0,0,1,0,1.563H7.531A.781.781,0,0,1,6.75,12.031Z"
                            transform="translate(-2.063 -3.438)" fill-rule="evenodd" />
                    </svg>
                    <p class="whitespace-nowrap text-sm font-medium text-black pt-1 pl-4">Filtrele </p>
                </div>

                <div class="flex flex-wrap">
                    <!--                    <category-box-->
                    <!--                        v-for="(product, index) in products" :key="index" :product="product"-->
                    <!--                    />-->
                    <VerticalProductList></VerticalProductList>
                    <VerticalProductList></VerticalProductList>
                    <VerticalProductList></VerticalProductList>
                    <VerticalProductList></VerticalProductList>
                    <VerticalProductList></VerticalProductList>
                    <!--                    <category-box></category-box>-->
                    <!--                    <category-box></category-box>-->
                    <!--                    <category-box></category-box>-->
                    <!--                    <category-box></category-box>-->
                    <!--                    <category-box></category-box>-->
                    <!--                    <category-box></category-box>-->
                    <!--                    <category-box></category-box>-->

                </div>
            </div>
        </section>
    </main>
    <section class="my-14 bg-[#f8f8f8] py-5">
        <div class="flex mx-auto flex-col max-w-7xl">
            <div class="flex w-full px-4 lg:px-0 justify-between">
                <div class="font-bold text-3xl my-2 lg:my-0 self-center text-center w-full md:w-auto">Son Gezdiğiniz Ürünler</div>
            </div>
            <div class="flex flex-wrap flex-col max-h-p-box pl-12 lg:pl-0 p-3 lg:p-0 md:my-4 overflow-y-scroll lg:overflow-y-visible">
                <product-box></product-box>
                <product-box></product-box>
                <product-box></product-box>
                <product-box></product-box>
            </div>

        </div>
    </section>

</template>
