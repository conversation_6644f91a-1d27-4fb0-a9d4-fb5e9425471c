<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
// import Rating from "vue-star-rating";
import { Dialog, DialogPanel, DialogTitle, Disclosure, DisclosureButton, DisclosurePanel, Popover, PopoverButton, PopoverPanel, TransitionChild, TransitionRoot } from "@headlessui/vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import { Inertia } from "@inertiajs/inertia";
// import Rating from "@/Components/rating/Rating.vue";
import AlpineRating from "@/Components/rating/AlpineRating.vue";
import RentPaymentModal from "@/Pages/Shared/RentPaymentModal.vue";
// import SimpleRating from "@/Components/rating/SimpleRating.vue";
import { vMaska } from "maska";

var ev;
export default {
    directives: { maska: vMaska },
    data() {
        return {
            rentAndBuyModalIsOpen: false,
            rentAndBuyModalFormStatus: false,
            selectedMonth: null,
            selectedClass: "border-2 h-14 md:h-20 rounded-full border-kbgreen p-2 lg:p-4 items-center justify-center flex bg-white !text-black text-[14px] md:text-[20px] hover:!scale-100",
            mainProductImageURL: "",
            mainProductImageURLWebP: "",
            price: 0,
            comparePrice: 0,
            selectedVariandId: 0,
            defaultVariandId: 0,
            isProductSelected: true,
            form: this.$inertia.form({}),
            favouriteSate: false,
            gozukecekAlan: "yorum",
            showmoreparam: false,
            hiddenbtn1: true,
            rating: 5,
            subscribetionMonthsOrdered: [],
            selectedMonthFull: null,
            isOpen: false,
            shareUrl: "",
            svglocation: 984,
            comment: this.$inertia.form({
                product: this.product.wp_product_id ?? this.product.id,
                author: this.auth?.user?.full_name,
                email: this.auth?.user?.email,
                reviewRating: null,
                // reviewTitle: null,
                reviewMessage: null,
                productName: this.product.attribute_data?.name?.tr,
                productSKU: this.product.id,
                productImageUrl: this.product.media_storage[0]?.url,
                productUrl: `https://kiralabunu.com/urun/` + this.product.default_url.slug
            }),
            showErrorPopup: this.$page.props.errors?.product_variant_id != null ? true : false,
            insurangePrice: 0,
            isInsuranceSelected: false,
            MotorcycleModalIsOpen: false,
            MotorcycleSuccessIsOpen: this.$page.props.success.success != null ? true : false,
            motorcycleForm: this.$inertia.form({
                name: null,
                email: null,
                gsm: null,
                firmName: null,
                // dateofbirth: null,
                motorcycleNumber: null,
                city: null,
                Contract1: null,
                // Contract2: null,
                // IDnumber: null,
                motorcycleName: this.product.name
            }),
            modalForm: this.$inertia.form({
                first_name: null,
                last_name: null,
                email: null,
                phone: null,
                product_id: null,
                tckn: null
            })
        };
    },
    components: {
        RentPaymentModal,
        // SimpleRating,
        AlpineRating,
        Link,
        Head,
        Disclosure,
        DisclosureButton,
        DisclosurePanel,
        Splide,
        SplideSlide,
        SplideTrack,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        Popover,
        PopoverButton,
        PopoverPanel
        // Rating,
    },
    props: {
        product: Object,
        productFeatures: Object,
        productBoxContent: Object,
        auth: Object,
        errors: { type: Object, default: false },
        reviews: {
            type: Object,
            default: undefined
            // default: () => {
            //     return {
            //         total: 0,
            //         rating: 0,
            //     };
            // },
        },
        favourite: String,
        // productVariantPrices: Object,
        isPurchased: {
            type: Boolean,
            default: false
        },
        commentSubmittedSuccessfully: {
            type: Boolean,
            default: false
        },
        isInsurangeOptionsEnabled: {
            type: Boolean,
            default: false
        },
        isAynet: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        closeMotorcycleFormModal() {
            this.MotorcycleModalIsOpen = false;
        },
        openMotorcycleFormModal() {
            this.MotorcycleModalIsOpen = true;
        },
        closeMotorcycleSuccessModal() {
            this.MotorcycleSuccessIsOpen = false;
        },
        openMotorcycleSuccessModal() {
            this.MotorcycleSuccessIsOpen = true;
        },
        scrollToElement(options) {
            const el = this.$el.getElementsByClassName("scrollclass")[0];

            if (el) {
                el.scrollIntoView(options);
            }
        },
        // showMoreToggle() {
        //     if (this.showmoreparam == false) {
        //         this.showmoreparam = true;
        //     } else {
        //         this.showmoreparam = false;
        //     }
        // },
        selectMonth(month) {
            this.selectedMonth = month;

            let variantId = this.selectedVariandId;
            if (variantId === 0) variantId = this.defaultVariandId;

            this.price = this.product.variants.find((variant) => variant.id == variantId).prices.find((price) => price.subscription_months_id == month).price.value / 100;
            this.comparePrice = this.product.variants.find((variant) => variant.id == variantId).prices.find((price) => price.subscription_months_id == month).compare_price.value / 100;
            this.insurangePrice = (this.price / 100) * this.getInsuranceRatio(month);
        },
        selectImage(image) {
            this.mainProductImageURL = image.urlThumbOrj;
            this.mainProductImageURLWebP = image.urlZoomWebP;
        },
        changeVariant() {
            //if (this.selectedVariandId != "") this.selectMonth(+Object.keys(this.product.variants.filter((variant) => variant.id == this.selectedVariandId)[0].prices.filter((price) => price.price.value > 0)).length);
            if (this.selectedVariandId != 0)
                this.selectMonth(
                    Math.max(
                        ...this.product.variants
                            .filter((variant) => variant.id == this.selectedVariandId)[0]
                            .prices.filter((price) => price.price.value > 0)
                            .map((price) => price.subscription_months_id)
                    )
                );
            // this.selectedVariandId = variant.id;
        },
        toogleFavourite() {
            // Kullanıcı login değil ise favori eklemek için login olmalı, yönlendir
            if (this.auth.token == null) {
                this.$inertia.get("/giris-yap");
            }

            window.axios.defaults.headers.common["Authorization"] = `Bearer ${this.auth.token}`;
            axios
                .post(`/favourite/${this.product.id}/toogle`, {
                    productSlug: this.product.default_url.slug
                })
                .then((res) => {
                    if (res.data.status == "created") {
                        this.favouriteSate = true;
                    } else {
                        this.favouriteSate = false;
                    }
                })
                .catch((err) => {
                    console.log(err);
                });
        },
        share() {
            console.log("share");
            if (navigator.share !== undefined) {
                navigator
                    .share({
                        title: this.product.name,
                        text: this.product.description,
                        url: window.location.href
                    })
                    .then(() => console.log("Shared!"))
                    .catch((err) => console.error(err));
            } else {
                console.log("Not supported");
                //window.location = `mailto:?subject=${title}&body=${text}%0A${url}`;
            }
        },
        addToCart() {
            dataLayer.push({
                event: "add_to_cart",
                ecommerce: {
                    currency: "TRY",
                    value: this.price,
                    items: [
                        {
                            item_id: this.product.id,
                            item_name: this.product.attribute_data.name?.tr,
                            price: this.price,
                            item_brand: this.product.brand.name,
                            item_category: this.product.collections[0]?.attribute_data?.name?.tr,
                            item_category2: this.product.collections[1]?.attribute_data?.name?.tr ?? "",
                            // item_comments: this.reviews.total,
                            // item_rating: this.reviews.rating,
                            item_color: this.product.variants[0]?.options[0] ?? "",
                            // item_list_id: "Homepage - Kampanya Ürünleri List",
                            // item_list_name: "Homepage - Kampanya Ürünleri List",
                            index: 0,
                            discount: 0,
                            item_variant: this.selectedMonthFull?.name,
                            quantity: 1
                        }
                    ]
                }
            });

            twq("event", "add_to_cart", {
                value: this.price,
                currency: "TRY",
                contents: [{ content_id: this.product.id, content_name: this.product.attribute_data.name?.tr, content_price: this.price, num_items: "1" }]
            });
        },
        closeModal() {
            this.isOpen = false;
        },
        openModal() {
            this.isOpen = true;
        },
        closeRentAndBuyModal() {
            this.rentAndBuyModalIsOpen = false;
        },
        openRentAndBuyModal() {
            this.rentAndBuyModalIsOpen = true;
        },
        ShareNavigate() {
            if (navigator.share) {
                navigator
                    .share({
                        title: "Kiralabunu.com",
                        url: "https://kiralabunu.com" + this.shareUrl
                    })
                    .catch(console.error);
            } else {
                // fallback
            }
        },
        ratingBar(star) {
            return (parseInt(star) / parseInt(this.reviews.total)) * 259;
        },
        getInsuranceRatio(subscriptionMonth) {
            let ratio = 0;
            switch (subscriptionMonth) {
                case 1:
                    ratio = 15;
                    break;
                case 2:
                    ratio = 20;
                    break;
                case 3:
                    ratio = 33;
                    break;
                case 4:
                    ratio = 50;
                    break;
                case 5:
                    ratio = 66;
                    break;
                default:
                    ratio = 66;
            }
            return ratio;
        },
        changeSelectedRating(rating) {
            this.comment.reviewRating = rating;
        },
        modalSubmit() {
            try {
                // Form verilerini hazırla
                this.modalForm.product_id = this.product.id;

                // Inertia post isteği
                this.modalForm.post(route("lead.rentandbuy"), {
                    preserveScroll: true,
                    onSuccess: () => {
                        this.rentAndBuyModalFormStatus = true;

                        // Form alanlarını temizle
                        this.modalForm.reset();

                        // 3 saniye sonra modalı kapat
                        setTimeout(() => {
                            this.rentAndBuyModalFormStatus = false;
                            this.closeRentAndBuyModal();
                        }, 3000);
                    },
                    onError: (errors) => {
                        console.log("modalsubmit onError", errors);
                        // Validasyon hataları için
                        Object.keys(errors).forEach(key => {
                            console.log("notif alanından error logu", errors[key]);
                            // this.$notify({
                            //     type: 'error',
                            //     text: errors[key]
                            // });
                        });
                        this.rentAndBuyModalFormStatus = false;
                    }
                });

            } catch (error) {
                console.error(error);
                this.rentAndBuyModalFormStatus = false;

                if (error.response?.status === 422) {
                    // Validasyon hatalarını göster
                    const errors = error.response.data.errors;
                    Object.keys(errors).forEach(key => {
                        this.$notify({
                            type: "error",
                            text: errors[key][0]
                        });
                    });
                } else {
                    // Genel hata mesajı
                    this.$notify({
                        type: "error",
                        text: "Bir hata oluştu, lütfen tekrar deneyiniz"
                    });
                }
            }
        }
    },
    mounted() {
        console.log("Product Detail Page mounted");
        //this.mainProductImageURL = this.product.media_storage[0]?.url;
        this.mainProductImageURL = this.product.imagesOptimized[0]?.urlThumbOrj;
        this.mainProductImageURLWebP = this.product.imagesOptimized[0]?.urlZoomWebP;
        if (this.product?.variants[0]?.prices.length > 0) this.subscribetionMonthsOrdered = Object.entries(this.product?.variants[0]?.prices).sort(([, a], [, b]) => a.subscription_months.value - b.subscription_months.value);
        this.selectedVariandId = this.product.variants[0].id;
        this.defaultVariandId = this.product.variants[0].id;
        if (this.product.variants.length > 1) this.selectedVariandId = 0;
        let nonZeroPrices = this.product.variants[0].prices.filter((price) => price.price.value > 0);
        this.subscribetionMonthsOrdered = this.subscribetionMonthsOrdered.filter((price) => price[1].price.value > 0);
        this.price = this.subscribetionMonthsOrdered[+Object.keys(nonZeroPrices).length - 1]?.[1]?.price?.value / 100;
        this.comparePrice = this.subscribetionMonthsOrdered[+Object.keys(nonZeroPrices).length - 1]?.[1]?.compare_price?.value / 100;
        this.selectedMonthFull = this.subscribetionMonthsOrdered[+Object.keys(nonZeroPrices).length - 1]?.[1]?.subscription_months;
        this.selectedMonth = this.selectedMonthFull?.id;
        this.favouriteSate = this.product.favourite;
        this.favouriteSate = this.favourite == "exist";
        this.insurangePrice = (this.price / 100) * this.getInsuranceRatio(this.selectedMonth);

        Inertia.reload({
            only: ["isPurchased", "reviews"]
        });
    },
    layout: Layout,
    setup() {
        const options = {
            rewind: true,
            perPage: 3,
            gap: "1rem",
            pagination: false,
            breakpoints: {
                640: {
                    perPage: 1
                },
                1100: {
                    perPage: 2
                }
            }
        };
        return { options };
    },
    created() {
        console.log("Product Detail Page created");

        ev = this.$inertia.on("success", (event) => {
            //console.log("ürün detay navigate", event.detail.page.url);
            this.shareUrl = event.detail.page.url;
            // Adres /urun ile başlıyorsa datalayer push et
            if (event.detail.page.url.startsWith("/urun")) {
                dataLayer.push({
                    event: "view_item",
                    ecommerce: {
                        currency: "TRY",
                        value: this.price,
                        items: [
                            {
                                item_id: this.product.id,
                                item_name: this.product.attribute_data.name?.tr,
                                price: this.price,
                                item_brand: this.product.brand.name,
                                item_category: this.product.collections[0]?.attribute_data?.name?.tr,
                                item_category2: this.product.collections[1]?.attribute_data?.name?.tr ?? "",
                                item_comments: "virtualPageView",
                                item_reviews: this.reviews?.total ?? 0,
                                item_rating: this.reviews?.rating ?? 0,
                                item_color: this.product.variants[0]?.options[0] ?? "",
                                // item_list_id: "Homepage - Kampanya Ürünleri List",
                                // item_list_name: "Homepage - Kampanya Ürünleri List",
                                index: 0,
                                discount: 0,
                                item_variant: this.selectedMonthFull?.name
                            }
                        ]
                    }
                });
            }
        });
    },
    watch: {
        "$page.props.errors": function(val) {
            console.log("watch errors", val);
            this.showErrorPopup = val?.product_variant_id != null ? true : false;
        },
        "$page.props.success": function(val) {
            this.MotorcycleSuccessIsOpen = val.success != null && !this.rentAndBuyModalIsOpen ? true : false;
            if (val.success == "Mesajınız başarıyla gönderildi.") {
                this.closeMotorcycleFormModal();
                this.motorcycleForm.reset();
            }
        }
    },
    unmounted() {
        document.removeEventListener("inertia:success", ev);
    },
    computed: {
        // add your computed properties here
        isMobilite: function() {
            return this.product.collections.filter((collection) => [96, 97, 98].includes(collection.id)).length > 0;
        }
    }
};
</script>

<template>

    <Head :title="product.title || (product.name + ' - Kiralama')">
        <meta name="description" :content="product.meta_description" />
        <link rel="canonical" :href="product.default_url.slug" />
    </Head>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white px-5 py-5 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                            <DialogTitle>
                                <!--                                <h2 class="text-base font-semibold text-center mb-2 mt-2">Paylaş</h2>-->
                            </DialogTitle>
                            <ul class="flex flex-row flex-wrap mts:flex-col mt-2 leading-4 space-y-2 mts:space-y-4">
                                <li class="flex w-1/2 mts:w-full items-center mt-2 mts:mt-4">
                                    <a :href="'https://www.facebook.com/share.php?u=https://kiralabunu.com' + shareUrl" target="_blank" class="flex justify-center items-center bg-kb-light-grey rounded-full w-8 h-8">
                                        <svg width="7.385" height="14" viewBox="0 0 7.385 16" class="">
                                            <path id="" data-name="Facebook Icon"
                                                  d="M6.981,2.83A4.442,4.442,0,0,0,5.69,2.617c-.525,0-1.654.362-1.654,1.064V5.362H6.719v2.83H4.035V16h-2.7V8.191H0V5.362H1.332V3.936C1.332,1.787,2.26,0,4.5,0A10.187,10.187,0,0,1,7.385.319Z"
                                                  fill="#70d44b"></path>
                                        </svg>
                                    </a>
                                    <a :href="'https://www.facebook.com/share.php?u=https://kiralabunu.com' + shareUrl" target="_blank">
                                        <span class="text-kbgray text-xs mts:text-sm ml-2">Facebook</span>
                                    </a>
                                </li>
                                <li class="flex w-1/2 mts:w-full items-center pl-2 mts:pl-0">
                                    <a :href="'https://twitter.com/intent/tweet?url=https://kiralabunu.com' + shareUrl" target="_blank" class="flex justify-center items-center bg-kb-light-grey rounded-full w-8 h-8">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
                                            <path fill="#70d44b"
                                                  d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                                        </svg>
                                    </a>
                                    <a :href="'https://twitter.com/intent/tweet?url=https://kiralabunu.com' + shareUrl" target="_blank">
                                        <span class="text-kbgray text-xs mts:text-sm ml-2">Twitter</span>
                                    </a>
                                </li>
                                <li class="flex w-1/2 mts:w-full items-center">
                                    <a :href="'https://api.whatsapp.com/send/https://kiralabunu.com.&type=custom_url&app_absent=0'" target="_blank" class="flex justify-center items-center bg-kb-light-grey rounded-full w-8 h-8">
                                        <svg width="16" height="16" viewBox="0 0 16 16" class="scale-75">
                                            <path id="LinkedIn_Icon" data-name="LinkedIn Icon"
                                                  d="M8.96,16H5.547q0-.937,0-1.872v-.006h0c0-3.042.008-6.189-.082-9.295h2.96L8.587,6.4H8.64a3.988,3.988,0,0,1,3.414-1.813,3.507,3.507,0,0,1,3.033,1.453A5.785,5.785,0,0,1,16,9.412V16H12.56V9.813c0-1.611-.583-2.427-1.733-2.427a1.909,1.909,0,0,0-1.76,1.307,2.518,2.518,0,0,0-.106.88V16ZM3.493,16H.08V4.826H3.493V16ZM1.76,3.467A1.69,1.69,0,0,1,0,1.733,1.716,1.716,0,0,1,1.813,0,1.7,1.7,0,0,1,3.6,1.733,1.711,1.711,0,0,1,1.76,3.467Z"
                                                  fill="#70d44b"></path>
                                        </svg>
                                    </a>
                                    <a href="https://www.linkedin.com/company/kiralabunu" target="_blank">
                                        <span class="text-kbgray text-xs mts:text-sm ml-2">Linkedin</span>
                                    </a>
                                </li>
                                <li class="flex w-1/2 md:w-full items-center pl-2 mts:pl-0">
                                    <a href="mailto:?subject=T%C3%BCrk%20Pediatri%20Kurumu%20Beslenme%20Okulu%209-10%20Haziran%202023%20%C3%9Csk%C3%BCp%27te%20yap%C4%B1lacakt%C4%B1r.&body=T%C3%BCrk%20Pediatri%20Kurumu%20Beslenme%20Okulu%209-10%20Haziran%202023%20%C3%9Csk%C3%BCp%27te%20yap%C4%B1lacakt%C4%B1r.%20Ba%C5%9Fl%C4%B1kl%C4%B1%20yay%C4%B1n%C4%B1%20http://turkpediatri.org.tr/haberler%20linkine%20t%C4%B1klayarak%20okuman%C4%B1%20tavsiye%20ediyorum."
                                       target="_blank" class="flex justify-center items-center bg-kb-light-grey rounded-full w-8 h-8">
                                        <svg width="20.415" height="15.315" viewBox="0 0 20.415 15.315" class="scale-75">
                                            <path id="Path_2998" data-name="Path 2998"
                                                  d="M10.566,5.776c2.6,0,4.733.044,6.7.132h.056A1.836,1.836,0,0,1,19,7.865v.044l0,.044c.092,1.356.136,2.763.136,4.183S19.1,14.962,19,16.318l0,.044v.044a2.112,2.112,0,0,1-.534,1.439,1.542,1.542,0,0,1-1.14.534h-.064c-2.11.1-4.338.151-6.635.151H10.2c-2.3,0-4.534-.052-6.627-.147H3.512a1.535,1.535,0,0,1-1.136-.534,2.131,2.131,0,0,1-.534-1.439v-.044l0-.044c-.1-1.36-.14-2.767-.132-4.175v-.008c0-1.407.04-2.811.132-4.171l0-.044V7.881A1.838,1.838,0,0,1,3.512,5.915h.056c1.973-.092,4.1-.132,6.707-.132h.291m0-1.284H9.848c-2.3,0-4.554.032-6.762.132A3.1,3.1,0,0,0,.139,7.873C.039,9.3,0,10.72,0,12.144s.036,2.843.136,4.266a3.113,3.113,0,0,0,2.947,3.253q3.277.156,6.69.151h.861q3.415,0,6.694-.151a3.114,3.114,0,0,0,2.951-3.253q.144-2.135.136-4.27c0-1.423-.04-2.843-.136-4.27a3.1,3.1,0,0,0-2.951-3.233C15.119,4.532,12.867,4.5,10.566,4.5Z"
                                                  transform="translate(0 -4.5)" fill="#70d44b"></path>
                                            <path id="Path_2999" data-name="Path 2999" d="M14.555,18.9V11.067l5.781,3.916Z" transform="translate(-6.301 -7.343)" fill="#70d44b"></path>
                                        </svg>
                                    </a><a href="https://www.youtube.com/channel/UCdUwTA8Y5EptffUoY3fU0OQ" target="_blank"><span class="text-kbgray text-xs mts:text-sm ml-2">Youtube</span></a>
                                </li>
                            </ul>
                            <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal">x</div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>

    <main class="my-3 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-4 ts:mt-1 2xl:mt-4 mx-auto scroll-smooth">
        <section class="mt-3 ts:mt-1 2xl:mt-3 flex flex-col xl:flex-row">
            <div class="flex flex-col justify-between w-full xl:w-1/2">
                <div class="w-full flex flex-row-reverse items-start">
                    <div class="flex justify-center mr-2 px-1 w-[calc(100%-60px)] md:w-[calc(100%-110px)]">
                        <!--                        <img :src="mainProductImageURL" alt="" class="mts:max-h-[450px] ts:max-h-none" />-->
                        <picture>
                            <source :srcset="mainProductImageURLWebP" type="image/webp" />
                            <source :srcset="mainProductImageURL" type="image/jpeg" />
                            <img :src="mainProductImageURL" alt="Alt Text!" />
                        </picture>
                    </div>
                    <div class="flex flex-col md:items-start md:justify-start space-y-4 mt-4 md:mt-0 overflow-x-visible overflow-y-scroll w-[60px] mts:w-[90px] md:w-[110px] max-h-[280px] mts:max-h-[450px]">
                        <picture v-for="image in product.imagesOptimized">
                            <source :srcset="image.urlThumbWebP" type="image/webp" @click="selectImage(image)" />
                            <source :srcset="image.urlThumbOrj" type="image/jpeg" @click="selectImage(image)" />
                            <img :src="image.urlThumbOrj" alt="Alt Text2!" @click="selectImage(image)" />
                        </picture>
                        <!--                        <img :src="image.url" alt="" class="border border-kb-gray rounded-md w-[55px] w-full md:w-[100px] w-full cursor-pointer ts:p-2" v-for="image in product.media_storage" @click="selectImage(image.url)" />-->
                    </div>
                </div>
            </div>
            <div class="flex flex-col xl:w-1/2 mt-6 md:mt-2 xl:mt-0">
                <div class="bg-kb-mid-grey md:bg-white md:shadow-productshadow w-full p-4 ts:pt-1 2xl:pt-4 rounded-md">
                    <div class="flex flex-wrap justify-start items-start mb-2" v-if="product.tags">
                        <div class="bg-kbdvred rounded-full text-xs px-2 py-0.5 text-white font-santralregular font-semibold leading-4 mr-1" v-if="discountRate > 0">%{{ discountRate }} indirim</div>
                        <div class="bg-kbgreen rounded-full text-xs px-2 py-0.5 text-white font-santralregular font-semibold leading-4 mr-1" v-if="product.tags && product.tags.filter((x) => x.id == 34).length == 1">Yeni</div>
                        <div class="bg-black rounded-full text-xs px-2 py-0.5 text-white font-santralregular font-semibold leading-4 mr-1" v-if="product.tags && product.tags.filter((x) => x.id == 35).length == 1">Kirala + Satın al</div>
                        <div class="bg-kbgray rounded-full text-xs px-2 py-0.5 text-white font-santralregular font-semibold leading-4 mr-1" v-if="product.tags && product.tags.filter((x) => x.id == 33).length == 1">Ön Sipariş</div>
                        <div class="bg-kbblue rounded-full text-xs px-2 py-0.5 text-white font-santralregular font-semibold leading-4 mr-1" v-if="product.tags && product.tags.filter((x) => x.id == 5).length == 1">1 TL</div>
                        <div class="bg-[#f44336] rounded-full text-xs px-2 py-0.5 text-white font-santralregular font-semibold leading-4 mr-1" v-if="product.tags && product.tags.filter((x) => x.id == 32).length == 1">Şubat</div>
                    </div>
                    <div class="w-full flex justify-between">
                        <a href="#scrollstar" class="" @click="gozukecekAlan = 'yorum'">
                            <template v-if="reviews !== undefined && reviews.rating">
                                <svg width="90.333" height="15.021" viewBox="0 0 90.333 15.021">
                                    <defs>
                                        <clipPath id="clip-path">
                                            <rect width="15.696" height="15.021" fill="none" />
                                        </clipPath>
                                    </defs>
                                    <g transform="translate(-930.094 0.5)">
                                        <path v-for="i in Math.round(reviews?.rating)" :key="i"
                                              d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                              :data-name="Math.round(reviews?.rating) >= i ? `star-solid` : `none`" :transform="`translate( ${(928.348 + (i - 1) * 18.659).toFixed(3)} -2.433)`"
                                              :fill="Math.round(reviews?.rating) >= i ? `#70d44b` : `none`" :stroke="Math.round(reviews?.rating) < i ? `#70d44b` : ``" :stroke-width="Math.round(reviews?.rating) < i ? `1` : ``" />
                                    </g>
                                </svg>
                            </template>
                            <!--                            <SimpleRating  v-model="value" :cancel="false" />-->
                            <!--                            <Rating :grade="3" :maxStars="5" :hasCounter="true" />-->

                            <!--                            <svg width="89.308" height="14.015" viewBox="0 0 89.308 14.015" class="cursor-pointer">-->
                            <!--                                <g id="Group_3480" data-name="Group 3480">-->
                            <!--                                    <g id="Group_568" data-name="Group 568">-->
                            <!--                                        <path-->
                            <!--                                            id="star-line"-->
                            <!--                                            d="M13.05,16.392a.983.983,0,0,1-.549-.168L9.2,14.01a.1.1,0,0,0-.111,0l-3.3,2.214a.983.983,0,0,1-1.5-1.067l1.085-3.825a.1.1,0,0,0-.035-.106L2.212,8.768A.983.983,0,0,1,2.783,7l3.985-.151a.1.1,0,0,0,.089-.066L8.229,3.048a.983.983,0,0,1,1.846,0L11.448,6.78a.1.1,0,0,0,.089.066L15.521,7a.983.983,0,0,1,.571,1.771l-3.126,2.457a.1.1,0,0,0-.035.106l1.085,3.825a.987.987,0,0,1-.965,1.235ZM9.145,13.107a.978.978,0,0,1,.545.186l3.3,2.214a.1.1,0,0,0,.151-.111l-1.085-3.825a.978.978,0,0,1,.341-1.04l3.126-2.457a.1.1,0,0,0-.058-.177l-3.985-.151A.983.983,0,0,1,10.6,7.1L9.225,3.367a.1.1,0,0,0-.186,0L7.68,7.094a.983.983,0,0,1-.886.646L2.81,7.891a.1.1,0,0,0-.058.177l3.13,2.462a.983.983,0,0,1,.341,1.04L5.143,15.4a.093.093,0,0,0,.035.106.089.089,0,0,0,.115,0l3.3-2.214a.983.983,0,0,1,.545-.164Z"-->
                            <!--                                            transform="translate(1002.914 -2.377)"-->
                            <!--                                            fill="#70d44b"-->
                            <!--                                        />-->
                            <!--                                        <path-->
                            <!--                                            id="star-solid"-->
                            <!--                                            d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"-->
                            <!--                                            fill="#70d44b"-->
                            <!--                                            v-for="(rating, index) in reviews.rating"-->
                            <!--                                            :key="index"-->
                            <!--                                        />-->
                            <!--                                    </g>-->
                            <!--                                </g>-->
                            <!--                            </svg>-->
                        </a>
                        <div class="flex">
                            <a class="mr-4" @click="ShareNavigate">
                                <svg class="block md:hidden mr-3" width="16" height="20" viewBox="0 0 20.981 26.525">
                                    <g id="share-outline" transform="translate(1.25 1.25)" opacity="0.5">
                                        <path id="Path_77" data-name="Path 77" d="M20.61,13.5h2.31a2.31,2.31,0,0,1,2.31,2.31V26.9a2.31,2.31,0,0,1-2.31,2.31H9.06A2.31,2.31,0,0,1,6.75,26.9V15.81A2.31,2.31,0,0,1,9.06,13.5h2.31"
                                              transform="translate(-6.75 -5.184)" fill="none" stroke="#231f20" stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" />
                                        <path id="Path_78" data-name="Path 78" d="M21.615,8,17,3.375,12.375,8" transform="translate(-7.755 -3.375)" fill="none" stroke="#231f20" stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" />
                                        <path id="Path_79" data-name="Path 79" d="M18,19.141V3.375" transform="translate(-8.76 -3.375)" fill="none" stroke="#231f20" stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" />
                                    </g>
                                </svg>
                            </a>
                            <svg width="20" height="20" viewBox="0 0 28.164 23.242" class="cursor-pointer" @click="toogleFavourite" v-if="!favouriteSate">
                                <path id="_23" data-name="23"
                                      d="M16.1,27.012a1.412,1.412,0,0,1-.833-.268L4.8,19.034a.734.734,0,0,1-.155-.141A8.833,8.833,0,0,1,16.1,5.507a8.839,8.839,0,0,1,11.522.861h0a8.853,8.853,0,0,1,0,12.525.735.735,0,0,1-.155.141L16.932,26.687A1.412,1.412,0,0,1,16.1,27.012ZM6.5,16.8l9.6,7.06,9.6-7.06a6.043,6.043,0,0,0-.071-8.472h0a6.029,6.029,0,0,0-8.472,0,1.412,1.412,0,0,1-2.005,0A6.056,6.056,0,0,0,6.5,16.8Z"
                                      transform="translate(-2.052 -3.77)" fill="#231f20" opacity="0.5" />
                                <path id="Path_50" data-name="Path 50" d="M2605.027,1375.483l3.363-2.4,5.89.491,4.247,2.575v4.546l-.806,4.555-4.806,4.015-7.888,2.158-4.967-3.347-4.8-6.758-.3-4.122,1.56-2.432,4.8-1.19Z"
                                      transform="translate(-2593 -1371.54)" fill="#231f20" opacity="0" />
                            </svg>
                            <svg width="28.164" height="23.242" viewBox="0 0 28.164 23.242" @click="toogleFavourite" v-if="favouriteSate">
                                <path id="_23" data-name="23"
                                      d="M16.1,27.012a1.412,1.412,0,0,1-.833-.268L4.8,19.034a.734.734,0,0,1-.155-.141A8.833,8.833,0,0,1,16.1,5.507a8.839,8.839,0,0,1,11.522.861h0a8.853,8.853,0,0,1,0,12.525.735.735,0,0,1-.155.141L16.932,26.687A1.412,1.412,0,0,1,16.1,27.012ZM6.5,16.8l9.6,7.06,9.6-7.06a6.043,6.043,0,0,0-.071-8.472h0a6.029,6.029,0,0,0-8.472,0,1.412,1.412,0,0,1-2.005,0A6.056,6.056,0,0,0,6.5,16.8Z"
                                      transform="translate(-2.052 -3.77)" fill="#eb3b5a" />
                                <path id="Path_50" data-name="Path 50" d="M2605.422,1373.578l3.578,1.19,5.655-1.681,5.6,6.636a23.222,23.222,0,0,1-3.26,5.942,31.3,31.3,0,0,1-4.188,3.753l-5.915,4.255-5.9-5.117-6.04-5.024V1377.2l1.56-2.432,4.8-1.19Z"
                                      transform="translate(-2593 -1371.54)" fill="#eb3b5a" />
                            </svg>
                        </div>
                    </div>
                    <div class="text-checkoutgray text-sm mt-1 ts:mt-0 2xl:mt-1" v-if="reviews !== undefined && reviews.data && reviews.data.length > 0">
                        <span class="text-black font-bold">{{ reviews.total }}</span>
                        değerlendirme ( {{ reviews.rating }} )
                    </div>
                    <div v-if="auth.user && auth.user.email.endsWith('@kiralabunu.com')">
                        <a :href="`https://kiralabunu-urun.kiralabunu.com/hub/products/${product.id}`" target="_blank">
                            <h1 class="font-bold text-xl mt-1 ts:mt-0 2xl:mt-1">{{ product.attribute_data.name?.tr }}</h1>
                        </a>
                    </div>
                    <div v-else>
                        <h1 class="font-bold text-xl mt-1 ts:mt-0 2xl:mt-1">{{ product.attribute_data.name?.tr }}</h1>
                    </div>
                    <div class="">
                        <Link :href="`/marka/${product.brand?.name?.toLowerCase()}`" class="text-sm text-kbgreen font-semibold">{{ product.brand.name }}</Link>
                    </div>
                    <div class="text-sm lg:text-xs mt-1 ts:mt-0 2xl:mt-1 text-kbgray" v-html="product.attribute_data.excerpt?.tr"></div>
                    <div class="flex mt-2 ts:mt-1 2xl:mt-2 items-end">
                        <span class="text-sm ts:text-base leading-none mb-1 mr-2 text-[#FE1151] line-through whitespace-nowrap" v-if="comparePrice > 0">{{ comparePrice }} TL </span>
                        <span class="text-2xl 2xl:text-3xl leading-none mr-1 whitespace-nowrap"> {{ price }} TL </span>
                        <span class="text-2xs md:text-sm text-black self-center ml-2 md:ml-0 leading-none font-santralregular"> / Aylık ödenecek tutar</span>
                    </div>
                    <div class="mt-4 bg-[#f4fbf1] py-2 px-2 md:px-4 rounded-lg md:rounded-2lg" v-if="false">
                        <div class="text-xs md:text-base text-[#7B7979] leading-none flex flex-col-reverse md:flex-row justify-between items-center">
                            <div class="w-full">
                                <input type="checkbox" id="discount" class="cursor-pointer bg-[#ace697] border-2 w-5 h-5 md:w-6 md:h-6 text-green-600 border-white rounded focus:ring-green-500" />
                                <label for="discount" class="cursor-pointer ml-1 md:ml-2">6 ay ve üzeri toplu ödemelerde %20'ye varan indirim</label>
                            </div>
                            <div class="flex items-center justify-between w-full md:w-auto">
                                <div class="text-lg whitespace-nowrap">X TL</div>
                                <Popover v-slot="{ open }" class="relative">
                                    <PopoverButton :class="open ? 'text-white' : 'text-white/90'" class="group inline-flex items-center px-3 py-2 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75">
                                        <svg id="question-circle-fill" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 md:w-6 md:h-6" viewBox="0 0 14.141 14.141">
                                            <path id="Path_92" data-name="Path 92"
                                                  d="M14.141,7.07A7.07,7.07,0,1,1,7.07,0a7.07,7.07,0,0,1,7.07,7.07ZM5.807,5.332H4.64A2.168,2.168,0,0,1,7.076,3.093c1.235,0,2.362.645,2.362,1.98a2.157,2.157,0,0,1-1.1,1.818c-.651.494-.892.679-.892,1.314v.314H6.29l-.006-.409A2.014,2.014,0,0,1,7.316,6.353c.521-.392.853-.65.853-1.212A1.027,1.027,0,0,0,7.007,4.109a1.128,1.128,0,0,0-1.2,1.223Zm1.106,5.694a.828.828,0,1,1,0-1.65.829.829,0,1,1,0,1.65Z"
                                                  transform="translate(0 0)" fill="#ace697" fill-rule="evenodd" />
                                        </svg>
                                    </PopoverButton>

                                    <transition enter-active-class="transition duration-200 ease-out" enter-from-class="translate-y-1 opacity-0" enter-to-class="translate-y-0 opacity-100" leave-active-class="transition duration-150 ease-in"
                                                leave-from-class="translate-y-0 opacity-100" leave-to-class="translate-y-1 opacity-0">
                                        <PopoverPanel class="absolute left-0 z-10 mt-3 w-screen max-w-sm -translate-x-3/4 -translate-y-full px-4 sm:px-0">
                                            <div class="overflow-hidden rounded-lg shadow-lg ring-1 ring-black/5 border-2 border-kbgreen">
                                                <div class="bg-gray-50 p-4">
                                                    <span class="flex items-center">
                                                        <span class="text-sm font-medium text-gray-900"> Lorem</span>
                                                    </span>
                                                    <span class="block text-sm text-gray-500"> İpsum </span>
                                                </div>
                                            </div>
                                        </PopoverPanel>
                                    </transition>
                                </Popover>
                            </div>
                        </div>
                    </div>

                    <div class="px-4 lg:px-6 py-2 bg-[#f4fbf1] rounded-2lg mt-2" v-if="false">
                        <div class="font-bold text-sm md:text-base mt-1 ts:mt-0 2xl:mt-1">6 ay ve üzeri toplu ödemelerde %20'ye varan indirim</div>
                    </div>
                    <!--                    v-if="product.tags && product.tags.filter((x) => x.id == 5).length != 1 && product.collections.filter((x) => x.id == 107).length != 1 && product.tags.filter((x) => x.id == 32).length != 1"-->
                    <template v-if="false">
                        <div class="px-4 lg:px-6 py-2 bg-[#FCF3EC] rounded-2lg mt-2" v-if="!isMobilite && product.brand?.name?.toLowerCase() != `honda` && product.brand?.name?.toLowerCase() != `yamaha`">
                            <div class="flex justify-start items-center">
                                <img class="w-[30px] h-[30px]" src="../../images/svg/discount.svg" alt="" />
                                <div class="ml-4 text-sm mt-1 ts:mt-0 2xl:mt-1">Yeni üyelere özel 6 ay ve üzeri kiralamaların ilk ayında 500 tl indirim Kiralabunu’dan hediye! İndirim kodu: merhaba500</div>
                            </div>
                        </div>
                    </template>
                    <!--                    <template v-if="product.tags && product.tags.filter((x) => x.id == 32).length == 1">-->
                    <!--                        <div class="px-4 lg:px-6 py-2 bg-[#FCF3EC] rounded-2lg mt-2">-->
                    <!--                            <div class="flex justify-start items-center">-->
                    <!--                                <img class="w-[30px] h-[30px]" src="../../images/svg/discount.svg" alt="" />-->
                    <!--                                <div class="ml-4 text-sm mt-1 ts:mt-0 2xl:mt-1">6 ay ve üzeri kiralamaların ilk 3 ayında geçerli %30 indirim! İndirim kodu: şubat30</div>-->
                    <!--                            </div>-->
                    <!--                        </div>-->
                    <!--                    </template>-->

                    <!--                    <div class="px-4 lg:px-6 py-2 bg-kbblue rounded-2lg mt-2" v-if="product.tags && product.tags.filter((x) => x.id == 5).length == 1">-->
                    <!--                    <div class="px-4 lg:px-6 py-2 bg-kbblue rounded-2lg mt-2" v-if="product.collections && !product.collections.some(x => [96, 97, 98, 100, 106, 107].includes(x.id))">-->
                    <!--                        <div class="flex justify-start items-center">-->
                    <!--                            <img class="w-[30px] h-[30px]" src="../../images/svg/gift.svg" alt="" />-->
                    <!--                            <div class="ml-4 text-sm mt-1 ts:mt-0 2xl:mt-1 text-white">6 ay ve üzeri kiralamaların ilk ayında %90 indirim. İndirim Kodu: mayıs90</div>-->
                    <!--                        </div>-->
                    <!--                    </div>-->
                    <div class="px-4 lg:px-6 py-2 bg-kbblue rounded-2lg mt-2" v-if="product.collections && product.collections.some(x => [37, 107].includes(x.id))">
                        <div class="flex justify-start items-center">
                            <img class="w-[30px] h-[30px]" src="../../images/svg/gift.svg" alt="" />
                            <div class="ml-4 text-sm mt-1 ts:mt-0 2xl:mt-1 text-white">Bu ürün kira süresi sonunda senin olur! Kira öder gibi sahip ol.</div>
                        </div>
                    </div>
                    <div class="px-4 lg:px-6 py-2 bg-kbblue rounded-2lg mt-2" v-if="product.collections && !product.collections.some(x => [37, 36, 47, 49, 50, 51, 52, 91, 96, 97, 98, 100, 106, 107].includes(x.id))">
                        <div class="flex justify-start items-center">
                            <img class="w-[30px] h-[30px]" src="../../images/svg/gift.svg" alt="" />
                            <div class="ml-4 text-sm mt-1 ts:mt-0 2xl:mt-1 text-white">Tek fiyat kampanyası başladı! 3 ay ve üzeri kiralamalarda en ucuz fiyatı öde.</div>
                        </div>
                    </div>
                    <div class="px-4 lg:px-6 py-2 bg-kbblue rounded-2lg mt-2" v-if="product.collections && product.collections.some(x => [36, 47, 49, 50, 51, 52, 91].includes(x.id))">
                        <div class="flex justify-start items-center">
                            <img class="w-[30px] h-[30px]" src="../../images/svg/gift.svg" alt="" />
                            <div class="ml-4 text-sm mt-1 ts:mt-0 2xl:mt-1 text-white">12 ay ve üzeri kiralamalarda ilk ay kirası sepette 1 TL!</div>
                        </div>
                    </div>
                </div>
                <div class="bg-kb-mid-grey md:bg-white md:shadow-productshadow w-full mt-0 p-4 rounded-md ts:rounded-none">
                    <!--                    <div class="flex text-base xl:text-xl text-center cursor-pointer text-gray-500 h-14 xl:h-18 ">-->
                    <!--                        <div class="border-y-2 w-1/5 p-2 border-l-2 bg-white" :class="[ selectedMonth == 1 ? selectedClass : 'rounded-l-full' ]" @click="selectMonth(1)">1 Ay</div>-->
                    <!--                        <div class="border-y-2 w-1/5 p-2 bg-white" :class="[ selectedMonth == 2 ? selectedClass : '' ]" @click="selectMonth(2)">3 Ay</div>-->
                    <!--                        <div class="border-y-2 w-1/5 p-2 bg-white" :class="[ selectedMonth == 3 ? selectedClass : '' ]" @click="selectMonth(3)">6 Ay</div>-->
                    <!--                        <div class="border-y-2 w-1/5 p-2 bg-white" :class="[ selectedMonth == 4 ? selectedClass : '-' ]" @click="selectMonth(4)">12 Ay</div>-->
                    <!--                        <div class="border-y-2 border-r-2 w-1/5 p-2 bg-white" :class="[ selectedMonth == 5 ? selectedClass : 'rounded-r-full' ]" @click="selectMonth(5)">18 Ay</div>-->
                    <!--                    </div>-->
                    <div class="flex text-sm lg:text-base xl:text-xl text-center cursor-pointer text-gray-500 h-12 md:h-20 ts:h-20 xl:h-20">
                        <ul class="flex border rounded-full bg-white w-full items-center justify-center shadow-xl h-8 md:h-12 md:max-w-[550px] mx-auto">
                            <li class="whitespace-nowrap w-16 md:w-20 md:text-lg hover:scale-125 transform duration-300 ease-in-out" :class="[selectedMonth == item[1].subscription_months_id ? selectedClass : '']"
                                @click="selectMonth(item[1].subscription_months_id)" v-for="item in subscribetionMonthsOrdered">
                                {{ item[1].subscription_months?.name }}
                            </li>
                            <!--                            <li class="w-16 md:w-24 md:text-lg" :class="[selectedMonth == 2 ? selectedClass : '']" @click="selectMonth(2)">3 Ay</li>-->
                            <!--                            <li class="w-16 md:w-24 md:text-lg" :class="[selectedMonth == 3 ? selectedClass : '']" @click="selectMonth(3)">6 Ay</li>-->
                            <!--                            <li class="w-16 md:w-24 md:text-lg" :class="[selectedMonth == 4 ? selectedClass : '']" @click="selectMonth(4)">12 Ay</li>-->
                            <!--                            <li class="w-16 md:w-24 md:text-lg" :class="[selectedMonth == 5 ? selectedClass : '']" @click="selectMonth(5)">18 Ay</li>-->
                        </ul>
                    </div>
                    <!--                    <div class="flex mt-3 items-end">-->
                    <!--                        <span class="font-semibold text-3xl leading-none mr-1"> {{ price }} TL /</span>-->
                    <!--                        <span class="text-2xs md:text-base text-black self-center ml-2 md:ml-0 leading-none font-kiralabunuthin">Aylık ödenecek tutar</span>-->
                    <!--                    </div>-->
                    <template v-if="!isMobilite && product.brand?.name?.toLowerCase() != `honda` && product.brand?.name?.toLowerCase() != `yamaha`">
                        <div class="flex items-center mt-2 ts:mt-1 2xl:mt-2" v-if="product.collections.filter((x) => x.id == 107).length != 1 && product.brand?.id != 26">
                            <svg id="Group_556" data-name="Group 556" width="25" height="25.009" viewBox="0 0 25 25.009">
                                <g id="umbrella-outline" transform="translate(5.966 6.25)">
                                    <path id="Path_82" data-name="Path 82" d="M12.892,23.59A1.394,1.394,0,0,1,11.5,22.2a.472.472,0,0,1,.943,0,.449.449,0,1,0,.9,0V17.6a.472.472,0,0,1,.943,0v4.6A1.394,1.394,0,0,1,12.892,23.59Z"
                                          transform="translate(-7.359 -10.683)" />
                                    <path id="Path_83" data-name="Path 83"
                                          d="M13.811,9.426a.472.472,0,0,1-.334-.138,1.481,1.481,0,0,0-2.094,0,.472.472,0,0,1-.667,0A1.78,1.78,0,0,0,8.39,9.122l-.278.209a.472.472,0,0,1-.566,0l-.279-.209a1.78,1.78,0,0,0-2.326.165.472.472,0,0,1-.667,0,1.481,1.481,0,0,0-2.094,0,.472.472,0,0,1-.805-.334,6.455,6.455,0,0,1,12.4-2.512,6.414,6.414,0,0,1,.507,2.512.472.472,0,0,1-.472.472ZM6.2,7.823a2.7,2.7,0,0,1,1.629.541,2.725,2.725,0,0,1,3.225-.026,2.434,2.434,0,0,1,2.214-.281A5.513,5.513,0,0,0,2.751,6.809,5.455,5.455,0,0,0,2.39,8.058,2.434,2.434,0,0,1,4.6,8.339,2.7,2.7,0,0,1,6.2,7.823Z"
                                          transform="translate(-1.375 -2.04)" />
                                    <path id="Path_84" data-name="Path 84" d="M16.472,2.779A.472.472,0,0,1,16,2.307v-.46a.472.472,0,1,1,.943,0v.46A.472.472,0,0,1,16.472,2.779Z" transform="translate(-10.018 -1.375)" />
                                </g>
                                <g id="noun-circle-175697">
                                    <path id="Path_86" data-name="Path 86"
                                          d="M110.41,29.739c-.006-.659-.423-1.182-.429-1.841s.407-1.456.4-2.115c-.208-.625-.768-.992-.977-1.617s-.065-1.512-.271-2.137c-.392-.532-1.039-.706-1.428-1.235s-.529-1.418-.918-1.947c-.535-.383-1.2-.351-1.742-.734s-.939-1.185-1.477-1.568c-.628-.2-1.253.04-1.882-.162s-1.26-.837-1.888-1.036c-.659.006-1.182.423-1.841.429s-1.456-.407-2.115-.4c-.625.208-.992.768-1.617.977s-1.512.065-2.137.274c-.532.392-.706,1.039-1.235,1.428s-1.418.529-1.947.921c-.383.535-.351,1.2-.734,1.742s-1.185.939-1.567,1.477c-.2.628.037,1.253-.162,1.882s-.837,1.26-1.036,1.888c0,.659.423,1.182.429,1.841s-.407,1.456-.4,2.115c.208.625.768.992.977,1.617s.065,1.512.274,2.137c.392.532,1.039.706,1.428,1.238s.529,1.418.918,1.947c.535.383,1.2.351,1.742.734s.939,1.185,1.477,1.568c.628.2,1.253-.037,1.882.162s1.26.837,1.888,1.036c.659-.006,1.182-.423,1.841-.429s1.456.407,2.115.4c.625-.208.992-.768,1.617-.977s1.512-.065,2.137-.274c.532-.392.706-1.039,1.238-1.428s1.418-.529,1.947-.918c.383-.535.351-1.2.734-1.742s1.185-.939,1.568-1.477c.2-.628-.037-1.253.162-1.882s.831-1.266,1.03-1.894Zm-1.095,1.816c-.283.557-.977,1.051-1.263,1.608s-.159,1.179-.442,1.736c-.442.442-1.253.7-1.7,1.138s-.513,1.073-.958,1.515c-.557.283-1.409.274-1.966.56s-.821.862-1.378,1.145c-.619.1-1.424-.174-2.044-.078s-1.048.566-1.664.663c-.619-.1-1.3-.606-1.919-.7s-1.17.215-1.788.115c-.557-.283-1.051-.98-1.608-1.263s-1.179-.159-1.739-.442c-.442-.442-.7-1.253-1.138-1.7s-1.073-.516-1.515-.958c-.283-.557-.274-1.409-.56-1.966s-.862-.821-1.145-1.378c-.1-.619.174-1.424.078-2.044s-.566-1.048-.663-1.664c.1-.619.606-1.3.7-1.919s-.215-1.17-.115-1.788c.283-.557.98-1.051,1.263-1.608s.159-1.179.442-1.739c.442-.442,1.253-.7,1.7-1.138s.516-1.073.958-1.515c.557-.283,1.409-.274,1.966-.56s.821-.862,1.378-1.145c.619-.1,1.424.174,2.044.078s1.048-.566,1.664-.663c.619.1,1.3.606,1.919.7s1.17-.215,1.788-.115c.557.283,1.051.98,1.608,1.263s1.179.159,1.739.442c.442.442.7,1.253,1.138,1.7s1.073.516,1.515.958c.283.557.274,1.409.56,1.966s.862.821,1.145,1.378c.1.619-.174,1.424-.078,2.044s.566,1.048.663,1.664c-.1.619-.606,1.3-.7,1.919s.215,1.172.115,1.791Z"
                                          transform="translate(-85.41 -15.348)" opacity="0.998" />
                                </g>
                            </svg>
                            <p class="ml-2 text-sm text-[#7B7979] leading-none">Hasar Onarım Garantisi</p>
                        </div>
                        <div class="flex items-center mt-1 ts:mt-1 2xl:mt-1" v-if="product.brand?.id != 26">
                            <svg width="25" height="25.009" viewBox="0 0 25 25.009">
                                <g id="Group_5004" data-name="Group 5004" transform="translate(-414.95)">
                                    <g id="Group_5013" data-name="Group 5013" transform="translate(414.95)">
                                        <path id="Path_3745" data-name="Path 3745"
                                              d="M110.41,29.739c-.006-.659-.423-1.182-.429-1.841s.407-1.456.4-2.115c-.208-.625-.768-.992-.977-1.617s-.065-1.512-.271-2.137c-.392-.532-1.039-.706-1.428-1.235s-.529-1.418-.918-1.947c-.535-.383-1.2-.351-1.742-.734s-.939-1.185-1.477-1.568c-.628-.2-1.253.04-1.882-.162s-1.26-.837-1.888-1.036c-.659.006-1.182.423-1.841.429s-1.456-.407-2.115-.4c-.625.208-.992.768-1.617.977s-1.512.065-2.137.274c-.532.392-.706,1.039-1.235,1.428s-1.418.529-1.947.921c-.383.535-.351,1.2-.734,1.742s-1.185.939-1.568,1.477c-.2.628.037,1.253-.162,1.882s-.837,1.26-1.036,1.888c0,.659.423,1.182.429,1.841s-.407,1.456-.4,2.115c.208.625.768.992.977,1.617s.065,1.512.274,2.137c.392.532,1.039.706,1.428,1.238s.529,1.418.918,1.947c.535.383,1.2.351,1.742.734s.939,1.185,1.477,1.568c.628.2,1.253-.037,1.882.162s1.26.837,1.888,1.036c.659-.006,1.182-.423,1.841-.429s1.456.407,2.115.4c.625-.208.992-.768,1.617-.977s1.512-.065,2.137-.274c.532-.392.706-1.039,1.238-1.428s1.418-.529,1.947-.918c.383-.535.351-1.2.734-1.742s1.185-.939,1.568-1.477c.2-.628-.037-1.253.162-1.882s.831-1.266,1.03-1.894Zm-1.095,1.816c-.283.557-.977,1.051-1.263,1.608s-.159,1.179-.442,1.736c-.442.442-1.253.7-1.7,1.138s-.513,1.073-.958,1.515c-.557.283-1.409.274-1.966.56s-.821.862-1.378,1.145c-.619.1-1.424-.174-2.044-.078s-1.048.566-1.664.663c-.619-.1-1.3-.606-1.919-.7s-1.17.215-1.788.115c-.557-.283-1.051-.98-1.608-1.263s-1.179-.159-1.739-.442c-.442-.442-.7-1.253-1.138-1.7s-1.073-.516-1.515-.958c-.283-.557-.274-1.409-.56-1.966s-.862-.821-1.145-1.378c-.1-.619.174-1.424.078-2.044s-.566-1.048-.663-1.664c.1-.619.606-1.3.7-1.919s-.215-1.17-.115-1.788c.283-.557.98-1.051,1.263-1.608s.159-1.179.442-1.739c.442-.442,1.253-.7,1.7-1.138s.516-1.073.958-1.515c.557-.283,1.409-.274,1.966-.56s.821-.862,1.378-1.145c.619-.1,1.424.174,2.044.078s1.048-.566,1.664-.663c.619.1,1.3.606,1.919.7s1.17-.215,1.788-.115c.557.283,1.051.98,1.608,1.263s1.179.159,1.739.442c.442.442.7,1.253,1.138,1.7s1.073.516,1.515.958c.283.557.274,1.409.56,1.966s.862.821,1.145,1.378c.1.619-.174,1.424-.078,2.044s.566,1.048.663,1.664c-.1.619-.606,1.3-.7,1.919s.215,1.172.115,1.791Z"
                                              transform="translate(-85.41 -15.348)" opacity="0.998" />
                                        <g id="Group_5008" data-name="Group 5008" transform="translate(6.748 6.058)">
                                            <path id="Path_3751" data-name="Path 3751" d="M3.649,9.094A1.189,1.189,0,0,1,4.835,8H12A1.189,1.189,0,0,1,13.19,9.094l.477,5.946a1.189,1.189,0,0,1-1.185,1.284H4.356A1.189,1.189,0,0,1,3.171,15.04Z"
                                                  transform="translate(-2.668 -3.932)" fill="rgba(0,0,0,0)" />
                                            <path id="Path_3751_-_Outline" data-name="Path 3751 - Outline"
                                                  d="M4.834,7.5v0H12a1.7,1.7,0,0,1,1.684,1.554L14.166,15a1.689,1.689,0,0,1-1.683,1.824H4.356A1.689,1.689,0,0,1,2.673,15L3.15,9.054A1.7,1.7,0,0,1,4.834,7.5Zm7.648,8.325a.689.689,0,0,0,.687-.744l-.477-5.946A.693.693,0,0,0,12,8.5H4.834a.693.693,0,0,0-.687.634L3.67,15.08a.689.689,0,0,0,.687.744Z"
                                                  transform="translate(-2.668 -3.932)" />
                                            <path id="Path_3752" data-name="Path 3752" d="M12.757,7.352V4.378A2.378,2.378,0,0,0,10.378,2h0A2.378,2.378,0,0,0,8,4.378V7.352" transform="translate(-4.627 -1.5)" fill="rgba(0,0,0,0)" />
                                            <path id="Path_3752_-_Outline" data-name="Path 3752 - Outline"
                                                  d="M12.757,7.852a.5.5,0,0,1-.5-.5V4.378a1.878,1.878,0,1,0-3.757,0V7.352a.5.5,0,1,1-1,0V4.378a2.878,2.878,0,1,1,5.757,0V7.352A.5.5,0,0,1,12.757,7.852Z" transform="translate(-4.627 -1.5)" />
                                        </g>
                                    </g>
                                </g>
                            </svg>
                            <p class="ml-2 text-sm text-[#7B7979] leading-none">Satın Alma Opsiyonu</p>
                        </div>
                        <div class="flex items-center mt-1 ts:mt-1 2xl:mt-1" v-if="product.collections.filter((x) => x.id == 107).length != 1">
                            <svg width="25" height="25.009" viewBox="0 0 25 25.009">
                                <g id="Group_3704" data-name="Group 3704" transform="translate(3.634)">
                                    <g id="Group_558" data-name="Group 558" transform="translate(-3.634 0)">
                                        <g id="noun-circle-175697">
                                            <path id="Path_86" data-name="Path 86"
                                                  d="M110.41,29.739c-.006-.659-.423-1.182-.429-1.841s.407-1.456.4-2.115c-.208-.625-.768-.992-.977-1.617s-.065-1.512-.271-2.137c-.392-.532-1.039-.706-1.428-1.235s-.529-1.418-.918-1.947c-.535-.383-1.2-.351-1.742-.734s-.939-1.185-1.477-1.568c-.628-.2-1.253.04-1.882-.162s-1.26-.837-1.888-1.036c-.659.006-1.182.423-1.841.429s-1.456-.407-2.115-.4c-.625.208-.992.768-1.617.977s-1.512.065-2.137.274c-.532.392-.706,1.039-1.235,1.428s-1.418.529-1.947.921c-.383.535-.351,1.2-.734,1.742s-1.185.939-1.568,1.477c-.2.628.037,1.253-.162,1.882s-.837,1.26-1.036,1.888c0,.659.423,1.182.429,1.841s-.407,1.456-.4,2.115c.208.625.768.992.977,1.617s.065,1.512.274,2.137c.392.532,1.039.706,1.428,1.238s.529,1.418.918,1.947c.535.383,1.2.351,1.742.734s.939,1.185,1.477,1.568c.628.2,1.253-.037,1.882.162s1.26.837,1.888,1.036c.659-.006,1.182-.423,1.841-.429s1.456.407,2.115.4c.625-.208.992-.768,1.617-.977s1.512-.065,2.137-.274c.532-.392.706-1.039,1.238-1.428s1.418-.529,1.947-.918c.383-.535.351-1.2.734-1.742s1.185-.939,1.568-1.477c.2-.628-.037-1.253.162-1.882s.831-1.266,1.03-1.894Zm-1.095,1.816c-.283.557-.977,1.051-1.263,1.608s-.159,1.179-.442,1.736c-.442.442-1.253.7-1.7,1.138s-.513,1.073-.958,1.515c-.557.283-1.409.274-1.966.56s-.821.862-1.378,1.145c-.619.1-1.424-.174-2.044-.078s-1.048.566-1.664.663c-.619-.1-1.3-.606-1.919-.7s-1.17.215-1.788.115c-.557-.283-1.051-.98-1.608-1.263s-1.179-.159-1.739-.442c-.442-.442-.7-1.253-1.138-1.7s-1.073-.516-1.515-.958c-.283-.557-.274-1.409-.56-1.966s-.862-.821-1.145-1.378c-.1-.619.174-1.424.078-2.044s-.566-1.048-.663-1.664c.1-.619.606-1.3.7-1.919s-.215-1.17-.115-1.788c.283-.557.98-1.051,1.263-1.608s.159-1.179.442-1.739c.442-.442,1.253-.7,1.7-1.138s.516-1.073.958-1.515c.557-.283,1.409-.274,1.966-.56s.821-.862,1.378-1.145c.619-.1,1.424.174,2.044.078s1.048-.566,1.664-.663c.619.1,1.3.606,1.919.7s1.17-.215,1.788-.115c.557.283,1.051.98,1.608,1.263s1.179.159,1.739.442c.442.442.7,1.253,1.138,1.7s1.073.516,1.515.958c.283.557.274,1.409.56,1.966s.862.821,1.145,1.378c.1.619-.174,1.424-.078,2.044s.566,1.048.663,1.664c-.1.619-.606,1.3-.7,1.919s.215,1.172.115,1.791Z"
                                                  transform="translate(-85.41 -15.348)" />
                                        </g>
                                    </g>
                                    <g id="Group_560" data-name="Group 560" transform="translate(1.879 6.691)">
                                        <path id="Rectangle_106" data-name="Rectangle 106"
                                              d="M2.275.91A1.366,1.366,0,0,0,.91,2.275v5.11A1.366,1.366,0,0,0,2.275,8.749H8.521A1.366,1.366,0,0,0,9.886,7.384V2.275A1.366,1.366,0,0,0,8.521.91H2.275m0-.91H8.521A2.275,2.275,0,0,1,10.8,2.275v5.11A2.275,2.275,0,0,1,8.521,9.659H2.275A2.275,2.275,0,0,1,0,7.384V2.275A2.275,2.275,0,0,1,2.275,0Z"
                                              transform="translate(0 0)" />
                                        <path id="Path_90" data-name="Path 90"
                                              d="M-5860.549-13141.212h2.34a.458.458,0,0,1,.428.3l1.122,3.057a.459.459,0,0,1-.055.418.455.455,0,0,1-.373.193h-3.462a.454.454,0,0,1-.454-.455v-3.055A.453.453,0,0,1-5860.549-13141.212Zm2.022.91h-1.567v2.146h2.355Z"
                                              transform="translate(5871.059 13142.686)" />
                                        <path id="Path_91" data-name="Path 91"
                                              d="M-5860.158-13130.456h3.351a.454.454,0,0,1,.454.455v4.279a.454.454,0,0,1-.454.455h-3.351a.455.455,0,0,1-.455-.455V-13130A.455.455,0,0,1-5860.158-13130.456Zm2.9.91h-2.441v3.369h2.441Z"
                                              transform="translate(5870.779 13134.985)" />
                                        <circle id="Ellipse_39" data-name="Ellipse 39" cx="1.989" cy="1.989" r="1.989" transform="translate(8.807 7.386)" fill="#fff" />
                                        <path id="Ellipse_39_-_Outline" data-name="Ellipse 39 - Outline" d="M1.989.91A1.079,1.079,0,1,0,3.067,1.989,1.08,1.08,0,0,0,1.989.91m0-.91A1.989,1.989,0,1,1,0,1.989,1.989,1.989,0,0,1,1.989,0Z"
                                              transform="translate(8.807 7.386)" />
                                        <circle id="Ellipse_40" data-name="Ellipse 40" cx="1.989" cy="1.989" r="1.989" transform="translate(1.136 7.386)" fill="#fff" />
                                        <path id="Ellipse_40_-_Outline" data-name="Ellipse 40 - Outline" d="M1.989.91A1.079,1.079,0,1,0,3.067,1.989,1.08,1.08,0,0,0,1.989.91m0-.91A1.989,1.989,0,1,1,0,1.989,1.989,1.989,0,0,1,1.989,0Z"
                                              transform="translate(1.136 7.386)" />
                                    </g>
                                </g>
                            </svg>
                            <p class="ml-2 text-sm text-[#7B7979] leading-none">{{ product.variants[0].min_delivery_time }}-{{ product.variants[0].max_delivery_time }} İş Günü Arasında Teslimat</p>
                        </div>
                    </template>
                    <div class="mt-1" v-if="isInsurangeOptionsEnabled && insurangePrice > 0 && product.brand?.id != 26">
                        <div class="text-xs md:text-base text-black leading-none flex flex-row justify-between items-center">
                            <div class="w-[90%] md:w-full flex items-center">
                                <input type="checkbox" id="insuranceSelected" v-model="isInsuranceSelected" class="cursor-pointer bg-green-50 border-2 w-5 h-5 md:w-6 md:h-6 text-kbgreen border-black rounded focus:ring-kbgreen" />
                                <label for="insuranceSelected" class="cursor-pointer ml-1 md:ml-2">Onarım garantisini %100'e tamamla <br /><span class="md:hidden text-sm whitespace-nowrap">{{ insurangePrice.toFixed(2) }} TL</span>
                                </label>
                            </div>
                            <div class="w-[10%] md:w-auto flex items-center justify-between">
                                <div class="hidden md:block text-sm md:text-lg whitespace-nowrap">{{ insurangePrice.toFixed(2) }} TL</div>
                                <Popover v-slot="{ open }" class="relative">
                                    <PopoverButton :class="open ? 'text-white' : 'text-white/90'" class="group inline-flex items-center px-3 py-2 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75">
                                        <svg id="question-circle-fill" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 md:w-6 md:h-6" viewBox="0 0 14.141 14.141">
                                            <path id="Path_92" data-name="Path 92"
                                                  d="M14.141,7.07A7.07,7.07,0,1,1,7.07,0a7.07,7.07,0,0,1,7.07,7.07ZM5.807,5.332H4.64A2.168,2.168,0,0,1,7.076,3.093c1.235,0,2.362.645,2.362,1.98a2.157,2.157,0,0,1-1.1,1.818c-.651.494-.892.679-.892,1.314v.314H6.29l-.006-.409A2.014,2.014,0,0,1,7.316,6.353c.521-.392.853-.65.853-1.212A1.027,1.027,0,0,0,7.007,4.109a1.128,1.128,0,0,0-1.2,1.223Zm1.106,5.694a.828.828,0,1,1,0-1.65.829.829,0,1,1,0,1.65Z"
                                                  transform="translate(0 0)" fill="#ace697" fill-rule="evenodd" />
                                        </svg>
                                    </PopoverButton>

                                    <transition enter-active-class="transition duration-200 ease-out" enter-from-class="translate-y-1 opacity-0" enter-to-class="translate-y-0 opacity-100" leave-active-class="transition duration-150 ease-in"
                                                leave-from-class="translate-y-0 opacity-100" leave-to-class="translate-y-1 opacity-0">
                                        <PopoverPanel class="absolute left-0 z-10 mt-3 w-screen max-w-sm -translate-x-3/4 -translate-y-full px-4 sm:px-0">
                                            <div class="overflow-hidden rounded-lg shadow-lg ring-1 ring-black/5 border-2 border-kbgreen">
                                                <div class="bg-gray-50 p-4">
                                                    <span class="flex items-center">
                                                        <span class="text-sm font-medium text-gray-900">Hasar onarımının %100’ü kapsaması için ek sigorta paket ücreti bir seferlik kredi kartından tahsil edilir.</span>
                                                    </span>
                                                    <span class="block text-sm text-gray-500"> Uyarı: Kayıp, çalınma, kasıtlı zarar verme, onarılamaz duruma getirme, cihazın kutusunu kaybetme veya kullanılamaz hale getirme, Türkiye sınırları dışında
                                                        meydana gelen zararlar ve 5000 TL'yi aşan hasarlarda ek güvence paketi geçerli değildir. </span>
                                                </div>
                                            </div>
                                        </PopoverPanel>
                                    </transition>
                                </Popover>
                            </div>
                        </div>
                    </div>
                    <select class="bg-kb-mid-grey w-full mt-2 px-4 py-2 rounded-full border-3 border-bordergray text-base" v-if="product.variants[0].options[0] != null" v-model="selectedVariandId" @change="changeVariant">
                        <!-- Varianst -->
                        <option class="flex text-base cursor-pointer text-gray-500 border border-checkoutgray rounded-full h-12 content-center p-2 mt-5" value="0" v-if="product.variants.length > 1">
                            <div class="w-8 h-8 bg-blue-500 rounded-full">&nbsp;</div>
                            <div>
                                <a href="#" class="w-full rounded-full py-2 px-4 lg:font-bold text-black">Lütfen seçiniz</a>
                            </div>
                        </option>
                        <option class="flex text-base cursor-pointer text-gray-500 border border-checkoutgray rounded-full h-12 content-center p-2 mt-5" v-for="variant in product.variants" :value="variant.id">
                            <div class="w-8 h-8 bg-blue-500 rounded-full">&nbsp;</div>
                            <div>
                                <a href="#" class="w-full rounded-full py-2 px-4 lg:font-bold text-black">{{ variant.options[0] }} {{ variant.purchasable == "in_stock" && variant.stock < 1 ? " / Stokta Yok" : "" }}</a>
                            </div>
                        </option>
                    </select>
                    <div class="w-full mt-4 flex flex-wrap space-y-2 md:space-y-0">
                        <div class="px-1 w-full" :class="{ 'md:w-1/2': product.tags && product.tags.filter((x) => x.id == 28).length === 1 }">
                            <Link href="/sepete-ekle" :data="{
                                product_id: product.id,
                                month: selectedMonth,
                                product_variant_id: selectedVariandId,
                                is_insurance_requested: isInsuranceSelected,
                            }" :disabled="selectedVariandId && product.variants.filter((variant) => variant.id == selectedVariandId)[0]?.purchasable == 'in_stock' && product.variants.filter((variant) => variant.id == selectedVariandId)[0].stock < 1"
                                  method="post" as="button" @click.prevent="addToCart"
                                  class="w-full block text-white rounded-full py-3 px-4 self-center font-semibold text-lg leading-none text-center bg-kbgreen disabled:opacity-50 disabled:cursor-not-allowed">Kirala
                            </Link>
                        </div>
                        <div class="px-4 lg:px-6 py-2 border-2 border-bordergray rounded-2lg !mt-4" v-if="product.collections.filter((x) => x.id == 107).length == 1">
                            <div class="flex justify-start items-center">
                                <img class="w-[30px] h-[30px] hidden md:block" src="../../images/svg/system-assembly.svg" alt="" />
                                <div class="md:ml-4 text-xs md:text-sm mt-1 ts:mt-0 2xl:mt-1 leading-tight font-santralregular">Ağır ve montaj gerektiren üründür. Montaj ve nakliye ücretleri kiralama lokasyonu ve ürüne göre değişiklik gösterebilir.
                                    Ek ücretler kullanıcıya bildirilir, onay sonrası servis ve nakliye yönlendirmesi yapılacaktır.
                                </div>
                            </div>
                        </div>
                        <div class="px-1 w-full md:w-1/2" v-if="product.tags && product.tags.filter((x) => x.id == 28).length === 1">
                            <div @click="openMotorcycleFormModal"
                                 class="w-full cursor-pointer block text-white rounded-full py-3 px-4 self-center font-semibold text-lg leading-none text-center bg-kbgreen disabled:opacity-50 disabled:cursor-not-allowed">Kurumsal
                                Teklif Al
                            </div>
                        </div>
                    </div>
                </div>
                <div class="px-3 lg:px-6 py-2 !mt-4 transition-all ease-in-out bg-kb-mid-grey md:bg-white md:shadow-productshadow w-full cursor-pointer border border-bordergray hover:border-kbgreen rounded-md ts:rounded-none" v-if="false">
                    <!-- v</div>-if="product.tags.filter((x) => x.id == 35).length === 1" @click="openRentAndBuyModal" -->

                    <div class="flex justify-start items-center">
                        <img class="w-[40px] h-[40px]" src="../../images/svg/discount-ticket.svg" alt="" />
                        <div class="md:ml-4 text-xs md:text-sm mt-1 ts:mt-0 2xl:mt-1 leading-tight font-santralregular">
                            <span class="block text-sm md:text-base font-bold cursor-pointer"> Kirala + Satın Al</span>
                            Kiraladığın ürün senin olsun istersen, <span class="underline cursor-pointer">Kirala + Satın Al</span> tam sana göre
                        </div>
                    </div>
                </div>
                <!--                <select class="bg-kb-mid-grey w-full mt-2 px-4 py-2 rounded-full border-3 border-bordergray text-base" v-if="product.variants[0].options[0] != null">-->
                <!--                    &lt;!&ndash; Varianst &ndash;&gt;-->
                <!--                    <option class="flex text-base cursor-pointer text-gray-500 border border-checkoutgray rounded-full h-12 content-center p-2 mt-5" v-for="variant in product.variants" @click="changeVariant(variant)">-->
                <!--                        <div class="w-8 h-8 bg-blue-500 rounded-full">&nbsp;</div>-->
                <!--                        <div>-->
                <!--                            <a href="#" class="w-full rounded-full py-2 px-4 lg:font-bold text-black">{{ variant.options[0] }}</a>-->
                <!--                        </div>-->
                <!--                    </option>-->
                <!--                </select>-->
                <!--
                <div class="grid gap-4 grid-cols-3 mt-4">
                    <div class="bg-kb-light-green p-3 lg:p-6 rounded-lg">
                        <div class="text-center">
                            <svg class="block mx-auto" width="60.38" height="60.401" viewBox="0 0 60.38 60.401">
                                <g id="umbrella-outline" transform="translate(14.409 15.095)">
                                    <path id="Path_82" data-name="Path 82" d="M14.862,32.74A3.366,3.366,0,0,1,11.5,29.378a1.139,1.139,0,0,1,2.279,0,1.084,1.084,0,0,0,2.167,0V18.264a1.139,1.139,0,1,1,2.279,0V29.378A3.366,3.366,0,0,1,14.862,32.74Z" transform="translate(-1.498 -1.566)" fill="#70d44b" />
                                    <path
                                        id="Path_83"
                                        data-name="Path 83"
                                        d="M31.411,19.227a1.139,1.139,0,0,1-.806-.334,3.576,3.576,0,0,0-5.057,0,1.139,1.139,0,0,1-1.611,0,4.3,4.3,0,0,0-5.618-.4l-.673.5a1.139,1.139,0,0,1-1.368,0l-.673-.5a4.3,4.3,0,0,0-5.618.4,1.139,1.139,0,0,1-1.611,0,3.576,3.576,0,0,0-5.057,0,1.139,1.139,0,0,1-1.945-.806,15.591,15.591,0,0,1,29.949-6.068,15.492,15.492,0,0,1,1.225,6.068,1.139,1.139,0,0,1-1.139,1.139ZM13.029,15.356a6.525,6.525,0,0,1,3.934,1.307,6.58,6.58,0,0,1,7.789-.062,5.879,5.879,0,0,1,5.346-.678A13.305,13.305,0,0,0,7.552,8.676a13.27,13.27,0,0,0-3.724,7.247,5.879,5.879,0,0,1,5.346.678A6.531,6.531,0,0,1,13.029,15.356Z"
                                        transform="translate(-1.375 -1.389)"
                                        fill="#70d44b"
                                    />
                                    <path id="Path_84" data-name="Path 84" d="M17.139,4.765A1.139,1.139,0,0,1,16,3.626V2.514a1.139,1.139,0,1,1,2.279,0V3.626A1.139,1.139,0,0,1,17.139,4.765Z" transform="translate(-1.552 -1.375)" fill="#70d44b" />
                                </g>
                                <g id="noun-circle-175697">
                                    <path
                                        id="Path_86"
                                        data-name="Path 86"
                                        d="M145.789,50.1c-.015-1.593-1.022-2.854-1.036-4.447s.984-3.516.969-5.108c-.5-1.51-1.855-2.4-2.359-3.906s-.158-3.651-.654-5.16c-.946-1.285-2.509-1.705-3.448-2.982s-1.277-3.425-2.216-4.7c-1.292-.924-2.907-.849-4.206-1.773s-2.269-2.862-3.568-3.786c-1.517-.481-3.027.1-4.545-.391s-3.042-2.021-4.56-2.5c-1.593.015-2.854,1.022-4.447,1.036s-3.516-.984-5.108-.969c-1.51.5-2.4,1.855-3.906,2.359s-3.651.158-5.16.661c-1.285.946-1.705,2.509-2.982,3.448s-3.425,1.277-4.7,2.223c-.924,1.292-.849,2.907-1.773,4.206s-2.862,2.269-3.786,3.568c-.481,1.517.09,3.027-.391,4.545s-2.021,3.042-2.5,4.56c.008,1.593,1.022,2.854,1.036,4.447s-.984,3.516-.969,5.108c.5,1.51,1.855,2.4,2.359,3.906s.158,3.651.661,5.16c.946,1.285,2.509,1.705,3.448,2.989s1.277,3.425,2.216,4.7c1.292.924,2.907.849,4.206,1.773s2.269,2.862,3.568,3.786c1.517.481,3.027-.09,4.545.391s3.042,2.021,4.56,2.5c1.593-.015,2.854-1.022,4.447-1.036s3.516.984,5.108.969c1.51-.5,2.4-1.855,3.906-2.359s3.651-.158,5.16-.661c1.285-.946,1.705-2.509,2.989-3.448s3.425-1.277,4.7-2.216c.924-1.292.849-2.907,1.773-4.206s2.862-2.269,3.786-3.568c.481-1.517-.09-3.027.391-4.545.467-1.532,2.007-3.057,2.487-4.575Zm-2.644,4.387c-.684,1.345-2.359,2.539-3.05,3.884s-.383,2.847-1.067,4.192c-1.067,1.067-3.027,1.683-4.1,2.749s-1.24,2.592-2.313,3.658c-1.345.684-3.4.661-4.747,1.352s-1.983,2.081-3.328,2.764c-1.495.233-3.44-.421-4.935-.188s-2.531,1.367-4.019,1.6c-1.495-.233-3.14-1.465-4.635-1.7s-2.825.518-4.319.278c-1.345-.684-2.539-2.366-3.884-3.05s-2.847-.383-4.2-1.067c-1.067-1.067-1.683-3.027-2.749-4.1s-2.592-1.247-3.658-2.314c-.684-1.345-.661-3.4-1.352-4.747s-2.081-1.983-2.764-3.328c-.233-1.495.421-3.44.188-4.935s-1.367-2.531-1.6-4.019c.24-1.495,1.465-3.14,1.7-4.635s-.518-2.825-.278-4.319c.684-1.345,2.366-2.539,3.05-3.884s.383-2.847,1.067-4.2c1.067-1.067,3.027-1.683,4.1-2.749s1.247-2.592,2.313-3.658c1.345-.684,3.4-.661,4.747-1.352s1.983-2.081,3.328-2.764c1.495-.233,3.44.421,4.935.188s2.531-1.367,4.019-1.6c1.495.233,3.14,1.465,4.635,1.7s2.825-.518,4.319-.278c1.345.684,2.539,2.366,3.884,3.05s2.847.383,4.2,1.067c1.067,1.067,1.683,3.027,2.749,4.1s2.592,1.247,3.658,2.313c.684,1.345.661,3.4,1.352,4.747s2.081,1.983,2.764,3.328c.24,1.495-.421,3.44-.188,4.935s1.367,2.531,1.6,4.019c-.233,1.495-1.465,3.14-1.7,4.635s.518,2.832.278,4.327Z"
                                        transform="translate(-85.41 -15.348)"
                                        fill="#70d44b"
                                        opacity="0.998"
                                    />
                                </g>
                            </svg>
                            <p class="text-base mt-4">Ürün Hasar Garantisi</p>
                        </div>
                    </div>
                    <div class="bg-kb-light-green p-3 lg:p-6 rounded-lg">
                        <div class="text-center">
                            <svg class="block mx-auto" width="60.38" height="60.401" viewBox="0 0 60.38 60.401">
                                <g id="Group_4447" data-name="Group 4447" transform="translate(-892 -1113.211)">
                                    <g id="Group_559" data-name="Group 559" transform="translate(902 1130.34)">
                                        <g id="Rectangle_105" data-name="Rectangle 105" fill="none" stroke="#70d44b" stroke-width="2">
                                            <rect width="36.303" height="26.809" rx="7" stroke="none" />
                                            <rect x="1" y="1" width="34.303" height="24.809" rx="6" fill="none" />
                                        </g>
                                        <path id="Path_87" data-name="Path 87" d="M-5812.605-13098.068v14.307" transform="translate(5851.922 13105.216)" fill="none" stroke="#70d44b" stroke-linecap="round" stroke-width="2" />
                                        <g id="check-circle" transform="translate(12.117 7.767)">
                                            <path id="Path_89" data-name="Path 89" d="M21.122,11.066a1.3,1.3,0,1,1,1.857,1.82L16.06,21.535a1.3,1.3,0,0,1-1.872.035L9.6,16.983a1.3,1.3,0,1,1,1.837-1.837l3.63,3.627L21.089,11.1a.409.409,0,0,1,.035-.038Z" transform="translate(-9.182 -10.676)" fill="#70d44b" fill-rule="evenodd" />
                                        </g>
                                    </g>
                                    <path
                                        id="Path_3745"
                                        data-name="Path 3745"
                                        d="M145.789,50.1c-.015-1.593-1.022-2.854-1.036-4.447s.984-3.516.969-5.108c-.5-1.51-1.855-2.4-2.359-3.906s-.158-3.651-.654-5.16c-.946-1.285-2.509-1.705-3.448-2.982s-1.277-3.425-2.216-4.7c-1.292-.924-2.907-.849-4.206-1.773s-2.269-2.862-3.568-3.786c-1.517-.481-3.027.1-4.545-.391s-3.042-2.021-4.56-2.5c-1.593.015-2.854,1.022-4.447,1.036s-3.516-.984-5.108-.969c-1.51.5-2.4,1.855-3.906,2.359s-3.651.158-5.16.661c-1.285.946-1.705,2.509-2.982,3.448s-3.425,1.277-4.7,2.223c-.924,1.292-.849,2.907-1.773,4.206s-2.862,2.269-3.786,3.568c-.481,1.517.09,3.027-.391,4.545s-2.021,3.042-2.5,4.56c.008,1.593,1.022,2.854,1.036,4.447s-.984,3.516-.969,5.108c.5,1.51,1.855,2.4,2.359,3.906s.158,3.651.661,5.16c.946,1.285,2.509,1.705,3.448,2.989s1.277,3.425,2.216,4.7c1.292.924,2.907.849,4.206,1.773s2.269,2.862,3.568,3.786c1.517.481,3.027-.09,4.545.391s3.042,2.021,4.56,2.5c1.593-.015,2.854-1.022,4.447-1.036s3.516.984,5.108.969c1.51-.5,2.4-1.855,3.906-2.359s3.651-.158,5.16-.661c1.285-.946,1.705-2.509,2.989-3.448s3.425-1.277,4.7-2.216c.924-1.292.849-2.907,1.773-4.206s2.862-2.269,3.786-3.568c.481-1.517-.09-3.027.391-4.545.467-1.532,2.007-3.057,2.487-4.575Zm-2.644,4.387c-.684,1.345-2.359,2.539-3.05,3.884s-.383,2.847-1.067,4.192c-1.067,1.067-3.027,1.683-4.1,2.749s-1.24,2.592-2.313,3.658c-1.345.684-3.4.661-4.747,1.352s-1.983,2.081-3.328,2.764c-1.495.233-3.44-.421-4.935-.188s-2.531,1.367-4.019,1.6c-1.495-.233-3.14-1.465-4.635-1.7s-2.825.518-4.319.278c-1.345-.684-2.539-2.366-3.884-3.05s-2.847-.383-4.2-1.067c-1.067-1.067-1.683-3.027-2.749-4.1s-2.592-1.247-3.658-2.314c-.684-1.345-.661-3.4-1.352-4.747s-2.081-1.983-2.764-3.328c-.233-1.495.421-3.44.188-4.935s-1.367-2.531-1.6-4.019c.24-1.495,1.465-3.14,1.7-4.635s-.518-2.825-.278-4.319c.684-1.345,2.366-2.539,3.05-3.884s.383-2.847,1.067-4.2c1.067-1.067,3.027-1.683,4.1-2.749s1.247-2.592,2.313-3.658c1.345-.684,3.4-.661,4.747-1.352s1.983-2.081,3.328-2.764c1.495-.233,3.44.421,4.935.188s2.531-1.367,4.019-1.6c1.495.233,3.14,1.465,4.635,1.7s2.825-.518,4.319-.278c1.345.684,2.539,2.366,3.884,3.05s2.847.383,4.2,1.067c1.067,1.067,1.683,3.027,2.749,4.1s2.592,1.247,3.658,2.313c.684,1.345.661,3.4,1.352,4.747s2.081,1.983,2.764,3.328c.24,1.495-.421,3.44-.188,4.935s1.367,2.531,1.6,4.019c-.233,1.495-1.465,3.14-1.7,4.635s.518,2.832.278,4.327Z"
                                        transform="translate(806.59 1097.863)"
                                        fill="#70d44b"
                                        opacity="0.998"
                                    />
                                </g>
                            </svg>
                            <p class="text-base mt-4">İyi Durum Garantisi</p>
                        </div>
                    </div>
                    <div class="bg-kb-light-green p-3 lg:p-6 rounded-lg">
                        <div class="text-center">
                            <svg class="block mx-auto" width="54.952" height="60.401" viewBox="0 0 54.952 54.972">
                                <g id="Group_3704" data-name="Group 3704" transform="translate(4.344)">
                                    <g id="Group_558" data-name="Group 558" transform="translate(-4.344 0)">
                                        <g id="noun-circle-175697">
                                            <path
                                                id="Path_86"
                                                data-name="Path 86"
                                                d="M140.362,46.981c-.014-1.449-.93-2.6-.943-4.047s.9-3.2.882-4.649c-.458-1.374-1.689-2.181-2.147-3.555s-.144-3.323-.595-4.7c-.861-1.169-2.284-1.552-3.138-2.714s-1.162-3.117-2.017-4.28c-1.176-.841-2.646-.773-3.828-1.613s-2.065-2.6-3.248-3.446c-1.381-.438-2.755.089-4.136-.356s-2.769-1.839-4.15-2.277c-1.449.014-2.6.93-4.047.943s-3.2-.9-4.649-.882c-1.374.458-2.181,1.689-3.555,2.147s-3.323.144-4.7.6c-1.169.861-1.552,2.284-2.714,3.138s-3.117,1.162-4.28,2.024c-.841,1.176-.773,2.646-1.613,3.828s-2.6,2.065-3.446,3.248c-.438,1.381.082,2.755-.356,4.136s-1.839,2.769-2.277,4.15c.007,1.449.93,2.6.943,4.047s-.9,3.2-.882,4.649c.458,1.374,1.689,2.181,2.147,3.555s.144,3.323.6,4.7c.861,1.169,2.284,1.552,3.138,2.721s1.162,3.117,2.017,4.28c1.176.841,2.646.773,3.828,1.613s2.065,2.6,3.248,3.446c1.381.438,2.755-.082,4.136.356s2.769,1.839,4.15,2.277c1.449-.014,2.6-.93,4.047-.943s3.2.9,4.649.882c1.374-.458,2.181-1.689,3.555-2.147s3.323-.144,4.7-.6c1.169-.861,1.552-2.284,2.721-3.138s3.117-1.162,4.28-2.017c.841-1.176.773-2.646,1.613-3.828s2.6-2.065,3.446-3.248c.438-1.381-.082-2.755.356-4.136s1.826-2.782,2.264-4.163Zm-2.407,3.993c-.622,1.224-2.147,2.311-2.775,3.535s-.349,2.591-.971,3.815c-.971.971-2.755,1.531-3.733,2.5s-1.128,2.359-2.106,3.329c-1.224.622-3.1.6-4.321,1.23s-1.8,1.894-3.029,2.516c-1.361.212-3.131-.383-4.492-.171s-2.3,1.244-3.658,1.456c-1.361-.212-2.858-1.333-4.218-1.545s-2.571.472-3.931.253c-1.224-.622-2.311-2.153-3.535-2.775s-2.591-.349-3.822-.971c-.971-.971-1.531-2.755-2.5-3.733s-2.359-1.135-3.329-2.106c-.622-1.224-.6-3.1-1.23-4.321s-1.894-1.8-2.516-3.029c-.212-1.361.383-3.131.171-4.492s-1.244-2.3-1.456-3.658c.219-1.361,1.333-2.858,1.545-4.218s-.472-2.571-.253-3.931c.622-1.224,2.153-2.311,2.775-3.535s.349-2.591.971-3.822c.971-.971,2.755-1.531,3.733-2.5s1.135-2.359,2.106-3.329c1.224-.622,3.1-.6,4.321-1.23s1.8-1.894,3.029-2.516c1.361-.212,3.131.383,4.492.171s2.3-1.244,3.658-1.456c1.361.212,2.858,1.333,4.218,1.545s2.571-.472,3.931-.253c1.224.622,2.311,2.153,3.535,2.775s2.591.349,3.822.971c.971.971,1.531,2.755,2.5,3.733s2.359,1.135,3.329,2.106c.622,1.224.6,3.1,1.23,4.321s1.894,1.8,2.516,3.029c.219,1.361-.383,3.131-.171,4.492s1.244,2.3,1.456,3.658c-.212,1.361-1.333,2.858-1.545,4.218s.472,2.577.253,3.938Z"
                                                transform="translate(-85.41 -15.348)"
                                                fill="#70d44b"
                                            />
                                        </g>
                                    </g>
                                    <g id="Group_560" data-name="Group 560" transform="translate(7.776 14.707)">
                                        <g id="Rectangle_106" data-name="Rectangle 106" fill="none" stroke="#70d44b" stroke-width="2">
                                            <rect width="23.729" height="21.232" rx="5" stroke="none" />
                                            <rect x="1" y="1" width="21.729" height="19.232" rx="4" fill="none" />
                                        </g>
                                        <path id="Path_90" data-name="Path 90" d="M-5860-13140.214h5.143l2.467,6.718H-5860Z" transform="translate(5883.106 13144.451)" fill="none" stroke="#70d44b" stroke-linejoin="round" stroke-width="2" />
                                        <path id="Path_91" data-name="Path 91" d="M-5852.248-13129.455v9.406h-7.365v-9.406Z" transform="translate(5882.96 13140.41)" fill="none" stroke="#70d44b" stroke-linejoin="round" stroke-width="2" />
                                        <g id="Ellipse_39" data-name="Ellipse 39" transform="translate(19.358 16.236)" fill="#fff" stroke="#70d44b" stroke-width="2">
                                            <circle cx="4.371" cy="4.371" r="4.371" stroke="none" />
                                            <circle cx="4.371" cy="4.371" r="3.371" fill="none" />
                                        </g>
                                        <g id="Ellipse_40" data-name="Ellipse 40" transform="translate(2.498 16.236)" fill="#fff" stroke="#70d44b" stroke-width="2">
                                            <circle cx="4.371" cy="4.371" r="4.371" stroke="none" />
                                            <circle cx="4.371" cy="4.371" r="3.371" fill="none" />
                                        </g>
                                    </g>
                                </g>
                            </svg>
                            <p class="text-base mt-4">Ücretsiz Teslimat</p>
                        </div>
                    </div>
                </div>
-->
            </div>
        </section>
        <section class="mt-6 flex flex-col lg:flex-row" :class="[productBoxContent && productBoxContent.length > 0 ? 'lg:space-x-2' : '']">
            <div class="flex-col w-full lg:w-1/2 bg-kb-mid-grey p-3 lg:p-6 py-6 rounded-lg" :class="[productBoxContent && productBoxContent.length > 0 ? 'flex' : 'hidden']">
                <h2 class="text-3xl ts:text-2xl 2xl:text-3xl font-semibold">Kutu İçeriği</h2>
                <div class="mt-3" :class="[productBoxContent && productBoxContent.length > 5 ? 'overflow-x-scroll space-x-3 flex' : 'grid lg:gap-2 grid-cols-4 mts:grid-cols-5']">
                    <div v-for="item in productBoxContent">
                        <div class="border-1 border-bordergray rounded-lg py-3 lg:py-4 mx-1 lg:mx-3 bg-white" :class="[productBoxContent && productBoxContent.length > 5 ? 'w-[70px] lg:w-[90px]' : '']">
                            <img class="block mx-auto w-[31px] h-[31px] lg:w-[47px] lg:h-[47px]" :src="`/images/svg/${item.key}.svg`" />
                        </div>
                        <div class="text-xs mt-2 text-center text-neutral-500">{{ item.value }}</div>
                    </div>
                </div>
            </div>
            <div class="hidden mts:block bg-kb-mid-grey rounded-lg mt-4 lg:mt-0 p-2" :class="[productBoxContent && productBoxContent.length > 0 ? 'lg:w-1/2' : 'w-full']"
                 v-if="!isMobilite && product.brand?.name?.toLowerCase() != `honda` && product.brand?.name?.toLowerCase() != `yamaha` && product.brand?.id != 26 && product.collections.filter((x) => x.id == 107).length != 1">
                <div class="ml-4 mt-2 text-3xl ts:text-2xl 2xl:text-3xl font-semibold">Avantajlar</div>
                <ul class="flex flex-wrap mts:flex-nowrap text-xs font-bold mts:space-x-1 lg:space-x-2 text-center mt-1 mr-1 mts:p-5">
                    <li class="w-1/2 mts:w-1/3 lg:w-1/5 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 107.74 78.83">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-2" x="17.71" y="1" width="89.04" height="61.66" rx="14.15" ry="14.15" />
                                                <rect x="18.71" y="15.15" width="86.99" height="14.34" />
                                                <circle class="cls-1" cx="85.86" cy="44.17" r="8.66" />
                                                <circle class="cls-1" cx="74.62" cy="44.17" r="8.66" />
                                            </g>
                                            <g>
                                                <circle class="cls-3" cx="20.12" cy="58.71" r="19.12" />
                                                <polyline class="cls-4" points="10.55 58.71 17.71 68.53 29.75 47.27" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Kart Limitin Sana Kalsın</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/5 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 126.16 126.7">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-2tr" x="40.92" y="19.93" width="44.97" height="86.95" rx="5.71" ry="5.71" />
                                                <rect class="cls-1tr" x="56.1" y="23.35" width="14.63" height="3.66" rx="1.83" ry="1.83" />
                                            </g>
                                            <g>
                                                <path
                                                    d="m63.4,126.7c-2.79,0-5.62-.19-8.46-.57-16.77-2.25-31.66-10.89-41.93-24.34S-1.68,71.71.57,54.94C2.82,38.17,11.46,23.28,24.91,13.01,38.36,2.74,55.01-1.67,71.77.57c18.15,2.43,34.36,12.6,44.46,27.9.61.92.35,2.16-.57,2.77-.92.61-2.16.35-2.77-.57-9.46-14.33-24.65-23.86-41.65-26.14-15.71-2.11-31.3,2.03-43.9,11.65C14.74,25.81,6.64,39.76,4.54,55.47c-2.11,15.71,2.03,31.3,11.65,43.9,9.62,12.6,23.57,20.7,39.28,22.8,32.43,4.35,62.36-18.5,66.7-50.94.15-1.1,1.17-1.86,2.25-1.72,1.09.15,1.86,1.15,1.72,2.25-4.26,31.78-31.52,54.94-62.74,54.94Z" />
                                                <polygon points="124.28 56.09 94.42 25.17 113.03 27.91 124.62 13.11 124.28 56.09" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Yenileme Opsiyonu</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/5 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 217.86 118.79">
                                    <g id="Layer_1-2">
                                        <g>
                                            <polyline class="cls-5eq"
                                                      points="110.17 64.25 159.33 64.25 159.33 9.13 156.46 5.14 159.33 16.92 184.09 16.92 188.93 19.41 215.36 50.87 215.36 90.92 209.27 98.15 199.4 98.15 195.2 88.03 184.37 82.31 172.49 85.32 165.49 96.61 165.29 99.24 110.17 99.24 106.81 89.07 101.96 84.65 93.11 82.18 81.1 87.13 76.06 99.24 62.2 98.85 55.91 94.11 54.29 85.93 54.29 62.21" />
                                            <g>
                                                <path class="cls-4eq" d="m31.1,2.5h118.85c5.18,0,9.38,4.2,9.38,9.38v87.36" />
                                                <path class="cls-4eq" d="m33.68,48.6h9.83c5.96,0,10.79,4.83,10.79,10.79v29.06c0,5.96,4.83,10.79,10.79,10.79h10.98" />
                                                <path class="cls-4eq" d="m159.33,16.92h21.55c4.3,0,8.37,1.93,11.08,5.26l23.41,28.69v37.58c0,5.96-4.83,10.79-10.79,10.79h-5.17" />
                                                <polyline class="cls-4eq" points="165.29 99.24 159.33 99.24 110.17 99.24" />
                                                <line class="cls-2eq" x1="54.29" y1="64.25" x2="159.33" y2="64.25" />
                                                <line class="cls-2eq" x1="54.29" y1="78.83" x2="159.33" y2="78.83" />
                                                <circle class="cls-1eq" cx="93.11" cy="99.24" r="17.06" />
                                                <circle class="cls-1eq" cx="182.35" cy="99.24" r="17.06" />
                                                <line class="cls-3eq" x1="18.84" y1="16.92" x2="62.2" y2="16.92" />
                                                <line class="cls-3eq" y1="32.28" x2="43.36" y2="32.28" />
                                                <polyline class="cls-4eq" points="171.1 16.92 171.1 50.87 215.36 50.87" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Ücretsiz Kargo</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/5 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 148.11 137.57">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-1re" x="0" y="79.35" width="20.8" height="54.14" rx="2.9" ry="2.9" />
                                                <path class="cls-2" d="m11.13,128.75c-2.61.49-4.85-1.74-4.37-4.36.27-1.46,1.45-2.65,2.91-2.93,2.62-.5,4.89,1.76,4.38,4.37-.28,1.46-1.46,2.63-2.92,2.91Z" />
                                                <path class="cls-2"
                                                      d="m108.64,106.42l30.07-12.44c5.69-2.35,10.59,4.85,6.22,9.19-.07.07-.14.14-.22.21-.37.35-.78.65-1.23.89l-57.41,31.1c-1.22.66-2.65.85-4,.53l-56.9-13.48c-2.56-.61-4.36-2.89-4.36-5.51v-20.81c0-2.65,1.83-4.94,4.41-5.53l25.11-5.69c.93-.21,1.9-.18,2.81.08l46.7,13.37c.08.02.17.05.25.07.96.23,7.9,2.06,8.51,7.26.06.52.02,1.04-.08,1.55-.4,2.06-2.06,7.24-8.54,5.59l-26.8-6.4" />
                                            </g>
                                            <path class="cls-2re"
                                                  d="m114.96,42.8c0-.5-.01-.99-.04-1.48-.04-.94.57-1.79,1.49-2.03l3.35-.88c1.07-.28,1.71-1.37,1.43-2.44l-2.07-7.91c-.28-1.07-1.37-1.71-2.44-1.43l-3.52.92c-.88.23-1.82-.15-2.25-.96-1.12-2.09-2.45-4.04-3.96-5.85-.6-.71-.6-1.75-.03-2.48l2.28-2.95c.68-.87.51-2.13-.36-2.81l-6.47-4.99c-.87-.67-2.13-.51-2.81.36l-2.34,3.04c-.56.73-1.55.99-2.38.61-2.1-.95-4.3-1.7-6.59-2.22-.9-.2-1.52-1.02-1.52-1.94v-3.87c0-1.1-.9-2-2-2h-8.18c-1.1,0-2,.9-2,2v3.87c0,.92-.63,1.74-1.53,1.94-2.12.48-4.17,1.16-6.12,2.01-.84.37-1.82.09-2.37-.64l-2.31-3.08c-.66-.88-1.92-1.06-2.8-.4l-6.54,4.9c-.88.66-1.06,1.92-.4,2.8l2.24,2.99c.56.74.54,1.78-.07,2.48-.93,1.07-1.79,2.2-2.58,3.38-.51.75-1.48,1.05-2.33.73l-3.45-1.27c-1.04-.38-2.18.15-2.57,1.19l-2.83,7.68c-.38,1.04.15,2.19,1.19,2.57l3.29,1.21c.88.32,1.42,1.21,1.29,2.14-.22,1.57-.34,3.17-.34,4.8,0,.22,0,.45,0,.67.02.92-.59,1.74-1.49,1.97l-3.32.87c-1.07.28-1.71,1.37-1.43,2.44l2.07,7.92c.28,1.07,1.37,1.71,2.44,1.43l3.13-.82c.9-.24,1.85.17,2.27,1,1.12,2.2,2.46,4.28,4.01,6.18.58.71.56,1.73,0,2.45l-1.93,2.5c-.68.87-.51,2.13.36,2.81l6.47,5c.87.67,2.13.51,2.81-.36l1.85-2.4c.57-.73,1.57-1,2.4-.6,2.23,1.06,4.6,1.88,7.06,2.44.9.2,1.52,1.02,1.52,1.94v3c0,1.1.9,2,2,2h8.18c1.1,0,2-.9,2-2v-3c0-.92.62-1.74,1.52-1.94,2.29-.52,4.5-1.27,6.6-2.22.84-.38,1.84-.11,2.4.63l1.81,2.43c.66.89,1.92,1.07,2.8.4l6.55-4.9c.88-.66,1.06-1.92.4-2.8l-1.89-2.52c-.55-.73-.55-1.75.04-2.45.97-1.16,1.87-2.39,2.69-3.68.5-.78,1.49-1.09,2.36-.77l3,1.11c1.04.38,2.19-.15,2.57-1.19l2.82-7.67c.38-1.04-.15-2.19-1.19-2.57l-3.17-1.17c-.86-.32-1.4-1.17-1.3-2.08.16-1.32.24-2.67.24-4.03Zm-22.17,12.18c-18,13.39-38.17-6.8-24.77-24.78.12-.17.28-.32.45-.45,17.99-13.38,38.16,6.79,24.77,24.78-.12.17-.28.32-.45.45Z" />
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Hasarda %70 Garanti</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/5 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-[44px] ts:w-[55px] h-auto ts:h-[55px]" id="Layer_2" viewBox="0 0 80.08 80.08">
                                    <g id="Layer_1-2">
                                        <g>
                                            <!--                                            <rect class="cls-1we" x="1" y="1" width="78.08" height="78.08" rx="11.14" ry="11.14" />-->
                                            <g>
                                                <path class="cls-3we" d="m9.99,17.19h6.06c1.35,0,2.56.86,2.99,2.15l10.99,32.56c.43,1.28,1.64,2.15,2.99,2.15h25.07c1.12,0,2.15-.59,2.72-1.56l9.11-15.48c1.24-2.1-.28-4.76-2.72-4.76H24.65" />
                                                <circle class="cls-2we" cx="37.38" cy="61.99" r="4.18" />
                                                <circle class="cls-2we" cx="53.66" cy="61.99" r="4.18" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Kirala, Beğenirsen Satın Al</div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="hidden mts:block w-full bg-kb-mid-grey p-2 rounded-lg mt-4 lg:mt-0" :class="[productBoxContent && productBoxContent.length > 0 ? 'lg:w-1/2' : 'w-full']" v-if="isMobilite">
                <div class="ml-4 mt-2 text-3xl ts:text-2xl 2xl:text-3xl font-semibold">Avantajlar</div>
                <ul class="flex flex-wrap mts:flex-nowrap text-xs font-bold mts:space-x-1 lg:space-x-2 text-center mt-1 mr-1 mts:p-5">
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 107.74 78.83">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-2" x="17.71" y="1" width="89.04" height="61.66" rx="14.15" ry="14.15" />
                                                <rect x="18.71" y="15.15" width="86.99" height="14.34" />
                                                <circle class="cls-1" cx="85.86" cy="44.17" r="8.66" />
                                                <circle class="cls-1" cx="74.62" cy="44.17" r="8.66" />
                                            </g>
                                            <g>
                                                <circle class="cls-3" cx="20.12" cy="58.71" r="19.12" />
                                                <polyline class="cls-4" points="10.55 58.71 17.71 68.53 29.75 47.27" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Kart Limitin Sana Kalsın</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 126.16 126.7">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-2tr" x="40.92" y="19.93" width="44.97" height="86.95" rx="5.71" ry="5.71" />
                                                <rect class="cls-1tr" x="56.1" y="23.35" width="14.63" height="3.66" rx="1.83" ry="1.83" />
                                            </g>
                                            <g>
                                                <path
                                                    d="m63.4,126.7c-2.79,0-5.62-.19-8.46-.57-16.77-2.25-31.66-10.89-41.93-24.34S-1.68,71.71.57,54.94C2.82,38.17,11.46,23.28,24.91,13.01,38.36,2.74,55.01-1.67,71.77.57c18.15,2.43,34.36,12.6,44.46,27.9.61.92.35,2.16-.57,2.77-.92.61-2.16.35-2.77-.57-9.46-14.33-24.65-23.86-41.65-26.14-15.71-2.11-31.3,2.03-43.9,11.65C14.74,25.81,6.64,39.76,4.54,55.47c-2.11,15.71,2.03,31.3,11.65,43.9,9.62,12.6,23.57,20.7,39.28,22.8,32.43,4.35,62.36-18.5,66.7-50.94.15-1.1,1.17-1.86,2.25-1.72,1.09.15,1.86,1.15,1.72,2.25-4.26,31.78-31.52,54.94-62.74,54.94Z" />
                                                <polygon points="124.28 56.09 94.42 25.17 113.03 27.91 124.62 13.11 124.28 56.09" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Yenileme Opsiyonu</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 217.86 118.79">
                                    <g id="Layer_1-2">
                                        <g>
                                            <polyline class="cls-5eq"
                                                      points="110.17 64.25 159.33 64.25 159.33 9.13 156.46 5.14 159.33 16.92 184.09 16.92 188.93 19.41 215.36 50.87 215.36 90.92 209.27 98.15 199.4 98.15 195.2 88.03 184.37 82.31 172.49 85.32 165.49 96.61 165.29 99.24 110.17 99.24 106.81 89.07 101.96 84.65 93.11 82.18 81.1 87.13 76.06 99.24 62.2 98.85 55.91 94.11 54.29 85.93 54.29 62.21" />
                                            <g>
                                                <path class="cls-4eq" d="m31.1,2.5h118.85c5.18,0,9.38,4.2,9.38,9.38v87.36" />
                                                <path class="cls-4eq" d="m33.68,48.6h9.83c5.96,0,10.79,4.83,10.79,10.79v29.06c0,5.96,4.83,10.79,10.79,10.79h10.98" />
                                                <path class="cls-4eq" d="m159.33,16.92h21.55c4.3,0,8.37,1.93,11.08,5.26l23.41,28.69v37.58c0,5.96-4.83,10.79-10.79,10.79h-5.17" />
                                                <polyline class="cls-4eq" points="165.29 99.24 159.33 99.24 110.17 99.24" />
                                                <line class="cls-2eq" x1="54.29" y1="64.25" x2="159.33" y2="64.25" />
                                                <line class="cls-2eq" x1="54.29" y1="78.83" x2="159.33" y2="78.83" />
                                                <circle class="cls-1eq" cx="93.11" cy="99.24" r="17.06" />
                                                <circle class="cls-1eq" cx="182.35" cy="99.24" r="17.06" />
                                                <line class="cls-3eq" x1="18.84" y1="16.92" x2="62.2" y2="16.92" />
                                                <line class="cls-3eq" y1="32.28" x2="43.36" y2="32.28" />
                                                <polyline class="cls-4eq" points="171.1 16.92 171.1 50.87 215.36 50.87" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Bayi Teslimi</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" xmlns="http://www.w3.org/2000/svg" id="Capa_1" enable-background="new 0 0 512 512" viewBox="0 0 512 512">
                                    <g>
                                        <path d="m391 362h-111.213l-38.787 38.787v72.426l38.787 38.787h111.213v-25h45v-30h-45v-40h45v-30h-45z" />
                                        <path
                                            d="m415.806 66.194c-42.686-42.686-99.439-66.194-159.806-66.194s-117.12 23.508-159.806 66.194-66.194 99.439-66.194 159.806 23.508 117.121 66.194 159.806c31.875 31.875 71.596 53.05 114.806 61.734v-30.761c-86.457-20.39-151-98.185-151-190.779 0-108.075 87.925-196 196-196s196 87.925 196 196v64h-181v-55h5c35.841 0 65-29.159 65-65v-20h-20c-35.841 0-65 29.159-65 65 0-35.841-29.159-65-65-65h-20v20c0 35.841 29.159 65 65 65h5v85h241v-94c0-60.367-23.508-117.12-66.194-159.806z" />
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Sürdürülebilir gelecek</div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="block mts:hidden w-full mt-4 bg-kb-mid-grey" v-if="!isMobilite && product.brand?.name?.toLowerCase() == `honda` && product.brand?.name?.toLowerCase() == `yamaha`">
                <h2 class="ml-4 mt-3 text-3xl ts:text-2xl 2xl:text-3x">Avantajlar</h2>
                <ul class="flex flex-wrap mts:flex-nowrap text-xs font-bold md:space-x-1 lg:space-x-4 text-center rounded-lg p-2 md:p-5">
                    <li class="w-1/2 mts:w-1/3 lg:w-1/5 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-10 mts:max-h-14 mx-auto" id="Layer_2" viewBox="0 0 107.74 78.83">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-2" x="17.71" y="1" width="89.04" height="61.66" rx="14.15" ry="14.15" />
                                                <rect x="18.71" y="15.15" width="86.99" height="14.34" />
                                                <circle class="cls-1" cx="85.86" cy="44.17" r="8.66" />
                                                <circle class="cls-1" cx="74.62" cy="44.17" r="8.66" />
                                            </g>
                                            <g>
                                                <circle class="cls-3" cx="20.12" cy="58.71" r="19.12" />
                                                <polyline class="cls-4" points="10.55 58.71 17.71 68.53 29.75 47.27" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-xs leading-none min-h-[24px]">Kart Limitin Sana Kalsın</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/5 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-10 mts:max-h-14 mx-auto" id="Layer_2" viewBox="0 0 126.16 126.7">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-2tr" x="40.92" y="19.93" width="44.97" height="86.95" rx="5.71" ry="5.71" />
                                                <rect class="cls-1tr" x="56.1" y="23.35" width="14.63" height="3.66" rx="1.83" ry="1.83" />
                                            </g>
                                            <g>
                                                <path
                                                    d="m63.4,126.7c-2.79,0-5.62-.19-8.46-.57-16.77-2.25-31.66-10.89-41.93-24.34S-1.68,71.71.57,54.94C2.82,38.17,11.46,23.28,24.91,13.01,38.36,2.74,55.01-1.67,71.77.57c18.15,2.43,34.36,12.6,44.46,27.9.61.92.35,2.16-.57,2.77-.92.61-2.16.35-2.77-.57-9.46-14.33-24.65-23.86-41.65-26.14-15.71-2.11-31.3,2.03-43.9,11.65C14.74,25.81,6.64,39.76,4.54,55.47c-2.11,15.71,2.03,31.3,11.65,43.9,9.62,12.6,23.57,20.7,39.28,22.8,32.43,4.35,62.36-18.5,66.7-50.94.15-1.1,1.17-1.86,2.25-1.72,1.09.15,1.86,1.15,1.72,2.25-4.26,31.78-31.52,54.94-62.74,54.94Z" />
                                                <polygon points="124.28 56.09 94.42 25.17 113.03 27.91 124.62 13.11 124.28 56.09" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-xs leading-none min-h-[24px]">Yenileme Opsiyonu</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/5 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-10 mts:max-h-14 mx-auto" id="Layer_2" viewBox="0 0 217.86 118.79">
                                    <g id="Layer_1-2">
                                        <g>
                                            <polyline class="cls-5eq"
                                                      points="110.17 64.25 159.33 64.25 159.33 9.13 156.46 5.14 159.33 16.92 184.09 16.92 188.93 19.41 215.36 50.87 215.36 90.92 209.27 98.15 199.4 98.15 195.2 88.03 184.37 82.31 172.49 85.32 165.49 96.61 165.29 99.24 110.17 99.24 106.81 89.07 101.96 84.65 93.11 82.18 81.1 87.13 76.06 99.24 62.2 98.85 55.91 94.11 54.29 85.93 54.29 62.21" />
                                            <g>
                                                <path class="cls-4eq" d="m31.1,2.5h118.85c5.18,0,9.38,4.2,9.38,9.38v87.36" />
                                                <path class="cls-4eq" d="m33.68,48.6h9.83c5.96,0,10.79,4.83,10.79,10.79v29.06c0,5.96,4.83,10.79,10.79,10.79h10.98" />
                                                <path class="cls-4eq" d="m159.33,16.92h21.55c4.3,0,8.37,1.93,11.08,5.26l23.41,28.69v37.58c0,5.96-4.83,10.79-10.79,10.79h-5.17" />
                                                <polyline class="cls-4eq" points="165.29 99.24 159.33 99.24 110.17 99.24" />
                                                <line class="cls-2eq" x1="54.29" y1="64.25" x2="159.33" y2="64.25" />
                                                <line class="cls-2eq" x1="54.29" y1="78.83" x2="159.33" y2="78.83" />
                                                <circle class="cls-1eq" cx="93.11" cy="99.24" r="17.06" />
                                                <circle class="cls-1eq" cx="182.35" cy="99.24" r="17.06" />
                                                <line class="cls-3eq" x1="18.84" y1="16.92" x2="62.2" y2="16.92" />
                                                <line class="cls-3eq" y1="32.28" x2="43.36" y2="32.28" />
                                                <polyline class="cls-4eq" points="171.1 16.92 171.1 50.87 215.36 50.87" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-xs leading-none min-h-[24px]">Ücretsiz Kargo</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/5 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-10 mts:max-h-14 mx-auto" id="Layer_2" viewBox="0 0 148.11 137.57">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-1re" x="0" y="79.35" width="20.8" height="54.14" rx="2.9" ry="2.9" />
                                                <path class="cls-2" d="m11.13,128.75c-2.61.49-4.85-1.74-4.37-4.36.27-1.46,1.45-2.65,2.91-2.93,2.62-.5,4.89,1.76,4.38,4.37-.28,1.46-1.46,2.63-2.92,2.91Z" />
                                                <path class="cls-2"
                                                      d="m108.64,106.42l30.07-12.44c5.69-2.35,10.59,4.85,6.22,9.19-.07.07-.14.14-.22.21-.37.35-.78.65-1.23.89l-57.41,31.1c-1.22.66-2.65.85-4,.53l-56.9-13.48c-2.56-.61-4.36-2.89-4.36-5.51v-20.81c0-2.65,1.83-4.94,4.41-5.53l25.11-5.69c.93-.21,1.9-.18,2.81.08l46.7,13.37c.08.02.17.05.25.07.96.23,7.9,2.06,8.51,7.26.06.52.02,1.04-.08,1.55-.4,2.06-2.06,7.24-8.54,5.59l-26.8-6.4" />
                                            </g>
                                            <path class="cls-2re"
                                                  d="m114.96,42.8c0-.5-.01-.99-.04-1.48-.04-.94.57-1.79,1.49-2.03l3.35-.88c1.07-.28,1.71-1.37,1.43-2.44l-2.07-7.91c-.28-1.07-1.37-1.71-2.44-1.43l-3.52.92c-.88.23-1.82-.15-2.25-.96-1.12-2.09-2.45-4.04-3.96-5.85-.6-.71-.6-1.75-.03-2.48l2.28-2.95c.68-.87.51-2.13-.36-2.81l-6.47-4.99c-.87-.67-2.13-.51-2.81.36l-2.34,3.04c-.56.73-1.55.99-2.38.61-2.1-.95-4.3-1.7-6.59-2.22-.9-.2-1.52-1.02-1.52-1.94v-3.87c0-1.1-.9-2-2-2h-8.18c-1.1,0-2,.9-2,2v3.87c0,.92-.63,1.74-1.53,1.94-2.12.48-4.17,1.16-6.12,2.01-.84.37-1.82.09-2.37-.64l-2.31-3.08c-.66-.88-1.92-1.06-2.8-.4l-6.54,4.9c-.88.66-1.06,1.92-.4,2.8l2.24,2.99c.56.74.54,1.78-.07,2.48-.93,1.07-1.79,2.2-2.58,3.38-.51.75-1.48,1.05-2.33.73l-3.45-1.27c-1.04-.38-2.18.15-2.57,1.19l-2.83,7.68c-.38,1.04.15,2.19,1.19,2.57l3.29,1.21c.88.32,1.42,1.21,1.29,2.14-.22,1.57-.34,3.17-.34,4.8,0,.22,0,.45,0,.67.02.92-.59,1.74-1.49,1.97l-3.32.87c-1.07.28-1.71,1.37-1.43,2.44l2.07,7.92c.28,1.07,1.37,1.71,2.44,1.43l3.13-.82c.9-.24,1.85.17,2.27,1,1.12,2.2,2.46,4.28,4.01,6.18.58.71.56,1.73,0,2.45l-1.93,2.5c-.68.87-.51,2.13.36,2.81l6.47,5c.87.67,2.13.51,2.81-.36l1.85-2.4c.57-.73,1.57-1,2.4-.6,2.23,1.06,4.6,1.88,7.06,2.44.9.2,1.52,1.02,1.52,1.94v3c0,1.1.9,2,2,2h8.18c1.1,0,2-.9,2-2v-3c0-.92.62-1.74,1.52-1.94,2.29-.52,4.5-1.27,6.6-2.22.84-.38,1.84-.11,2.4.63l1.81,2.43c.66.89,1.92,1.07,2.8.4l6.55-4.9c.88-.66,1.06-1.92.4-2.8l-1.89-2.52c-.55-.73-.55-1.75.04-2.45.97-1.16,1.87-2.39,2.69-3.68.5-.78,1.49-1.09,2.36-.77l3,1.11c1.04.38,2.19-.15,2.57-1.19l2.82-7.67c.38-1.04-.15-2.19-1.19-2.57l-3.17-1.17c-.86-.32-1.4-1.17-1.3-2.08.16-1.32.24-2.67.24-4.03Zm-22.17,12.18c-18,13.39-38.17-6.8-24.77-24.78.12-.17.28-.32.45-.45,17.99-13.38,38.16,6.79,24.77,24.78-.12.17-.28.32-.45.45Z" />
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-xs leading-none min-h-[24px]">Hasarda %70 Garanti</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/5 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 px-2 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-10 mts:max-h-14 mx-auto" id="Layer_2" viewBox="0 0 80.08 80.08">
                                    <g id="Layer_1-2">
                                        <g>
                                            <!--                                            <rect class="cls-1we" x="1" y="1" width="78.08" height="78.08" rx="11.14" ry="11.14" />-->
                                            <g>
                                                <path class="cls-3we" d="m9.99,17.19h6.06c1.35,0,2.56.86,2.99,2.15l10.99,32.56c.43,1.28,1.64,2.15,2.99,2.15h25.07c1.12,0,2.15-.59,2.72-1.56l9.11-15.48c1.24-2.1-.28-4.76-2.72-4.76H24.65" />
                                                <circle class="cls-2we" cx="37.38" cy="61.99" r="4.18" />
                                                <circle class="cls-2we" cx="53.66" cy="61.99" r="4.18" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-xs leading-none min-h-[24px]">Kirala, Beğenirsen Satın Al</div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="w-full bg-kb-mid-grey p-2 rounded-lg mt-4 lg:mt-0" v-if="!isMobilite && (product.brand?.name?.toLowerCase() == `honda` || product.brand?.name?.toLowerCase() == `yamaha`)">
                <div class="ml-4 mt-2 text-3xl ts:text-2xl 2xl:text-3xl font-semibold">Avantajlar</div>
                <ul class="flex flex-wrap mts:flex-nowrap text-xs font-bold mts:space-x-1 lg:space-x-2 text-center mt-1 mr-1 mts:p-5">
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <img class="w-10/12 lg:w-full max-h-14 max-w-14 mx-auto" src="../../images/kiralamotor/kiralamotor-i2.svg" />
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Sigorta ve MTV Dahil</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <img class="w-10/12 lg:w-full max-h-14 max-w-14 mx-auto" src="../../images/kiralamotor/kiralamotor-i3.png" />
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Kira Dönemi Sonunda İstersen Satın Al</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <img class="w-10/12 lg:w-full max-h-14 max-w-14 mx-auto" src="../../images/kiralamotor/kiralamotor-i4.svg" />
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Vergi Avantajları</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <img class="w-10/12 lg:w-full max-h-14 max-w-14 mx-auto" src="../../images/kiralamotor/kiralamotor-i6.svg" />
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">0 KM Yeni Motorlar</div>
                        </div>
                    </li>
                </ul>
            </div>
        </section>
        <div class="text-xl md:text-3xl text-center w-full md:w-auto mt-3 lg:mt-9">Kiralamak</div>
        <div class="text-xl md:text-2xl font-santralregular text-center w-full md:w-auto">Esnek ve Sürdürülebilir</div>
        <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0">
            <Splide class="surdurebilirlik-slider" :has-track="false" aria-label=""
                    :options="{ arrows: false, autoplay: true, rewind: true, perPage: 4, pagination: true, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 4 } } }">
                <SplideTrack>
                    <SplideSlide class="">
                        <div class="py-4 px-4 flex flex-col justify-center items-center">
                            <div class="relative">
                                <img src="../../images/welcome-webp/esnek-surdurulebilir-yeni-1.png" class="w-full z-50 relative max-w-[200px]">
                            </div>
                            <span class="font-santralextrabold text-sm md:text-base text-center"> Ürünler yeni veya yeni gibi </span>
                            <span class="font-santralregular text-xs md:text-sm text-center"> Kalite kontrolü tamamlanmış tüm teknoloji ürünlerini kiralayabilirsin. </span>
                        </div>
                    </SplideSlide>
                    <SplideSlide class="">
                        <div class="py-4 px-4 flex flex-col justify-center items-center">
                            <div class="relative">
                                <img src="../../images/welcome-webp/esnek-surdurulebilir-yeni-2.png" class="w-full z-50 relative max-w-[200px]">
                            </div>
                            <span class="font-santralextrabold text-sm md:text-base text-center"> Hasar onarım garantisi </span>
                            <span class="font-santralregular text-xs md:text-sm text-center"> %70 hasar onarım garantisi bizden, seçili ürünlerde istersen %100’e sen tamamlayabilirsin. </span>
                        </div>
                    </SplideSlide>
                    <SplideSlide class="">
                        <div class="py-4 px-4 flex flex-col justify-center items-center">
                            <div class="relative">
                                <img src="../../images/welcome-webp/esnek-surdurulebilir-yeni-3.png" class="w-full z-50 relative max-w-[200px]">
                            </div>
                            <span class="font-santralextrabold text-sm md:text-base text-center"> Esnek kira süreleri </span>
                            <span class="font-santralregular text-xs md:text-sm text-center"> 1 aydan 24 aya varan kira sürelerinden sana en uygun olanı seçebilirsin. </span>
                        </div>
                    </SplideSlide>
                    <SplideSlide class="">
                        <div class="py-4 px-4 flex flex-col justify-center items-center">
                            <div class="relative">
                                <img src="../../images/welcome-webp/esnek-surdurulebilir-yeni-4.png" class="w-full z-50 relative max-w-[200px]">
                            </div>
                            <span class="font-santralextrabold text-sm md:text-base text-center"> Üst modele geç veya iade et </span>
                            <span class="font-santralregular text-xs md:text-sm text-center"> Kiralaman devam ederken ürününü üst modeli ile değiştirebilir ya da kira sürenin sonunda iade edebilirsin. </span>
                        </div>
                    </SplideSlide>
                </SplideTrack>
            </Splide>
        </div>

        <div class="flex flex-col w-full bg-kb-mid-grey p-2 py-6 rounded-lg mt-4" v-if="productFeatures && productFeatures.length > 0">
            <h2 class="text-3xl ts:text-2xl 2xl:text-3xl font-semibold ml-3">Ürün Özellikleri</h2>
            <div class="hidden lg:block mt-6 ts:mt-3 2xl:mt-6">
                <div class="lg:grid grid-cols-2 gap-4 border-t-1 border-bordergray">
                    <ul class="pl-3 mt-1">
                        <li class="flex text-sm ts:text-xs 2xl:text-sm border-b-1 border-bordergray pb-1 mb-1 items-center" v-for="feature in productFeatures.slice(0, productFeatures.length / 2)">
                            <div class="w-1/2">{{ feature.key }}</div>
                            <div class="w-1/2 font-santralregular">{{ feature.value }}</div>
                        </li>
                    </ul>
                    <ul class="pl-3 mt-1">
                        <li class="flex text-sm ts:text-xs 2xl:text-sm border-b-1 border-bordergray pb-1 mb-1 items-center" v-for="feature in productFeatures.slice(productFeatures.length / 2)">
                            <div class="w-1/2">{{ feature.key }}</div>
                            <div class="w-1/2 font-santralregular">{{ feature.value }}</div>
                        </li>
                    </ul>
                </div>
                <div class="pl-3 mt-5 flex" v-if="product.collections.filter((collection) => collection.id === 80).length > 0">
                    <Link href="/kiralamobil-bilgilendirme" class="w-auto text-black rounded-full py-1 self-center font-semibold text-lg leading-none text-center disabled:opacity-50 disabled:cursor-not-allowed">Kiralama ile ilgili detaylı bilgi için
                        lütfen
                        <span class="text-kbblue">tıklayınız</span>
                    </Link>
                </div>
            </div>
            <div class="block lg:hidden mt-6 ts:mt-3 2xl:mt-6">
                <div class="lg:grid grid-cols-2 gap-4 border-t-1 border-bordergray">
                    <ul class="pl-3 mt-1">
                        <li class="flex text-sm ts:text-xs 2xl:text-sm border-b-1 border-bordergray pb-1 mb-1 items-center" v-for="feature in productFeatures.slice(0, 5)">
                            <div class="w-1/2">{{ feature.key }}</div>
                            <div class="w-1/2 font-santralregular">{{ feature.value }}</div>
                        </li>
                        <div class="pl-3 mt-5 flex" v-if="product.collections.filter((collection) => collection.id === 80).length > 0">
                            <Link href="/kiralamobil-bilgilendirme" class="w-auto text-black rounded-full py-1 self-center font-semibold text-lg leading-none text-center disabled:opacity-50 disabled:cursor-not-allowed">Kiralama ile ilgili detaylı
                                bilgi için lütfen <span class="text-kbblue">tıklayınız</span></Link>
                        </div>
                        <Disclosure as="div" v-slot="{ open }">
                            <DisclosurePanel class="">
                                <li class="flex text-sm ts:text-xs 2xl:text-sm border-b-1 border-bordergray pb-1 mb-1 items-center" v-for="feature in productFeatures.slice(5)">
                                    <div class="w-1/2">{{ feature.key }}</div>
                                    <div class="w-1/2 font-santralregular">{{ feature.value }}</div>
                                </li>
                            </DisclosurePanel>
                            <DisclosureButton @click="hiddenbtn1 = !hiddenbtn1" v-if="hiddenbtn1" class="w-full flex justify-center">
                                <button class="bg-white text-xs text-kbgray border-1 border-kbgray rounded-full py-1 px-2 self-center font-santralextrabold mt-2 hover:bg-kbgreen hover:text-white hover:border-white">Daha Fazla</button>
                            </DisclosureButton>
                        </Disclosure>
                    </ul>
                </div>
            </div>
        </div>
        <section class="flex flex-col w-full bg-[#f8f8f8] p-5 lg:p-6 rounded-lg mt-6 ts:mt-3 2xl:mt-6" v-if="false">
            <h2 class="text-3xl ts:text-2xl 2xl:text-3xl font-semibold">Birlikte kirala</h2>
            <div class="flex flex-col lg:flex-row">
                <div class="w-full lg:w-7/12">
                    <div class="grid md:gap-2 grid-rows-3 md:grid-rows-none md:grid-cols-3 mt-6">
                        <div class="row-span-6 md:row-auto">
                            <img src="../../images/n_0382eaf7befb85a5c9f37cb97a89.png" alt="" />
                        </div>
                        <div class="flex flex-col justify-center text-center md:text-left mt-2 sm:mt-0">
                            <h2 class="text-xl ts:text-lg 2xl:text:xl font-semibold">Apple iPhone 13, 256 GB Mavi</h2>
                            <h2 class="text-xl ts:text-lg 2xl:text:xl font-semibold mt-4">
                                540 TL /
                                <span class="text-sm ts:text-xs 2xl:text-sm text-textgray">Ay’dan kirala</span>
                            </h2>
                        </div>
                        <div class="row-span-5 md:row-auto flex items-center justify-center my-7 lg:my-0">
                            <!--                            <svg  width="62" height="62" viewBox="0 0 62 62">-->
                            <!--                                <g id="Group_4465" data-name="Group 4465" transform="translate(-0.045 0)">-->
                            <!--                                    <g id="Group_120" data-name="Group 120">-->
                            <!--                                        <circle id="Ellipse_20" data-name="Ellipse 20" cx="31" cy="31" r="31" transform="translate(0.045 0)" fill="#70d44b" />-->
                            <!--                                    </g>-->
                            <!--                                    <g id="Group_3803" data-name="Group 3803" transform="translate(11.176 10)">-->
                            <!--                                        <path id="Path_2976" data-name="Path 2976" d="M12.186,24.053H0V17.472H12.186a6.433,6.433,0,0,0,6.426-6.426V0h6.581V11.048A13.022,13.022,0,0,1,12.186,24.055Z" fill="#5f4af4" />-->
                            <!--                                        <path id="Path_2977" data-name="Path 2977" d="M108.077,95.96h12.186v6.581H108.077a6.433,6.433,0,0,0-6.426,6.426v11.048H95.07V108.967A13.022,13.022,0,0,1,108.077,95.96Z" transform="translate(-77.806 -78.061)" fill="#fff" />-->
                            <!--                                    </g>-->
                            <!--                                </g>-->
                            <!--                            </svg>-->
                            <svg fill="#5f4af4" class="w-[45px]" viewBox="0 0 45.402 45.402" xml:space="preserve">
                                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                                <g id="SVGRepo_iconCarrier">
                                    <g>
                                        <path
                                            d="M41.267,18.557H26.832V4.134C26.832,1.851,24.99,0,22.707,0c-2.283,0-4.124,1.851-4.124,4.135v14.432H4.141 c-2.283,0-4.139,1.851-4.138,4.135c-0.001,1.141,0.46,2.187,1.207,2.934c0.748,0.749,1.78,1.222,2.92,1.222h14.453V41.27 c0,1.142,0.453,2.176,1.201,2.922c0.748,0.748,1.777,1.211,2.919,1.211c2.282,0,4.129-1.851,4.129-4.133V26.857h14.435 c2.283,0,4.134-1.867,4.133-4.15C45.399,20.425,43.548,18.557,41.267,18.557z">
                                        </path>
                                    </g>
                                </g>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-6/12 flex flex-col md:flex-row md:mt-3">
                    <div class="w-full md:w-8/12 lg:w-8/12">
                        <div class="flex flex-row lg:flex-col w-full lg:px-3">
                            <div class="mx-auto w-6/12 lg:w-full flex flex-col lg:flex-row items-start">
                                <div class="w-full lg:w-5/12 border-2 border-bordergray rounded-2lg bg-white py-3 lg:py-5 px-5 lg:px-9">
                                    <img class="w-20 mx-auto" src="../../images/watch.png" alt="" />
                                </div>
                                <div class="w-full lg:w-7/12 flex flex-col justify-center lg:ml-4 mt-2 lg:mt-0">
                                    <h2 class="text-xs md:text-sm ts:text-xs 2xl:text-sm font-semibold">Apple Watch SE GPS</h2>
                                    <h2 class="text-xs md:text-sm ts:text-xs 2xl:text-sm font-semibold mt-1">
                                        300 TL /
                                        <span class="text-xs ts:text-2xs 2xl:text-xs font-medium text-textgray">Ay’dan kirala</span>
                                    </h2>
                                    <div class="mt-2">
                                        <Link href="/sepete-ekle" :data="{
                                            product_id: 54,
                                            month: 5,
                                            product_variant_id: 136,
                                        }" method="post" as="button" class="bg-black text-white rounded-full py-1 px-2 md:px-3 self-center font-semibold text-sm text-center">Kirala
                                        </Link>
                                    </div>
                                </div>
                            </div>
                            <div class="mx-auto w-6/12 lg:w-full flex flex-col lg:flex-row lg:mt-8 pl-2 lg:pl-0 items-start">
                                <div class="w-full lg:w-5/12 border-2 border-bordergray rounded-2lg bg-white py-3 lg:py-5 px-5 lg:px-9">
                                    <img class="w-20 mx-auto" src="../../images/airpods.png" alt="" />
                                </div>
                                <div class="w-full lg:w-7/12 flex flex-col justify-center lg:ml-4 mt-2 lg:mt-0">
                                    <h2 class="text-xs md:text-sm ts:text-xs 2xl:text-sm font-semibold">Apple AirPods 3 In-ear Bluetooth Headphones</h2>
                                    <h2 class="text-xs md:text-sm ts:text-xs 2xl:text-sm font-semibold mt-1">
                                        220 TL /
                                        <span class="text-xs font-medium text-textgray">Ay’dan kirala</span>
                                    </h2>
                                    <div class="mt-2">
                                        <Link href="/sepete-ekle" :data="{
                                            product_id: 412,
                                            month: 5,
                                            product_variant_id: 535,
                                        }" method="post" as="button" class="bg-black text-white rounded-full py-1 px-2 md:px-3 self-center font-semibold text-sm text-center">Kirala
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full md:w-4/12 lg:w-4/12 flex items-center flex-col md:flex-row lg:pl-4">
                        <div class="hidden md:block md:ml-6 lg:ml-0">
                            <svg width="2" height="225.881" viewBox="0 0 2 225.881">
                                <path id="Path_104" data-name="Path 104" d="M-5989.843-12210.509v225.881" transform="translate(5990.843 12210.509)" fill="none" stroke="#70d44b" stroke-width="2" />
                            </svg>
                        </div>
                        <div class="block md:hidden my-6">
                            <svg width="239.236" height="2" viewBox="0 0 239.236 2">
                                <path id="Path_3747" data-name="Path 3747" d="M-5989.843-12210.511v239.236" transform="translate(-11971.274 5990.843) rotate(90)" fill="none" stroke="#70d44b" stroke-width="2" />
                            </svg>
                        </div>
                        <div class="w-full flex flex-row md:flex-col items-center md:items-end justify-between md:justify-end 2xl:mr-4">
                            <div>
                                <h2 class="text-lg font-semibold">Toplam :</h2>
                                <h2 class="text-3xl font-semibold mt-2">1060 TL</h2>
                            </div>
                            <!--                            <div class="md:mt-4">-->
                            <!--                                <Link href="/sepetim" class="bg-black text-white rounded-full py-2 px-4 self-center font-semibold text-lg lg:text-base text-center leading-tight" method="post" as="button">Kirala </Link>-->
                            <!--                            </div>-->
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="mt-8 ts:mt-5 2xl:mt-8 bg-white" v-if="false">
            <div class="flex mx-auto flex-col max-w-7xl">
                <div class="flex w-full px-0 justify-between">
                    <div class="font-bold text-2xl lg:text-3xl my-0 self-center text-center w-full md:w-auto">Bunlar da hoşuna gidebilir</div>
                </div>
                <Splide :has-track="false" aria-label="" :options="options">
                    <SplideTrack>
                        <SplideSlide>
                            <div class="relative flex-1 bg-white rounded-2lg py-6 pt-10 md:pt-10 px-4 w-full border-2 border-bordergray my-4">
                                <div class="flex w-full">
                                    <div class="w-4/12">
                                        <img src="../../images/ipad.png" alt="" />
                                    </div>
                                    <div class="w-8/12 flex flex-col justify-between items-start pl-3">
                                        <h2 class="text-sm font-semibold">Apple iPad Air 12 (2022)</h2>
                                        <div class="text-xs text-textgray">6.1” Super Retina XDR display, Dual Rear Camera, 4GB RAM…</div>
                                        <h2 class="text-base font-semibold">
                                            540 TL /
                                            <span class="text-xs font-medium text-textgray">Ay’dan kirala</span>
                                        </h2>
                                        <div class="mt-2">
                                            <a href="/sepetim" class="bg-black text-white rounded-full py-1 md:py-2 px-2 md:px-3 self-center font-semibold text-sm text-center">Kirala</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="absolute right-3 top-3" @click="share()">
                                    <svg class="hidden md:block" width="24.133" height="19.916" viewBox="0 0 24.133 19.916">
                                        <path id="_23" data-name="23"
                                              d="M14.088,23.686a1.21,1.21,0,0,1-.714-.23L4.409,16.849a.629.629,0,0,1-.133-.121,7.569,7.569,0,0,1,9.813-11.47A7.574,7.574,0,0,1,23.961,6h0a7.586,7.586,0,0,1,0,10.732.629.629,0,0,1-.133.121L14.8,23.407A1.21,1.21,0,0,1,14.088,23.686ZM5.861,14.938l8.228,6.05,8.228-6.05a5.179,5.179,0,0,0-.06-7.26h0a5.166,5.166,0,0,0-7.26,0,1.21,1.21,0,0,1-1.718,0,5.189,5.189,0,1,0-7.417,7.26Z"
                                              transform="translate(-2.052 -3.77)" fill="#231f20" opacity="0.6" />
                                    </svg>
                                    <svg class="block md:hidden" width="18.373" height="15.162" viewBox="0 0 18.373 15.162">
                                        <path id="_23" data-name="23"
                                              d="M11.215,18.932a.921.921,0,0,1-.543-.175L3.846,13.727a.479.479,0,0,1-.1-.092A5.762,5.762,0,0,1,11.215,4.9a5.766,5.766,0,0,1,7.516.562h0a5.775,5.775,0,0,1,0,8.17.479.479,0,0,1-.1.092L11.759,18.72A.921.921,0,0,1,11.215,18.932Zm-6.264-6.66,6.264,4.606,6.264-4.606a3.942,3.942,0,0,0-.046-5.527h0a3.933,3.933,0,0,0-5.527,0,.921.921,0,0,1-1.308,0,3.951,3.951,0,1,0-5.647,5.527Z"
                                              transform="translate(-2.052 -3.77)" fill="#231f20" opacity="0.6" />
                                    </svg>
                                </div>
                            </div>
                        </SplideSlide>
                        <SplideSlide>
                            <div class="relative flex-1 bg-white rounded-2lg py-6 pt-10 md:pt-10 px-4 w-full border-2 border-bordergray my-4">
                                <div class="flex w-full">
                                    <div class="w-4/12">
                                        <img src="../../images/iphone12.png" alt="" />
                                    </div>
                                    <div class="w-8/12 flex flex-col justify-between items-start pl-3">
                                        <h2 class="text-sm font-semibold">Apple iPhone 12 64 GB</h2>
                                        <div class="text-xs text-textgray">6.1” Super Retina XDR display, Dual Rear Camera, 4GB RAM…</div>
                                        <h2 class="text-base font-semibold">
                                            480 TL /
                                            <span class="text-xs font-medium text-textgray">Ay’dan kirala</span>
                                        </h2>
                                        <div class="mt-2">
                                            <a href="/sepetim" class="bg-black text-white rounded-full py-1 md:py-2 px-2 md:px-3 self-center font-semibold text-sm text-center">Kirala</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="absolute right-3 top-3">
                                    <svg class="hidden md:block" width="24.133" height="19.916" viewBox="0 0 24.133 19.916">
                                        <path id="_23" data-name="23"
                                              d="M14.088,23.686a1.21,1.21,0,0,1-.714-.23L4.409,16.849a.629.629,0,0,1-.133-.121,7.569,7.569,0,0,1,9.813-11.47A7.574,7.574,0,0,1,23.961,6h0a7.586,7.586,0,0,1,0,10.732.629.629,0,0,1-.133.121L14.8,23.407A1.21,1.21,0,0,1,14.088,23.686ZM5.861,14.938l8.228,6.05,8.228-6.05a5.179,5.179,0,0,0-.06-7.26h0a5.166,5.166,0,0,0-7.26,0,1.21,1.21,0,0,1-1.718,0,5.189,5.189,0,1,0-7.417,7.26Z"
                                              transform="translate(-2.052 -3.77)" fill="#231f20" opacity="0.6" />
                                    </svg>
                                    <svg class="block md:hidden" width="18.373" height="15.162" viewBox="0 0 18.373 15.162">
                                        <path id="_23" data-name="23"
                                              d="M11.215,18.932a.921.921,0,0,1-.543-.175L3.846,13.727a.479.479,0,0,1-.1-.092A5.762,5.762,0,0,1,11.215,4.9a5.766,5.766,0,0,1,7.516.562h0a5.775,5.775,0,0,1,0,8.17.479.479,0,0,1-.1.092L11.759,18.72A.921.921,0,0,1,11.215,18.932Zm-6.264-6.66,6.264,4.606,6.264-4.606a3.942,3.942,0,0,0-.046-5.527h0a3.933,3.933,0,0,0-5.527,0,.921.921,0,0,1-1.308,0,3.951,3.951,0,1,0-5.647,5.527Z"
                                              transform="translate(-2.052 -3.77)" fill="#231f20" opacity="0.6" />
                                    </svg>
                                </div>
                            </div>
                        </SplideSlide>
                        <SplideSlide>
                            <div class="relative flex-1 bg-white rounded-2lg py-6 pt-10 md:pt-10 px-4 w-full border-2 border-bordergray my-4">
                                <div class="flex w-full">
                                    <div class="w-4/12">
                                        <img src="../../images/applewatch.png" alt="" />
                                    </div>
                                    <div class="w-8/12 flex flex-col justify-between items-start pl-3">
                                        <h2 class="text-sm font-semibold">Apple Watch</h2>
                                        <div class="text-xs text-textgray">6.1” Super Retina XDR display, Dual Rear Camera, 4GB RAM…</div>
                                        <h2 class="text-base font-semibold">
                                            270 TL /
                                            <span class="text-xs font-medium text-textgray">Ay’dan kirala</span>
                                        </h2>
                                        <div class="mt-2">
                                            <a href="/sepetim" class="bg-black text-white rounded-full py-1 md:py-2 px-2 md:px-3 self-center font-semibold text-sm text-center">Kirala</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="absolute right-3 top-3">
                                    <svg class="hidden md:block" width="24.133" height="19.916" viewBox="0 0 24.133 19.916">
                                        <path id="_23" data-name="23"
                                              d="M14.088,23.686a1.21,1.21,0,0,1-.714-.23L4.409,16.849a.629.629,0,0,1-.133-.121,7.569,7.569,0,0,1,9.813-11.47A7.574,7.574,0,0,1,23.961,6h0a7.586,7.586,0,0,1,0,10.732.629.629,0,0,1-.133.121L14.8,23.407A1.21,1.21,0,0,1,14.088,23.686ZM5.861,14.938l8.228,6.05,8.228-6.05a5.179,5.179,0,0,0-.06-7.26h0a5.166,5.166,0,0,0-7.26,0,1.21,1.21,0,0,1-1.718,0,5.189,5.189,0,1,0-7.417,7.26Z"
                                              transform="translate(-2.052 -3.77)" fill="#231f20" opacity="0.6" />
                                    </svg>
                                    <svg class="block md:hidden" width="18.373" height="15.162" viewBox="0 0 18.373 15.162">
                                        <path id="_23" data-name="23"
                                              d="M11.215,18.932a.921.921,0,0,1-.543-.175L3.846,13.727a.479.479,0,0,1-.1-.092A5.762,5.762,0,0,1,11.215,4.9a5.766,5.766,0,0,1,7.516.562h0a5.775,5.775,0,0,1,0,8.17.479.479,0,0,1-.1.092L11.759,18.72A.921.921,0,0,1,11.215,18.932Zm-6.264-6.66,6.264,4.606,6.264-4.606a3.942,3.942,0,0,0-.046-5.527h0a3.933,3.933,0,0,0-5.527,0,.921.921,0,0,1-1.308,0,3.951,3.951,0,1,0-5.647,5.527Z"
                                              transform="translate(-2.052 -3.77)" fill="#231f20" opacity="0.6" />
                                    </svg>
                                </div>
                            </div>
                        </SplideSlide>
                        <SplideSlide>
                            <div class="relative flex-1 bg-white rounded-2lg py-6 pt-10 md:pt-10 px-4 w-full border-2 border-bordergray my-4">
                                <div class="flex w-full">
                                    <div class="w-4/12">
                                        <img src="../../images/applewatch.png" alt="" />
                                    </div>
                                    <div class="w-8/12 flex flex-col justify-between items-start pl-3">
                                        <h2 class="text-sm font-semibold">Apple Watch</h2>
                                        <div class="text-xs text-textgray">6.1” Super Retina XDR display, Dual Rear Camera, 4GB RAM…</div>
                                        <h2 class="text-base font-semibold">
                                            270 TL /
                                            <span class="text-xs font-medium text-textgray">Ay’dan kirala</span>
                                        </h2>
                                        <div class="mt-2">
                                            <a href="/sepetim" class="bg-black text-white rounded-full py-1 md:py-2 px-2 md:px-3 self-center font-semibold text-sm text-center">Kirala</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="absolute right-3 top-3">
                                    <svg class="hidden md:block" width="24.133" height="19.916" viewBox="0 0 24.133 19.916">
                                        <path id="_23" data-name="23"
                                              d="M14.088,23.686a1.21,1.21,0,0,1-.714-.23L4.409,16.849a.629.629,0,0,1-.133-.121,7.569,7.569,0,0,1,9.813-11.47A7.574,7.574,0,0,1,23.961,6h0a7.586,7.586,0,0,1,0,10.732.629.629,0,0,1-.133.121L14.8,23.407A1.21,1.21,0,0,1,14.088,23.686ZM5.861,14.938l8.228,6.05,8.228-6.05a5.179,5.179,0,0,0-.06-7.26h0a5.166,5.166,0,0,0-7.26,0,1.21,1.21,0,0,1-1.718,0,5.189,5.189,0,1,0-7.417,7.26Z"
                                              transform="translate(-2.052 -3.77)" fill="#231f20" opacity="0.6" />
                                    </svg>
                                    <svg class="block md:hidden" width="18.373" height="15.162" viewBox="0 0 18.373 15.162">
                                        <path id="_23" data-name="23"
                                              d="M11.215,18.932a.921.921,0,0,1-.543-.175L3.846,13.727a.479.479,0,0,1-.1-.092A5.762,5.762,0,0,1,11.215,4.9a5.766,5.766,0,0,1,7.516.562h0a5.775,5.775,0,0,1,0,8.17.479.479,0,0,1-.1.092L11.759,18.72A.921.921,0,0,1,11.215,18.932Zm-6.264-6.66,6.264,4.606,6.264-4.606a3.942,3.942,0,0,0-.046-5.527h0a3.933,3.933,0,0,0-5.527,0,.921.921,0,0,1-1.308,0,3.951,3.951,0,1,0-5.647,5.527Z"
                                              transform="translate(-2.052 -3.77)" fill="#231f20" opacity="0.6" />
                                    </svg>
                                </div>
                            </div>
                        </SplideSlide>
                    </SplideTrack>

                    <div class="splide__arrows">
                        <button class="splide__arrow splide__arrow--prev !-left-9">
                            <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                      transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                        <button class="splide__arrow splide__arrow--next !-right-9">
                            <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                      transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                    </div>
                </Splide>
                <!--                <div class="flex space-x-4 pl-0 p-3 lg:p-0 overflow-x-scroll">-->
                <!--                    <div-->
                <!--                        class="relative flex-1 bg-white rounded-2lg py-6 pt-10 md:pt-10 px-4 w-4/12 border-2 border-bordergray my-4 min-w-[303px] lg:min-w-[400px]">-->
                <!--                        <div class="flex w-full">-->
                <!--                            <div class="w-4/12">-->
                <!--                                <img src="../../images/ipad.png" alt="" />-->
                <!--                            </div>-->
                <!--                            <div class="w-8/12 flex flex-col justify-between items-start pl-3">-->
                <!--                                <h2 class="text-sm font-semibold">Apple iPad Air 12 (2022)</h2>-->
                <!--                                <div class="text-xs text-textgray">6.1” Super Retina XDR display, Dual Rear Camera, 4GB-->
                <!--                                    RAM…-->
                <!--                                </div>-->
                <!--                                <h2 class="text-base font-semibold">-->
                <!--                                    540 TL /-->
                <!--                                    <span class="text-xs font-medium text-textgray">Ay’dan kirala</span>-->
                <!--                                </h2>-->
                <!--                                <div class="mt-2">-->
                <!--                                    <a href="/sepetim"-->
                <!--                                       class="bg-black text-white rounded-full py-1 md:py-2 px-2 md:px-3 self-center font-semibold text-sm text-center">Kirala</a>-->
                <!--                                </div>-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                        <div class="absolute right-3 top-3" @click="share()">-->
                <!--                            <svg class="hidden md:block" width="24.133" height="19.916" viewBox="0 0 24.133 19.916">-->
                <!--                                <path-->
                <!--                                    id="_23"-->
                <!--                                    data-name="23"-->
                <!--                                    d="M14.088,23.686a1.21,1.21,0,0,1-.714-.23L4.409,16.849a.629.629,0,0,1-.133-.121,7.569,7.569,0,0,1,9.813-11.47A7.574,7.574,0,0,1,23.961,6h0a7.586,7.586,0,0,1,0,10.732.629.629,0,0,1-.133.121L14.8,23.407A1.21,1.21,0,0,1,14.088,23.686ZM5.861,14.938l8.228,6.05,8.228-6.05a5.179,5.179,0,0,0-.06-7.26h0a5.166,5.166,0,0,0-7.26,0,1.21,1.21,0,0,1-1.718,0,5.189,5.189,0,1,0-7.417,7.26Z"-->
                <!--                                    transform="translate(-2.052 -3.77)"-->
                <!--                                    fill="#231f20"-->
                <!--                                    opacity="0.6"-->
                <!--                                />-->
                <!--                            </svg>-->
                <!--                            <svg class="block md:hidden" width="18.373" height="15.162" viewBox="0 0 18.373 15.162">-->
                <!--                                <path-->
                <!--                                    id="_23"-->
                <!--                                    data-name="23"-->
                <!--                                    d="M11.215,18.932a.921.921,0,0,1-.543-.175L3.846,13.727a.479.479,0,0,1-.1-.092A5.762,5.762,0,0,1,11.215,4.9a5.766,5.766,0,0,1,7.516.562h0a5.775,5.775,0,0,1,0,8.17.479.479,0,0,1-.1.092L11.759,18.72A.921.921,0,0,1,11.215,18.932Zm-6.264-6.66,6.264,4.606,6.264-4.606a3.942,3.942,0,0,0-.046-5.527h0a3.933,3.933,0,0,0-5.527,0,.921.921,0,0,1-1.308,0,3.951,3.951,0,1,0-5.647,5.527Z"-->
                <!--                                    transform="translate(-2.052 -3.77)"-->
                <!--                                    fill="#231f20"-->
                <!--                                    opacity="0.6"-->
                <!--                                />-->
                <!--                            </svg>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                    <div-->
                <!--                        class="relative flex-1 bg-white rounded-2lg py-6 pt-10 md:pt-10 px-4 w-4/12 border-2 border-bordergray my-4 min-w-[303px] lg:min-w-[400px]">-->
                <!--                        <div class="flex w-full">-->
                <!--                            <div class="w-4/12">-->
                <!--                                <img src="../../images/iphone12.png" alt="" />-->
                <!--                            </div>-->
                <!--                            <div class="w-8/12 flex flex-col justify-between items-start pl-3">-->
                <!--                                <h2 class="text-sm font-semibold">Apple iPhone 12 64 GB</h2>-->
                <!--                                <div class="text-xs text-textgray">6.1” Super Retina XDR display, Dual Rear Camera, 4GB-->
                <!--                                    RAM…-->
                <!--                                </div>-->
                <!--                                <h2 class="text-base font-semibold">-->
                <!--                                    480 TL /-->
                <!--                                    <span class="text-xs font-medium text-textgray">Ay’dan kirala</span>-->
                <!--                                </h2>-->
                <!--                                <div class="mt-2">-->
                <!--                                    <a href="/sepetim"-->
                <!--                                       class="bg-black text-white rounded-full py-1 md:py-2 px-2 md:px-3 self-center font-semibold text-sm text-center">Kirala</a>-->
                <!--                                </div>-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                        <div class="absolute right-3 top-3">-->
                <!--                            <svg class="hidden md:block" width="24.133" height="19.916" viewBox="0 0 24.133 19.916">-->
                <!--                                <path-->
                <!--                                    id="_23"-->
                <!--                                    data-name="23"-->
                <!--                                    d="M14.088,23.686a1.21,1.21,0,0,1-.714-.23L4.409,16.849a.629.629,0,0,1-.133-.121,7.569,7.569,0,0,1,9.813-11.47A7.574,7.574,0,0,1,23.961,6h0a7.586,7.586,0,0,1,0,10.732.629.629,0,0,1-.133.121L14.8,23.407A1.21,1.21,0,0,1,14.088,23.686ZM5.861,14.938l8.228,6.05,8.228-6.05a5.179,5.179,0,0,0-.06-7.26h0a5.166,5.166,0,0,0-7.26,0,1.21,1.21,0,0,1-1.718,0,5.189,5.189,0,1,0-7.417,7.26Z"-->
                <!--                                    transform="translate(-2.052 -3.77)"-->
                <!--                                    fill="#231f20"-->
                <!--                                    opacity="0.6"-->
                <!--                                />-->
                <!--                            </svg>-->
                <!--                            <svg class="block md:hidden" width="18.373" height="15.162" viewBox="0 0 18.373 15.162">-->
                <!--                                <path-->
                <!--                                    id="_23"-->
                <!--                                    data-name="23"-->
                <!--                                    d="M11.215,18.932a.921.921,0,0,1-.543-.175L3.846,13.727a.479.479,0,0,1-.1-.092A5.762,5.762,0,0,1,11.215,4.9a5.766,5.766,0,0,1,7.516.562h0a5.775,5.775,0,0,1,0,8.17.479.479,0,0,1-.1.092L11.759,18.72A.921.921,0,0,1,11.215,18.932Zm-6.264-6.66,6.264,4.606,6.264-4.606a3.942,3.942,0,0,0-.046-5.527h0a3.933,3.933,0,0,0-5.527,0,.921.921,0,0,1-1.308,0,3.951,3.951,0,1,0-5.647,5.527Z"-->
                <!--                                    transform="translate(-2.052 -3.77)"-->
                <!--                                    fill="#231f20"-->
                <!--                                    opacity="0.6"-->
                <!--                                />-->
                <!--                            </svg>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                    <div-->
                <!--                        class="relative flex-1 bg-white rounded-2lg py-6 pt-10 md:pt-10 px-4 w-4/12 border-2 border-bordergray my-4 min-w-[303px] lg:min-w-[400px]">-->
                <!--                        <div class="flex w-full">-->
                <!--                            <div class="w-4/12">-->
                <!--                                <img src="../../images/applewatch.png" alt="" />-->
                <!--                            </div>-->
                <!--                            <div class="w-8/12 flex flex-col justify-between items-start pl-3">-->
                <!--                                <h2 class="text-sm font-semibold">Apple Watch</h2>-->
                <!--                                <div class="text-xs text-textgray">6.1” Super Retina XDR display, Dual Rear Camera, 4GB-->
                <!--                                    RAM…-->
                <!--                                </div>-->
                <!--                                <h2 class="text-base font-semibold">-->
                <!--                                    270 TL /-->
                <!--                                    <span class="text-xs font-medium text-textgray">Ay’dan kirala</span>-->
                <!--                                </h2>-->
                <!--                                <div class="mt-2">-->
                <!--                                    <a href="/sepetim"-->
                <!--                                       class="bg-black text-white rounded-full py-1 md:py-2 px-2 md:px-3 self-center font-semibold text-sm text-center">Kirala</a>-->
                <!--                                </div>-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                        <div class="absolute right-3 top-3">-->
                <!--                            <svg class="hidden md:block" width="24.133" height="19.916" viewBox="0 0 24.133 19.916">-->
                <!--                                <path-->
                <!--                                    id="_23"-->
                <!--                                    data-name="23"-->
                <!--                                    d="M14.088,23.686a1.21,1.21,0,0,1-.714-.23L4.409,16.849a.629.629,0,0,1-.133-.121,7.569,7.569,0,0,1,9.813-11.47A7.574,7.574,0,0,1,23.961,6h0a7.586,7.586,0,0,1,0,10.732.629.629,0,0,1-.133.121L14.8,23.407A1.21,1.21,0,0,1,14.088,23.686ZM5.861,14.938l8.228,6.05,8.228-6.05a5.179,5.179,0,0,0-.06-7.26h0a5.166,5.166,0,0,0-7.26,0,1.21,1.21,0,0,1-1.718,0,5.189,5.189,0,1,0-7.417,7.26Z"-->
                <!--                                    transform="translate(-2.052 -3.77)"-->
                <!--                                    fill="#231f20"-->
                <!--                                    opacity="0.6"-->
                <!--                                />-->
                <!--                            </svg>-->
                <!--                            <svg class="block md:hidden" width="18.373" height="15.162" viewBox="0 0 18.373 15.162">-->
                <!--                                <path-->
                <!--                                    id="_23"-->
                <!--                                    data-name="23"-->
                <!--                                    d="M11.215,18.932a.921.921,0,0,1-.543-.175L3.846,13.727a.479.479,0,0,1-.1-.092A5.762,5.762,0,0,1,11.215,4.9a5.766,5.766,0,0,1,7.516.562h0a5.775,5.775,0,0,1,0,8.17.479.479,0,0,1-.1.092L11.759,18.72A.921.921,0,0,1,11.215,18.932Zm-6.264-6.66,6.264,4.606,6.264-4.606a3.942,3.942,0,0,0-.046-5.527h0a3.933,3.933,0,0,0-5.527,0,.921.921,0,0,1-1.308,0,3.951,3.951,0,1,0-5.647,5.527Z"-->
                <!--                                    transform="translate(-2.052 -3.77)"-->
                <!--                                    fill="#231f20"-->
                <!--                                    opacity="0.6"-->
                <!--                                />-->
                <!--                            </svg>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                    <div-->
                <!--                        class="relative flex-1 bg-white rounded-2lg py-6 pt-10 md:pt-10 px-4 w-4/12 border-2 border-bordergray my-4 min-w-[303px] lg:min-w-[400px]">-->
                <!--                        <div class="flex w-full">-->
                <!--                            <div class="w-4/12">-->
                <!--                                <img src="../../images/applewatch.png" alt="" />-->
                <!--                            </div>-->
                <!--                            <div class="w-8/12 flex flex-col justify-between items-start pl-3">-->
                <!--                                <h2 class="text-sm font-semibold">Apple Watch</h2>-->
                <!--                                <div class="text-xs text-textgray">6.1” Super Retina XDR display, Dual Rear Camera, 4GB-->
                <!--                                    RAM…-->
                <!--                                </div>-->
                <!--                                <h2 class="text-base font-semibold">-->
                <!--                                    270 TL /-->
                <!--                                    <span class="text-xs font-medium text-textgray">Ay’dan kirala</span>-->
                <!--                                </h2>-->
                <!--                                <div class="mt-2">-->
                <!--                                    <a href="/sepetim"-->
                <!--                                       class="bg-black text-white rounded-full py-1 md:py-2 px-2 md:px-3 self-center font-semibold text-sm text-center">Kirala</a>-->
                <!--                                </div>-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                        <div class="absolute right-3 top-3">-->
                <!--                            <svg class="hidden md:block" width="24.133" height="19.916" viewBox="0 0 24.133 19.916">-->
                <!--                                <path-->
                <!--                                    id="_23"-->
                <!--                                    data-name="23"-->
                <!--                                    d="M14.088,23.686a1.21,1.21,0,0,1-.714-.23L4.409,16.849a.629.629,0,0,1-.133-.121,7.569,7.569,0,0,1,9.813-11.47A7.574,7.574,0,0,1,23.961,6h0a7.586,7.586,0,0,1,0,10.732.629.629,0,0,1-.133.121L14.8,23.407A1.21,1.21,0,0,1,14.088,23.686ZM5.861,14.938l8.228,6.05,8.228-6.05a5.179,5.179,0,0,0-.06-7.26h0a5.166,5.166,0,0,0-7.26,0,1.21,1.21,0,0,1-1.718,0,5.189,5.189,0,1,0-7.417,7.26Z"-->
                <!--                                    transform="translate(-2.052 -3.77)"-->
                <!--                                    fill="#231f20"-->
                <!--                                    opacity="0.6"-->
                <!--                                />-->
                <!--                            </svg>-->
                <!--                            <svg class="block md:hidden" width="18.373" height="15.162" viewBox="0 0 18.373 15.162">-->
                <!--                                <path-->
                <!--                                    id="_23"-->
                <!--                                    data-name="23"-->
                <!--                                    d="M11.215,18.932a.921.921,0,0,1-.543-.175L3.846,13.727a.479.479,0,0,1-.1-.092A5.762,5.762,0,0,1,11.215,4.9a5.766,5.766,0,0,1,7.516.562h0a5.775,5.775,0,0,1,0,8.17.479.479,0,0,1-.1.092L11.759,18.72A.921.921,0,0,1,11.215,18.932Zm-6.264-6.66,6.264,4.606,6.264-4.606a3.942,3.942,0,0,0-.046-5.527h0a3.933,3.933,0,0,0-5.527,0,.921.921,0,0,1-1.308,0,3.951,3.951,0,1,0-5.647,5.527Z"-->
                <!--                                    transform="translate(-2.052 -3.77)"-->
                <!--                                    fill="#231f20"-->
                <!--                                    opacity="0.6"-->
                <!--                                />-->
                <!--                            </svg>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </div>-->
            </div>
        </section>
        <section class="mt-8 ts:mt-5 2xl:mt-8 bg-white" id="commentSection">
            <div class="flex mx-auto flex-col max-w-7xl">
                <div class="flex">
                    <!--Yorum ve açıklama sekmeler-->
                    <div @click="gozukecekAlan = 'aciklama'" v-if="this.product.collections.filter((collection) => [37, 84, 97].includes(collection.id)).length > 0"
                         class="relative transition-all cursor-pointer font-bold text-base md:text-lg lg:text-xl mt-2 mx-2 lg:mx-4 self-center text-center w-full md:w-auto" :class="[gozukecekAlan == 'aciklama' ? '' : 'mb-0']">
                        Ürün Açıklaması
                        <svg class="absolute left-0 -bottom-1 transition-all ease-in-out delay-150" style="width: 100%" height="4" viewBox="0 0 160.999 4" :class="[gozukecekAlan == 'aciklama' ? 'scale-100' : 'scale-0']">
                            <path id="Path_2973" data-name="Path 2973" d="M-8643-15807.608h161" transform="translate(8642.999 15809.608)" fill="none" stroke="#139a52" stroke-width="4" />
                        </svg>
                    </div>
                    <div @click="gozukecekAlan = 'yorum'" v-if="reviews !== undefined" class="relative transition-all cursor-pointer font-bold text-base md:text-lg lg:text-xl mt-2 mx-2 lg:mx-4 self-center text-center w-full md:w-auto"
                         :class="[gozukecekAlan == 'yorum' ? '' : 'mb-0']">
                        Ürün Yorumları
                        <svg class="absolute left-0 -bottom-1 transition-all ease-in-out delay-150" style="width: 100%" height="4" viewBox="0 0 160.999 4" :class="[gozukecekAlan == 'yorum' ? 'scale-100' : 'scale-0']">
                            <path id="Path_2973" data-name="Path 2973" d="M-8643-15807.608h161" transform="translate(8642.999 15809.608)" fill="none" stroke="#139a52" stroke-width="4" />
                        </svg>
                    </div>
                </div>
                <div class="w-full flex justify-center flex-col items-center transition-height ease-in-out delay-1000 duration-700" :class="[showmoreparam ? '' : 'max-h-[1000px]']" v-if="gozukecekAlan == 'aciklama'">
                    <div class="transition ease-in-out delay-1000 duration-700 p-7 leading-7" :class="[showmoreparam ? '' : 'overflow-y-hidden']">
                        <div v-html="product.attribute_data.description.tr" class="font-santralregular product-description"></div>
                    </div>
                    <div class="mt-4" v-if="product.attribute_data?.description?.tr">
                        <button @click="showmoreparam = !showmoreparam" class="bg-black text-white rounded-full py-2 px-4 self-center font-semibold text-lg text-center whitespace-nowrap">
                            <span v-if="!showmoreparam">Daha Fazla</span>
                            <span v-if="showmoreparam">Daha Az</span>
                        </button>
                    </div>
                </div>

                <div v-if="gozukecekAlan == 'yorum' && reviews !== undefined">
                    <div class="font-bold text-2xl lg:text-3xl w-full mt-8 ts:mt-5 2xl:mt-8" id="scrollstar">
                        {{ product.attribute_data.name?.tr }}
                    </div>
                    <div class="block lg:hidden font-bold text-lg w-full mt-5">Ürünü Değerlendir</div>

                    <div class="w-full flex flex-wrap">
                        <div class="w-full md:w-1/3 lg:w-1/2 flex flex-wrap mt-2 lg:mt-10">
                            <div class="w-full lg:w-2/6 flex flex-row md:flex-col justify-center md:px-5 lg:px-0">
                                <div class="w-full p-5 border-2 border-bordergray rounded-2lg">
                                    <img class="max-w-[120px] lg:max-w-[140px] mx-auto" :src="mainProductImageURL" alt="" />
                                </div>
                                <div class="w-full flex items-center justify-center flex-col mt-0 md:mt-4 lg:mt-0" v-if="true">
                                    <svg class="block lg:hidden" width="122.989" height="22.05" viewBox="0 0 122.989 22.05">
                                        <g data-name="Group 3931" transform="translate(1.03 1)" v-if="(reviews.rating * 10 <= 50) & (reviews.rating * 10 >= 45)">
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(98.226 -2.432)" fill="#70d44b" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(73.269 -2.432)" fill="#70d44b" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(48.313 -2.432)" fill="#70d44b" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(23.356 -2.432)" fill="#70d44b" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(-1.746 -2.432)" fill="#70d44b" />
                                        </g>
                                        <g data-name="Group 3931" transform="translate(1.03 1)" v-if="(reviews.rating * 10 < 45) & (reviews.rating * 10 > 40)">
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                transform="translate(98.226 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(73.269 -2.432)" fill="#70d44b" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(48.313 -2.432)" fill="#70d44b" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(23.356 -2.432)" fill="#70d44b" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(-1.746 -2.432)" fill="#70d44b" />
                                        </g>
                                        <g data-name="Group 3931" transform="translate(1.03 1)" v-if="(reviews.rating * 10 < 40) & (reviews.rating * 10 >= 30)">
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                transform="translate(98.226 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                transform="translate(73.269 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(48.313 -2.432)" fill="#70d44b" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(23.356 -2.432)" fill="#70d44b" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(-1.746 -2.432)" fill="#70d44b" />
                                        </g>
                                        <g data-name="Group 3931" transform="translate(1.03 1)" v-if="(reviews.rating * 10 < 30) & (reviews.rating * 10 >= 20)">
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                transform="translate(98.226 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                transform="translate(73.269 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                transform="translate(48.313 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(23.356 -2.432)" fill="#70d44b" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(-1.746 -2.432)" fill="#70d44b" />
                                        </g>
                                        <g data-name="Group 3931" transform="translate(1.03 1)" v-if="(reviews.rating * 10 < 20) & (reviews.rating * 10 >= 0)">
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                transform="translate(98.226 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                transform="translate(73.269 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                transform="translate(48.313 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                transform="translate(23.356 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                            <path
                                                d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                data-name="star-solid" transform="translate(-1.746 -2.432)" fill="#70d44b" />
                                        </g>
                                    </svg>
                                    <div class="w-full text-sm text-textgray text-center lg:ml-4 mt-1 lg:mt-4">
                                        <span class="text-black text-base font-bold">{{ reviews.total }}</span>
                                        değerlendirme
                                    </div>
                                </div>
                            </div>
                            <div class="w-4/6 hidden lg:flex flex-col pl-4">
                                <div class="flex items-center pt-2 w-full" v-for="i in 5">
                                    <svg width="26.941" height="25.712" viewBox="0 0 26.941 25.712">
                                        <g id="Group_568" data-name="Group 568" transform="translate(-1709.376)">
                                            <path id="star-solid"
                                                  d="M27.961,14.094a1.8,1.8,0,0,0-1.048-3.251L19.6,10.566a.187.187,0,0,1-.163-.122L16.907,3.617a1.8,1.8,0,0,0-3.389,0L11,10.469a.187.187,0,0,1-.163.122l-7.315.276a1.8,1.8,0,0,0-1.048,3.251l5.738,4.511a.187.187,0,0,1,.065.2l-1.975,7a1.8,1.8,0,0,0,2.747,1.991l6.063-4.064a.179.179,0,0,1,.2,0l6.063,4.064a1.783,1.783,0,0,0,2.747-1.951L22.133,18.84a.179.179,0,0,1,.065-.2Z"
                                                  transform="translate(1707.63 -2.432)" fill="#70d44b" />
                                        </g>
                                    </svg>
                                    <div class="text-xl font-bold text-black ml-4">{{ 5 - (i - 1) }}</div>
                                    <div class="ml-4" v-if="reviews.data">
                                        <svg id="Group_3921" data-name="Group 3921" width="259.943" height="5.285" viewBox="0 0 269.845 5.285">
                                            <rect id="Rectangle_870" data-name="Rectangle 870" width="259.943" height="5.285" rx="2.642" fill="#c7c7c7" />
                                            <rect id="Rectangle_871" data-name="Rectangle 871" :width="ratingBar(reviews.data.filter((review) => review.reviewRating == 5 - (i - 1)).length)" height="5.285" rx="2.642" fill="#61ba3f" />
                                        </svg>
                                    </div>
                                    <div class="text-sm font-bold text-textgray ml-4" v-if="reviews.data" v-html="reviews.data.filter((review) => review.reviewRating == 5 - (i - 1)).length"></div>
                                </div>
                            </div>
                        </div>
                        <div v-if="auth.isUserLoggedIn && isPurchased" class="w-full md:w-2/3 lg:w-1/2">
                            <div class="hidden lg:flex justify-between items-center">
                                <div class="font-bold text-lg w-full">Ürünü Değerlendir</div>
                                <AlpineRating @update:localRating="changeSelectedRating" />
                            </div>
                            <input type="text" v-model="comment.reviewTitle"
                                   class="w-full mt-3 lg:mt-6 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-sm lg:text-base tracking-wider placeholder:mt-2"
                                   required placeholder="Konu" />
                            <!--                            {{ comment.reviewRating }}-->
                            <textarea
                                class="w-full mt-3 lg:mt-6 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-sm lg:text-base tracking-wider placeholder:mt-2 h-44 md:h-36 lg:h-44"
                                required="required" name="comment" placeholder="Yorumunuz" cols="20" rows="10" v-model="comment.reviewMessage"></textarea>
                            <div class="flex justify-end w-full">
                                <Link :href="route('sendComment')" as="submit" method="post"
                                      class="cursor-pointer mt-3 bg-black text-white rounded-2lg py-2 text-base px-6 self-center font-bold leading-none flex items-center justify-center disabled:opacity-80" :data="comment" :disabled="comment.processing"
                                      preserve-state preserve-scroll :only="['reviews', 'errors', 'commentSubmittedSuccessfully']">Gönder
                                </Link>
                            </div>
                            <div class="flex mt-4 self-center justify-center flex-col" v-if="this.$page.props.errors?.commentValidation">
                                <div class="flex text-sm text-kbred mr-[2px] tracking-normal" v-for="error in errors.commentValidation">{{ error }}</div>
                            </div>

                            <div class="flex mt-4 self-center justify-center flex-col" v-if="commentSubmittedSuccessfully">
                                <div class="flex text-sm text-kbred mr-[2px] tracking-normal">Yorumunuz kayıt edildi</div>
                            </div>
                        </div>
                        <div v-if="!auth.isUserLoggedIn || !isPurchased" class="w-full md:w-2/3 lg:w-1/2 items-center flex flex-col justify-center mt-16 md:mt-0">
                            <div class="text-sm font-bold text-textgray w-full lg:w-1/2 text-center">Bu ürüne yorum veya değerlendirme yapabilmeniz için ürünü kiralamanız gerekmektedir.</div>
                            <Link method="post" as="button" href="/sepete-ekle" :data="{
                                product_id: product.id,
                                month: selectedMonth,
                                product_variant_id: selectedVariandId,
                                is_insurance_requested: isInsuranceSelected,
                            }" preserve-scroll class="mt-3 lg:mt-8 bg-black text-white rounded-full py-2 text-base px-6 self-center font-bold leading-tight">
                                Kirala
                            </Link>
                        </div>
                    </div>
                    <div class="w-full border-1 border-bordergray my-5 lg:my-5" v-if="auth.isUserLoggedIn || (reviews !== undefined && reviews.data && reviews.data.length > 0)"></div>
                    <Disclosure as="div" v-slot="{ open }">
                        <div class="w-full flex justify-between items-center px-4">
                            <div class="font-bold text-base w-full" v-if="reviews !== undefined && reviews.data && reviews.data.length > 0">Ürün Yorumları ({{ reviews.total }})</div>
                            <!--                            <DisclosureButton class="cursor-pointer border-3 rounded-full py-1.5 px-2 text-center mb-4 w-32 hover:bg-kbgreen hover:text-white font-bold text-sm"> Tümünü Gör</DisclosureButton>-->
                        </div>
                        <div class="flex flex-wrap">
                            <div v-if="reviews !== undefined" v-for="(review, index) in reviews.data" :key="index" class="w-full flex flex-col justify-between border-2 border-bordergray rounded-2lg p-5 mt-4">
                                <div class="flex items-center">
                                    <div class="text-[27px] rounded-full font-bold border-black border-2 flex justify-center items-center w-16 h-16 text-center leading-none">
                                        {{
                                            review.author
                                                .split(" ")
                                                .map((n) => n[0])
                                                .join("")
                                        }}
                                    </div>
                                    <div class="flex-col">
                                        <div class="flex">
                                            <div class="ml-2 text-base font-bold whitespace-nowrap">{{ review.author }}</div>
                                            <div class="mx-3 text-sm text-checkoutgray whitespace-nowrap">|</div>
                                            <div class="text-sm text-checkoutgray whitespace-nowrap">
                                                {{ new Date(review.reviewDate).toLocaleDateString() }}
                                            </div>
                                        </div>
                                        <div class="ml-2 mt-2">
                                            <svg width="90.333" height="15.021" viewBox="0 0 90.333 15.021">
                                                <defs>
                                                    <clipPath id="clip-path">
                                                        <rect width="15.696" height="15.021" fill="none" />
                                                    </clipPath>
                                                </defs>
                                                <g transform="translate(-930.094 0.5)">
                                                    <path v-for="i in 5" :key="i"
                                                          d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                                          :data-name="review.reviewRating >= i ? `star-solid` : `none`" :transform="`translate( ${(928.348 + (i - 1) * 18.659).toFixed(3)} -2.433)`" :fill="review.reviewRating >= i ? `#70d44b` : `none`"
                                                          :stroke="review.reviewRating < i ? `#70d44b` : ``" :stroke-width="review.reviewRating < i ? `1` : ``" />
                                                </g>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-base w-full mt-6">{{ review.reviewMessage }}</div>
                            </div>
                        </div>
                    </Disclosure>
                </div>
            </div>
        </section>

        <TransitionRoot appear :show="showErrorPopup" as="template">
            <Dialog as="div" @close="closeModal" class="relative z-50">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white px-5 py-5 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#ef4444" class="w-6 h-6">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                          d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                                </svg>

                                <DialogTitle>
                                    <h2 class="text-base font-semibold text-center mb-2 mt-2">Renk Seçimi</h2>
                                </DialogTitle>
                                <p class="text-xs font-kiralabunuthin ts:text-sm text-center">Ürünü sepete eklemek için lütfen renk seçiniz</p>

                                <div class="absolute top-2 right-5 cursor-pointer p-2" @click="showErrorPopup = !showErrorPopup">x</div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
        <TransitionRoot appear :show="MotorcycleModalIsOpen" as="template">
            <Dialog as="div" @close="closeMotorcycleFormModal" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>
                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-[350px] lg:max-w-[850px] xl:max-w-[1000px] transform overflow-hidden rounded-sm bg-white px-1 py-1 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                                <h1 class="font-bold text-xl lg:text-3xl mt-5">Kiralama Talebi oluştur</h1>
                                <div class="w-full flex justify-center items-center mb-5" v-if="errors.length > 0">
                                    <div class="flex mt-4 whitespace-nowrap self-center flex-col p-4">
                                        <div class="text-sm mx-auto text-black my-1" v-for="error in errors">{{ error }}</div>
                                    </div>
                                </div>
                                <form class="w-full mt-8 px-4 lg:px-8" action="#">
                                    <div class="flex flex-wrap">
                                        <div class="group relative w-full lg:w-1/2 lg:pr-2">
                                            <input :class="{ 'border-kbred': errors.name != null }" id="fullname" required="" type="text" name="AdSoyad*" v-model="motorcycleForm.name"
                                                   class="!py-2 !leading-none w-full mb-5 ts:mb-7 rounded-lg border-2 border-bordergray placeholder:text-xs text-lg tracking-wider placeholder:text-placeholdergray focus:border-acordion-green hover:border-kbblue focus:outline-none focus:ring-kbgreen group-hover:placeholder-white peer" />
                                            <label for="fullname"
                                                   class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">İsim,
                                                Soyisim*</label>
                                            <div class="text-sm text-kbred absolute bottom-1" v-if="errors.name">{{ errors.name }}</div>
                                        </div>
                                        <div class="relative group w-full lg:w-1/2 lg:pr-2">
                                            <input :class="{ 'border-kbred': errors.firmName != null }" id="firmName" type="text" v-model="motorcycleForm.firmName" placeholder="Firma İsmi*" autocomplete="off" required=""
                                                   class="!py-1.5 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />
                                            <label for="firmName"
                                                   class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">Firma
                                                İsmi*</label>
                                            <div class="text-sm text-kbred absolute bottom-1" v-if="errors.firmName">{{ errors.firmName }}</div>
                                        </div>
                                        <div class="relative group w-full lg:w-1/2 lg:pr-2">
                                            <input :class="{ 'border-kbred': errors.gsm != null }" v-maska data-maska="(5##) ### ## ##" id="telephone"
                                                   class="!py-2 peer placeholder-transparent w-full mb-5 ts:mb-7 rounded-lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                                   required type="text" placeholder="" name="telno" v-model="motorcycleForm.gsm" />
                                            <label for="telephone"
                                                   class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Telefon*</label>
                                            <div class="text-sm text-kbred absolute bottom-1" v-if="errors.gsm">{{ errors.gsm }}</div>
                                        </div>
                                        <div class="relative group w-full lg:w-1/2 lg:pr-2">
                                            <input :class="{ 'border-kbred': errors.email != null }" id="email" type="email" v-model="motorcycleForm.email" placeholder="E-posta" autocomplete="off" required=""
                                                   class="!py-2 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />
                                            <label for="email"
                                                   class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">E-Posta*</label>
                                            <div class="text-sm text-kbred absolute bottom-1" v-if="errors.email">{{ errors.email }}</div>
                                        </div>
                                        <!--                                        <div class="relative group w-full lg:w-1/2 lg:pr-2">-->
                                        <!--                                            <input :class="{'border-kbred': errors.IDnumber != null}" id="IDnumber" v-maska data-maska="###########" type="text" v-model="motorcycleForm.IDnumber" placeholder="T.C. Kimlik No." autocomplete="off" required="" class="!py-2 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />-->
                                        <!--                                            <label for="IDnumber" class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">T.C. Kimlik No*</label>-->
                                        <!--                                            <div class="text-sm text-kbred absolute bottom-2" v-if="errors.IDnumber"> {{ errors.IDnumber }}</div>-->
                                        <!--                                        </div>-->
                                        <!--                                        <div class="relative group w-full lg:w-1/2 lg:pr-2">-->
                                        <!--                                            <input :class="{'border-kbred': errors.dateofbirth != null}" id="dateofbirth" v-maska data-maska="##/##/####" type="text" v-model="motorcycleForm.dateofbirth" placeholder="Doğum Tarihi" autocomplete="off" required="" class="!py-2 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />-->
                                        <!--                                            <label for="dateofbirth" class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">Doğum Tarihi*</label>-->
                                        <!--                                            <div class="text-sm text-kbred absolute bottom-1" v-if="errors.dateofbirth"> {{ errors.dateofbirth }}</div>-->
                                        <!--                                        </div>-->
                                        <div class="relative group w-full lg:w-1/2 lg:pr-2">
                                            <input :class="{ 'border-kbred': errors.city != null }" id="city" type="text" v-model="motorcycleForm.city" placeholder="Şehir" autocomplete="off" required=""
                                                   class="!py-2 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />
                                            <label for="city"
                                                   class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">Şehir*</label>
                                            <div class="text-sm text-kbred absolute bottom-1" v-if="errors.city">{{ errors.city }}</div>
                                        </div>
                                        <div class="relative group w-full lg:w-1/2 lg:pr-2">
                                            <input :class="{ 'border-kbred': errors.motorcycleNumber != null }" id="motorcycleNumber" type="number" v-model="motorcycleForm.motorcycleNumber" placeholder="Motosiklet Adedi" autocomplete="off"
                                                   required=""
                                                   class="!py-2 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />
                                            <label for="motorcycleNumber"
                                                   class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">Motosiklet
                                                Adedi*</label>
                                            <div class="text-sm text-kbred absolute bottom-1" v-if="errors.motorcycleNumber">{{ errors.motorcycleNumber }}</div>
                                        </div>
                                        <input type="hidden" v-model="product.name" />
                                        <div class="w-full flex justify-start items-center mb-3 relative">
                                            <input :class="{ 'border-kbred': errors.Contract1 != null }" v-model="motorcycleForm.Contract1" id="aydinlatma"
                                                   class="cursor-pointer w-4 h-4 border-3 rounded-md focus:outline-none checked:bg-black focus:bg-black" type="checkbox" required="required" />
                                            <label class="cursor-pointer text-sm text-left text-checkoutgray pl-2" for="aydinlatma"> <a href="/aydinlatma-metni" target="_blank"> Aydınlatma metni ve açık rıza beyan sözleşmesini kabul
                                                ediyorum.</a></label>
                                        </div>
                                        <!--                                        <div class="w-full flex justify-start items-center mb-3 relative">-->
                                        <!--                                            <input :class="{'border-kbred': errors.Contract2 != null}" v-model="motorcycleForm.Contract2" id="uyelik" class="cursor-pointer w-4 h-4 border-3 rounded-md focus:outline-none checked:bg-black focus:bg-black" type="checkbox" required="required">-->
                                        <!--                                            <label class="cursor-pointer text-sm text-left text-checkoutgray pl-2" for="uyelik"> <a href="/uyelik-sozlesmesi" target="_blank">Üyelik sözleşmesini kabul ediyorum.</a> </label>-->
                                        <!--                                        </div>-->
                                        <div class="text-sm text-kbred w-full" v-if="errors.Contract1">{{ errors.Contract1 }}</div>
                                        <!--                                        <div class="text-sm text-kbred w-full" v-if="errors.Contract2"> {{ errors.Contract2 }}</div>-->
                                        <div class="mt-4 flex justify-center w-full mb-8">
                                            <Link :href="route('sendMotorcycleInstitutionalForm')" method="post" class="bg-black text-white rounded-full py-2 px-4 self-center font-bold w-full lg:w-24" :data="motorcycleForm">Gönder</Link>
                                        </div>
                                    </div>
                                </form>
                                <div class="font-santralextrabold absolute top-0 right-3 cursor-pointer text-base bg-white rounded-lg p-3" @click="closeMotorcycleFormModal">X</div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>

        <TransitionRoot appear :show="MotorcycleSuccessIsOpen" as="template">
            <Dialog as="div" @close="closeMotorcycleSuccessModal" class="relative z-50">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white px-5 py-5 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 62.051 62.051">
                                    <g id="Group_4884" data-name="Group 4884" transform="translate(-73.036 -250.295)">
                                        <circle id="Ellipse_199" data-name="Ellipse 199" cx="31.025" cy="31.025" r="31.025" transform="translate(73.036 250.295)" fill="#70d44b" />
                                        <path id="Path_3299" data-name="Path 3299"
                                              d="M351.649,476.232a3.45,3.45,0,0,1-2.761-1.381l-8.464-11.286a3.451,3.451,0,0,1,5.522-4.141l5.538,7.383,14.223-21.334a3.451,3.451,0,1,1,5.743,3.829l-16.928,25.393a3.453,3.453,0,0,1-2.776,1.536Z"
                                              transform="translate(-251.82 -178.764)" fill="#fff" />
                                    </g>
                                </svg>
                                <DialogTitle>
                                    <h2 class="text-base font-semibold text-center mb-2 mt-2">Talebiniz alınmıştır.</h2>
                                </DialogTitle>
                                <p class="text-xs font-kiralabunuthin ts:text-sm text-center">
                                    En kısa süre içerisinde tarafınıza dönüş sağlanacaktır. <br />
                                    Çalışma saatlerimiz hafta içi 09:00 - 18:00 arasındadır. <br />
                                    Mesai saatleri dışındaki taleplere ilk mesai günü içerisinde dönüş sağlanacaktır. <br />
                                    Teşekkürler!
                                </p>
                                <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeMotorcycleSuccessModal">x</div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
        <TransitionRoot appear :show="rentAndBuyModalIsOpen" as="template">
            <Dialog as="div" @close="closeRentAndBuyModal" class="relative z-[99]">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>
                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel class="max-w-[350px] lg:max-w-[750px] xl:max-w-[1000px] transform overflow-hidden rounded-sm bg-white z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                                <div class="flex flex-wrap justify-start items-center">
                                    <div class="w-full lg:w-1/2 p-6" v-if="!rentAndBuyModalFormStatus">
                                        <div class="">
                                            <span class="font-santralregular text-black text-xl 2xl:text-3xl font-extrabold pb-2 lg:pb-6 block">Kirala+Satın Al Nedir?</span>

                                            <span class="font-santralregular text-sm 2xl:text-base text-black">
                                                Kirala+satın al, kira süresi sonunda ürünün senin olduğu bir abonelik seçeneğidir. <br>
                                                Ödeme planı için aşağıdaki formu doldur, en kısa sürede seninle iletişime geçelim.
                                            </span>
                                        </div>
                                        <div class="flex lg:hidden justify-center items-start w-full mt-3">
                                            <div class="flex flex-col items-center justify-center w-1/3 group">
                                                <div class="rounded-full border-2 border-bordergray p-2 group-hover:border-kbgreen transition-all">
                                                    <img class="max-h-[50px]" src="/images/cropped-ikon-192x192.png" alt="">
                                                </div>
                                                <div class="pt-2 font-santralregular text-xs text-black capitalize text-center group-hover:font-bold transition-all">
                                                    aydan aya <br> öde
                                                </div>
                                            </div>
                                            <div class="flex flex-col items-center justify-center w-1/3 group">
                                                <div class="rounded-full border-2 border-bordergray p-2 group-hover:border-kbgreen transition-all">
                                                    <img class="max-h-[50px]" src="/images/cropped-ikon-192x192.png" alt="">
                                                </div>
                                                <div class="pt-2 font-santralregular text-xs text-black capitalize text-center group-hover:font-bold transition-all">
                                                    kart limitin <br> sana kalsın
                                                </div>
                                            </div>
                                            <div class="flex flex-col items-center justify-center w-1/3 group">
                                                <div class="rounded-full border-2 border-bordergray p-2 group-hover:border-kbgreen transition-all">
                                                    <img class="max-h-[50px]" src="/images/cropped-ikon-192x192.png" alt="">
                                                </div>
                                                <div class="pt-2 font-santralregular text-xs text-black capitalize text-center group-hover:font-bold transition-all">
                                                    süre sonunda <br> satın alırsın
                                                </div>
                                            </div>
                                        </div>
                                        <form @submit.prevent="modalSubmit" class="w-full">
                                            <div class="w-full group relative mt-6">
                                                <input @keypress="preventNumericInput" id="name" type="text" v-model="modalForm.first_name"
                                                       class="peer block p-2.5 w-full z-20 text-sm text-gray-900 rounded-sm border border-black hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen" required
                                                       autocomplete="off" />
                                                <label for="name"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-2 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">
                                                    Ad*
                                                </label>
                                            </div>
                                            <div class="w-full group relative mt-6">
                                                <input @keypress="preventNumericInput" id="surname" type="text" v-model="modalForm.last_name" placeholder=""
                                                       class="peer block p-2.5 w-full z-20 text-sm text-gray-900 rounded-sm border border-black hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen" required
                                                       autocomplete="off" />
                                                <label for="surname"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-2 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">
                                                    Soyad*
                                                </label>
                                            </div>
                                            <div class="mt-6 group relative w-full">
                                                <input id="email" type="email" placeholder="" v-model="modalForm.email" autocomplete="off" required
                                                       class="peer block p-2.5 w-full z-20 text-sm text-gray-900 rounded-sm border border-black hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen" />
                                                <label for="email"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-2 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">
                                                    E-posta adresi*
                                                </label>
                                            </div>
                                            <div class="relative w-full group mt-6">
                                                <input v-model="modalForm.phone" id="location-search"
                                                       class="peer block p-2.5 w-full z-20 text-sm text-gray-900 rounded-sm border border-black hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen" placeholder=""
                                                       required v-maska data-maska="(5##) ### ## ##" />
                                                <label for="location-search"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-2 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Telefon*</label>
                                            </div>
                                            <div class="relative w-full group mt-6">
                                                <input v-model="modalForm.tckn" id="tckn"
                                                       class="peer block p-2.5 w-full z-20 text-sm text-gray-900 rounded-sm border border-black hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen" placeholder=""
                                                       required v-maska data-maska="###########" />
                                                <label for="tckn"
                                                       class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-2 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">T.C.
                                                    Kimlik No*</label>
                                            </div>
                                            <div class="flex mt-4 w-full justify-center">
                                                <button class="bg-black text-white rounded-sm py-2 px-4 self-center font-bold w-60 hover:bg-kbgreen font-santralextrabold" type="submit">Bilgi Al</button>
                                            </div>
                                            <div class="flex mt-4 w-full justify-start">
                                                <div class="w-full pt-2 font-santralregular text-xs text-kbred">
                                                    <b>Bilgilendirme</b>: Talebinizin değerlendirilmesi için findeks kontrolü yapılacaktır. Talep oluşturduktan sonra tarafınıza iletilen findeks sms'ine lütfen onay veriniz.
                                                </div>
                                            </div>

                                            <!-- loop validation errors -->
                                            <div v-for="error in $page.props.errors?.leadValidation" :key="error" class="mt-4">
                                                <div class="text-red-500 text-center">{{ error }}</div>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="w-full lg:w-1/2 p-6" v-if="rentAndBuyModalFormStatus">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 62.051 62.051" class="mx-auto">
                                            <g id="Group_4884" data-name="Group 4884" transform="translate(-73.036 -250.295)">
                                                <circle id="Ellipse_199" data-name="Ellipse 199" cx="31.025" cy="31.025" r="31.025" transform="translate(73.036 250.295)" fill="#70d44b" />
                                                <path id="Path_3299" data-name="Path 3299"
                                                      d="M351.649,476.232a3.45,3.45,0,0,1-2.761-1.381l-8.464-11.286a3.451,3.451,0,0,1,5.522-4.141l5.538,7.383,14.223-21.334a3.451,3.451,0,1,1,5.743,3.829l-16.928,25.393a3.453,3.453,0,0,1-2.776,1.536Z"
                                                      transform="translate(-251.82 -178.764)" fill="#fff" />
                                            </g>
                                        </svg>
                                        <h2 class="text-xl font-semibold text-center mb-2 mt-2">Talebiniz Alındı</h2>
                                        <p class="text-sm font-kiralabunuthin ts:text-base text-center">En kısa sürede çalışma arkadaşlarımız sizinle iletişime geçecek</p>
                                    </div>
                                    <img class="hidden lg:block w-full lg:w-1/2" src="../../images/popup/kirala-satin-al-popup.png" alt="" />
                                </div>
                                <div class="font-santralextrabold absolute top-0 right-0 cursor-pointer px-1 text-base bg-white rounded-lg" @click="closeRentAndBuyModal">X</div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>

    </main>
</template>
