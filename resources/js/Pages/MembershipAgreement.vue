<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";

export default {
    components: {
        Link,
        Head
    },
    data() {
        return {
            blocks: [
                {
                    title: "Sorumluluklar",
                    content:
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4\">\n" +
                        "                   <li class='font-santralregular'><PERSON><PERSON><PERSON><PERSON>, fiyatlar ve sunulan ürün ve hizmetler üzerinde değişiklik yapma hakkını her zaman saklı tutar.</li>\n" +
                        "                   <li class='font-santralregular'><PERSON><PERSON><PERSON><PERSON>, üyenin sözleşme konusu hizmet<PERSON>den, teknik arızalar dışında yararlandırılacağını kabul ve taahhüt eder.</li>\n" +
                        "                   <li class='font-santralregular'><PERSON><PERSON><PERSON><PERSON><PERSON>, sitenin kullanımında tersine mühendislik yapmayacağını ya da bunların kaynak kodunu bulmak veya elde etmek amacına yönelik herhangi bir başka işlemde bulunmayacağını aksi halde ve 3. Kişiler nezdinde doğacak zararlardan sorumlu olacağını, hakkında hukuki ve cezai işlem yapılacağını peşinen kabul eder.</li>\n" +
                        "                   <li class='font-santralregular'>\n" +
                        "                        Kullanıcı, site içindeki faaliyetlerinde, sitenin herhangi bir bölümünde veya iletişimlerinde genel ahlaka ve adaba aykırı, kanuna aykırı, 3. Kişilerin haklarını zedeleyen, yanıltıcı, saldırgan, müstehcen, pornografik, kişilik haklarını zedeleyen, telif haklarına aykırı, yasa dışı faaliyetleri teşvik eden içerikler\n" +
                        "                        üretmeyeceğini, paylaşmayacağını kabul eder. Aksi halde oluşacak zarardan tamamen kendisi sorumludur ve bu durumda ‘Site’ yetkilileri, bu tür hesapları askıya alabilir, sona erdirebilir, yasal süreç başlatma hakkını saklı tutar. Bu sebeple yargı mercilerinden etkinlik veya kullanıcı hesapları ile ilgili bilgi talepleri\n" +
                        "                        gelirse paylaşma hakkını saklı tutar.\n" +
                        "                    </li>\n" +
                        "                   <li class='font-santralregular'>Sitenin üyelerinin birbirleri veya üçüncü şahıslarla olan ilişkileri kendi sorumluluğundadır.</li>\n" +
                        "                </ul>",
                    showBlock: true
                },
                {
                    title: "Fikri Mülkiyet Hakları",
                    content:
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4\">\n" +
                        "                   <li class='font-santralregular'>İşbu sitede yer alan unvan, işletme adı, marka, patent, logo, tasarım, bilgi ve yöntem gibi tescilli veya tescilsiz tüm fikri mülkiyet hakları site işleteni ve sahibi Kiralabunu’ya veya belirtilen ilgilisine ait olup, ulusal ve uluslararası hukukun koruması altındadır. İşbu sitenin ziyaret edilmesi veya bu sitedeki hizmetlerden yararlanılması söz konusu fikri mülkiyet hakları konusunda hiçbir hak vermez.</li>\n" +
                        "                   <li class='font-santralregular'>Sitede yer alan bilgiler hiçbir şekilde çoğaltılamaz, yayınlanamaz, kopyalanamaz, sunulamaz ve/veya aktarılamaz. Site’nin bütünü veya bir kısmı diğer bir internet sitesinde izinsiz olarak kullanılamaz. </li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: "Gizli Bilgi",
                    content:
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4\">\n" +
                        "                   <li class='font-santralregular'>Kiralabunu, site üzerinden kullanıcıların ilettiği kişisel bilgileri 3. Kişilere açıklamayacaktır. Bu kişisel bilgiler; kişi adı-soyadı, adresi, telefon numarası, cep telefonu, e-posta adresi gibi kullanıcıyı tanımlamaya yönelik her türlü diğer bilgiyi içermekte olup, kısaca ‘Gizli Bilgiler’ olarak anılacaktır.</li>\n" +
                        "                   <li class='font-santralregular'>Kullanıcı, sadece tanıtım, reklam, kampanya, promosyon, duyuru vb. pazarlama faaliyetleri kapsamında kullanılması ile sınırlı olmak üzere, Site’nin sahibi olan Kiralabunu’nun kendisine ait iletişim, portföy durumu ve demografik bilgilerini iştirakleri ya da bağlı bulunduğu grup şirketleri ile paylaşmasına muvafakat ettiğini kabul ve beyan eder. Bu kişisel bilgiler Kiralabunu bünyesinde müşteri profili belirlemek, müşteri profiline uygun promosyon ve kampanyalar sunmak ve istatistiksel çalışmalar yapmak amacıyla kullanılabilecektir.</li>\n" +
                        "                   <li class='font-santralregular'>Gizli Bilgiler, ancak resmi makamlarca usulü dairesinde bu bilgilerin talep edilmesi halinde ve yürürlükteki emredici mevzuat hükümleri gereğince resmi makamlara açıklama yapılmasının zorunlu olduğu durumlarda resmi makamlara açıklanabilecektir.</li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: "Garanti Vermeme",
                    content:
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4\">\n" +
                        "                   <li class='font-santralregular'>Kiralabunu tarafından sunulan hizmetler “olduğu gibi” ve “mümkün olduğu” temelde sunulmakta ve pazarlanabilirlik, belirli bir amaca uygunluk veya ihlal etmeme konusunda tüm zımni garantiler de dâhil olmak üzere hizmetler veya uygulama ile ilgili olarak (bunlarda yer alan tüm bilgiler dâhil) sarih veya zımni, kanuni veya başka bir nitelikte hiçbir garanti kapsamında bulunmamaktadır. </li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: " Kayıt ve Güvenlik ",
                    content:
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4\">\n" +
                        "                   <li class='font-santralregular'> Kullanıcı, doğru, eksiksiz ve güncel kayıt bilgilerini vermek zorundadır. Aksi halde bu Sözleşme ihlal edilmiş sayılacak ve Kullanıcı bilgilendirilmeksizin hesap kapatılabilecektir. </li>\n" +
                        "                   <li class='font-santralregular'> Kullanıcı, site ve üçüncü taraf sitelerdeki şifre ve hesap güvenliğinden kendisi sorumludur. Aksi halde oluşacak veri kayıplarından ve güvenlik ihlallerinden veya donanım ve cihazların zarar görmesinden Kiralabunu sorumlu tutulamaz.</li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: "Mücbir Sebep",
                    content:
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4\">\n" +
                        "                   <li class='font-santralregular'>Tarafların kontrolünde olmayan; tabii afetler, yangın, patlamalar, iç savaşlar, savaşlar, ayaklanmalar, halk hareketleri, seferberlik ilanı, grev, lokavt ve salgın hastalıklar, altyapı ve internet arızaları, elektrik kesintisi gibi sebeplerden (aşağıda birlikte “Mücbir Sebep” olarak anılacaktır) dolayı sözleşmeden doğan yükümlülükler taraflarca ifa edilemez hale gelirse, taraflar bundan sorumlu değildir. Bu sürede Taraflar’ın işbu Sözleşme’den doğan hak ve yükümlülükleri askıya alınır. </li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: " Sözleşmenin Bütünlüğü ve Uygulanabilirlik",
                    content:
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4\">\n" +
                        "                   <li class='font-santralregular'>İşbu sözleşme şartlarından biri, kısmen veya tamamen geçersiz hale gelirse, sözleşmenin geri kalanı geçerliliğini korumaya devam eder. </li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: "Sözleşmede Yapılacak Değişiklikler ",
                    content:
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4\">\n" +
                        "                   <li class='font-santralregular'>Kiralabunu, dilediği zaman sitede sunulan hizmetleri ve işbu sözleşme şartlarını kısmen veya tamamen değiştirebilir. Değişiklikler sitede yayınlandığı tarihten itibaren geçerli olacaktır. Değişiklikleri takip etmek Kullanıcı’nın sorumluluğundadır. Kullanıcı, sunulan hizmetlerden yararlanmaya devam etmekle bu değişiklikleri de kabul etmiş sayılır.</li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: "Tebligat",
                    content:
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4\">\n" +
                        "                   <li class='font-santralregular'>İşbu Sözleşme ile ilgili taraflara gönderilecek olan tüm bildirimler, Kiralabunu’nun bilinen e-posta adresi " +
                        "(<a href='mailto:<EMAIL>'><EMAIL></a>) ve kullanıcının üyelik formunda belirttiği e-posta adresi vasıtasıyla yapılacaktır. Kullanıcı, üye olurken belirttiği adresin geçerli tebligat adresi olduğunu, değişmesi durumunda beş (5) gün içinde yazılı olarak diğer tarafa bildireceğini, aksi halde bu adrese yapılacak tebligatların geçerli sayılacağını kabul eder.</li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: "Delil Sözleşmesi",
                    content:
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4\">\n" +
                        "                   <li class='font-santralregular'>Taraflar arasında işbu sözleşme ile ilgili işlemler için çıkabilecek her türlü uyuşmazlıklarda Taraflar’ın defter, kayıt ve belgeleri ile ve bilgisayar kayıtları ve faks kayıtları 6100 sayılı Hukuk Muhakemeleri Kanunu uyarınca delil olarak kabul edilecek olup, kullanıcı bu kayıtlara itiraz etmeyeceğini kabul eder.</li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: "Uyuşmazlıkların Çözümü",
                    content:
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4\">\n" +
                        "                   <li class='font-santralregular'>İşbu Sözleşme’nin uygulanmasından veya yorumlanmasından doğacak her türlü uyuşmazlığın çözümünde İstanbul (Merkez) Adliyesi Mahkemeleri ve İcra Daireleri yetkilidir.</li>\n" +
                        "                </ul>",
                    showBlock: false
                }
            ]
        };
    },
    methods: {
        toogleBlock(block) {
            block.showBlock = !block.showBlock;
        }
    },
    layout: Layout
};
</script>

<template>

    <Head title="Üyelik Sözleşmesi" />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <!--        <section class="flex justify-center items-center flex-col w-full mb-12">-->
        <!--            <div class="flex justify-center items-center mb-10">-->
        <!--                <div class="flex justify-center items-center bg-kbgreen rounded-full w-28 h-28">-->
        <!--                    <svg xmlns="http://www.w3.org/2000/svg" width="43.015" height="78.955" viewBox="0 0 43.015 78.955">-->
        <!--                        <g id="Group_4141" data-name="Group 4141" transform="translate(0 0)">-->
        <!--                            <path-->
        <!--                                id="Path_3349"-->
        <!--                                data-name="Path 3349"-->
        <!--                                d="M473.847,415.2H472.2a5.587,5.587,0,0,1-5.581-5.581v-1.387a17.076,17.076,0,0,1,1.934-8.548,44.963,44.963,0,0,1,5.417-7.278,32.075,32.075,0,0,0,3.683-4.439,7.467,7.467,0,0,0,1.163-4.13c0-3.445-2.245-5.121-6.863-5.121a27.868,27.868,0,0,0-7.564,1.135,6.368,6.368,0,0,1-8.116-6.123v-2.807a6.246,6.246,0,0,1,3.685-5.725,36.563,36.563,0,0,1,27-.235,21.4,21.4,0,0,1,9.074,6.877,16.563,16.563,0,0,1,3.255,10.039,16.327,16.327,0,0,1-3.147,10.04,57.393,57.393,0,0,1-8.8,8.966,62.707,62.707,0,0,0-5.642,5.315,9.811,9.811,0,0,0-2.346,4.367,5.566,5.566,0,0,1-5.5,4.636Z"-->
        <!--                                transform="translate(-456.268 -362.491)"-->
        <!--                                fill="#fff"-->
        <!--                            />-->
        <!--                            <path id="Path_3350" data-name="Path 3350"-->
        <!--                                  d="M480.951,518.1h-.218a10.344,10.344,0,0,1,0-20.688h.218a10.344,10.344,0,0,1,0,20.688Z"-->
        <!--                                  transform="translate(-464.291 -439.141)" fill="#fff" />-->
        <!--                        </g>-->
        <!--                    </svg>-->
        <!--                </div>-->
        <!--            </div>-->
        <!--            <button class="bg-black text-white rounded-full py-2 px-4 self-center text-2xl">Sıkça Sorulan Sorular-->
        <!--            </button>-->
        <!--        </section>-->
        <section class="mt-6 flex flex-col items-center justify-center mb-20">
            <div class="text-2xl font-semibold text-center mb-7">Üyelik Sözleşmesi</div>
            <div class="flex justify-between w-full lg:w-full py-4 rounded-xl mb-4">
                <div>
                    <p class="text-sm text-justify font-santralregular">
                        Bu alışveriş sitesini kullanan ve alışveriş yapan müşterilerimiz aşağıdaki şartları kabul etmiş
                        varsayılmaktadır: <br />
                        <br />
                        Sitemizdeki web sayfaları ve ona bağlı tüm sayfalar (“site”) <a href="https://www.kiralabunu.com">www.kiralabunu.com</a> adresindeki Kiralabunu Elektronik
                        Pazarlama ve Ticaret Anonim Şirketi malıdır ve onun tarafından işletilir. Sizler (“Kullanıcı”)
                        sitede sunulan tüm hizmetleri kullanırken aşağıdaki şartlara tabi olduğunuzu, sitedeki hizmetten
                        yararlanmakla ve kullanmaya devam etmekle; sözleşme imzalama hakkına, yetkisine ve hukuki
                        ehliyetine sahip ve 18 yaşın üzerinde olduğunuzu, bu sözleşmeyi okuduğunuzu, anladığınızı ve
                        sözleşmede yazan şartlarla bağlı olduğunuzu kabul etmiş sayılırsınız.
                        <br />
                        <br />
                        İşbu sözleşme taraflara sözleşme konusu site ile ilgili hak ve yükümlülükler yükler ve taraflar
                        işbu sözleşmeyi kabul ettiklerinde bahsi geçen hak ve yükümlülükleri eksiksiz, doğru, zamanında,
                        işbu sözleşmede talep edilen şartlar dâhilinde yerine getireceklerini beyan ederler.
                    </p>
                </div>
            </div>

            <!--            <div-->
            <!--                :class="[block.showBlock ? 'bg-kb-mid-green py-4 rounded-xl mb-4 w-full px-5' : 'flex justify-between items-center w-full bg-kb-mid-grey py-4 px-4 rounded-full mb-4']"-->
            <!--                v-for="(block, index) in blocks" :key="index">-->
            <!--                <div class="flex justify-between w-full" :class="[!block.showBlock ? '' : '']">-->
            <!--                    <div>-->
            <!--                        <p @click="toogleBlock(block)"-->
            <!--                           class="text-base ts:text-lg font-semibold text-left cursor-pointer">{{ block.title }}</p>-->
            <!--                    </div>-->
            <!--                    <div class="rounded-full" :class="[block.showBlock ? 'bg-kbgreen rounded-full ' : 'bg-black ']"-->
            <!--                         @click="toogleBlock(block)">-->
            <!--                        <button type="button" class="flex justify-center items-center w-8 h-8">-->
            <!--                            <svg xmlns="http://www.w3.org/2000/svg" width="23.872" height="21.712"-->
            <!--                                 viewBox="0 0 23.872 21.712">-->
            <!--                                <g id="Group_119" data-name="Group 119" transform="translate(0)">-->
            <!--                                    <path id="Path_19" data-name="Path 19"-->
            <!--                                          d="M1.6,21.712A1.528,1.528,0,0,1,0,20.261V1.451A1.528,1.528,0,0,1,1.6,0a1.528,1.528,0,0,1,1.6,1.451v18.81A1.528,1.528,0,0,1,1.6,21.712Z"-->
            <!--                                          transform="translate(10.341 0)" fill="#fff" />-->
            <!--                                    <path id="Path_20" data-name="Path 20"-->
            <!--                                          d="M1.451,23.872A1.528,1.528,0,0,1,0,22.277V1.6A1.528,1.528,0,0,1,1.451,0,1.528,1.528,0,0,1,2.9,1.6V22.277A1.528,1.528,0,0,1,1.451,23.872Z"-->
            <!--                                          transform="translate(23.872 9.405) rotate(90)" fill="#fff" />-->
            <!--                                </g>-->
            <!--                            </svg>-->
            <!--                        </button>-->
            <!--                    </div>-->
            <!--                </div>-->
            <!--                <span v-html="block.content" v-if="block.showBlock"></span>-->
            <!--            </div>-->
            <div :class="[block.showBlock ? ' py-4 rounded-xl mb-4 w-full px-4' : 'flex justify-between items-center w-full py-4 px-4 rounded-full mb-4']" v-for="(block, index) in blocks" :key="index">
                <div class="flex justify-between w-full" :class="[!block.showBlock ? '' : '']">
                    <div>
                        <p @click="toogleBlock(block)" class="text-base font-santralextrabold ts:text-xl text-left cursor-pointer">
                            {{ block.title }}</p>
                    </div>
                    <div class="rounded-full w-8 h-8 bg-white " :class="[block.showBlock ? ' rounded-full ' : ' ']" @click="toogleBlock(block)">
                        <button type="button" class="flex justify-center items-center w-8 h-8" v-if="!block.showBlock">
                            <svg xmlns="http://www.w3.org/2000/svg" width="23.872" height="21.712" viewBox="0 0 23.872 21.712">
                                <g id="Group_119" data-name="Group 119" transform="translate(0)">
                                    <path id="Path_19" data-name="Path 19" d="M1.6,21.712A1.528,1.528,0,0,1,0,20.261V1.451A1.528,1.528,0,0,1,1.6,0a1.528,1.528,0,0,1,1.6,1.451v18.81A1.528,1.528,0,0,1,1.6,21.712Z" transform="translate(10.341 0)"
                                        fill="#000000" />
                                    <path id="Path_20" data-name="Path 20" d="M1.451,23.872A1.528,1.528,0,0,1,0,22.277V1.6A1.528,1.528,0,0,1,1.451,0,1.528,1.528,0,0,1,2.9,1.6V22.277A1.528,1.528,0,0,1,1.451,23.872Z"
                                        transform="translate(23.872 9.405) rotate(90)" fill="#000000" />
                                </g>
                            </svg>
                        </button>
                        <button type="button" class="flex justify-center items-center w-8 h-8" v-if="block.showBlock">
                            <svg id="Group_119" data-name="Group 119" xmlns="http://www.w3.org/2000/svg" width="22.332" height="2.985" viewBox="0 0 22.332 2.985">
                                <path id="Path_19" data-name="Path 19" d="M1.492,21.712A1.472,1.472,0,0,1,0,20.261V1.451A1.472,1.472,0,0,1,1.492,0,1.472,1.472,0,0,1,2.985,1.451v18.81A1.472,1.472,0,0,1,1.492,21.712Z"
                                    transform="translate(22.022) rotate(90)" fill="#000" />
                                <path id="Path_20" data-name="Path 20" d="M1.451,22.332A1.472,1.472,0,0,1,0,20.84V1.492A1.472,1.472,0,0,1,1.451,0,1.472,1.472,0,0,1,2.9,1.492V20.84A1.472,1.472,0,0,1,1.451,22.332Z"
                                    transform="translate(22.332 0.042) rotate(90)" fill="#000" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="mr-5" v-html="block.content" v-if="block.showBlock"></div>
            </div>

        </section>
    </main>
</template>
