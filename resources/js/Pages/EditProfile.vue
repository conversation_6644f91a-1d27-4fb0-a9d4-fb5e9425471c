<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import Datepicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import { vMaska } from "maska";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Datepicker,
    },
    directives: { maska: vMaska },
    props: {
        auth: Object,
        errors: { type: Object, default: false },
        user: { type: Object },
        order_count: Number,
    },
    data() {
        return {
            form: this.$inertia.form({
                // first_name: JSON.parse(window.localStorage.getItem("user")).first_name,
                // last_name: JSON.parse(window.localStorage.getItem("user")).last_name,
                // email: JSON.parse(window.localStorage.getItem("user")).email,
                // gender: JSON.parse(window.localStorage.getItem("user")).gender,
                // tckn: JSON.parse(window.localStorage.getItem("user")).tckn,
                // date_of_birth: JSON.parse(window.localStorage.getItem("user")).birth_date,
                first_name: this.auth?.user?.first_name,
                last_name: this.auth?.user?.last_name,
                email: this.auth?.user?.email,
                gender: this.auth?.user?.gender,
                tckn: this.auth?.user?.tckn,
                date_of_birth: this.auth?.user?.birth_date,
            }),
            authObject: this.auth,
        };
    },
    methods: {
        submit() {
            this.form.post("/profili-duzenle");
        },
        preventNumericInput($event) {
            //console.log($event.keyCode); will display the keyCode value
            //console.log($event.key); will show the key value

            var charCode = $event.keyCode ? $event.keyCode : $event.which;
            if (
                (charCode <= 93 && charCode >= 65) ||
                (charCode <= 122 && charCode >= 97) ||
                charCode == 32 ||
                charCode == 8 ||
                charCode == 350 ||
                charCode == 351 ||
                charCode == 304 ||
                charCode == 286 ||
                charCode == 287 ||
                charCode == 231 ||
                charCode == 199 ||
                charCode == 305 ||
                charCode == 214 ||
                charCode == 246 ||
                charCode == 220 ||
                charCode == 252
            ) {
                if (charCode != 33 || charCode != 43) {
                    return true;
                } else {
                    $event.preventDefault();
                }
            } else {
                $event.preventDefault();
            }
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.authObject = this.auth;
        });
    },
    layout: Layout,
};
</script>

<template>

    <Head title="Profil Düzenle" />
    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <user-menu :order_count="order_count" :active="`EditProfile`" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 lg:px-4 pt-2">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7">Profil Düzenle</h3>
                </div>
                <div v-if="!authObject.user?.email_verified_at && false" class="relative flex justify-center lg:justify-between w-full bg-acordion-light-green border-2 border-kb-light-grey py-5 px-5 rounded-2lg">
                    <div class="bg-acordion-green rounded-full w-8 md:w-12 h-8 md:h-12 absolute md:relative -top-5 md:top-0">
                        <div class="flex justify-center items-center w-8 md:w-12 h-8 md:h-12">
                            <svg id="exclamation" class="w-6 md:w-10 h-6 md:h-10" viewBox="0 0 8.558 34.247">
                                <path id="Path_170" data-name="Path 170" d="M15.755,38.96a4.279,4.279,0,1,1,4.279,4.279A4.279,4.279,0,0,1,15.755,38.96Zm.418-25.694a3.872,3.872,0,1,1,7.7,0l-1.5,15.006a2.362,2.362,0,0,1-4.707,0Z"
                                    transform="translate(-15.755 -8.992)" fill="#fff"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="md:pl-7">
                        <p class="text-sm md:text-base text-black leading-tight">
                            Hesabının güvenliğini sağlamaya yardımcı olmak için lütfen e-posta adresini doğrula. <span class="font-bold">{{ authObject.user?.email }}</span> adresine bir doğrulama e-postası gönder veya e-posta adresini güncelle.
                        </p>
                        <button class="mt-4 bg-black text-white rounded-full py-1.5 lg:py-2 px-4 self-center text-base lg:text-lg font-bold">Doğrulama Kodu Gönder</button>
                    </div>
                </div>

                <form class="flex flex-col justify-center items-center w-full mt-10" @submit.prevent="submit">
                    <div class="w-full lg:w-6/12 text-center">
                        <div class="relative group">
                            <input id="name" v-model="form.first_name" @keypress="preventNumericInput"
                                class="peer w-full mb-7 rounded-2lg border-2 border-bordergray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required
                                type="text" placeholder="" name="AdSoyad" autofocus />
                            <label for="name"
                                class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Ad*</label>
                        </div>
                        <div class="relative group">
                            <input id="surname" v-model="form.last_name" @keypress="preventNumericInput"
                                class="peer w-full mb-7 rounded-2lg border-2 border-bordergray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required
                                type="text" placeholder="" name="Soyad" />
                            <label for="name"
                                class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Soyad*</label>
                        </div>
                        <!--                        <input class="w-full mb-7 rounded-2lg border-2 border-bordergray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required type="text" placeholder="Görünen Adın*" name="Görünen Adın" >-->
                        <div class="relative group">
                            <input id="email" v-model="form.email"
                                class="peer w-full mb-7 rounded-2lg border-2 border-bordergray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider" required
                                type="email" placeholder="" name="Eposta" />
                            <label for="email"
                                class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">E-Posta*</label>
                        </div>
                        <div class="relative group">
                            <input v-maska data-maska="###########" id="tckn"
                                class="disabled:text-textgray peer w-full mb-5 rounded-2lg hover:border-acordion-green group-hover:placeholder-white border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                type="text" placeholder="" name="tckn" v-model="form.tckn" :readonly="auth.user?.tckn_verified_at" :disabled="auth.user?.tckn_verified_at" />
                            <label for="tckn" class="transform transition-all absolute top-0 left-4 px-1 pl-0 text-2xs -translate-y-full text-black">TCKN</label>
                        </div>
                        <div class="relative group">
                            <!--                            <input-->
                            <!--                                id="date_of_birth"-->
                            <!--                                class="disabled:text-textgray peer w-full mb-5 rounded-2lg hover:border-acordion-green group-hover:placeholder-white border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"-->
                            <!--                                required-->
                            <!--                                type="date"-->
                            <!--                                placeholder=""-->
                            <!--                                name="date_of_birth"-->
                            <!--                                max="2005-01-01"-->
                            <!--                                min="1950-01-01"-->
                            <!--                                :disabled="auth.user?.tckn_verified_at"-->
                            <!--                                :readonly="auth.user?.tckn_verified_at"-->
                            <!--                                v-model="form.date_of_birth"-->
                            <!--                                onkeypress="return false"-->
                            <!--                                onfocus="this.value=''"-->
                            <!--  :readonly="auth.user?.tckn_verified_at"
                                :disabled="auth.user?.tckn_verified_at"                          />-->
                            <Datepicker required v-model="form.date_of_birth" locale="tr" :enable-time-picker="false" :format="`dd/MM/yyyy`" auto-apply :max-date="new Date().setFullYear(new Date().getFullYear() - 18)"
                                class="peer w-full mb-5 group-hover:placeholder-white focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"></Datepicker>

                            <label for="date_of_birth" class="transform transition-all absolute top-0 left-4 px-1 pl-0 text-2xs -translate-y-full text-black">Doğum Tarihi*</label>
                        </div>
                        <div class="flex justify-around w-full bg-acordion-light-green py-5 px-3 ts:px-5 rounded-2lg">
                            <p class="text-lg text-black font-bold">Cinsiyet :</p>
                            <div class="flex justify-center items-center">
                                <input class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" id="kadin" name="cinsiyet" value="kadin" v-model="form.gender" />
                                <label class="pl-3 text-lg text-black font-bold" for="kadin">Kadın</label>
                                <input class="ml-5 ts:ml-9 border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" id="erkek" name="cinsiyet" value="Erkek" v-model="form.gender" />
                                <label class="pl-3 text-lg text-black font-bold" for="erkek">Erkek</label>
                            </div>
                        </div>

                        <div class="flex mt-4 self-center justify-center" v-if="errors?.errors">
                            <div class="text-xs mx-auto text-kbred" v-for="(error, index) in errors.errors">{{ error[0] }}</div>
                        </div>

                        <div v-else>
                            <div class="flex mt-4 self-center justify-center" v-if="errors?.message">
                                <div class="text-xs mx-auto text-kbred">
                                    {{ errors.message }}
                                </div>
                            </div>
                        </div>

                        <div class="w-full ts:flex ts:justify-end">
                            <button class="bg-black mb-7 mt-5 text-white text-lg rounded-full py-2 px-4 self-center font-bold w-full ts:w-24" type="submit" :disabled="form.processing">Kaydet</button>
                        </div>
                    </div>
                </form>
            </div>
        </section>
    </main>
</template>
<style>
.dp__input {
    font-family: kiralabunufont;
    border-radius: 1rem;
    border: 2px solid rgb(235, 235, 235);
}

.dp__input:hover {
    border-color: rgb(21, 165, 89) !important;
}

.dp__disabled {
    background-color: white;
    color: rgb(155, 155, 155);
}
</style>
