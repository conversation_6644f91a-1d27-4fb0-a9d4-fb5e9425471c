<script>
import { ref, onMounted } from "vue";
import { Splide, SplideSlide } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";

import Layout from "@/Pages/Shared/LayoutDemo.vue";
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";

export default {
    layout: Layout,
    name: "Volta RS7 Scooter",
    components: { Splide, SplideSlide, Head },
    setup() {
        const mainSplide = ref(null);
        const thumbSplide = ref(null);

        const mainOptions = {
            type: "loop",
            height: "400px",
            pagination: false,
            arrows: false,
            breakpoints: {
                600: {
                    height: "280px"
                }
            }
        };

        const thumbOptions = {
            type: "slide",
            fixedWidth: 100,
            fixedHeight: 100,
            height: "400px",
            gap: "1rem",
            direction: "ttb",
            pagination: false,
            cover: true,
            perPage: 6,
            focus: "center",
            isNavigation: true,
            updateOnMove: true,
            loop: true,
            breakpoints: {
                600: {
                    fixedWidth: "100%",
                    fixedHeight: "100%",
                    height: "280px"
                }
            }
        };

        onMounted(() => {
            mainSplide.value.sync(thumbSplide.value.splide);
        });

        return {
            mainSplide,
            thumbSplide,
            mainOptions,
            thumbOptions
        };
    }
};
</script>
<style>
button.splide__arrow.splide__arrow--prev {
    transform: rotate(270deg);
    top: 0 !important;
}

button.splide__arrow.splide__arrow--next {
    bottom: 0 !important;
}
</style>

<template>

    <Head :title="'Volta RS7 Scooter'">
    </Head>

    <main class="my-3 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-4 ts:mt-1 2xl:mt-4 mx-auto scroll-smooth">
        <section class="mt-3 ts:mt-1 2xl:mt-3 flex flex-col xl:flex-row items-center">
            <div class="flex flex-col justify-between w-full xl:w-1/2">
                <div class="w-full flex flex-row-reverse items-start">
                    <div class="flex justify-center mr-2 px-1 w-[calc(100%-60px)] md:w-[calc(100%-110px)]">
                        <Splide :options="mainOptions" ref="mainSplide">
                            <SplideSlide>
                                <picture>
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-1.webp" type="image/webp">
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-1.png" type="image/png">
                                    <img src="../../../../images/kiralamotor/volta-rs7-1.png" alt="Alt Text!">
                                </picture>
                            </SplideSlide>
                            <SplideSlide>
                                <picture>
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-2.webp" type="image/webp">
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-2.png" type="image/png">
                                    <img src="../../../../images/kiralamotor/volta-rs7-2.png" alt="Alt Text!">
                                </picture>
                            </SplideSlide>
                            <SplideSlide>
                                <picture>
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-3.webp" type="image/webp">
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-3.png" type="image/png">
                                    <img src="../../../../images/kiralamotor/volta-rs7-3.png" alt="Alt Text!">
                                </picture>
                            </SplideSlide>
                            <SplideSlide>
                                <picture>
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-4.webp" type="image/webp">
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-4.png" type="image/png">
                                    <img src="../../../../images/kiralamotor/volta-rs7-4.png" alt="Alt Text!">
                                </picture>
                            </SplideSlide>
                            <SplideSlide>
                                <picture>
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-5.webp" type="image/webp">
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-5.png" type="image/png">
                                    <img src="../../../../images/kiralamotor/volta-rs7-5.png" alt="Alt Text!">
                                </picture>
                            </SplideSlide>
                            <SplideSlide>
                                <picture>
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-6.webp" type="image/webp">
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-6.png" type="image/png">
                                    <img src="../../../../images/kiralamotor/volta-rs7-6.png" alt="Alt Text!">
                                </picture>
                            </SplideSlide>
                            <SplideSlide>
                                <picture>
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-7.webp" type="image/webp">
                                    <source srcset="../../../../images/kiralamotor/volta-rs7-7.png" type="image/png">
                                    <img src="../../../../images/kiralamotor/volta-rs7-7.png" alt="Alt Text!">
                                </picture>
                            </SplideSlide>

                        </Splide>
                    </div>
                    <Splide :options="thumbOptions" ref="thumbSplide" class="w-[60px] mts:w-[90px] md:w-[110px] max-h-[280px] mts:max-h-[400px] h-full">
                        <SplideSlide>
                            <picture>
                                <source srcset="../../../../images/kiralamotor/volta-rs7-1.webp" type="image/webp">
                                <source srcset="../../../../images/kiralamotor/volta-rs7-1.png" type="image/png">
                                <img src="../../../../images/kiralamotor/volta-rs7-1.png" alt="Alt Text!">
                            </picture>
                        </SplideSlide>
                        <SplideSlide>
                            <picture>
                                <source srcset="../../../../images/kiralamotor/volta-rs7-2.webp" type="image/webp">
                                <source srcset="../../../../images/kiralamotor/volta-rs7-2.png" type="image/png">
                                <img src="../../../../images/kiralamotor/volta-rs7-2.png" alt="Alt Text!">
                            </picture>
                        </SplideSlide>
                        <SplideSlide>
                            <picture>
                                <source srcset="../../../../images/kiralamotor/volta-rs7-3.webp" type="image/webp">
                                <source srcset="../../../../images/kiralamotor/volta-rs7-3.png" type="image/png">
                                <img src="../../../../images/kiralamotor/volta-rs7-3.png" alt="Alt Text!">
                            </picture>
                        </SplideSlide>
                        <SplideSlide>
                            <picture>
                                <source srcset="../../../../images/kiralamotor/volta-rs7-4.webp" type="image/webp">
                                <source srcset="../../../../images/kiralamotor/volta-rs7-4.png" type="image/png">
                                <img src="../../../../images/kiralamotor/volta-rs7-4.png" alt="Alt Text!">
                            </picture>
                        </SplideSlide>
                        <SplideSlide>
                            <picture>
                                <source srcset="../../../../images/kiralamotor/volta-rs7-5.webp" type="image/webp">
                                <source srcset="../../../../images/kiralamotor/volta-rs7-5.png" type="image/png">
                                <img src="../../../../images/kiralamotor/volta-rs7-5.png" alt="Alt Text!">
                            </picture>
                        </SplideSlide>
                        <SplideSlide>
                            <picture>
                                <source srcset="../../../../images/kiralamotor/volta-rs7-6.webp" type="image/webp">
                                <source srcset="../../../../images/kiralamotor/volta-rs7-6.png" type="image/png">
                                <img src="../../../../images/kiralamotor/volta-rs7-6.png" alt="Alt Text!">
                            </picture>
                        </SplideSlide>
                        <SplideSlide>
                            <picture>
                                <source srcset="../../../../images/kiralamotor/volta-rs7-7.webp" type="image/webp">
                                <source srcset="../../../../images/kiralamotor/volta-rs7-7.png" type="image/png">
                                <img src="../../../../images/kiralamotor/volta-rs7-7.png" alt="Alt Text!">
                            </picture>
                        </SplideSlide>
                    </Splide>
                </div>
            </div>
            <div class="flex flex-col xl:w-1/2 mt-6 md:mt-2 xl:mt-0">
                <div class="bg-kb-mid-grey md:bg-white md:shadow-productshadow w-full p-4 ts:pt-1 2xl:pt-4 rounded-md">
                    <div class="w-full flex justify-between">
                        <svg width="90.333" height="15.021" viewBox="0 0 90.333 15.021">
                            <g transform="translate(-930.094 0.5)">
                                <path
                                    d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                    data-name="star-solid" transform="translate( 928.348 -2.433)" fill="#70d44b" stroke="" stroke-width=""></path>
                                <path
                                    d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                    data-name="star-solid" transform="translate( 947.007 -2.433)" fill="#70d44b" stroke="" stroke-width=""></path>
                                <path
                                    d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                    data-name="star-solid" transform="translate( 965.666 -2.433)" fill="#70d44b" stroke="" stroke-width=""></path>
                                <path
                                    d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                    data-name="star-solid" transform="translate( 984.325 -2.433)" fill="#70d44b" stroke="" stroke-width=""></path>
                                <path
                                    d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                    data-name="star-solid" transform="translate( 1002.984 -2.433)" fill="#70d44b" stroke="" stroke-width=""></path>
                            </g>
                        </svg>
                        <div class="flex">
                            <svg width="20" height="20" viewBox="0 0 28.164 23.242" class="cursor-pointer">
                                <path id="_23" data-name="23"
                                    d="M16.1,27.012a1.412,1.412,0,0,1-.833-.268L4.8,19.034a.734.734,0,0,1-.155-.141A8.833,8.833,0,0,1,16.1,5.507a8.839,8.839,0,0,1,11.522.861h0a8.853,8.853,0,0,1,0,12.525.735.735,0,0,1-.155.141L16.932,26.687A1.412,1.412,0,0,1,16.1,27.012ZM6.5,16.8l9.6,7.06,9.6-7.06a6.043,6.043,0,0,0-.071-8.472h0a6.029,6.029,0,0,0-8.472,0,1.412,1.412,0,0,1-2.005,0A6.056,6.056,0,0,0,6.5,16.8Z"
                                    transform="translate(-2.052 -3.77)" fill="#231f20" opacity="0.5"></path>
                                <path id="Path_50" data-name="Path 50" d="M2605.027,1375.483l3.363-2.4,5.89.491,4.247,2.575v4.546l-.806,4.555-4.806,4.015-7.888,2.158-4.967-3.347-4.8-6.758-.3-4.122,1.56-2.432,4.8-1.19Z"
                                    transform="translate(-2593 -1371.54)" fill="#231f20" opacity="0"></path>
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h1 class="font-bold text-xl mt-1 ts:mt-0 2xl:mt-1">Volta Rs7 125 Scooter</h1>
                    </div>
                    <div class=""><span class="text-sm text-kbgreen font-semibold cursor-pointer">Volta</span></div>
                    <div class="text-sm lg:text-xs mt-1 ts:mt-0 2xl:mt-1 text-kbgray">80 km/h, 5.5 kW, 7.9 Nm</div>
                    <div class="flex mt-2 ts:mt-1 2xl:mt-2 items-end"><span class="text-2xl 2xl:text-3xl leading-none mr-1 whitespace-nowrap">6000 TL </span><span
                            class="text-2xs md:text-sm text-black self-center ml-2 md:ml-0 leading-none font-santralregular"> / Aylık ödenecek tutar</span></div>
                </div>
                <div class="bg-kb-mid-grey md:bg-white md:shadow-productshadow w-full mt-0 p-4 rounded-md ts:rounded-none">
                    <!--                    <div class="flex text-base xl:text-xl text-center cursor-pointer text-gray-500 h-14 xl:h-18 ">--><!--                        <div class="border-y-2 w-1/5 p-2 border-l-2 bg-white" :class="[ selectedMonth == 1 ? selectedClass : 'rounded-l-full' ]" @click="selectMonth(1)">1 Ay</div>-->
                    <!--                        <div class="border-y-2 w-1/5 p-2 bg-white" :class="[ selectedMonth == 2 ? selectedClass : '' ]" @click="selectMonth(2)">3 Ay</div>--><!--                        <div class="border-y-2 w-1/5 p-2 bg-white" :class="[ selectedMonth == 3 ? selectedClass : '' ]" @click="selectMonth(3)">6 Ay</div>-->
                    <!--                        <div class="border-y-2 w-1/5 p-2 bg-white" :class="[ selectedMonth == 4 ? selectedClass : '-' ]" @click="selectMonth(4)">12 Ay</div>--><!--                        <div class="border-y-2 border-r-2 w-1/5 p-2 bg-white" :class="[ selectedMonth == 5 ? selectedClass : 'rounded-r-full' ]" @click="selectMonth(5)">18 Ay</div>--><!--                    </div>-->
                    <div class="flex text-sm lg:text-base xl:text-xl text-center cursor-pointer text-gray-500 h-12 md:h-20 ts:h-20 xl:h-20">
                        <ul class="flex border rounded-full bg-white w-full items-center justify-center shadow-xl h-8 md:h-12 md:max-w-[550px] mx-auto">
                            <li class="whitespace-nowrap w-16 md:w-20 md:text-lg hover:scale-125 transform duration-300 ease-in-out">3 Ay</li>
                            <li class="whitespace-nowrap w-16 md:w-20 md:text-lg hover:scale-125 transform duration-300 ease-in-out">6 Ay</li>
                            <li
                                class="whitespace-nowrap w-16 md:w-20 md:text-lg hover:scale-125 transform duration-300 ease-in-out border-2 h-14 md:h-20 rounded-full border-kbgreen p-2 lg:p-4 items-center justify-center flex bg-white !text-black text-[14px] md:text-[20px] hover:!scale-100">
                                12 Ay</li><!--                            <li class="w-16 md:w-24 md:text-lg" :class="[selectedMonth == 2 ? selectedClass : '']" @click="selectMonth(2)">3 Ay</li>-->
                            <!--                            <li class="w-16 md:w-24 md:text-lg" :class="[selectedMonth == 3 ? selectedClass : '']" @click="selectMonth(3)">6 Ay</li>--><!--                            <li class="w-16 md:w-24 md:text-lg" :class="[selectedMonth == 4 ? selectedClass : '']" @click="selectMonth(4)">12 Ay</li>--><!--                            <li class="w-16 md:w-24 md:text-lg" :class="[selectedMonth == 5 ? selectedClass : '']" @click="selectMonth(5)">18 Ay</li>-->
                        </ul>
                    </div>
                    <!--                    <div class="flex mt-3 items-end">--><!--                        <span class="font-semibold text-3xl leading-none mr-1"> {{ price }} TL /</span>--><!--                        <span class="text-2xs md:text-base text-black self-center ml-2 md:ml-0 leading-none font-kiralabunuthin">Aylık ödenecek tutar</span>--><!--                    </div>-->
                    <div class="w-full mt-4">
                        <button class="w-full block text-white rounded-full py-3 px-4 self-center font-semibold text-lg leading-none text-center bg-kbgreen disabled:opacity-50 disabled:cursor-not-allowed">Kirala</button>
                    </div>
                </div>
            </div>
        </section>
        <section class="mt-6 flex flex-col lg:flex-row lg:space-x-2">
            <div class="flex-col w-full lg:w-1/2 bg-kb-mid-grey p-3 lg:p-6 py-6 rounded-lg hidden">
                <h2 class="text-3xl ts:text-2xl 2xl:text-3xl font-semibold">Kutu İçeriği</h2>
                <div class="mt-3 grid lg:gap-2 grid-cols-4 mts:grid-cols-5"></div>
            </div>
            <div class="hidden mts:block w-full bg-kb-mid-grey p-2 rounded-lg mt-4 lg:mt-0">
                <div class="ml-4 mt-2 text-3xl ts:text-2xl 2xl:text-3xl font-semibold">Avantajlar</div>
                <ul class="flex flex-wrap mts:flex-nowrap text-xs font-bold mts:space-x-1 lg:space-x-2 text-center mt-1 mr-1 mts:p-5">
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 107.74 78.83">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-2" x="17.71" y="1" width="89.04" height="61.66" rx="14.15" ry="14.15"></rect>
                                                <rect x="18.71" y="15.15" width="86.99" height="14.34"></rect>
                                                <circle class="cls-1" cx="85.86" cy="44.17" r="8.66"></circle>
                                                <circle class="cls-1" cx="74.62" cy="44.17" r="8.66"></circle>
                                            </g>
                                            <g>
                                                <circle class="cls-3" cx="20.12" cy="58.71" r="19.12"></circle>
                                                <polyline class="cls-4" points="10.55 58.71 17.71 68.53 29.75 47.27"></polyline>
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Kart Limitin Sana Kalsın</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 126.16 126.7">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-2tr" x="40.92" y="19.93" width="44.97" height="86.95" rx="5.71" ry="5.71"></rect>
                                                <rect class="cls-1tr" x="56.1" y="23.35" width="14.63" height="3.66" rx="1.83" ry="1.83"></rect>
                                            </g>
                                            <g>
                                                <path
                                                    d="m63.4,126.7c-2.79,0-5.62-.19-8.46-.57-16.77-2.25-31.66-10.89-41.93-24.34S-1.68,71.71.57,54.94C2.82,38.17,11.46,23.28,24.91,13.01,38.36,2.74,55.01-1.67,71.77.57c18.15,2.43,34.36,12.6,44.46,27.9.61.92.35,2.16-.57,2.77-.92.61-2.16.35-2.77-.57-9.46-14.33-24.65-23.86-41.65-26.14-15.71-2.11-31.3,2.03-43.9,11.65C14.74,25.81,6.64,39.76,4.54,55.47c-2.11,15.71,2.03,31.3,11.65,43.9,9.62,12.6,23.57,20.7,39.28,22.8,32.43,4.35,62.36-18.5,66.7-50.94.15-1.1,1.17-1.86,2.25-1.72,1.09.15,1.86,1.15,1.72,2.25-4.26,31.78-31.52,54.94-62.74,54.94Z">
                                                </path>
                                                <polygon points="124.28 56.09 94.42 25.17 113.03 27.91 124.62 13.11 124.28 56.09"></polygon>
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Yenileme Opsiyonu</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 217.86 118.79">
                                    <g id="Layer_1-2">
                                        <g>
                                            <polyline class="cls-5eq"
                                                points="110.17 64.25 159.33 64.25 159.33 9.13 156.46 5.14 159.33 16.92 184.09 16.92 188.93 19.41 215.36 50.87 215.36 90.92 209.27 98.15 199.4 98.15 195.2 88.03 184.37 82.31 172.49 85.32 165.49 96.61 165.29 99.24 110.17 99.24 106.81 89.07 101.96 84.65 93.11 82.18 81.1 87.13 76.06 99.24 62.2 98.85 55.91 94.11 54.29 85.93 54.29 62.21">
                                            </polyline>
                                            <g>
                                                <path class="cls-4eq" d="m31.1,2.5h118.85c5.18,0,9.38,4.2,9.38,9.38v87.36"></path>
                                                <path class="cls-4eq" d="m33.68,48.6h9.83c5.96,0,10.79,4.83,10.79,10.79v29.06c0,5.96,4.83,10.79,10.79,10.79h10.98"></path>
                                                <path class="cls-4eq" d="m159.33,16.92h21.55c4.3,0,8.37,1.93,11.08,5.26l23.41,28.69v37.58c0,5.96-4.83,10.79-10.79,10.79h-5.17"></path>
                                                <polyline class="cls-4eq" points="165.29 99.24 159.33 99.24 110.17 99.24"></polyline>
                                                <line class="cls-2eq" x1="54.29" y1="64.25" x2="159.33" y2="64.25"></line>
                                                <line class="cls-2eq" x1="54.29" y1="78.83" x2="159.33" y2="78.83"></line>
                                                <circle class="cls-1eq" cx="93.11" cy="99.24" r="17.06"></circle>
                                                <circle class="cls-1eq" cx="182.35" cy="99.24" r="17.06"></circle>
                                                <line class="cls-3eq" x1="18.84" y1="16.92" x2="62.2" y2="16.92"></line>
                                                <line class="cls-3eq" y1="32.28" x2="43.36" y2="32.28"></line>
                                                <polyline class="cls-4eq" points="171.1 16.92 171.1 50.87 215.36 50.87"></polyline>
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Bayi Teslimi</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" xmlns="http://www.w3.org/2000/svg" id="Capa_1" enable-background="new 0 0 512 512" viewBox="0 0 512 512">
                                    <g>
                                        <path d="m391 362h-111.213l-38.787 38.787v72.426l38.787 38.787h111.213v-25h45v-30h-45v-40h45v-30h-45z"></path>
                                        <path
                                            d="m415.806 66.194c-42.686-42.686-99.439-66.194-159.806-66.194s-117.12 23.508-159.806 66.194-66.194 99.439-66.194 159.806 23.508 117.121 66.194 159.806c31.875 31.875 71.596 53.05 114.806 61.734v-30.761c-86.457-20.39-151-98.185-151-190.779 0-108.075 87.925-196 196-196s196 87.925 196 196v64h-181v-55h5c35.841 0 65-29.159 65-65v-20h-20c-35.841 0-65 29.159-65 65 0-35.841-29.159-65-65-65h-20v20c0 35.841 29.159 65 65 65h5v85h241v-94c0-60.367-23.508-117.12-66.194-159.806z">
                                        </path>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Sürdürülebilir gelecek</div>
                        </div>
                    </li>
                </ul>
            </div>
        </section>
        <section class="mt-8 ts:mt-5 2xl:mt-8 bg-white" id="commentSection">
            <div class="flex mx-auto flex-col max-w-7xl">
                <div class="flex"><!--Yorum ve açıklama sekmeler-->
                    <div class="relative transition-all cursor-pointer font-bold text-base md:text-lg lg:text-xl mt-2 mx-2 lg:mx-4 self-center text-center w-full md:w-auto"> Ürün Açıklaması
                        <svg class="absolute left-0 -bottom-1 transition-all ease-in-out delay-150 scale-100" height="4" viewBox="0 0 160.999 4" style="width: 100%;">
                            <path id="Path_2973" data-name="Path 2973" d="M-8643-15807.608h161" transform="translate(8642.999 15809.608)" fill="none" stroke="#139a52" stroke-width="4"></path>
                        </svg>
                    </div>
                </div>
                <div class="w-full flex justify-center flex-col items-center transition-height ease-in-out delay-1000 duration-700 max-h-[1000px]">
                    <div class="grid gap-2 xl:grid-cols-2 mt-6 w-full">
                        <div>
                            <ul>
                                <li class="flex text-sm odd:bg-[#139a5212] h-6">
                                    <div class="mr-2"> Motor Hacmi :</div>
                                    <div> 125 cc</div>
                                </li>
                                <li class="flex text-sm odd:bg-[#139a5212] h-6">
                                    <div class="mr-2"> Azami Net Güç :</div>
                                    <div> 5.5 kw</div>
                                </li>
                                <li class="flex text-sm odd:bg-[#139a5212] h-6">
                                    <div class="mr-2"> Maksimum Tork :</div>
                                    <div> 7.9 nm</div>
                                </li>
                                <li class="flex text-sm odd:bg-[#139a5212] h-6">
                                    <div class="mr-2"> Şanzıman Tİpi :</div>
                                    <div> Otomatik Vites (CVT)</div>
                                </li>
                                <li class="flex text-sm odd:bg-[#139a5212] h-6">
                                    <div class="mr-2"> Net Ağırlık :</div>
                                    <div> 96 kg</div>
                                </li>
                                <li class="flex text-sm odd:bg-[#139a5212] h-6">
                                    <div class="mr-2"> Yakıt Tankı Kapasitesi :</div>
                                    <div> 5.5 L</div>
                                </li>
                            </ul>
                        </div>
                        <div>
                            <ul>
                                <li class="flex text-sm odd:bg-[#139a5212] h-6">
                                    <div class="mr-2"> Azami Taşıma Kapasitesi :</div>
                                    <div> 150 kg</div>
                                </li>
                                <li class="flex text-sm odd:bg-[#139a5212] h-6">
                                    <div class="mr-2"> Azami Hız :</div>
                                    <div> 80 kg</div>
                                </li>
                                <li class="flex text-sm odd:bg-[#139a5212] h-6">
                                    <div class="mr-2"> Tekerlekler :</div>
                                    <div> 3.00-10/3.50-10</div>
                                </li>
                                <li class="flex text-sm odd:bg-[#139a5212] h-6">
                                    <div class="mr-2"> Uzunluk :</div>
                                    <div> 168 cm</div>
                                </li>
                                <li class="flex text-sm odd:bg-[#139a5212] h-6">
                                    <div class="mr-2"> Genişlik :</div>
                                    <div> 129.5 cm</div>
                                </li>
                                <li class="flex text-sm odd:bg-[#139a5212] h-6">
                                    <div class="mr-2"> Yükseklik :</div>
                                    <div> 63.5 cm</div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
</template>
