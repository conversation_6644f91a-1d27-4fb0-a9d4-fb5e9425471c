<script>
import { ref, onMounted } from "vue";
import { Splide, SplideSlide } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from "@headlessui/vue";
import { vMaska } from "maska";

import Layout from "@/Pages/Shared/LayoutDemo.vue";
import { Head, Link } from "@inertiajs/inertia-vue3";

export default {
    layout: Layout,
    components: {
        Link,
        Splide,
        SplideSlide,
        Head,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle
    },
    props: {
        errors: { type: Object, default: false }
    },
    directives: { maska: vMaska },
    methods: {
        closeModal() {
            this.isOpen = false;
        },
        openModal() {
            this.isOpen = true;
        },
        closeFormModal() {
            this.modalIsOpen = false;
        },
        openFormModal() {
            this.modalIsOpen = true;
        },
        selectMonth(month) {
            this.selectedMonth = month;
            this.selectedPrice = this.product.price[month];
        }
    },
    watch: {
        "$page.props.success": function (val) {
            this.isOpen = val.success != null ? true : false;
            if (val.success == "Mesajınız başarıyla gönderildi.") {
                this.closeFormModal();
                this.motorcycleForm.reset();
            }
        }
    },
    setup() {
        const mainSplide = ref(null);
        const thumbSplide = ref(null);

        const mainOptions = {
            type: "loop",
            height: "450px",
            pagination: false,
            arrows: false,
            breakpoints: {
                600: {
                    height: "280px"
                }
            }
        };

        const thumbOptions = {
            type: "slide",
            fixedWidth: 100,
            fixedHeight: 100,
            height: "450px",
            gap: "1rem",
            direction: "ttb",
            pagination: false,
            cover: true,
            perPage: 6,
            focus: "center",
            isNavigation: true,
            updateOnMove: true,
            loop: true,
            breakpoints: {
                600: {
                    fixedWidth: "100%",
                    fixedHeight: "100%",
                    height: "280px"
                }
            }
        };

        onMounted(() => {
            mainSplide.value.sync(thumbSplide.value.splide);
        });

        return {
            mainSplide,
            thumbSplide,
            mainOptions,
            thumbOptions
        };
    },

    name: "Honda PCX 125 Scooter",
    data() {
        return {
            selectedClass: "border-2 h-14 md:h-20 rounded-full border-kbgreen p-2 lg:p-4 items-center justify-center flex bg-white !text-black text-[14px] md:text-[20px] hover:!scale-100",
            selectedPrice: 9750,
            selectedMonth: 24,
            modalIsOpen: false,
            isOpen: this.$page.props.success.success != null ? true : false,
            motorcycleForm: this.$inertia.form({
                name: null,
                email: null,
                gsm: null,
                firmName: null,
                dateofbirth: null,
                motorcycleNumber: null,
                city: null,
                Contract1: null,
                Contract2: null,
                IDnumber: null,
                motorcycleName: "Honda PCX 125 Scooter"
            }),
            product: {
                name: "Honda PCX 125 Scooter",
                brand: "honda",
                explanation: "100 km/h, 9.2 kW, 11.6 Nm",
                price: { 12: 15185, 18: 11530, 24: 9750 },
                features: {
                    "Motor tipi": "eSP+su soğutmalı, SOHC, tek silindir",
                    "Motor hacmi": "125 cc",
                    "Maksimum güç": "9,2 kW, 8.750 d/d",
                    "Maksimum tork": "11,8 Nm, 6.500 d/d",
                    "Uzunluk x Genişlik x Yükseklik (mm)": "1,935 x 740 x 1,105",
                    "Sele yüksekliği (mm)": "764",
                    "Dingil mesafesi (mm)": "1.315",
                    "Boş ağırlık (kg)": "130",
                    "Frenler (Ön / Arka)": "2 piston kaliperli 220 mm hidrolik disk /",
                    "Lastikler (Ön / Arka)": "110/70 R 14 / 130/70 R13",
                    "Ön Süspansiyon": "31 mm teleskopik ön çatal",
                    "Arka Süspansiyon": "Tekli amortisör, Pro-Link salıncak"
                },
                metaTitle: "Honda PCX 125 Scooter Kiralama",
                description: "Motosiklet kiralama KiralaBunu ile çok kolay! Kiralık Honda PCX Scooter modellerini keşfetmek ve kiralamak için hemen tıklayın!",
                images: {
                    "honda-pcx-1.webp": "honda-pcx-1.png",
                    "honda-pcx-2.webp": "honda-pcx-2.png",
                    "honda-pcx-3.webp": "honda-pcx-3.png",
                    "honda-pcx-4.webp": "honda-pcx-4.png",
                    "honda-pcx-5.webp": "honda-pcx-5.png",
                    "honda-pcx-6.webp": "honda-pcx-6.png",
                    "honda-pcx-7.webp": "honda-pcx-7.png"
                }
            }
        };
    }
};
</script>
<style>
button.splide__arrow.splide__arrow--prev {
    transform: rotate(270deg);
    top: 0 !important;
}

button.splide__arrow.splide__arrow--next {
    bottom: 0 !important;
}
</style>

<template>

    <Head :title="product.metaTitle">
        <meta name="description" :content="product.description">
    </Head>

    <main class="my-3 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-4 ts:mt-1 2xl:mt-4 mx-auto scroll-smooth">
        <section class="mt-3 ts:mt-1 2xl:mt-3 flex flex-col xl:flex-row items-center">
            <div class="flex flex-col justify-between w-full xl:w-1/2">
                <div class="w-full flex flex-row-reverse items-start">
                    <div class="flex justify-center mr-2 px-1 w-[calc(100%-60px)] md:w-[calc(100%-110px)]">
                        <Splide :options="mainOptions" ref="mainSplide">
                            <SplideSlide v-for="(pngImage, webpImage) in product.images" :key="webpImage">
                                <picture>
                                    <source :srcset="`/images/kiralamotor/${webpImage}`" type="image/webp">
                                    <source :srcset="`/images/kiralamotor/${pngImage}`" type="image/png">
                                    <img :src="`/images/kiralamotor/${pngImage}`">
                                </picture>
                            </SplideSlide>
                        </Splide>
                    </div>
                    <Splide :options="thumbOptions" ref="thumbSplide" class="w-[60px] mts:w-[90px] md:w-[110px] max-h-[280px] mts:max-h-[450px] h-full">
                        <SplideSlide v-for="(pngImage, webpImage) in product.images" :key="webpImage">
                            <picture>
                                <source :srcset="`/images/kiralamotor/${webpImage}`" type="image/webp">
                                <source :srcset="`/images/kiralamotor/${pngImage}`" type="image/png">
                                <img :src="`/images/kiralamotor/${pngImage}`">
                            </picture>
                        </SplideSlide>
                    </Splide>
                </div>
            </div>
            <div class="flex flex-col xl:w-1/2 mt-6 md:mt-2 xl:mt-0">
                <div class="bg-kb-mid-grey md:bg-white md:shadow-productshadow w-full p-4 ts:pt-1 2xl:pt-4 rounded-md">
                    <div class="w-full flex justify-between">
                        <svg width="90.333" height="15.021" viewBox="0 0 90.333 15.021">
                            <g transform="translate(-930.094 0.5)">
                                <path
                                    d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                    data-name="star-solid" transform="translate( 928.348 -2.433)" fill="#70d44b" stroke="" stroke-width=""></path>
                                <path
                                    d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                    data-name="star-solid" transform="translate( 947.007 -2.433)" fill="#70d44b" stroke="" stroke-width=""></path>
                                <path
                                    d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                    data-name="star-solid" transform="translate( 965.666 -2.433)" fill="#70d44b" stroke="" stroke-width=""></path>
                                <path
                                    d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                    data-name="star-solid" transform="translate( 984.325 -2.433)" fill="#70d44b" stroke="" stroke-width=""></path>
                                <path
                                    d="M16.01,8.777a.982.982,0,0,0-.57-1.769l-3.98-.15a.1.1,0,0,1-.088-.066L10,3.077a.982.982,0,0,0-1.844,0L6.78,6.805a.1.1,0,0,1-.088.066l-3.98.15a.982.982,0,0,0-.57,1.769l3.122,2.454a.1.1,0,0,1,.035.106L4.224,15.159a.982.982,0,0,0,1.495,1.083l3.3-2.211a.1.1,0,0,1,.111,0l3.3,2.211a.97.97,0,0,0,1.495-1.061L12.839,11.36a.1.1,0,0,1,.035-.106Z"
                                    data-name="star-solid" transform="translate( 1002.984 -2.433)" fill="#70d44b" stroke="" stroke-width=""></path>
                            </g>
                        </svg>
                        <div class="flex">
                            <svg width="20" height="20" viewBox="0 0 28.164 23.242" class="cursor-pointer">
                                <path id="_23" data-name="23"
                                    d="M16.1,27.012a1.412,1.412,0,0,1-.833-.268L4.8,19.034a.734.734,0,0,1-.155-.141A8.833,8.833,0,0,1,16.1,5.507a8.839,8.839,0,0,1,11.522.861h0a8.853,8.853,0,0,1,0,12.525.735.735,0,0,1-.155.141L16.932,26.687A1.412,1.412,0,0,1,16.1,27.012ZM6.5,16.8l9.6,7.06,9.6-7.06a6.043,6.043,0,0,0-.071-8.472h0a6.029,6.029,0,0,0-8.472,0,1.412,1.412,0,0,1-2.005,0A6.056,6.056,0,0,0,6.5,16.8Z"
                                    transform="translate(-2.052 -3.77)" fill="#231f20" opacity="0.5"></path>
                                <path id="Path_50" data-name="Path 50" d="M2605.027,1375.483l3.363-2.4,5.89.491,4.247,2.575v4.546l-.806,4.555-4.806,4.015-7.888,2.158-4.967-3.347-4.8-6.758-.3-4.122,1.56-2.432,4.8-1.19Z"
                                    transform="translate(-2593 -1371.54)" fill="#231f20" opacity="0"></path>
                            </svg>
                        </div>
                    </div>
                    <h1 class="font-bold text-xl mt-1 ts:mt-0 2xl:mt-1" v-html="product.name"></h1>
                    <div class=""><a class="text-sm text-kbgreen font-semibold capitalize" target="_blank" :href="'/marka/' + product.brand" v-html="product.brand"></a></div>
                    <div class="text-sm lg:text-xs mt-1 ts:mt-0 2xl:mt-1 text-kbgray" v-html="product.explanation"></div>
                    <div class="flex mt-2 ts:mt-1 2xl:mt-2 items-end">
                        <span class="text-2xl 2xl:text-3xl leading-none mr-1 whitespace-nowrap" v-text="selectedPrice + ' TL + KDV'"></span>
                        <span class="text-2xs md:text-sm text-black self-center ml-2 md:ml-0 leading-none font-santralregular"> / Aylık ödenecek tutar</span>
                    </div>
                </div>
                <div class="bg-kb-mid-grey md:bg-white md:shadow-productshadow w-full mt-0 p-4 rounded-md ts:rounded-none">
                    <div class="flex text-sm lg:text-base xl:text-xl text-center cursor-pointer text-gray-500 h-12 md:h-20 ts:h-20 xl:h-20">
                        <ul class="flex border rounded-full bg-white w-full items-center justify-center shadow-xl h-8 md:h-12 md:max-w-[550px] mx-auto">
                            <li v-for="(value, key) in product.price" :key="key" class="whitespace-nowrap w-16 md:w-20 md:text-lg hover:scale-125 transform duration-300 ease-in-out" :class="[selectedMonth == key ? selectedClass : '']"
                                @click="selectMonth(key)" v-html="key + ' Ay'"></li>
                        </ul>
                    </div>
                    <div class="w-full mt-4">
                        <button @click="openFormModal" class="w-full block text-white rounded-full py-3 px-4 self-center font-semibold text-lg leading-none text-center bg-kbgreen disabled:opacity-50 disabled:cursor-not-allowed">Kiralama talebi
                            oluştur</button>
                    </div>
                </div>
            </div>
        </section>
        <section class="mt-6 flex flex-col lg:flex-row lg:space-x-2">
            <div class="flex-col w-full lg:w-1/2 bg-kb-mid-grey p-3 lg:p-6 py-6 rounded-lg hidden">
                <h2 class="text-3xl ts:text-2xl 2xl:text-3xl font-semibold">Kutu İçeriği</h2>
                <div class="mt-3 grid lg:gap-2 grid-cols-4 mts:grid-cols-5"></div>
            </div>
            <div class="hidden mts:block w-full bg-kb-mid-grey p-2 rounded-lg mt-4 lg:mt-0">
                <div class="ml-4 mt-2 text-3xl ts:text-2xl 2xl:text-3xl font-semibold">Avantajlar</div>
                <ul class="flex flex-wrap mts:flex-nowrap text-xs font-bold mts:space-x-1 lg:space-x-2 text-center mt-1 mr-1 mts:p-5">
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <img class="w-10/12 lg:w-full max-h-14 mx-auto" src="../../../images/kiralamotor/kiralamotor-i2.svg" />
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Sigorta ve MTV Dahil</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <img class="w-10/12 lg:w-full max-h-14 mx-auto" src="../../../images/kiralamotor/kiralamotor-i3.png" />
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Kira Dönemi Sonunda Sahip Olma Fırsatı</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <img class="w-10/12 lg:w-full max-h-14 mx-auto" src="../../../images/kiralamotor/kiralamotor-i4.svg" />
                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">Vergi Avantajları</div>
                        </div>
                    </li>
                    <li class="w-1/2 mts:w-1/3 lg:w-1/4 px-1.5 py-1.5 mts:py-0 mts:px-0">
                        <div class="flex flex-col rounded-md bg-white mts:bg-transparent p-3 mts:p-0 mts:rounded-none border-1 mts:border-0 border-bordergray">
                            <div class="h-10 mts:h-14 self-center mb-3 flex items-end">
                                <img class="w-10/12 lg:w-full max-h-14 mx-auto" src="../../../images/kiralamotor/kiralamotor-i6.svg" />

                            </div>
                            <div class="text-3xs mts:text-xs ts:text-2xs 2xl:text-xs leading-none">0 KM Yeni Motorlar</div>
                        </div>
                    </li>
                </ul>
            </div>
        </section>
        <div class="flex flex-col w-full bg-kb-mid-grey p-2 py-6 rounded-lg mt-4">
            <h2 class="text-3xl ts:text-2xl 2xl:text-3xl font-semibold ml-3">Ürün Özellikleri</h2>
            <div class="mt-6 ts:mt-3 2xl:mt-6">
                <div class="lg:grid grid-cols-2 gap-4 border-t-1 border-bordergray">
                    <ul class="pl-3 mt-1">
                        <li v-for="(key, index) in Object.keys(product.features).slice(0, 6)" :key="key" class="flex text-sm ts:text-xs 2xl:text-sm border-b-1 border-bordergray pb-1 mb-1 items-center">
                            <div class="w-1/2" v-html="key"></div>
                            <div class="w-1/2 font-santralregular" v-html="product.features[key]"></div>
                        </li>
                    </ul>
                    <ul class="pl-3 mt-1">
                        <li v-for="(key, index) in Object.keys(product.features).slice(6, 12)" :key="key" class="flex text-sm ts:text-xs 2xl:text-sm border-b-1 border-bordergray pb-1 mb-1 items-center">
                            <div class="w-1/2" v-html="key"></div>
                            <div class="w-1/2 font-santralregular" v-html="product.features[key]"></div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </main>
    <TransitionRoot appear :show="modalIsOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>
            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-[350px] lg:max-w-[850px] xl:max-w-[1000px] transform overflow-hidden rounded-sm bg-white px-1 py-1 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                            <h1 class="font-bold text-xl lg:text-3xl mt-5">Kiralama Talebi oluştur</h1>
                            <form class="w-full mt-8 px-4 lg:px-8" action="#">
                                <div class="flex flex-wrap">
                                    <div class="group relative w-full lg:w-1/2 lg:pr-2">
                                        <input :class="{ 'border-kbred': errors.name != null }" id="fullname" required="" type="text" name="AdSoyad*" autofocus="" v-model="motorcycleForm.name"
                                            class="!py-2 !leading-none w-full mb-5 ts:mb-7 rounded-lg border-2 border-bordergray placeholder:text-xs text-lg tracking-wider placeholder:text-placeholdergray focus:border-acordion-green hover:border-kbblue focus:outline-none focus:ring-kbgreen group-hover:placeholder-white peer" />
                                        <label for="fullname"
                                            class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">İsim,
                                            Soyisim*</label>
                                        <div class="text-sm text-kbred absolute bottom-1" v-if="errors.name"> {{ errors.name }}</div>
                                    </div>
                                    <div class="relative group w-full lg:w-1/2 lg:pr-2">
                                        <input :class="{ 'border-kbred': errors.firmName != null }" id="firmName" type="text" v-model="motorcycleForm.firmName" placeholder="Firma İsmi*" autocomplete="off" required=""
                                            class="!py-1.5 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />
                                        <label for="firmName"
                                            class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">Firma
                                            İsmi*</label>
                                        <div class="text-sm text-kbred absolute bottom-1" v-if="errors.firmName"> {{ errors.firmName }}</div>
                                    </div>
                                    <div class="relative group w-full lg:w-1/2 lg:pr-2">
                                        <input :class="{ 'border-kbred': errors.gsm != null }" v-maska data-maska="(5##) ### ## ##" id="telephone"
                                            class="!py-2 peer placeholder-transparent w-full mb-5 ts:mb-7 rounded-lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                            required type="text" placeholder="" name="telno" v-model="motorcycleForm.gsm" />
                                        <label for="telephone"
                                            class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Telefon*</label>
                                        <div class="text-sm text-kbred absolute bottom-1" v-if="errors.gsm"> {{ errors.gsm }}</div>
                                    </div>
                                    <div class="relative group w-full lg:w-1/2 lg:pr-2">
                                        <input :class="{ 'border-kbred': errors.email != null }" id="email" type="email" v-model="motorcycleForm.email" placeholder="E-posta" autocomplete="off" required=""
                                            class="!py-2 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />
                                        <label for="email"
                                            class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">E-Posta*</label>
                                        <div class="text-sm text-kbred absolute bottom-1" v-if="errors.email"> {{ errors.email }}</div>
                                    </div>
                                    <div class="relative group w-full lg:w-1/2 lg:pr-2">
                                        <input :class="{ 'border-kbred': errors.IDnumber != null }" id="IDnumber" v-maska data-maska="###########" type="text" v-model="motorcycleForm.IDnumber" placeholder="T.C. Kimlik No." autocomplete="off"
                                            required=""
                                            class="!py-2 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />
                                        <label for="IDnumber"
                                            class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">T.C.
                                            Kimlik No*</label>
                                        <div class="text-sm text-kbred absolute bottom-2" v-if="errors.IDnumber"> {{ errors.IDnumber }}</div>
                                    </div>
                                    <div class="relative group w-full lg:w-1/2 lg:pr-2">
                                        <input :class="{ 'border-kbred': errors.dateofbirth != null }" id="dateofbirth" v-maska data-maska="##/##/####" type="text" v-model="motorcycleForm.dateofbirth" placeholder="Doğum Tarihi" autocomplete="off"
                                            required=""
                                            class="!py-2 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />
                                        <label for="dateofbirth"
                                            class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">Doğum
                                            Tarihi*</label>
                                        <div class="text-sm text-kbred absolute bottom-1" v-if="errors.dateofbirth"> {{ errors.dateofbirth }}</div>
                                    </div>
                                    <div class="relative group w-full lg:w-1/2 lg:pr-2">
                                        <input :class="{ 'border-kbred': errors.city != null }" id="city" type="text" v-model="motorcycleForm.city" placeholder="Şehir" autocomplete="off" required=""
                                            class="!py-2 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />
                                        <label for="city"
                                            class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">Şehir*</label>
                                        <div class="text-sm text-kbred absolute bottom-1" v-if="errors.city"> {{ errors.city }}</div>
                                    </div>
                                    <div class="relative group w-full lg:w-1/2 lg:pr-2">
                                        <input :class="{ 'border-kbred': errors.motorcycleNumber != null }" id="motorcycleNumber" type="number" v-model="motorcycleForm.motorcycleNumber" placeholder="Motosiklet Adedi" autocomplete="off" required=""
                                            class="!py-2 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />
                                        <label for="motorcycleNumber"
                                            class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">Motosiklet
                                            Adedi*</label>
                                        <div class="text-sm text-kbred absolute bottom-1" v-if="errors.motorcycleNumber"> {{ errors.motorcycleNumber }}</div>
                                    </div>
                                    <input type="hidden" v-model="motorcycleForm.motorcycleName">
                                    <div class="w-full flex justify-start items-center mb-3 relative">
                                        <input :class="{ 'border-kbred': errors.Contract1 != null }" v-model="motorcycleForm.Contract1" id="uyelik" class="cursor-pointer w-4 h-4 border-3 rounded-md focus:outline-none checked:bg-black focus:bg-black"
                                            type="checkbox" required="">
                                        <label class="cursor-pointer text-sm text-left text-checkoutgray pl-2" for="uyelik"> <a href="/aydinlatma-metni" target="_blank"> Aydınlatma metni ve açık rıza beyan sözleşmesini kabul ediyorum.</a></label>
                                    </div>
                                    <div class="w-full flex justify-start items-center mb-3 relative">
                                        <input :class="{ 'border-kbred': errors.Contract2 != null }" v-model="motorcycleForm.Contract2" id="aydinlatma"
                                            class="cursor-pointer w-4 h-4 border-3 rounded-md focus:outline-none checked:bg-black focus:bg-black" type="checkbox" required="">
                                        <label class="cursor-pointer text-sm text-left text-checkoutgray pl-2" for="aydinlatma"> <a href="/uyelik-sozlesmesi" target="_blank">Üyelik sözleşmesini kabul ediyorum.</a> </label>
                                    </div>
                                    <div class="text-sm text-kbred w-full" v-if="errors.Contract1"> {{ errors.Contract1 }}</div>
                                    <div class="text-sm text-kbred w-full" v-if="errors.Contract2"> {{ errors.Contract2 }}</div>
                                    <div class="mt-4 flex justify-center w-full mb-8">
                                        <Link :href="route('sendHondaPCX125')" method="post" class="bg-black text-white rounded-full py-2 px-4 self-center font-bold w-full lg:w-24" :data="motorcycleForm">Gönder</Link>
                                    </div>
                                </div>
                            </form>
                            <div class="font-santralextrabold absolute top-0 right-3 cursor-pointer text-base bg-white rounded-lg p-3" @click="closeFormModal">X</div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>

    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-50">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white px-5 py-5 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 62.051 62.051">
                                <g id="Group_4884" data-name="Group 4884" transform="translate(-73.036 -250.295)">
                                    <circle id="Ellipse_199" data-name="Ellipse 199" cx="31.025" cy="31.025" r="31.025" transform="translate(73.036 250.295)" fill="#70d44b" />
                                    <path id="Path_3299" data-name="Path 3299"
                                        d="M351.649,476.232a3.45,3.45,0,0,1-2.761-1.381l-8.464-11.286a3.451,3.451,0,0,1,5.522-4.141l5.538,7.383,14.223-21.334a3.451,3.451,0,1,1,5.743,3.829l-16.928,25.393a3.453,3.453,0,0,1-2.776,1.536Z"
                                        transform="translate(-251.82 -178.764)" fill="#fff" />
                                </g>
                            </svg>
                            <DialogTitle>
                                <h2 class="text-base font-semibold text-center mb-2 mt-2">Talebiniz alınmıştır.</h2>
                            </DialogTitle>
                            <p class="text-xs font-kiralabunuthin ts:text-sm text-center"> En kısa süre içerisinde tarafınıza dönüş sağlanacaktır. <br> Çalışma saatlerimiz hafta içi 09:00 - 18:00 arasındadır. <br> Mesai saatleri dışındaki taleplere
                                ilk mesai günü içerisinde dönüş sağlanacaktır. <br> Teşekkürler!</p>
                            <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal">x</div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>

</template>
