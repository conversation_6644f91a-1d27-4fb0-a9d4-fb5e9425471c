<script>
import {Head, <PERSON>} from '@inertiajs/inertia-vue3';
import Layout from '@/Pages/Shared/Layout.vue';
import UserMenu from '@/Pages/Shared/UserMenu.vue';

export default {
    components: {
        <PERSON>,
        Head,
        UserMenu,
    },
    props: {
        order_count: Number,
    },
    layout: Layout,
}
</script>

<template>
    <Head title="İstek Listem"/>

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row items-start">
            <user-menu :order_count="order_count" :active="`MyWishlists`" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 lg:px-4 pt-2">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7">İstek
                        Listem </h3>
                </div>
                <div class="flex flex-col justify-center items-center mb-10">
                    <div class="flex justify-center items-center bg-kb-light-grey rounded-full w-32 h-32 mb-5">
                        <div class="flex justify-center items-center bg-black rounded-full w-20 h-20">
                            <svg xmlns="http://www.w3.org/2000/svg" width="39.104" height="42.629"
                                 viewBox="0 0 39.104 42.629">
                                <g id="noun-list-1322460" transform="translate(-93.065 0)">
                                    <path id="Path_3768" data-name="Path 3768"
                                          d="M236.391,19.285h23.228A2.409,2.409,0,0,0,262.2,17.1a2.409,2.409,0,0,0-2.581-2.184H236.391A2.409,2.409,0,0,0,233.81,17.1a2.409,2.409,0,0,0,2.581,2.184Z"
                                          transform="translate(-130.031 -13.782)" fill="#fff"/>
                                    <path id="Path_3769" data-name="Path 3769"
                                          d="M236,105.352h14.2a2.184,2.184,0,0,0,0-4.368H236a2.184,2.184,0,0,0,0,4.368Z"
                                          transform="translate(-130.033 -93.297)" fill="#fff"/>
                                    <path id="Path_3770" data-name="Path 3770"
                                          d="M259.623,215.74H236.391a2.214,2.214,0,1,0,0,4.368h23.228a2.214,2.214,0,1,0,0-4.368Z"
                                          transform="translate(-130.031 -199.318)" fill="#fff"/>
                                    <path id="Path_3771" data-name="Path 3771"
                                          d="M236,306.172h14.2a2.184,2.184,0,0,0,0-4.368H236a2.184,2.184,0,0,0,0,4.368Z"
                                          transform="translate(-130.033 -278.83)" fill="#fff"/>
                                    <path id="Path_3772" data-name="Path 3772"
                                          d="M259.623,416.56H236.391a2.214,2.214,0,1,0,0,4.368h23.228a2.214,2.214,0,1,0,0-4.368Z"
                                          transform="translate(-130.031 -384.85)" fill="#fff"/>
                                    <path id="Path_3773" data-name="Path 3773"
                                          d="M250.192,502.62H236a2.184,2.184,0,0,0,0,4.368h14.2a2.184,2.184,0,0,0,0-4.368Z"
                                          transform="translate(-130.033 -464.359)" fill="#fff"/>
                                    <path id="Path_3774" data-name="Path 3774"
                                          d="M96.354.982a2.3,2.3,0,1,1-.013,0m0-.983a3.261,3.261,0,1,0,.013,0Z"
                                          fill="#fff"/>
                                    <path id="Path_3775" data-name="Path 3775"
                                          d="M96.354,202.372a2.29,2.29,0,1,1-.013,0m0-.983a3.307,3.307,0,1,0,.013,0Z"
                                          transform="translate(0 -186.059)" fill="#fff"/>
                                    <path id="Path_3776" data-name="Path 3776"
                                          d="M96.354,403.192a2.3,2.3,0,1,1-.013,0m0-.983h0a3.3,3.3,0,1,0,.013,0Z"
                                          transform="translate(0 -371.592)" fill="#fff"/>
                                </g>
                            </svg>

                        </div>
                    </div>
                    <p class="p-0 text-lg font-bold text-center lg:text-left text-gray-900 box-border whitespace-no-wrap mb-5">
                        Henüz istek listende ürün bulunmamaktadır.</p>
                    <p class="p-0 text-base text-center text-kbgray box-border whitespace-no-wrap mb-5">Hemen bir ürün
                        kiralamak için ürünler sayfasına bir göz at.</p>
                    <Link href="/istek-listem-dolu"
                          class="bg-black text-white rounded-full py-2 px-4 self-center text-lg font-bold">Ürünlere Git
                    </Link>
                </div>
            </div>
        </section>
    </main>

</template>
