<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";

export default {
    components: {
        <PERSON>,
        Head,
        UserMenu,
    },
    props: {
        supportRequests: Object,
        order_count: Number,
    },
    layout: Layout,
};
</script>

<template>

    <Head title="Geçmiş Taleplerim " />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <user-menu :order_count="order_count" :active="`OldRequest`" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 lg:px-4 pt-2">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7">Geçmiş Taleplerim</h3>
                    <!--                    <form class="hidden lg:flex h-10" action="#">-->
                    <!--                        <input-->
                    <!--                            class="w-32 bg-kb-mid-grey p-1 pl-2 mr-4 rounded-2lg border-3 border-bordergray placeholder:text-sm placeholder:text-black focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-base tracking-wider"-->
                    <!--                            onfocus="(this.type='date')"-->
                    <!--                            onblur="(this.type='text')"-->
                    <!--                            required-->
                    <!--                            type="text"-->
                    <!--                            placeholder="Tarihe Git"-->
                    <!--                            name="Tarih"-->
                    <!--                        />-->

                    <!--                        <select class="w-32 bg-kb-mid-grey p-1 pl-2 mr-4 rounded-2lg border-3 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-base tracking-wider" name="sirala" id="sirala">-->
                    <!--                            <option value="Sırala" selected>Tümü</option>-->
                    <!--                        </select>-->
                    <!--                    </form>-->
                </div>
                <div class="w-full flex flex-row border-3 border-bordergray rounded-lg p-3 mb-5" v-for="(supportRequest, index) in supportRequests">
                    <div class="w-2/12 flex justify-center">
                        <div class="flex justify-center items-center w-25 h-16 md:h-25 shadow-lg bg-white rounded-lg">
                            <picture>
                                <source class="w-13 lg:w-16" :srcset="supportRequest.product.product.firstPhotoUrlCover?.thumb_webp" type="image/webp" />
                                <img class="w-13 lg:w-16" :src="supportRequest.product.product.firstPhotoUrlCover?.thumb" />
                            </picture>
                        </div>
                    </div>
                    <div class="w-10/12 pl-3">
                        <div class="flex flex-col w-full">
                            <div class="w-full mb-2 ts:mb-5 pb-1 ts:pb-5 border-b-2 border-bordergray">
                                <div class="w-full flex justify-between">
                                    <div>
                                        <div class="text-sm text-black font-normal pb-1 ts:pb-2">Ürün Adı:</div>
                                        <div class="text-base text-black font-bold">
                                            {{ supportRequest.product.product.attribute_data.name.tr }}
                                        </div>
                                    </div>
                                    <div class="text-right hidden ts:block">
                                        <div class="text-sm text-black font-normal pb-1 ts:pb-2">Talep Oluşturma Tarihi:</div>
                                        <div class="text-base text-black font-bold">
                                            {{ supportRequest.support_request_date }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="w-full mb-2 ts:mb-5 pb-1 ts:pb-5 border-b-2 border-bordergray">
                                <div class="text-sm text-black font-normal pb-1 ts:pb-2">Durumu:</div>
                                <div class="text-base text-black font-bold">{{ supportRequest.support_request_type.name }}</div>
                            </div>
                            <div class="w-full mb-2 ts:mb-5 pb-1 ts:pb-5 border-b-2 ts:border-b-0 border-bordergray">
                                <div class="text-sm text-black font-normal pb-1 ts:pb-2">Durumu:</div>
                                <div class="text-base text-kbyellow font-bold">{{ supportRequest.status_text }}</div>
                                <div class="mt-3" v-if="supportRequest.status == 'App\\States\\SupportRequest\\SupportProductWaiting'">Ürününüzü Yurtiçi Kargo ile {{ supportRequest.cargo_return_code }} müşteri koduna göndermeniz gerekmektedir</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="false" class="w-full flex flex-row border-3 border-bordergray rounded-lg p-3 mb-5">
                    <div class="w-2/12 flex justify-center">
                        <div class="flex justify-center items-center w-25 h-16 md:h-25 shadow-lg bg-white rounded-lg">
                            <img class="w-20" src="../../images/<EMAIL>" alt="" />
                        </div>
                    </div>
                    <div class="w-10/12 pl-3">
                        <div class="flex flex-col w-full">
                            <div class="w-full mb-2 ts:mb-5 pb-1 ts:pb-5 border-b-2 border-bordergray">
                                <div class="w-full flex justify-between">
                                    <div>
                                        <div class="text-sm text-black font-normal pb-1 ts:pb-2">Ürün Adı:</div>
                                        <div class="text-base text-black font-bold">Apple iPhone 13 Mini 128 GB</div>
                                    </div>
                                    <div class="text-right hidden ts:block">
                                        <div class="text-sm text-black font-normal pb-1 ts:pb-2">Talep Oluşturma Tarihi:</div>
                                        <div class="text-base text-black font-bold">18 Eylül 2022</div>
                                    </div>
                                </div>
                            </div>
                            <div class="w-full mb-2 ts:mb-5 pb-1 ts:pb-5 border-b-2 border-bordergray">
                                <div class="text-sm text-black font-normal pb-1 ts:pb-2">Durumu:</div>
                                <div class="text-base text-black font-bold">Ürün arızalı ya da bozuk</div>
                            </div>
                            <div class="w-full mb-2 ts:mb-5 pb-1 ts:pb-5 border-b-2 ts:border-b-0 border-bordergray">
                                <div class="text-sm text-black font-normal pb-1 ts:pb-2">Durumu:</div>
                                <div class="text-base text-kbyellow font-bold">Bekleniyor</div>
                            </div>
                            <div class="w-full mb-2 ts:mb-5 block ts:hidden">
                                <div class="text-sm text-black font-normal pb-1 ts:pb-2">Talep Oluşturma Tarihi:</div>
                                <div class="text-base text-black font-bold">18 Eylül 2022</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
</template>
