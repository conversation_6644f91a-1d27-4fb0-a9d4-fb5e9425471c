<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from "@headlessui/vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
    },
    props: {
        orders: Object,
        errors: { type: Object, default: false },
        order_count: Number,
        pid: Number,
        req: Number,
        order: Number,
    },
    data() {
        return {
            form: this.$inertia.form({
                product: {},
                order: {},
                supportRequestType: null,
                note: null,
            }),
            isOpen: this.$page.props.success.success != null ? true : false,
            t: null,
        };
    },
    methods: {
        // submit() {
        //     this.form.post("/destek-talebi-olustur", {
        //         preserveScroll: true,
        //         onSuccess: () => {
        //             this.isOpen = true;
        //         },
        //     });
        // },
        closeModal() {
            console.log(this.isOpen);
            this.isOpen = false;
        },
        changeSelectedOrder() {
            let orderId = this.orders.find((item) => {
                return item.items.find((i) => {
                    return i.id == this.form.product.id;
                });
            }).id;

            console.log(orderId);
            this.form.order = orderId;
        },
    },
    watch: {
        "$page.props.success": function (val) {
            this.isOpen = val.success != null ? true : false;
            if (val.success == "Destek Talebiniz Oluşturuldu") {
                this.form.reset();
            }
        },
    },
    mounted() {
        if (this.pid) {
            this.form.product = this.orders
                .find((item) => {
                    return item.items.find((i) => {
                        return i.id == this.pid && item.id == this.order;
                    });
                })
                .items.find((item) => {
                    return item.id == this.pid;
                });
        }

        if (this.req) this.form.supportRequestType = this.req;
        if (this.order) this.form.order = this.order;
    },
    layout: Layout,
};
</script>

<template>

    <Head title="Talep Oluştur" />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row items-start">
            <user-menu :order_count="order_count" :active="`CreateRequest`" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 lg:px-4 pt-2">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7">Talep Oluştur</h3>
                    <!--                    <form class="hidden lg:flex h-10" action="#">-->
                    <!--                        <input-->
                    <!--                            class="w-32 bg-kb-mid-grey p-1 pl-2 mr-4 rounded-2lg border-3 border-bordergray placeholder:text-sm placeholder:text-black focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-base tracking-wider"-->
                    <!--                            onfocus="(this.type='date')"-->
                    <!--                            onblur="(this.type='text')"-->
                    <!--                            required-->
                    <!--                            type="text"-->
                    <!--                            placeholder="Tarihe Git"-->
                    <!--                            name="Tarih"-->
                    <!--                        />-->

                    <!--                        <select class="w-32 bg-kb-mid-grey p-1 pl-2 mr-4 rounded-2lg border-3 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-base tracking-wider" name="sirala" id="sirala">-->
                    <!--                            <option value="Sırala" selected>Tümü</option>-->
                    <!--                        </select>-->
                    <!--                    </form>-->
                </div>
                <form @submit.prevent="submit">
                    <div class="flex flex-wrap justify-start items-center w-full md:9/12 lg:w-6/12 mb-3 bg-white">
                        <select class="w-full bg-white p-2 pl-3 font-bold rounded-lg border-2 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-base tracking-wider" name="sirala" id="urunler" v-model="form.product"
                            required @change="changeSelectedOrder">
                            <option :value="{}">Cihaz Seçiniz</option>
                            <template v-for="orderLocal in orders">
                                <template v-for="item in orderLocal.items">
                                    <option :value="item">{{ item.product.attribute_data.name.tr }} / Sipariş No: {{ orderLocal.order_number }}</option>
                                </template>
                            </template>
                        </select>
                    </div>
                    <div class="w-full flex items-center flex-row bg-kb-mid-grey rounded-lg p-3 mb-4 mt-3" v-if="form.product && Object.keys(form.product).length > 0">
                        <div class="w-20 h-20">
                            <div class="flex justify-center items-center w-20 h-20 shadow-lg bg-white rounded-2lg">
                                <img class="w-16" :src="form.product?.product?.imagesWebP?.thumb_webp" alt="" />
                            </div>
                        </div>
                        <div class="w-9/12 pl-5">
                            <p class="text-base text-black font-bold">{{ form.product.product?.attribute_data.name.tr }}</p>
                            <div class="w-full flex flex-wrap md:flex-nowrap items-center">
                                <p class="w-full mts:w-auto text-sm text-black font-normal mt-2 pr-2 md:border-r-1 border-checkoutgray">
                                    Sipariş No :
                                    <span class="font-bold">{{
                                        orders.find((item) => {
                                            return item.items.find((i) => {
                                                return i.id == form.product.id;
                                            });
                                        })?.order_number ?? null
                                    }}</span>
                                </p>
                                <p class="text-xs lg:text-sm text-black font-normal mt-2 mts:pl-2">
                                    {{
                                        orders.find((item) => {
                                            return item.items.find((i) => {
                                                return i.id == form.product.id;
                                            });
                                        })?.created_at ?? null
                                    }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-start items-center w-full mb-3 bg-white border-2 border-kb-light-grey py-3 px-5 rounded-lg">
                        <input class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" id="uzatma" name="yanlis" value="6" v-model="form.supportRequestType" required />
                        <label class="pl-3 text-base text-black font-bold cursor-pointer" for="uzatma">Kira süremi uzatmak istiyorum.</label>
                    </div>
                    <div class="flex justify-start items-center w-full mb-3 bg-white border-2 border-kb-light-grey py-3 px-5 rounded-lg">
                        <input class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" id="iptal" name="yanlis" value="7" v-model="form.supportRequestType" required />
                        <label class="pl-3 text-base text-black font-bold cursor-pointer" for="iptal">Ürünü iade etmek istiyorum.</label>
                    </div>
                    <div class="flex justify-start items-center w-full mb-3 bg-white border-2 border-kb-light-grey py-3 px-5 rounded-lg">
                        <input class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" id="bozuk" name="yanlis" value="2" v-model="form.supportRequestType" required />
                        <label class="pl-3 text-base text-black font-bold cursor-pointer" for="bozuk">Ürünümle ilgili arıza kaydı oluşturmak istiyorum.</label>
                    </div>
                    <div class="flex justify-start items-center w-full mb-3 bg-white border-2 border-kb-light-grey py-3 px-5 rounded-lg">
                        <input class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" id="kargo" name="yanlis" value="4" v-model="form.supportRequestType" required />
                        <label class="pl-3 text-base text-black font-bold cursor-pointer" for="kargo">Kargo süreçleriyle ilgili sorun yaşıyorum.</label>
                    </div>
                    <div class="flex justify-start items-center w-full mb-3 bg-white border-2 border-kb-light-grey py-3 px-5 rounded-lg">
                        <input class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" id="yanlis" name="yanlis" value="1" v-model="form.supportRequestType" required />
                        <label class="pl-3 text-base text-black font-bold cursor-pointer" for="yanlis">Ürünümle ilgili sorun yaşıyorum (farklı ürüm gönderimi).</label>
                    </div>
                    <div class="flex justify-start items-center w-full mb-3 bg-white border-2 border-kb-light-grey py-3 px-5 rounded-lg">
                        <input class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" id="odeme" name="yanlis" value="3" v-model="form.supportRequestType" required />
                        <label class="pl-3 text-base text-black font-bold cursor-pointer" for="odeme">Ödemeyle ilgili sorun yaşıyorum.</label>
                    </div>
                    <div class="flex justify-start items-center w-full mb-3 bg-white border-2 border-kb-light-grey py-3 px-5 rounded-lg">
                        <input class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" id="harici" name="yanlis" value="5" v-model="form.supportRequestType" required />
                        <label class="pl-3 text-base text-black font-bold cursor-pointer" for="harici">Bunların dışında sorunum var.</label>
                    </div>
                    <textarea class="w-full mb-4 rounded-md border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-lg tracking-wider placeholder:mt-2 h-32"
                        required name="note" placeholder="Ürün ya da hizmetle alakalı yaşadığın sorunu kısaca yaz.*" cols="20" rows="10" v-model="form.note"></textarea>

                    <div class="mt-4 w-80 md:w-96 self-center justify-between w-full space-y-2" v-if="Object.entries(errors).length > 0">
                        <div class="text-sm mx-auto text-red-900" v-for="error in errors">{{ error }}</div>
                    </div>

                    <div class="text-right">
                        <Link :href="route('sendSupportRequest')" method="post" class="lg:w-3/12 bg-black text-white rounded-full py-3 text-lg px-4 self-center font-bold w-full" :data="form" :disabled="form.processing">Gönder</Link>
                    </div>
                </form>
            </div>
        </section>
    </main>

    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white px-5 py-5 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 62.051 62.051">
                                <g id="Group_4884" data-name="Group 4884" transform="translate(-73.036 -250.295)">
                                    <circle id="Ellipse_199" data-name="Ellipse 199" cx="31.025" cy="31.025" r="31.025" transform="translate(73.036 250.295)" fill="#70d44b" />
                                    <path id="Path_3299" data-name="Path 3299"
                                        d="M351.649,476.232a3.45,3.45,0,0,1-2.761-1.381l-8.464-11.286a3.451,3.451,0,0,1,5.522-4.141l5.538,7.383,14.223-21.334a3.451,3.451,0,1,1,5.743,3.829l-16.928,25.393a3.453,3.453,0,0,1-2.776,1.536Z"
                                        transform="translate(-251.82 -178.764)" fill="#fff" />
                                </g>
                            </svg>
                            <DialogTitle>
                                <h2 class="text-base font-semibold text-center mb-2 mt-2">Başarılı</h2>
                            </DialogTitle>
                            <p class="text-xs font-kiralabunuthin ts:text-sm text-center">Talebini aldık. En yakın zamanda sana döneceğiz.</p>
                            <!--                            <div class="mt-4">-->
                            <!--                                <button-->
                            <!--                                    type="button"-->
                            <!--                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"-->
                            <!--                                    @click="closeModal"-->
                            <!--                                >-->
                            <!--                                    Got it, thanks!-->
                            <!--                                </button>-->
                            <!--                            </div>-->
                            <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal">x</div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>
