<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import TextClamp from "vue3-text-clamp";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        TextClamp,
    },
    props: {
        orders: Object,
    },
    data() {
        return {
            searchTerm: "",
            detailedOrder: null,
            filteredOrders: this.orders,
        };
    },
    watch: {
        searchTerm() {
            this.filteredOrders = this.orders.filter((order) => {
                return (
                    order.order_number.toLowerCase().includes(this.searchTerm.toLowerCase()) || order.status.toLowerCase().includes(this.searchTerm.toLowerCase()) || order.created_at.toLowerCase().includes(this.searchTerm.toLowerCase())
                    // order.items.filter((i) => {
                    //     return i.product.attribute_data.name.tr.toLowerCase().includes(this.searchTerm.toLowerCase());
                    // })
                );
            });
        },
    },
    layout: Layout,
};
</script>

<template>

    <Head title="Siparişler" />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <UserMenu :active="`MyOrders`" class="hidden lg:flex"></UserMenu>
            <div class="w-full lg:w-9/12 pl-3.5 pt-2">
                <div class="flex w-full flex-col ts:flex-row justify-between mb-5 ts:mb-0">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-3 md:mb-7">
                        Siparişler</h3>
                    <form class="flex h-10 w-full md:w-auto" action="#">
                        <div class="w-2/4 md:w-full lg:w-1/3 lg:w-auto px-1 relative group">
                            <input v-model="searchTerm"
                                class="peer font-kiralabunuthin w-full md:w-auto bg-kb-mid-grey p-1.5 pl-2 mr-1 md:mr-4 rounded-lg border-2 border-bordergray placeholder:text-xs lg:placeholder:text-sm placeholder:text-placeholdergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                required="" type="text" placeholder="" name="KiralamaAra" autofocus="" id="search" />
                            <label for="search"
                                class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-4 group-focus-within:left-3 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Siparişlerimde
                                Ara</label>
                        </div>
                        <select class="px-1 font-bold w-1/4 md:w-32 lg:w-40 bg-kb-mid-grey p-1 pl-2 mr-1 md:mr-4 rounded-lg border-2 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-xs ts:text-base tracking-wider"
                            name="sirala" id="sirala">
                            <option value="Sırala" selected>Sırala</option>
                        </select>
                        <select class="px-1 font-bold w-1/4 md:w-32 lg:w-40 bg-kb-mid-grey p-1 pl-2 mr-1 rounded-lg border-2 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-xs ts:text-base tracking-wider"
                            name="sirala" id="sirala1">
                            <option value="Sırala" selected>Tümü</option>
                        </select>
                    </form>
                </div>

                <div v-for="order in filteredOrders">
                    <div v-for="item in order.items">
                        <div class="w-full flex flex-wrap lg:flex-nowrap lg:flex-row border-2 border-bordergray rounded-lg px-2 ts:px-3 py-1 ts:py-3 mb-5" v-if="detailedOrder != item.id">
                            <div class="w-3/12 md:w-2/12 lg:w-1/12">
                                <div class="flex justify-center items-center w-16 md:w-20 h-16 md:h-20 shadow-lg bg-white rounded-2lg">
                                    <img class="w-13 md:w-16" :src="item.product.images[0].url" alt="" />
                                </div>
                            </div>
                            <div class="w-8/12 md:w-9/12 lg:w-10/12 flex flex-wrap lg:flex-nowrap lg:flex-row">
                                <div class="w-full md:w-6/12 lg:w-3/12 pl-1 lg:pl-8 md:border-r-2 border-bordergray mt-3 lg:mt-0">
                                    <div class="text-xs text-black font-normal lg:pb-2">Ürün Adı:</div>
                                    <text-clamp class="text-sm text-black font-bold leading-tight" :max-lines="2" :text="item.product.attribute_data.name.tr" />
                                </div>
                                <div class="w-6/12 md:w-3/12 lg:w-auto ts:pr-2 pl-1 lg:pl-1 mt-3 lg:mt-0 md:border-r-2 border-bordergray">
                                    <div class="text-xs text-black font-normal lg:pb-2">Sipariş No:</div>
                                    <div class="text-xs md:text-sm text-black font-bold">{{ order.order_number }}</div>
                                    <div class="hidden lg:block text-2xs mt-1 text-black font-medium whitespace-nowrap">
                                        {{ order.created_at }}
                                    </div>
                                </div>
                                <div class="hidden lg:block w-auto pl-2 lg:pl-1 border-r-2 border-bordergray">
                                    <div class="text-xs text-black font-normal pb-2 whitespace-nowrap ts:pr-2">Kiralama
                                        Süresi:
                                    </div>
                                    <div class="text-sm text-black font-bold">{{ item.plan?.name }}</div>
                                </div>
                                <div class="hidden lg:block w-3/12 pl-2 border-r-2 border-bordergray">
                                    <div class="text-xs text-black font-normal pb-2">Aylık Kiralama Ücreti:</div>
                                    <div class="text-sm text-black font-bold">{{ item.total }} TL</div>
                                </div>
                                <div class="w-6/12 md:w-3/12 lg:w-2/12 pl-3 mt-3 lg:mt-0">
                                    <div class="text-xs text-black font-normal lg:pb-2">Durumu:</div>
                                    <div class="text-xs md:text-sm text-kbgreen font-bold">{{ order.status }}</div>
                                </div>
                            </div>
                            <div class="w-1/12 pl-3 flex items-center justify-end">
                                <div class="bg-black rounded-full w-6 lg:w-8 h-6 lg:h-8">
                                    <button class="flex justify-center items-center w-6 lg:w-8 h-6 lg:h-8" @click="detailedOrder = item.id">
                                        <svg class="w-5 h-5 lg:w-6 lg:h-6" viewBox="0 0 23.872 21.712">
                                            <g id="Group_119" data-name="Group 119" transform="translate(0)">
                                                <path id="Path_19" data-name="Path 19" d="M1.6,21.712A1.528,1.528,0,0,1,0,20.261V1.451A1.528,1.528,0,0,1,1.6,0a1.528,1.528,0,0,1,1.6,1.451v18.81A1.528,1.528,0,0,1,1.6,21.712Z"
                                                    transform="translate(10.341 0)" fill="#fff"></path>
                                                <path id="Path_20" data-name="Path 20" d="M1.451,23.872A1.528,1.528,0,0,1,0,22.277V1.6A1.528,1.528,0,0,1,1.451,0,1.528,1.528,0,0,1,2.9,1.6V22.277A1.528,1.528,0,0,1,1.451,23.872Z"
                                                    transform="translate(23.872 9.405) rotate(90)" fill="#fff"></path>
                                            </g>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="w-full flex flex-col md:flex-row bg-kb-mid-grey rounded-lg p-3 mb-5" v-if="detailedOrder == item.id">
                            <div class="md:w-4/12 lg:w-3/12 ts:px-2 p2l-2 2xl:pl-5 pt-5">
                                <div class="flex justify-center items-center shadow-lg bg-white rounded-2lg w-9/12 md: mx-auto">
                                    <img class="w-38 md:w-48 lg:w-38 m-6" :src="item.product.images[0].url" alt="" />
                                </div>
                                <div class="flex flex-wrap justify-center md:justify-start ml-2 mt-5">
                                    <p class="text-sm w-full text-center md:text-left text-black font-bold">Ürünü
                                        Değerlendir</p>
                                    <svg class="mt-1" width="127.071" height="22.041" viewBox="0 0 127.071 22.041">
                                        <g id="Group_568" data-name="Group 568" transform="translate(0.525 0.5)">
                                            <g id="Group_568-2" data-name="Group 568" transform="translate(-1396.138)">
                                                <path id="star-solid"
                                                    d="M23.157,11.957A1.474,1.474,0,0,0,22.3,9.3l-5.974-.226a.153.153,0,0,1-.133-.1L14.129,3.4a1.474,1.474,0,0,0-2.768,0L9.3,9a.153.153,0,0,1-.133.1L3.2,9.321a1.474,1.474,0,0,0-.856,2.655l4.687,3.684a.153.153,0,0,1,.053.159L5.466,21.536A1.474,1.474,0,0,0,7.71,23.162l4.952-3.319a.146.146,0,0,1,.166,0l4.952,3.319a1.456,1.456,0,0,0,2.244-1.593L18.4,15.833a.146.146,0,0,1,.053-.159Z"
                                                    transform="translate(1498.409 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                                <path id="star-solid-2" data-name="star-solid"
                                                    d="M23.157,11.957A1.474,1.474,0,0,0,22.3,9.3l-5.974-.226a.153.153,0,0,1-.133-.1L14.129,3.4a1.474,1.474,0,0,0-2.768,0L9.3,9a.153.153,0,0,1-.133.1L3.2,9.321a1.474,1.474,0,0,0-.856,2.655l4.687,3.684a.153.153,0,0,1,.053.159L5.466,21.536A1.474,1.474,0,0,0,7.71,23.162l4.952-3.319a.146.146,0,0,1,.166,0l4.952,3.319a1.456,1.456,0,0,0,2.244-1.593L18.4,15.833a.146.146,0,0,1,.053-.159Z"
                                                    transform="translate(1472.405 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                                <path id="star-solid-3" data-name="star-solid"
                                                    d="M23.157,11.957A1.474,1.474,0,0,0,22.3,9.3l-5.974-.226a.153.153,0,0,1-.133-.1L14.129,3.4a1.474,1.474,0,0,0-2.768,0L9.3,9a.153.153,0,0,1-.133.1L3.2,9.321a1.474,1.474,0,0,0-.856,2.655l4.687,3.684a.153.153,0,0,1,.053.159L5.466,21.536A1.474,1.474,0,0,0,7.71,23.162l4.952-3.319a.146.146,0,0,1,.166,0l4.952,3.319a1.456,1.456,0,0,0,2.244-1.593L18.4,15.833a.146.146,0,0,1,.053-.159Z"
                                                    transform="translate(1446.4 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                                <path id="star-solid-4" data-name="star-solid"
                                                    d="M23.157,11.957A1.474,1.474,0,0,0,22.3,9.3l-5.974-.226a.153.153,0,0,1-.133-.1L14.129,3.4a1.474,1.474,0,0,0-2.768,0L9.3,9a.153.153,0,0,1-.133.1L3.2,9.321a1.474,1.474,0,0,0-.856,2.655l4.687,3.684a.153.153,0,0,1,.053.159L5.466,21.536A1.474,1.474,0,0,0,7.71,23.162l4.952-3.319a.146.146,0,0,1,.166,0l4.952,3.319a1.456,1.456,0,0,0,2.244-1.593L18.4,15.833a.146.146,0,0,1,.053-.159Z"
                                                    transform="translate(1420.396 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                                <path id="star-solid-5" data-name="star-solid"
                                                    d="M23.157,11.957A1.474,1.474,0,0,0,22.3,9.3l-5.974-.226a.153.153,0,0,1-.133-.1L14.129,3.4a1.474,1.474,0,0,0-2.768,0L9.3,9a.153.153,0,0,1-.133.1L3.2,9.321a1.474,1.474,0,0,0-.856,2.655l4.687,3.684a.153.153,0,0,1,.053.159L5.466,21.536A1.474,1.474,0,0,0,7.71,23.162l4.952-3.319a.146.146,0,0,1,.166,0l4.952,3.319a1.456,1.456,0,0,0,2.244-1.593L18.4,15.833a.146.146,0,0,1,.053-.159Z"
                                                    transform="translate(1394.392 -2.432)" fill="none" stroke="#70d44b" stroke-width="1" />
                                            </g>
                                        </g>
                                    </svg>
                                </div>
                            </div>
                            <div class="w-full md:w-5/12 ts:w-7/12 pl-3 lg:pl-8 pt-5">
                                <div class="text-lg ts:text-xl text-black font-bold">
                                    {{ item.product.attribute_data.name.tr }}
                                </div>
                                <div class="text-sm mt-3 ts:mt-5 text-textgray font-bold">Kiralama Süresi:
                                    {{ item.plan.name }}
                                </div>
                                <div class="text-2xl mt-5 text-black font-bold">{{ item.total }} TL</div>
                                <div class="flex justify-start items-center w-12/12 bg-acordion-light-green py-2 ts:py-3 px-2 ts:px-5 mt-4 rounded-lg">
                                    <svg id="check-circle-fill" xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36">
                                        <path id="Path_142" data-name="Path 142"
                                            d="M36,18A18,18,0,1,1,18,0,18,18,0,0,1,36,18Zm-8.933-6.817a1.688,1.688,0,0,0-2.43.049l-7.814,9.956-4.709-4.711a1.688,1.688,0,0,0-2.385,2.385l5.953,5.955a1.688,1.688,0,0,0,2.428-.045l8.982-11.228a1.688,1.688,0,0,0-.022-2.363Z"
                                            fill="#70d44b" fill-rule="evenodd" />
                                    </svg>
                                    <div class="pl-0 ts:pl-7 flex ts:items-center flex-col ts:flex-row">
                                        <p class="text-base pl-3 ts:pl-0 font-bold text-kbgreen text-left whitespace-nowrap">
                                            {{ order.status }}</p>
                                        <p class="font-medium pl-3 ts:pl-10 text-black text-sm whitespace-nowrap">
                                            {{ order.created_at }}</p>
                                    </div>
                                </div>
                                <Link href="/sepete-ekle" :data="{
                                    product_id: item.product.product_id,
                                    month: item.plan.id,
                                    product_variant_id: item.product.id,
                                }" method="post" as="button" class="bg-black text-center text-white rounded-full py-2 text-lg px-6 font-bold w-11/12 lg:w-3/4 mt-5 text-base">Tekrar Kirala
                                </Link>
                            </div>
                            <div class="mt-4 md:mt-0 w-full md:w-3/12 2xl:w-2/12 flex flex-wrap justify-end max-h-[235px] 2xl:max-h-[248px]">
                                <div class="w-1/3 md:w-1/2 lg:w-1/2">
                                    <div class="flex w-14 md:w-[62px] ts:w-16 h-14 md:h-[62px] ts:h-16 justify-center items-center bg-white rounded-lg border-1 mx-auto">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-[22px] h-[30px] ts:w-[29px] ts:h-[38px]" viewBox="0 0 29.171 38.894">
                                            <g id="noun-bill-5024028" transform="translate(0)">
                                                <path id="Path_137" data-name="Path 137"
                                                    d="M176.182,36.748A3.994,3.994,0,0,1,179.371,35H199.79a3.994,3.994,0,0,1,3.189,1.748,6.507,6.507,0,0,1,1.186,3.843V72.922a.972.972,0,0,1-1.625.721l-4.308-3.9L195.468,73.5a.972.972,0,0,1-1.435.144l-4.452-4.029-4.452,4.029a.972.972,0,0,1-1.5-.251l-2-3.625-5.058,3.923A.972.972,0,0,1,175,72.922V40.591a6.508,6.508,0,0,1,1.186-3.843Zm1.566,1.154a4.57,4.57,0,0,0-.807,2.689V70.938l4.388-3.4a.972.972,0,0,1,1.447.3l1.959,3.545,4.195-3.8a.973.973,0,0,1,1.3,0l4.308,3.9,2.765-3.753a.973.973,0,0,1,1.435-.145l3.48,3.149V40.591a4.57,4.57,0,0,0-.807-2.689,2.062,2.062,0,0,0-1.624-.957H179.371a2.063,2.063,0,0,0-1.624.957Z"
                                                    transform="translate(-174.995 -35)" fill="#70d44b" fill-rule="evenodd" />
                                                <path id="Path_138" data-name="Path 138" d="M291.116,130.187H280v-1.853h11.116Z" transform="translate(-271.662 -120.923)" fill="#70d44b" fill-rule="evenodd" />
                                                <path id="Path_139" data-name="Path 139" d="M291.116,223.521H280v-1.853h11.116Z" transform="translate(-271.662 -206.846)" fill="#70d44b" fill-rule="evenodd" />
                                                <path id="Path_140" data-name="Path 140" d="M286.487,316.851H280V315h6.485Z" transform="translate(-271.664 -292.765)" fill="#70d44b" fill-rule="evenodd" />
                                            </g>
                                        </svg>
                                    </div>
                                    <p class="text-xs text-center w-14 ts:w-16 text-textgray font-normal pb-2 mx-auto">
                                        <a href="">Faturayı Görüntüle</a>
                                    </p>
                                </div>
                                <div class="w-1/3 md:w-1/2 lg:w-1/2">
                                    <div class="flex w-14 md:w-[62px] ts:w-16 md:h-[62px] h-14 ts:h-16 justify-center items-center bg-white rounded-lg border-1 mx-auto">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-[27.5px] h-[29px] ts:w-[36px] ts:h-[38px]" viewBox="0 0 36.546 38.467">
                                            <path id="noun-customer-service-3370552"
                                                d="M167.036,68.1V64.906a11.21,11.21,0,0,0-11.2-11.2h-5.43a11.21,11.21,0,0,0-11.2,11.2V68.1a4.112,4.112,0,0,0-3.856,3.791v5.789c0,2.124,2.037,3.851,4.539,3.851s4.542-1.727,4.542-3.851V71.888a4.111,4.111,0,0,0-3.859-3.791V64.906a9.846,9.846,0,0,1,9.835-9.836h5.43a9.849,9.849,0,0,1,9.838,9.836V68.1a4.112,4.112,0,0,0-3.862,3.791v5.789a4,4,0,0,0,3.283,3.681,9.657,9.657,0,0,1-7.561,6.358,2.612,2.612,0,0,0-2.464-1.775h-3.9a2.617,2.617,0,1,0,0,5.234h3.9a2.619,2.619,0,0,0,2.564-2.109,11.157,11.157,0,0,0,8.843-7.548c2.445-.055,4.42-1.753,4.42-3.84V71.89a4.123,4.123,0,0,0-3.864-3.793ZM143.07,71.889v5.789c0,1.372-1.425,2.489-3.18,2.489s-3.174-1.117-3.174-2.489V71.889c0-1.37,1.423-2.489,3.174-2.489S143.07,70.518,143.07,71.889Zm12,17.925h-3.9a1.254,1.254,0,0,1,0-2.508h3.9a1.254,1.254,0,0,1,0,2.508Zm14.46-12.136c0,1.372-1.425,2.489-3.177,2.489s-3.177-1.117-3.177-2.489V71.889c0-1.37,1.425-2.489,3.177-2.489s3.177,1.117,3.177,2.489Z"
                                                transform="translate(-134.854 -53.209)" fill="#70d44b" stroke="#70d44b" stroke-width="1" />
                                        </svg>
                                    </div>
                                    <div class="text-xs text-center w-14 ts:w-auto text-textgray font-normal pb-2 mx-auto">
                                        <a href="">Talep Oluştur</a>
                                    </div>
                                </div>
                                <div class="w-1/3 md:w-1/2 lg:w-1/2">
                                    <div class="flex w-14 md:w-[62px] ts:w-16 h-14 md:h-[62px] ts:h-16 justify-center items-center bg-white rounded-lg border-1 mx-auto">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-[27px] h-[29px] ts:w-[35px] ts:h-[38px]" viewBox="0 0 35.691 38.482">
                                            <g id="noun-pack-3409085" transform="translate(0)">
                                                <path id="Path_141" data-name="Path 141"
                                                    d="M125.98,7.759a.772.772,0,0,0-.478-.7L108.435.054a.77.77,0,0,0-.583,0l-17.07,7a.763.763,0,0,0-.478.829.7.7,0,0,0,0,.075v22.2a.77.77,0,0,0,.454.7l16.818,7.551.066.021.054.018h0a.691.691,0,0,0,.388,0l.051-.015.063-.021,17.323-7.551h0a.77.77,0,0,0,.463-.707V7.962a.7.7,0,0,0,0-.075.77.77,0,0,0,0-.129ZM103.135,3.785l14.729,6.469L114.476,11.8,99.747,5.24Zm15.931,7.689v9.7l-3.818,1.653,0-9.682ZM108.143,1.6l15.129,6.213L119.761,9.4,104.969,2.9Zm-10.6,4.347,15.026,6.676-4.533,1.993-15.03-6.8Zm-5.711,3.2,15.288,6.863v20.51l-15.279-6.86Zm32.608,20.506-15.781,6.88V16.127l5.053-2.3L113.749,24a.769.769,0,0,0,1.076.7l5.354-2.318a.769.769,0,0,0,.466-.707V10.8l3.814-1.662Z"
                                                    transform="translate(-90.294 0.003)" fill="#70d44b"></path>
                                            </g>
                                        </svg>
                                    </div>
                                    <div class="text-xs text-center w-14 ts:w-auto text-textgray font-normal pb-2 mx-auto">
                                        <a href="">Kargo <br />
                                            Detay</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--                <div class="w-full flex flex-wrap lg:flex-nowrap lg:flex-row border-2 border-bordergray rounded-lg px-2 ts:px-3 py-1 ts:py-3 mb-5">-->
                <!--                    <div class="w-3/12 md:w-2/12 lg:w-1/12">-->
                <!--                        <div class="flex justify-center items-center w-16 md:w-20 h-16 md:h-20 shadow-lg bg-white rounded-2lg">-->
                <!--                            <img class="w-13 md:w-16" src="../../images/<EMAIL>" alt="" />-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                    <div class="w-8/12 md:w-9/12 lg:w-10/12 flex flex-wrap lg:flex-nowrap lg:flex-row">-->
                <!--                        <div class="w-full md:w-6/12 lg:w-3/12 pl-1 lg:pl-8 md:border-r-2 border-bordergray mt-3 lg:mt-0">-->
                <!--                            <div class="text-xs text-black font-normal lg:pb-2">Ürün Adı:</div>-->
                <!--                            <div class="text-xs md:text-sm text-black font-bold">Apple iPhone 13 Mini 128 GB</div>-->
                <!--                        </div>-->
                <!--                        <div class="w-6/12 md:w-3/12 lg:w-2/12 pl-3 lg:pl-1 mt-3 lg:mt-0 md:border-r-2 border-bordergray">-->
                <!--                            <div class="text-xs text-black font-normal lg:pb-2">Sipariş No:</div>-->
                <!--                            <div class="text-xs md:text-sm text-black font-bold">37463301</div>-->
                <!--                            <div class="hidden lg:block text-2xs mt-1 text-black font-medium">27.01.2022 / 13:39</div>-->
                <!--                        </div>-->
                <!--                        <div class="hidden lg:block w-2/12 pl-2 lg:pl-1 border-r-2 border-bordergray">-->
                <!--                            <div class="text-xs text-black font-normal pb-2">Kiralama Süresi:</div>-->
                <!--                            <div class="text-sm text-black font-bold">3 Ay</div>-->
                <!--                        </div>-->
                <!--                        <div class="hidden lg:block w-3/12 pl-2 border-r-2 border-bordergray">-->
                <!--                            <div class="text-xs text-black font-normal pb-2">Aylık Kiralama Ücreti:</div>-->
                <!--                            <div class="text-sm text-black font-bold">350 TL</div>-->
                <!--                        </div>-->
                <!--                        <div class="w-6/12 md:w-3/12 lg:w-2/12 pl-3 mt-3 lg:mt-0">-->
                <!--                            <div class="text-xs text-black font-normal lg:pb-2">Durumu:</div>-->
                <!--                            <div class="text-xs md:text-sm text-kbgreen font-bold">Tamamlandı</div>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                    <div class="w-1/12 pl-3 flex items-center justify-end">-->
                <!--                        <div class="bg-black rounded-full w-6 lg:w-8 h-6 lg:h-8">-->
                <!--                            <a href="" class="flex justify-center items-center w-6 lg:w-8 h-6 lg:h-8">-->
                <!--                                <svg class="w-5 h-5 lg:w-6 lg:h-6" viewBox="0 0 23.872 21.712">-->
                <!--                                    <g id="Group_119" data-name="Group 119" transform="translate(0)">-->
                <!--                                        <path id="Path_19" data-name="Path 19" d="M1.6,21.712A1.528,1.528,0,0,1,0,20.261V1.451A1.528,1.528,0,0,1,1.6,0a1.528,1.528,0,0,1,1.6,1.451v18.81A1.528,1.528,0,0,1,1.6,21.712Z" transform="translate(10.341 0)" fill="#fff"></path>-->
                <!--                                        <path id="Path_20" data-name="Path 20" d="M1.451,23.872A1.528,1.528,0,0,1,0,22.277V1.6A1.528,1.528,0,0,1,1.451,0,1.528,1.528,0,0,1,2.9,1.6V22.277A1.528,1.528,0,0,1,1.451,23.872Z" transform="translate(23.872 9.405) rotate(90)" fill="#fff"></path>-->
                <!--                                    </g>-->
                <!--                                </svg>-->
                <!--                            </a>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </div>-->
                <!--                <div class="w-full flex flex-wrap lg:flex-nowrap lg:flex-row border-2 border-bordergray rounded-lg px-2 ts:px-3 py-1 ts:py-3 mb-5">-->
                <!--                    <div class="w-3/12 md:w-2/12 lg:w-1/12">-->
                <!--                        <div class="flex justify-center items-center w-16 md:w-20 h-16 md:h-20 shadow-lg bg-white rounded-2lg">-->
                <!--                            <img class="w-13 md:w-16" src="../../images/<EMAIL>" alt="" />-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                    <div class="w-8/12 md:w-9/12 lg:w-10/12 flex flex-wrap lg:flex-nowrap lg:flex-row">-->
                <!--                        <div class="w-full md:w-6/12 lg:w-3/12 pl-1 lg:pl-8 md:border-r-2 border-bordergray mt-3 lg:mt-0">-->
                <!--                            <div class="text-xs text-black font-normal lg:pb-2">Ürün Adı:</div>-->
                <!--                            <div class="text-xs md:text-sm text-black font-bold">Apple iPhone 13 Mini 128 GB</div>-->
                <!--                        </div>-->
                <!--                        <div class="w-6/12 md:w-3/12 lg:w-2/12 pl-3 lg:pl-1 mt-3 lg:mt-0 md:border-r-2 border-bordergray">-->
                <!--                            <div class="text-xs text-black font-normal lg:pb-2">Sipariş No:</div>-->
                <!--                            <div class="text-xs md:text-sm text-black font-bold">37463301</div>-->
                <!--                            <div class="hidden lg:block text-2xs mt-1 text-black font-medium">27.01.2022 / 13:39</div>-->
                <!--                        </div>-->
                <!--                        <div class="hidden lg:block w-2/12 pl-2 lg:pl-1 border-r-2 border-bordergray">-->
                <!--                            <div class="text-xs text-black font-normal pb-2">Kiralama Süresi:</div>-->
                <!--                            <div class="text-sm text-black font-bold">3 Ay</div>-->
                <!--                        </div>-->
                <!--                        <div class="hidden lg:block w-3/12 pl-2 border-r-2 border-bordergray">-->
                <!--                            <div class="text-xs text-black font-normal pb-2">Aylık Kiralama Ücreti:</div>-->
                <!--                            <div class="text-sm text-black font-bold">350 TL</div>-->
                <!--                        </div>-->
                <!--                        <div class="w-6/12 md:w-3/12 lg:w-2/12 pl-3 mt-3 lg:mt-0">-->
                <!--                            <div class="text-xs text-black font-normal lg:pb-2">Durumu:</div>-->
                <!--                            <div class="text-xs md:text-sm text-kbgreen font-bold">Tamamlandı</div>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                    <div class="w-1/12 pl-3 flex items-center justify-end">-->
                <!--                        <div class="bg-black rounded-full w-6 lg:w-8 h-6 lg:h-8">-->
                <!--                            <a href="" class="flex justify-center items-center w-6 lg:w-8 h-6 lg:h-8">-->
                <!--                                <svg class="w-5 h-5 lg:w-6 lg:h-6" viewBox="0 0 23.872 21.712">-->
                <!--                                    <g id="Group_119" data-name="Group 119" transform="translate(0)">-->
                <!--                                        <path id="Path_19" data-name="Path 19" d="M1.6,21.712A1.528,1.528,0,0,1,0,20.261V1.451A1.528,1.528,0,0,1,1.6,0a1.528,1.528,0,0,1,1.6,1.451v18.81A1.528,1.528,0,0,1,1.6,21.712Z" transform="translate(10.341 0)" fill="#fff"></path>-->
                <!--                                        <path id="Path_20" data-name="Path 20" d="M1.451,23.872A1.528,1.528,0,0,1,0,22.277V1.6A1.528,1.528,0,0,1,1.451,0,1.528,1.528,0,0,1,2.9,1.6V22.277A1.528,1.528,0,0,1,1.451,23.872Z" transform="translate(23.872 9.405) rotate(90)" fill="#fff"></path>-->
                <!--                                    </g>-->
                <!--                                </svg>-->
                <!--                            </a>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </div>-->
                <!--                <div class="w-full flex flex-wrap lg:flex-nowrap lg:flex-row border-2 border-bordergray rounded-lg px-2 ts:px-3 py-1 ts:py-3 mb-5">-->
                <!--                    <div class="w-3/12 md:w-2/12 lg:w-1/12">-->
                <!--                        <div class="flex justify-center items-center w-16 md:w-20 h-16 md:h-20 shadow-lg bg-white rounded-2lg">-->
                <!--                            <img class="w-13 md:w-16" src="../../images/<EMAIL>" alt="" />-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                    <div class="w-8/12 md:w-9/12 lg:w-10/12 flex flex-wrap lg:flex-nowrap lg:flex-row">-->
                <!--                        <div class="w-full md:w-6/12 lg:w-3/12 pl-1 lg:pl-8 md:border-r-2 border-bordergray mt-3 lg:mt-0">-->
                <!--                            <div class="text-xs text-black font-normal lg:pb-2">Ürün Adı:</div>-->
                <!--                            <div class="text-xs md:text-sm text-black font-bold">Apple iPhone 13 Mini 128 GB</div>-->
                <!--                        </div>-->
                <!--                        <div class="w-6/12 md:w-3/12 lg:w-2/12 pl-3 lg:pl-1 mt-3 lg:mt-0 md:border-r-2 border-bordergray">-->
                <!--                            <div class="text-xs text-black font-normal lg:pb-2">Sipariş No:</div>-->
                <!--                            <div class="text-xs md:text-sm text-black font-bold">37463301</div>-->
                <!--                            <div class="hidden lg:block text-2xs mt-1 text-black font-medium">27.01.2022 / 13:39</div>-->
                <!--                        </div>-->
                <!--                        <div class="hidden lg:block w-2/12 pl-2 lg:pl-1 border-r-2 border-bordergray">-->
                <!--                            <div class="text-xs text-black font-normal pb-2">Kiralama Süresi:</div>-->
                <!--                            <div class="text-sm text-black font-bold">3 Ay</div>-->
                <!--                        </div>-->
                <!--                        <div class="hidden lg:block w-3/12 pl-2 border-r-2 border-bordergray">-->
                <!--                            <div class="text-xs text-black font-normal pb-2">Aylık Kiralama Ücreti:</div>-->
                <!--                            <div class="text-sm text-black font-bold">350 TL</div>-->
                <!--                        </div>-->
                <!--                        <div class="w-6/12 md:w-3/12 lg:w-2/12 pl-3 mt-3 lg:mt-0">-->
                <!--                            <div class="text-xs text-black font-normal lg:pb-2">Durumu:</div>-->
                <!--                            <div class="text-xs md:text-sm text-kbgreen font-bold">Tamamlandı</div>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                    <div class="w-1/12 pl-3 flex items-center justify-end">-->
                <!--                        <div class="bg-black rounded-full w-6 lg:w-8 h-6 lg:h-8">-->
                <!--                            <a href="" class="flex justify-center items-center w-6 lg:w-8 h-6 lg:h-8">-->
                <!--                                <svg class="w-5 h-5 lg:w-6 lg:h-6" viewBox="0 0 23.872 21.712">-->
                <!--                                    <g id="Group_119" data-name="Group 119" transform="translate(0)">-->
                <!--                                        <path id="Path_19" data-name="Path 19" d="M1.6,21.712A1.528,1.528,0,0,1,0,20.261V1.451A1.528,1.528,0,0,1,1.6,0a1.528,1.528,0,0,1,1.6,1.451v18.81A1.528,1.528,0,0,1,1.6,21.712Z" transform="translate(10.341 0)" fill="#fff"></path>-->
                <!--                                        <path id="Path_20" data-name="Path 20" d="M1.451,23.872A1.528,1.528,0,0,1,0,22.277V1.6A1.528,1.528,0,0,1,1.451,0,1.528,1.528,0,0,1,2.9,1.6V22.277A1.528,1.528,0,0,1,1.451,23.872Z" transform="translate(23.872 9.405) rotate(90)" fill="#fff"></path>-->
                <!--                                    </g>-->
                <!--                                </svg>-->
                <!--                            </a>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </div>-->
                <!--                <div class="w-full flex flex-wrap lg:flex-nowrap lg:flex-row border-2 border-bordergray rounded-lg px-2 ts:px-3 py-1 ts:py-3 mb-5">-->
                <!--                    <div class="w-3/12 md:w-2/12 lg:w-1/12">-->
                <!--                        <div class="flex justify-center items-center w-16 md:w-20 h-16 md:h-20 shadow-lg bg-white rounded-2lg">-->
                <!--                            <img class="w-13 md:w-16" src="../../images/<EMAIL>" alt="" />-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                    <div class="w-8/12 md:w-9/12 lg:w-10/12 flex flex-wrap lg:flex-nowrap lg:flex-row">-->
                <!--                        <div class="w-full md:w-6/12 lg:w-3/12 pl-1 lg:pl-8 md:border-r-2 border-bordergray mt-3 lg:mt-0">-->
                <!--                            <div class="text-xs text-black font-normal lg:pb-2">Ürün Adı:</div>-->
                <!--                            <div class="text-xs md:text-sm text-black font-bold">Apple iPhone 13 Mini 128 GB</div>-->
                <!--                        </div>-->
                <!--                        <div class="w-6/12 md:w-3/12 lg:w-2/12 pl-3 lg:pl-1 mt-3 lg:mt-0 md:border-r-2 border-bordergray">-->
                <!--                            <div class="text-xs text-black font-normal lg:pb-2">Sipariş No:</div>-->
                <!--                            <div class="text-xs md:text-sm text-black font-bold">37463301</div>-->
                <!--                            <div class="hidden lg:block text-2xs mt-1 text-black font-medium">27.01.2022 / 13:39</div>-->
                <!--                        </div>-->
                <!--                        <div class="hidden lg:block w-2/12 pl-2 lg:pl-1 border-r-2 border-bordergray">-->
                <!--                            <div class="text-xs text-black font-normal pb-2">Kiralama Süresi:</div>-->
                <!--                            <div class="text-sm text-black font-bold">3 Ay</div>-->
                <!--                        </div>-->
                <!--                        <div class="hidden lg:block w-3/12 pl-2 border-r-2 border-bordergray">-->
                <!--                            <div class="text-xs text-black font-normal pb-2">Aylık Kiralama Ücreti:</div>-->
                <!--                            <div class="text-sm text-black font-bold">350 TL</div>-->
                <!--                        </div>-->
                <!--                        <div class="w-6/12 md:w-3/12 lg:w-2/12 pl-3 mt-3 lg:mt-0">-->
                <!--                            <div class="text-xs text-black font-normal lg:pb-2">Durumu:</div>-->
                <!--                            <div class="text-xs md:text-sm text-kbgreen font-bold">Tamamlandı</div>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                    <div class="w-1/12 pl-3 flex items-center justify-end">-->
                <!--                        <div class="bg-black rounded-full w-6 lg:w-8 h-6 lg:h-8">-->
                <!--                            <a href="" class="flex justify-center items-center w-6 lg:w-8 h-6 lg:h-8">-->
                <!--                                <svg class="w-5 h-5 lg:w-6 lg:h-6" viewBox="0 0 23.872 21.712">-->
                <!--                                    <g id="Group_119" data-name="Group 119" transform="translate(0)">-->
                <!--                                        <path id="Path_19" data-name="Path 19" d="M1.6,21.712A1.528,1.528,0,0,1,0,20.261V1.451A1.528,1.528,0,0,1,1.6,0a1.528,1.528,0,0,1,1.6,1.451v18.81A1.528,1.528,0,0,1,1.6,21.712Z" transform="translate(10.341 0)" fill="#fff"></path>-->
                <!--                                        <path id="Path_20" data-name="Path 20" d="M1.451,23.872A1.528,1.528,0,0,1,0,22.277V1.6A1.528,1.528,0,0,1,1.451,0,1.528,1.528,0,0,1,2.9,1.6V22.277A1.528,1.528,0,0,1,1.451,23.872Z" transform="translate(23.872 9.405) rotate(90)" fill="#fff"></path>-->
                <!--                                    </g>-->
                <!--                                </svg>-->
                <!--                            </a>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </div>-->
            </div>
        </section>
    </main>
</template>
