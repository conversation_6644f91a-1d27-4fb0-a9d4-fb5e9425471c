<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import CategoryBox from "@/Pages/Shared/CategoryBox.vue";
import VerticalProductBox from "@/Pages/Shared/VerticalProductBox.vue";
import ProductBox from "@/Pages/Shared/ProductBox.vue";
import VerticalProductList from "@/Pages/Shared/VerticalProductList.vue";
import Pagination from "@/Pages/Shared/Pagination.vue";
import Loader from "@/Pages/Shared/Loader.vue";
import { Inertia } from "@inertiajs/inertia";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import { Disclosure, DisclosureButton, DisclosurePanel } from "@headlessui/vue";
import _ from "lodash";

var ev;
export default {
    components: {
        Loader,
        Pagination,
        Link,
        Head,
        CategoryBox,
        ProductBox,
        VerticalProductBox,
        VerticalProductList,
        Splide,
        SplideSlide,
        SplideTrack,
        Disclosure,
        DisclosureButton,
        DisclosurePanel
    },
    props: {
        discounted: Object,
        category: Object,
        brands: Object,
        filters: Object,
        orderBy: String,
        categories: Object,
        pageType: String,
        paginateType: String,
        auth: Object,
        mobilefilter: false,
        mostRentedProducts: Object,
        canonical: String,
        categoryNote: String,
        showCategoryNote: Boolean,
        newTypeSeoDefinition: {
            type: Boolean,
            default: false
        },
        title: String,
        meta_description: String,
        meta_keywords: String
    },
    data() {
        return {
            hiddenBtnBrands: true,
            // Component başlatılırken grid type'ı URL'den oku
            selectedGridType: this.getInitialGridType(),
            selectedFilters: {
                brand: this.filters.brand?.split(",") ?? [],
                price: this.filters.price?.split(",") ?? [],
                category: this.filters.collections?.split(",") ?? [],
                color: [],
                storage: [],
                os: [],
                orderBy: ""
            },
            categoryItems: this.category,
            hiddenSeoText: true
        };
    },
    methods: {
        validateGridType(gridType) {
            // Güvenlik kontrolü: Sadece önceden tanımlı değerleri kabul et
            const validGridTypes = ["category-box", "verticalGrid"];

            // XSS ve injection saldırılarına karşı koruma
            if (typeof gridType !== "string") {
                return false;
            }

            // Geçerli seçeneklerden biri mi kontrol et
            return validGridTypes.includes(gridType);
        },

        getValidatedGridType(gridType) {
            // Fallback mekanizması: Geçersiz değerlerde category-box kullan
            return this.validateGridType(gridType) ? gridType : "category-box";
        },
        getGridTypeFromSession() {
            try {
                return sessionStorage.getItem("selectedGridType");
            } catch (error) {
                console.warn("Session storage okuma hatası:", error);
                return null;
            }
        },
        saveGridTypeToSession(gridType) {
            try {
                sessionStorage.setItem("selectedGridType", gridType);
                // console.log("GridType session'a kaydedildi:", gridType);
            } catch (error) {
                console.warn("Session storage yazma hatası:", error);
            }
        },
        getInitialGridType(urlOverride = null) {
            try {
                // Geçerli grid type değerleri
                const validGridTypes = ["category-box", "verticalGrid"];

                // Önce URL'den kontrol et
                let targetUrl;
                if (urlOverride) {
                    targetUrl = new URL(urlOverride, window.location.origin);
                    // console.log("getInitialGridType: urlOverride kullanıldı:", urlOverride);
                } else if (this.$page && this.$page.url) {
                    targetUrl = new URL(this.$page.url, window.location.origin);
                    // console.log("getInitialGridType: $page.url kullanıldı:", this.$page.url);
                } else {
                    targetUrl = new URL(window.location.href);
                    // console.log("getInitialGridType: window.location kullanıldı:", window.location.href);
                }

                const urlGridType = targetUrl.searchParams.get("gridType");
                // console.log("getInitialGridType: URL'den okunan gridType:", urlGridType);

                // Eğer URL'de geçerli bir gridType varsa onu kullan ve session'a kaydet
                if (validGridTypes.includes(urlGridType)) {
                    // console.log("getInitialGridType: URL'den geçerli gridType bulundu:", urlGridType);
                    this.saveGridTypeToSession(urlGridType);
                    return urlGridType;
                }

                // URL'de yoksa session'dan kontrol et
                const sessionGridType = this.getGridTypeFromSession();
                if (sessionGridType && validGridTypes.includes(sessionGridType)) {
                    // console.log("getInitialGridType: Session'dan gridType alındı:", sessionGridType);
                    return sessionGridType;
                }

                // Hiçbiri yoksa default döndür
                // console.log("getInitialGridType: Default gridType kullanıldı: category-box");
                return "category-box";
            } catch (error) {
                console.warn("Grid type okuma hatası:", error);
                return "category-box"; // Fallback
            }
        },
        addToFilter(type, value) {
            if (type == "orderBy") {
                this.selectedFilters[type] = value;
            } else {
                // check if value is already in array
                if (this.selectedFilters[type].find((e) => e == value)) {
                    // remove value from array
                    this.selectedFilters[type] = this.selectedFilters[type].filter((e) => e != value);
                } else {
                    // add value to array
                    this.selectedFilters[type].push(value);
                }
            }

            // GridType'ı da URL parametrelerine ekle
            const params = {
                "filter[brand]": this.selectedFilters.brand.join(","), // join array to string
                "filter[price]": this.selectedFilters.price.join(","), // join array to string,
                "filter[collections]": this.selectedFilters.category.join(","), // join array to string,
                orderBy: this.selectedFilters.orderBy
            };

            // Mevcut gridType'ı koru
            if (this.selectedGridType && this.selectedGridType !== "category-box") {
                params.gridType = this.selectedGridType;
            }

            this.$inertia.get(`/kategoriler/${this.category.items.slug ?? "tum-urunler"}`, params);
        },
        splidedArray(arr, size) {
            return arr.reduce((acc, e, i) => (i % size ? acc[acc.length - 1].push(e) : acc.push([e]), acc), []);
        },
        orderCategories() {
            console.log("orderCategories");
            window.cats = this.categories;
            return this.categories;
            return Object.values(this.categories).sort((a, b) => {
                return a.name?.name?.tr.localeCompare(b.name?.name?.tr);
                // return a.collection_name.localeCompare(b.collection_name);
            });
        },
        slugify(text) {
            return _.kebabCase(text);
        },
        getComponentProps(product, index) {
            // Her grid type için uygun props'ları hazırla
            const baseProps = {
                key: index,
                product: product,
                auth: this.auth
            };

            // Grid type'a göre ek props
            if (this.selectedGridType === "category-box") {
                return {
                    ...baseProps
                    // CategoryBox için özel props
                };
            } else if (this.selectedGridType === "verticalGrid") {
                return {
                    ...baseProps
                    // VerticalProductList için özel props
                };
            }

            return baseProps;
        },
        updateUrlWithGridType(gridType) {
            try {
                // Geçerli grid type'ı doğrula
                const validatedGridType = this.getValidatedGridType(gridType);

                // URL'ı güncelle
                const url = new URL(window.location);
                url.searchParams.set("gridType", validatedGridType);

                // Browser history'yi bozmadan parametre ekle/güncelle
                window.history.replaceState({}, "", url);

                // SEO dostu URL yapısını koru
            } catch (error) {
                console.error("Grid tipi URL güncelleme hatası:", error);
            }
        },
        changeGridType(newType) {
            // Grid type değiştiğinde URL'ı anında güncelle
            const validatedType = this.getValidatedGridType(newType);

            // Component state'ini yeni grid type'a göre ayarla
            this.selectedGridType = validatedType;

            // Session'a kaydet
            this.saveGridTypeToSession(validatedType);

            // URL'ı güncelle
            this.updateUrlWithGridType(validatedType);

            // Template'in yeniden render edilmesini sağla
            this.$nextTick(() => {
            });
        },
        handleUrlChange(eventUrl = null) {
            // URL değişikliklerini dinle ve component'i güncelle
            // console.log("handleUrlChange çağrıldı, eventUrl:", eventUrl);
            // console.log("handleUrlChange öncesi selectedGridType:", this.selectedGridType);
            const newGridType = this.getInitialGridType(eventUrl);
            if (newGridType !== this.selectedGridType) {
                console.log("selectedGridType değişiyor:", this.selectedGridType, "->", newGridType);
                this.selectedGridType = newGridType;
                // Session'a kaydet
                this.saveGridTypeToSession(newGridType);
            }
            // else {
            //     console.log("selectedGridType aynı kaldı:", this.selectedGridType);
            // }
        }
    },
    computed: {
        categoriesOrdered() {
            return this.orderCategories();
        },
        splidedMostRentedProducts() {
            return this.splidedArray(this.mostRentedProducts.items.data, 1);
        },
        splidedDiscounted() {
            // Take first 8 items than split them into 2
            return this.splidedArray(this.discounted.items.data.slice(0, 16), 1);
        },
        isAraPage() {
            return this.$page.url.includes("/ara");
        }
    },
    created() {
        console.log("CategoryGrid created");
        this.selectedFilters.orderBy = this.orderBy;

        // Component başlatıldığında selectedGridType'ı doğru set et
        const initialGridType = this.getInitialGridType();
        if (initialGridType !== this.selectedGridType) {
            // console.log("selectedGridType güncellendi:", this.selectedGridType, "->", initialGridType);
            this.selectedGridType = initialGridType;
            // Session'a kaydet
            this.saveGridTypeToSession(initialGridType);
        }

        // Sadece arama sayfasında ve sonuç yok ise o zaman tavsiye ürünleri getir
        if (this.pageType === "search" && this.categoryItems.items?.data?.length === 0) {
            // this.categoryItems = this.category.items.element.products;
            Inertia.reload({
                only: ["mostRentedProducts"]
            });
        }

        ev = this.$inertia.on("success", (event) => {
            //console.log("Kategpri", event.detail.page.url);

            // URL değişikliği sonrasında gridType'ı güncelle
            this.handleUrlChange(event.detail.page.url);

            let datas = this.categoryItems.items.data;
            if (this.pageType === "category") {
                datas = this.categoryItems.items.element.products.data;
            }

            let categoryProducts = [];
            datas.map((item, keys) => {
                let product = {};

                let subscribetionMonthsOrdered = [];
                if (item.variants[0]?.prices.length > 0) subscribetionMonthsOrdered = Object.entries(item.variants[0]?.prices).sort(([, a], [, b]) => a.subscription_months.value - b.subscription_months.value);
                let nonZeroPrices = item.variants[0]?.prices.filter((price) => price.price.value > 0);
                subscribetionMonthsOrdered = subscribetionMonthsOrdered.filter((price) => price[1].price.value > 0);
                let productPrice = subscribetionMonthsOrdered[+Object.keys(nonZeroPrices).length - 1]?.[1]?.price?.value / 100;

                product.item_id = item.id;
                product.item_name = item.attribute_data.name.tr;
                product.price = productPrice;
                product.item_brand = item.brand.name;
                product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                //product.item_color = item.variants[0]?.options[0] ?? ""; // Kategori verisidne yok
                product.item_list_id = "HOME-1";
                product.item_list_name = "Homepage - Kampanya Ürünleri List";
                product.index = keys;
                categoryProducts.push(product);
            });

            if (event.detail.page.url.startsWith("/indirimli-urunler")) {
                dataLayer.push({
                    event: "view_item_list",
                    ecommerce: {
                        item_list_id: "HOME-IUL-2",
                        item_list_name: "Category - İndirimli Ürünler Sayfasi List",
                        items: categoryProducts
                    }
                });
            } else if (event.detail.page.url.startsWith("/ara")) {
                dataLayer.push({
                    event: "view_item_list",
                    ecommerce: {
                        item_list_id: "HOME-SP",
                        item_list_name: "Category - Search List",
                        items: categoryProducts
                    }
                });
            } else if (event.detail.page.url.startsWith("/kategoriler")) {
                //console.log(this.category);
                dataLayer.push({
                    event: "view_item_list",
                    ecommerce: {
                        item_list_id: "HOME-" + this.category.items.element?.id,
                        item_list_name: "Category - " + this.category.items.element?.attribute_data?.name?.tr + " List",
                        items: categoryProducts
                    }
                });
            }
        });
    },
    layout: Layout,
    mounted() {
        console.log("CategoryGrid mounted");

        // Browser'ın geri/ileri butonları için URL değişikliklerini dinle
        window.addEventListener("popstate", this.handleUrlChange);
    },
    beforeUnmount() {
        // Event listener'ı temizle
        window.removeEventListener("popstate", this.handleUrlChange);

        // Inertia event listener'ını da temizle
        if (ev) {
            ev();
        }
    }
};
</script>

<template>
    <template v-if="!newTypeSeoDefinition">
        <Head :title="category?.items?.element?.title">
            <meta name="description" :content="category?.items?.element?.meta_description" />
            <link rel="canonical" :href="canonical" />
        </Head>
    </template>
    <template v-else>
        <Head :title="title">
            <meta name="description" :content="meta_description" />
            <link rel="canonical" :href="canonical" />
        </Head>
    </template>

    <main class="my-3 mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-4 mx-auto">
        <div class="promotion-bar w-full px-5 py-1 bg-[#FCF3EC] flex justify-center rounded-lg border-1 border-[#FCF3EC] mb-3" v-if="false">
            <div class="font-bold text-base">Yeni üyelere özel 6 ay ve üzeri kiralamaların ilk ayında 500 tl indirim Kiralabunu’dan hediye! İndirim kodu: merhaba500</div>
        </div>
        <section class="mb-12 flex flex-row">
            <div class="w-full bg-[#0000007a] absolute z-60 h-[700vh] left-0 block md:hidden" v-if="mobilefilter" @click="mobilefilter = !mobilefilter"></div>
            <div v-if="mobilefilter" class="w-6/12 flex md:hidden flex-col justify-start items-start absolute z-60 h-[700vh] left-0 bg-white">
                <div class="w-full p-3 ts:p-5 bg-white z-80" v-if="categories !== undefined">
                    <div class="max-h-[310px] overflow-y-scroll">
                        <div class="w-full flex justify-start items-center mb-2" v-for="(category, index) in categoriesOrdered" :key="index">
                            <input :id="`category-${category.id}`" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.category.find((e) => e == category.id)" @click="addToFilter('category', category.id)" />
                            <label class="text-base text-black pl-2 cursor-pointer font-santralregular" :for="`category-${category.id}`">{{ category.name?.name?.tr }}</label>
                        </div>
                    </div>
                </div>
                <div class="z-80 w-full border-t-4 border-white bg-white p-3 lg:p-5">
                    <div class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Markalar</div>
                    <div class="max-h-[310px] overflow-y-scroll">
                        <div class="w-full flex justify-start items-center mb-2" v-for="(brand, index) in brands" :key="index">
                            <input :id="brand.name" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.brand.find((e) => e == brand.id)" @click="addToFilter('brand', brand.id)" />
                            <label class="text-base text-black pl-2 cursor-pointer font-santralregular" :for="brand.name">{{ brand.name }}</label>
                        </div>
                    </div>

                    <!--                    <p class="w-full text-sm font-medium text-black pt-1 pl-4">-->
                    <!--                        <a href=""><u>Daha Fazla</u></a>-->
                    <!--                    </p>-->
                </div>
                <div class="z-80 w-full border-t-4 border-white bg-white p-3 lg:p-5" v-if="false">
                    <div class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">İşletim Sistemi</div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="Android" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                        <label class="text-base text-black pl-2" for="Android">Android</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="iOS" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                        <label class="text-base text-black pl-2" for="iOS">iOS</label>
                    </div>
                </div>
                <div class="z-80 w-full border-t-4 border-white bg-white py-3 lg:py-5" v-if="true">
                    <div class="px-3 lg:px-5 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Fiyat Aralığı</div>
                    <div v-if="false" class="px-1 lg:px-1 w-full flex justify-around items-center mb-2">
                        <input id="min" class="p-2 mr-1 px-1 w-1/3 rounded-lg border-2 border-textgray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-xs tracking-wider" type="text" placeholder="En Az" name="min" />
                        <input id="max" class="p-2 mr-1 px-1 w-1/3 rounded-lg border-2 border-textgray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-xs tracking-wider" type="text" placeholder="En Çok" name="max" />
                        <button class="w-1/3 flex items-center bg-kbblue text-white rounded-lg py-2 px-1 self-center text-xs font-bold">
                            Ara
                            <svg class="ml-1" width="20" height="20" viewBox="0 0 24.61 24.611">
                                <defs>
                                    <clipPath id="clip-path">
                                        <path id="Mask" d="M8.991,0a8.991,8.991,0,1,0,5.845,15.815l.381.378v1.1l6.914,6.9,2.062-2.063-6.9-6.915h-1.1l-.382-.38A8.982,8.982,0,0,0,8.991,0Zm0,15.215a6.224,6.224,0,1,1,6.224-6.224A6.231,6.231,0,0,1,8.991,15.215Z" transform="matrix(1, -0.017, 0.017, 1, -0.209, 0.213)" fill="none" />
                                    </clipPath>
                                </defs>
                                <g id="Icon_Search_Sharp" data-name="Icon / Search / Sharp" transform="translate(0.209 0.209)">
                                    <path id="Mask-2" data-name="Mask" d="M8.991,0a8.991,8.991,0,1,0,5.845,15.815l.381.378v1.1l6.914,6.9,2.062-2.063-6.9-6.915h-1.1l-.382-.38A8.982,8.982,0,0,0,8.991,0Zm0,15.215a6.224,6.224,0,1,1,6.224-6.224A6.231,6.231,0,0,1,8.991,15.215Z" fill="none" />
                                    <g id="Icon_Search_Sharp-2" data-name="Icon / Search / Sharp" clip-path="url(#clip-path)">
                                        x
                                        <g id="_Icon_Color" data-name="↳ Icon Color" transform="translate(-4.15 -3.884)">
                                            <rect id="Rectangle" width="24.197" height="23.653" fill="#fff" />
                                        </g>
                                    </g>
                                </g>
                            </svg>
                        </button>
                    </div>
                    <div class="px-3 lg:px-5 w-full flex justify-start items-center mb-2">
                        <input id="1100" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.price.filter((e) => e === '1-500').length == 1" @click="addToFilter('price', '1-500')" />
                        <label class="text-base text-black pl-2 cursor-pointer font-santralregular" for="1100">500 Altı</label>
                    </div>
                    <div class="px-3 lg:px-5 w-full flex justify-start items-center mb-2">
                        <input id="100250" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.price.filter((e) => e === '500-1500').length == 1" @click="addToFilter('price', '500-1500')" />
                        <label class="text-base text-black pl-2 cursor-pointer font-santralregular" for="100250">500 - 1500</label>
                    </div>
                    <div class="px-3 lg:px-5 w-full flex justify-start items-center mb-2">
                        <input id="250500" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.price.filter((e) => e === '1500-3000').length == 1" @click="addToFilter('price', '1500-3000')" />
                        <label class="text-base text-black pl-2 cursor-pointer font-santralregular" for="250500">1500 - 3000</label>
                    </div>
                    <div class="px-3 lg:px-5 w-full flex justify-start items-center">
                        <input id="2000uzeri" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.price.filter((e) => e === '3000-999999').length == 1" @click="addToFilter('price', '3000-999999')" />
                        <label class="text-base text-black pl-2 cursor-pointer font-santralregular" for="2000uzeri">3000 Üzeri</label>
                    </div>
                </div>
                <div class="z-80 w-full border-t-4 border-white bg-white p-3 lg:p-5" v-if="false">
                    <div class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Depolama</div>
                    <div class="max-h-[310px] overflow-y-scroll">
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="32GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="32GB">32 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="64GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="64GB">64 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="128GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="128GB">128 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="256GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="256GB">256 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="512GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="512GB">512 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="1TB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="1TB">1TB</label>
                        </div>
                    </div>
                </div>
                <div class="z-80 w-full border-t-4 border-white bg-white p-3 lg:p-5 rounded-b-2lg" v-if="false">
                    <div class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Hafıza (RAM)</div>
                    <div class="max-h-[310px] overflow-y-scroll">
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="3GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="3GB">3 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="4GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="4GB">4 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="6GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="6GB">6 GB </label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="8GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="8GB">8 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="12GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="12GB">12 GB </label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="16GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="16GB">16 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="32GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="32GB">32 GB</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="w-4/12 md:w-3/12 lg:w-2/12 hidden md:flex flex-col justify-start items-start rounded-lg">
                <div class="w-full py-5 px-1" v-if="categories != undefined">
                    <div class="max-h-[310px] overflow-y-scroll">
                        <div class="w-full flex justify-start items-center mb-2" v-for="(categoryname, index) in categoriesOrdered" :key="index">
                            <!-- <pre>{{ categoryname }}</pre> -->
                            <Link :href="`/kategoriler/${categoryname.slug}`" class="flex justify-start items-center mb-2">
                                <div class="rounded-full bg-bordergray p-2 w-[40px]">
                                    <img :src="`https://kiralabunu.fra1.digitaloceanspaces.com/${categoryname.icon}`" alt="" class="w-full" />
                                </div>
                                <div class="text-sm text-black pl-2 cursor-pointer font-santralregular w-[calc(100% - 40px)]">
                                    {{ categoryname.collection_name }}
                                </div>
                            </Link>
                        </div>
                    </div>
                </div>

                <div class="w-full bg-kb-mid-grey p-5 rounded-t-2lg" v-if="false && categories != undefined">
                    <div class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Kategoriler</div>
                    <div class="max-h-[310px] overflow-y-scroll">
                        <div class="w-full flex justify-start items-center mb-2" v-for="(categoryname, index) in categoriesOrdered" :key="index">
                            <input :id="`category-${categoryname.id}`" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.category.find((e) => e == categoryname.id)" @click="addToFilter('category', categoryname.id)" />
                            <label class="text-base text-black pl-2 cursor-pointer font-santralregular" :for="`category-${categoryname.id}`">{{ categoryname.collection_name }}</label>
                        </div>
                    </div>
                </div>

                <div class="w-full border-t-4 border-white bg-kb-mid-grey p-3 lg:p-5" :class="[categories != undefined ? '' : 'rounded-t-2lg']">
                    <div class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Markalar</div>
                    <div class="w-full flex justify-start items-center mb-2" v-for="(brand, index) in brands.slice(0, 5)" :key="index">
                        <input :id="brand.name" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.brand.find((e) => e == brand.id)" @click="addToFilter('brand', brand.id)" />
                        <label class="text-base text-black pl-2 cursor-pointer font-santralregular" :for="brand.name">{{ brand.name }}</label>
                    </div>
                    <Disclosure as="div" v-slot="{ open }">
                        <DisclosurePanel class="">
                            <div class="w-full flex justify-start items-center mb-2" v-for="(brand, index) in brands.slice(5)" :key="index">
                                <input :id="brand.name" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.brand.find((e) => e == brand.id)" @click="addToFilter('brand', brand.id)" />
                                <label class="text-base text-black pl-2 cursor-pointer font-santralregular" :for="brand.name">{{ brand.name }}</label>
                            </div>
                        </DisclosurePanel>
                        <DisclosureButton @click="hiddenBtnBrands = !hiddenBtnBrands" v-if="hiddenBtnBrands" class="bg-white text-xs text-kbgray border-1 border-kbgray rounded-full py-1 px-2 self-center font-santralextrabold mt-2 hover:bg-kbgreen hover:text-white hover:border-white"> Daha Fazla</DisclosureButton>
                    </Disclosure>
                </div>
                <div class="w-full border-t-4 border-white bg-kb-mid-grey p-3 lg:p-5" v-if="false">
                    <div class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">İşletim Sistemi</div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="Android" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                        <label class="text-base text-black pl-2" for="Android">Android</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-2">
                        <input id="iOS" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                        <label class="text-base text-black pl-2" for="iOS">iOS</label>
                    </div>
                </div>
                <div class="w-full border-t-4 border-white bg-kb-mid-grey py-3 lg:py-5" v-if="true">
                    <div class="px-3 lg:px-5 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Fiyat Aralığı</div>
                    <div v-if="false" class="px-1 lg:px-1 w-full flex justify-around items-center mb-2">
                        <input id="min" class="p-2 mr-1 px-1 w-1/3 rounded-lg border-2 border-textgray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-xs tracking-wider" type="text" placeholder="En Az" name="min" />
                        <input id="max" class="p-2 mr-1 px-1 w-1/3 rounded-lg border-2 border-textgray placeholder:text-xs placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-xs tracking-wider" type="text" placeholder="En Çok" name="max" />
                        <button class="w-1/3 flex items-center bg-kbblue text-white rounded-lg py-2 px-1 self-center text-xs font-bold">
                            Ara
                            <svg class="ml-1" width="20" height="20" viewBox="0 0 24.61 24.611">
                                <defs>
                                    <clipPath id="clip-path">
                                        <path id="Mask" d="M8.991,0a8.991,8.991,0,1,0,5.845,15.815l.381.378v1.1l6.914,6.9,2.062-2.063-6.9-6.915h-1.1l-.382-.38A8.982,8.982,0,0,0,8.991,0Zm0,15.215a6.224,6.224,0,1,1,6.224-6.224A6.231,6.231,0,0,1,8.991,15.215Z" transform="matrix(1, -0.017, 0.017, 1, -0.209, 0.213)" fill="none" />
                                    </clipPath>
                                </defs>
                                <g id="Icon_Search_Sharp" data-name="Icon / Search / Sharp" transform="translate(0.209 0.209)">
                                    <path id="Mask-2" data-name="Mask" d="M8.991,0a8.991,8.991,0,1,0,5.845,15.815l.381.378v1.1l6.914,6.9,2.062-2.063-6.9-6.915h-1.1l-.382-.38A8.982,8.982,0,0,0,8.991,0Zm0,15.215a6.224,6.224,0,1,1,6.224-6.224A6.231,6.231,0,0,1,8.991,15.215Z" fill="none" />
                                    <g id="Icon_Search_Sharp-2" data-name="Icon / Search / Sharp" clip-path="url(#clip-path)">
                                        x
                                        <g id="_Icon_Color" data-name="↳ Icon Color" transform="translate(-4.15 -3.884)">
                                            <rect id="Rectangle" width="24.197" height="23.653" fill="#fff" />
                                        </g>
                                    </g>
                                </g>
                            </svg>
                        </button>
                    </div>
                    <div class="px-3 lg:px-5 w-full flex justify-start items-center mb-2">
                        <input id="1100" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.price.filter((e) => e === '1-500').length == 1" @click="addToFilter('price', '1-500')" />
                        <label class="text-base text-black pl-2 cursor-pointer font-santralregular" for="1100">500 Altı</label>
                    </div>
                    <div class="px-3 lg:px-5 w-full flex justify-start items-center mb-2">
                        <input id="100250" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.price.filter((e) => e === '500-1500').length == 1" @click="addToFilter('price', '500-1500')" />
                        <label class="text-base text-black pl-2 cursor-pointer font-santralregular" for="100250">500 - 1500</label>
                    </div>
                    <div class="px-3 lg:px-5 w-full flex justify-start items-center mb-2">
                        <input id="250500" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.price.filter((e) => e === '1500-3000').length == 1" @click="addToFilter('price', '1500-3000')" />
                        <label class="text-base text-black pl-2 cursor-pointer font-santralregular" for="250500">1500 - 3000</label>
                    </div>
                    <div class="px-3 lg:px-5 w-full flex justify-start items-center">
                        <input id="2000uzeri" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" :checked="selectedFilters.price.filter((e) => e === '3000-999999').length == 1" @click="addToFilter('price', '3000-999999')" />
                        <label class="text-base text-black pl-2 cursor-pointer font-santralregular" for="2000uzeri">3000 Üzeri</label>
                    </div>
                </div>
                <div class="w-full border-t-4 border-white bg-kb-mid-grey p-3 lg:p-5" v-if="false">
                    <div class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Depolama</div>
                    <div class="max-h-[310px] overflow-y-scroll">
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="32GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="32GB">32 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="64GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="64GB">64 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="128GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="128GB">128 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="256GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="256GB">256 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="512GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="512GB">512 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="1TB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="1TB">1TB</label>
                        </div>
                    </div>
                </div>
                <div class="w-full border-t-4 border-white bg-kb-mid-grey p-3 lg:p-5 rounded-b-2lg" v-if="false">
                    <div class="p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">Hafıza (RAM)</div>
                    <div class="max-h-[310px] overflow-y-scroll">
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="3GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="3GB">3 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="4GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="4GB">4 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="6GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="6GB">6 GB </label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="8GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="8GB">8 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="12GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="12GB">12 GB </label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="16GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="16GB">16 GB</label>
                        </div>
                        <div class="w-full flex justify-start items-center mb-2">
                            <input id="32GB" class="w-5 h-5 border-2 border-black rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                            <label class="text-base text-black pl-2" for="32GB">32 GB</label>
                        </div>
                    </div>
                </div>
            </div>
            <!--mobile-->
            <div class="w-full md:w-9/12 lg:w-10/12 ts:pl-3.5 lg:pt-2">
                <div class="mb-4" v-if="false">
                    <div class="flex w-full flex-col">
                        <div class="flex w-full justify-between">
                            <div class="text-2xl md:text-3xl my-2 lg:my-0 mx-0 lg:mx-4 self-center text-center w-full md:w-auto">İndirimli Ürünler</div>
                            <div class="flex-1 ml-6 hidden md:flex">
                                <div class="flex-1 self-center border border-gray-200"></div>
                                <Link href="/indirimli-urunler" class="cursor-pointer text-sm font-santralextrabold flex justify-center items-center border-2 rounded-full px-4 border-2 whitespace-nowrap md:ml-4 bg-white hover:bg-kbgreen hover:text-white"> Tümünü Gör</Link>
                            </div>
                        </div>
                        <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0">
                            <Splide :has-track="false" aria-label="" :options="{ rewind: true, perPage: 3, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 3 } } }" v-if="discounted">
                                <SplideTrack>
                                    <SplideSlide v-for="(productGroup, index) in splidedDiscounted" :key="index" class="flex">
                                        <category-box v-for="(product, index) in productGroup" :product="product" :new-container-classes="`w-full`" :auth="auth" />
                                    </SplideSlide>
                                </SplideTrack>
                                <div class="splide__arrows">
                                    <button class="splide__arrow splide__arrow--prev !border-2 !border-solid !border-bordergray">
                                        <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                            <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z" transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                        </svg>
                                    </button>
                                    <button class="splide__arrow splide__arrow--next !border-2 !border-solid !border-bordergray">
                                        <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                            <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z" transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                        </svg>
                                    </button>
                                </div>
                            </Splide>
                            <div v-else>
                                <loader :active="true" message="Please wait 5 seconds" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex w-full justify-between" v-if="false">
                    <div class="text-2xl md:text-3xl my-5 mx-0 lg:mx-4 self-center text-center w-full md:w-auto">Tüm Ürünler</div>
                    <div class="flex-1 ml-6 hidden md:flex">
                        <div class="flex-1 self-center border border-gray-200"></div>
                    </div>
                </div>
                <div class="flex w-full md:px-4 justify-between items-center">
                    <div class="flex md:hidden ml-3">
                        <svg @click="mobilefilter = !mobilefilter" id="filter-circle" xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25">
                            <path id="Path_3751" data-name="Path 3751" d="M12.5,23.438A10.938,10.938,0,1,0,1.563,12.5,10.937,10.937,0,0,0,12.5,23.438ZM12.5,25A12.5,12.5,0,1,0,0,12.5,12.5,12.5,0,0,0,12.5,25Z" fill-rule="evenodd" />
                            <path
                                id="Path_3752"
                                data-name="Path 3752"
                                d="M13,21.406a.781.781,0,0,1,.781-.781h1.563a.781.781,0,1,1,0,1.563H13.781A.781.781,0,0,1,13,21.406ZM9.875,16.719a.781.781,0,0,1,.781-.781h7.813a.781.781,0,1,1,0,1.563H10.656A.781.781,0,0,1,9.875,16.719ZM6.75,12.031a.781.781,0,0,1,.781-.781H21.594a.781.781,0,0,1,0,1.563H7.531A.781.781,0,0,1,6.75,12.031Z"
                                transform="translate(-2.063 -3.438)"
                                fill-rule="evenodd"
                            />
                        </svg>
                        <p @click="mobilefilter = !mobilefilter" class="whitespace-nowrap text-sm font-medium text-black pt-1 pl-4">Filtrele</p>
                    </div>
                    <div class="hidden md:flex space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32.57" height="33.621" class="cursor-pointer" @click="changeGridType('category-box')" :class="[selectedGridType == 'category-box' ? '' : 'opacity-40']" viewBox="0 0 32.57 33.621">
                            <g id="Group_753" data-name="Group 753">
                                <g id="Rectangle_165" data-name="Rectangle 165" transform="translate(0 0)" fill="#fff" stroke="#231f20" stroke-width="2">
                                    <rect width="32.57" height="33.621" rx="5" stroke="none" />
                                    <rect x="1" y="1" width="30.57" height="31.621" rx="4" fill="none" />
                                </g>
                                <g id="Group_753-2" data-name="Group 753" transform="translate(25.963 5.332) rotate(90)">
                                    <rect id="Rectangle_166" data-name="Rectangle 166" width="23.251" height="5.425" rx="2" fill="#231f20" />
                                    <rect id="Rectangle_167" data-name="Rectangle 167" width="23.251" height="5.425" rx="2" transform="translate(0 6.975)" fill="#231f20" />
                                    <rect id="Rectangle_168" data-name="Rectangle 168" width="23.251" height="5.425" rx="2" transform="translate(0 13.95)" fill="#231f20" />
                                </g>
                            </g>
                        </svg>

                        <svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" class="cursor-pointer" @click="changeGridType('verticalGrid')" :class="[selectedGridType == 'verticalGrid' ? '' : 'opacity-40']" viewBox="0 0 34 34">
                            <g id="Group_754" data-name="Group 754" transform="translate(0.379)">
                                <g id="Rectangle_169" data-name="Rectangle 169" transform="translate(-0.379)" fill="#fff" stroke="#231f20" stroke-width="2">
                                    <rect width="34" height="34" rx="5" stroke="none" />
                                    <rect x="1" y="1" width="32" height="32" rx="4" fill="none" />
                                </g>
                                <g id="Group_756" data-name="Group 756" transform="translate(4.803 7.037)">
                                    <g id="Group_755" data-name="Group 755" transform="translate(7.204)">
                                        <rect id="Rectangle_166" data-name="Rectangle 166" width="16.81" height="5.473" rx="2" fill="#231f20" />
                                        <rect id="Rectangle_167" data-name="Rectangle 167" width="16.81" height="5.473" rx="2" transform="translate(0 7.037)" fill="#231f20" />
                                        <rect id="Rectangle_168" data-name="Rectangle 168" width="16.81" height="5.473" rx="2" transform="translate(0 14.074)" fill="#231f20" />
                                    </g>
                                    <g id="Group_754-2" data-name="Group 754">
                                        <rect id="Rectangle_166-2" data-name="Rectangle 166" width="5.603" height="5.473" rx="2" fill="#231f20" />
                                        <rect id="Rectangle_167-2" data-name="Rectangle 167" width="5.603" height="5.473" rx="2" transform="translate(0 7.037)" fill="#231f20" />
                                        <rect id="Rectangle_168-2" data-name="Rectangle 168" width="5.603" height="5.473" rx="2" transform="translate(0 14.074)" fill="#231f20" />
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </div>
                    <select class="hidden mts:block w-52 bg-kb-mid-grey p-1 pl-2 rounded-2lg border-3 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-sm tracking-wider" name="sirala" id="sirala" v-model="selectedFilters.orderBy" @change="addToFilter('orderBy', selectedFilters.orderBy)">
                        <option value="">Sırala</option>
                        <option value="artan-fiyat">Fiyata Göre (Artan)</option>
                        <option value="azalan-fiyat">Fiyata Göre (Azalan)</option>
                        <option value="yeniden-eskiye">Yeniden Eskiye</option>
                        <option value="eskiden-yeniye">Eskiden Yeniye</option>
                    </select>
                </div>
                <div class="mt-6 mb-12 flex flex-col lg:flex-row border-2 border-bordergray rounded-lg items-center justify-center" v-if="pageType === 'search' && categoryItems.items?.data?.length == 0">
                    <div class="flex justify-center items-center bg-kbgreen rounded-full w-28 h-28 m-5">
                        <svg xmlns="http://www.w3.org/2000/svg" width="43.015" height="78.955" viewBox="0 0 43.015 78.955">
                            <g id="Group_4141" data-name="Group 4141" transform="translate(0 0)">
                                <path
                                    id="Path_3349"
                                    data-name="Path 3349"
                                    d="M473.847,415.2H472.2a5.587,5.587,0,0,1-5.581-5.581v-1.387a17.076,17.076,0,0,1,1.934-8.548,44.963,44.963,0,0,1,5.417-7.278,32.075,32.075,0,0,0,3.683-4.439,7.467,7.467,0,0,0,1.163-4.13c0-3.445-2.245-5.121-6.863-5.121a27.868,27.868,0,0,0-7.564,1.135,6.368,6.368,0,0,1-8.116-6.123v-2.807a6.246,6.246,0,0,1,3.685-5.725,36.563,36.563,0,0,1,27-.235,21.4,21.4,0,0,1,9.074,6.877,16.563,16.563,0,0,1,3.255,10.039,16.327,16.327,0,0,1-3.147,10.04,57.393,57.393,0,0,1-8.8,8.966,62.707,62.707,0,0,0-5.642,5.315,9.811,9.811,0,0,0-2.346,4.367,5.566,5.566,0,0,1-5.5,4.636Z"
                                    transform="translate(-456.268 -362.491)"
                                    fill="#fff"
                                ></path>
                                <path id="Path_3350" data-name="Path 3350" d="M480.951,518.1h-.218a10.344,10.344,0,0,1,0-20.688h.218a10.344,10.344,0,0,1,0,20.688Z" transform="translate(-464.291 -439.141)" fill="#fff"></path>
                            </g>
                        </svg>
                    </div>
                    <div class="text-black text-base">
                        <div class="p-3 text-center">
                            Aradığınız ürün bulunamadı. <br />
                            <a href="#popular-product" class="underline">Bunları arıyor olabilirsin</a>
                        </div>
                    </div>
                </div>
                <div class="flex flex-wrap">
                    <VerticalProductList v-for="(product, index) in categoryItems.items.element.products.data" :key="index" :product="product" :auth="auth" v-if="selectedGridType === 'verticalGrid' && pageType === 'category'"></VerticalProductList>
                    <VerticalProductList v-for="(product, index) in categoryItems.items.data" :key="index" :product="product" :auth="auth" v-if="selectedGridType === 'verticalGrid' && pageType === 'search'"></VerticalProductList>
                    <category-box v-for="(product, index) in categoryItems.items.element.products.data" :key="index" :product="product" v-if="selectedGridType === 'category-box' && pageType === 'category'" :auth="auth" />
                    <!--                    <category-box v-for="(product, index) in categoryItems.items.data" :key="index" :product="product" v-if="selectedGridType === 'category-box' && pageType === 'category'" :auth="auth" />-->
                    <category-box v-for="(product, index) in categoryItems.items.data" :key="index" :product="product" :auth="auth" v-if="selectedGridType === 'category-box' && pageType === 'search'" />
                </div>
                <div class="w-full flex justify-center mt-5">
                    <!--                    <pagination :links="categoryItems.items.pagination.links" v-if="selectedGridType === 'category-box' && pageType === 'category'" />-->
                    <!--                    <pagination :links="categoryItems.links" v-if="selectedGridType === 'category-box' && pageType === 'search' && paginateType !== 'tag'" />-->
                    <!--                    <VuePaginationTw :total-items="5" :current-page="1" :per-page="5" @page-changed="functionName" :go-button="false" styled="centered" :borderActiveColor="border - kbgreen" />-->
                    <!--                    <pagination :links="categoryItems.links" v-if="selectedGridType === 'category-box' && pageType === 'search' && paginateType !== 'tag'" />-->

                    <pagination :links="categoryItems.items.element.products.pagination.links" :gridType="selectedGridType" v-if="pageType === 'category'" />
                    <pagination :links="categoryItems.items?.pagination?.links" :gridType="selectedGridType" v-if="pageType === 'search' && paginateType !== 'tag' && !isAraPage" />
                    <pagination :links="categoryItems.items?.pagination?.links" :gridType="selectedGridType" v-if="paginateType === 'tag'" />
                </div>
            </div>
        </section>
        <section class="flex" v-if="showCategoryNote && categoryNote">
            <div class="w-4/12 md:w-3/12 lg:w-2/12 hidden md:flex flex-col justify-start items-start">&nbsp;</div>
            <div class="w-full md:w-9/12 lg:w-10/12 ts:pl-3.5 lg:pt-2">
                <div class="text-justify font-santralregular text-sm" v-html="categoryNote.split('---')[0]"></div>
                <div v-show="!hiddenSeoText" class="text-justify font-santralregular text-sm" v-html="categoryNote.split('---')[1]"></div>
                <button @click="hiddenSeoText = !hiddenSeoText" v-show="hiddenSeoText && categoryNote.split('---')[1] != null" class="bg-white text-xs text-kbgray border-1 border-kbgray rounded-full py-1 px-2 self-center font-santralextrabold mt-2 hover:bg-kbgreen hover:text-white hover:border-white">Daha Fazla</button>
            </div>
        </section>
    </main>

    <section id="popular-product" class="mt-14" v-if="pageType === 'search' && categoryItems.items?.data?.length == 0">
        <div class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
            <div class="flex">
                <div class="text-2xl ts:text-3xl self-center md:text-left ts:mx-4 w-full lg:w-auto">En Popüler Ürünler</div>
                <div class="hidden md:flex md:w-2/3 flex-1 ml-6">
                    <div class="flex-1 self-center border-1 border-bordergray"></div>
                    <!--                    <Link href="#" class="font-santralextrabold border text-sm whitespace-nowrap rounded-full py-1 flex justify-center items-center px-4 border-2 md:w-32 ml-4 hover:bg-kbgreen hover:text-white"> Tümünü Gör</Link>-->
                </div>
            </div>
            <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0 lg:mt-9">
                <Splide :has-track="false" aria-label="" :options="{ rewind: true, perPage: 4, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 4 } } }" v-if="mostRentedProducts">
                    <SplideTrack>
                        <SplideSlide class="flex" v-for="(productGroup, index) in splidedMostRentedProducts" :key="index">
                            <category-box v-for="(product, index) in productGroup" :key="index" :product="product" :new-container-classes="`w-full`" :auth="auth" />
                        </SplideSlide>
                    </SplideTrack>
                    <div class="splide__arrows">
                        <button class="splide__arrow splide__arrow--prev !-left-9">
                            <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z" transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                        <button class="splide__arrow splide__arrow--next !-right-9">
                            <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z" transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                    </div>
                </Splide>
                <div v-else>
                    <loader :active="true" message="Please wait 5 seconds" />
                </div>
            </div>
        </div>
    </section>
</template>
