<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/Layout.vue";
import CheckoutPaymentInfo from "@/Components/CheckoutPaymentInfo.vue";
import CartProductPreview from "@/Components/CartProductPreview.vue";
import CartAddresses from "@/Pages/Shared/CartAddresses.vue";
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from "@headlessui/vue";
import Datepicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import { vMaska } from "maska";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import OnBilgilendirmeFormu from "@/Pages/Shared/OnBilgilendirmeFormu.vue";
import MesafeliKiralamaSozlesmesi from "@/Pages/Shared/MesafeliKiralamaSozlesmesi.vue";

let intervalCode = null;

export default {
    components: {
        OnBilgilendirmeFormu,
        MesafeliKiralamaSozlesmesi,
        Link,
        Head,
        CheckoutPaymentInfo,
        CartProductPreview,
        CartAddresses,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        Splide,
        SplideSlide,
        SplideTrack,
        Datepicker
    },
    directives: { maska: vMaska },
    props: {
        products: Array,
        total: Number,
        discount_amount: Number,
        sub_total: Number,
        insurance: Number,
        user: Object,
        auth: Object,
        cards: Object,
        errors: { type: Object, default: false },
        paymentType: String,
        validationMessage: String,
        cities: Array,
        coupon: { type: Object, default: false },
        hopi_campaigns: { type: Object, default: false },
        hopi_bird: { type: Number, default: 0 },
        hopi_balance: { type: Number, default: 0 },
        non_mass_payment_total: { type: Number, default: 0 },
        is_mass_payment_enabled: { type: Boolean, default: false },
        paymnentValidationMessage: { type: String, default: null },
        tosla_active: { type: Boolean, default: false }
    },
    data() {
        return {
            form: this.$inertia.form({
                // first_name: JSON.parse(window.localStorage.getItem("user")).first_name,
                // last_name: JSON.parse(window.localStorage.getItem("user")).last_name,
                // tckn: JSON.parse(window.localStorage.getItem("user")).tckn,
                // date_of_birth: JSON.parse(window.localStorage.getItem("user")).birth_date,
                first_name: this.user?.first_name ?? "",
                last_name: this.user?.last_name ?? "",
                tckn: this.user?.tckn ?? "",
                date_of_birth: this.user?.birth_date ?? ""
            }),
            ccForm: this.$inertia.form({
                holder: null,
                number: null,
                month: null,
                year: null,
                cvc: null,
                source: "cart"
            }),
            isOpen: false,
            isOpen2: false,
            showCCForm: this.paymentType == "nonRegisteredCard" ? true : false,
            selectedCC: null,
            formSubmitted: false,
            errorText: "",
            onBilgilendirme: false,
            mesafeliKiralama: false,
            selectedHopiCampaign: localStorage.getItem("selectedHopiCampaign"),
            enteredHopiBalance: localStorage.getItem("enteredHopiBalance") ?? 0,
            formSubmiting: false
        };
    },
    methods: {
        submitCardSave() {
            this.ccForm.post(route("saveCCOnPaymentScreen"));
        },
        closeModal() {
            this.isOpen = false;
        },
        closeModal2() {
            this.isOpen2 = false;
        },
        openModal() {
            this.isOpen = true;
        },
        openModal2() {
            this.isOpen2 = true;
        },
        submitTCKN() {
            console.log(this.form);
            this.form.post("/tckn-dogrula");
        },
        compeletePaymentWithRegisteredCard() {
            console.log("compeletePaymentWithRegisteredCard");

            let uyelik = document.getElementById("uyelik");
            let aydinlatma = document.getElementById("aydinlatma");

            if (uyelik.checked == false) {
                uyelik.setCustomValidity("Ön Bilgilenme Formunu Onaylamalısınız.");
                uyelik.reportValidity();

                return;
            }

            if (aydinlatma.checked == false) {
                aydinlatma.setCustomValidity("Mesafeli Kiralama Sözleşmesini Onaylamalısınız.");
                aydinlatma.reportValidity();
                return;
            }

            if (this.selectedCC == null) {
                this.errorText = "Lütfen bir kart seçiniz.";
                return;
            }

            if (this.user.addresses.length == 0) {
                this.errorText = "Lütfen bir adres ekleyiniz.";
                return;
            }

            this.formSubmitted = true;
            this.formSubmiting = true;

            this.sendPaymentRequest();

            if (this.selectedCC.id < 0)
                intervalCode = setInterval(() => this.sendPaymentRequest(), 10000);
        },
        sendPaymentRequest() {
            this.$inertia.post("/compelete-payment-with-registered-card", {
                cc: this.selectedCC.id,
                hopiCampaign: localStorage.getItem("selectedHopiCampaign"),
                enteredBalance: localStorage.getItem("enteredHopiBalance"),
                birdId: this.hopi_bird
            }, {
                onSuccess: () => {
                    this.formSubmiting = false;
                    //clearInterval(intervalCode);
                    console.log("sendPaymentRequest succeed so form closed");
                },
                onError: () => {
                    this.formSubmiting = false;
                    clearInterval(intervalCode);
                    console.error("sendPaymentRequest failed so form closed");
                }
            });
        },
        preventNumericInput($event) {
            console.log($event.keyCode); //will display the keyCode value
            //console.log($event.key); will show the key value

            var charCode = $event.keyCode ? $event.keyCode : $event.which;
            if (
                (charCode <= 93 && charCode >= 65) ||
                (charCode <= 122 && charCode >= 97) ||
                charCode == 32 ||
                charCode == 8 ||
                charCode == 350 ||
                charCode == 351 ||
                charCode == 304 ||
                charCode == 286 ||
                charCode == 287 ||
                charCode == 231 ||
                charCode == 199 ||
                charCode == 305 ||
                charCode == 214 ||
                charCode == 246 ||
                charCode == 220 ||
                charCode == 252 ||
                charCode == 32
            ) {
                if (charCode != 33 || charCode != 43) {
                    return true;
                } else {
                    $event.preventDefault();
                }
            } else {
                $event.preventDefault();
            }
        },
        creditCardSelected(card) {
            this.selectedCC = card;
            console.log(this.selectedCC);
            let cartProducts = [];
            this.products.map((item) => {
                let product = {};
                product.item_id = item.id;
                product.item_name = item.product.attribute_data.name.tr;
                product.price = item.total;
                // product.item_brand = item.product.brand.name;
                // product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                // product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                // (product.item_color = item.variants[0]?.options[0] ?? ""), (product.index = 0);
                product.discount = 0;
                product.item_variant = item.month?.name;
                product.quantity = item.quantity;
                cartProducts.push(product);
            });

            dataLayer.push({
                event: "add_payment_info",
                ecommerce: {
                    currency: "TRY",
                    value: this.total,
                    coupon: null,
                    shipping_tier: "Address",
                    payment_type: card.card_association, // or Mastercard
                    items: cartProducts
                }
            });
        },
        initPayment() {
            let cartProducts = [];
            this.products.map((item) => {
                let product = {};
                product.item_id = item.id;
                product.item_name = item.product.attribute_data.name.tr;
                product.price = item.total;
                // product.item_brand = item.product.brand.name;
                // product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                // product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                // (product.item_color = item.variants[0]?.options[0] ?? ""), (product.index = 0);
                product.discount = 0;
                product.item_variant = item.month?.name;
                product.quantity = item.quantity;
                cartProducts.push(product);
            });

            dataLayer.push({
                event: "begin_checkout",
                ecommerce: {
                    currency: "TRY",
                    value: this.total,
                    items: cartProducts
                }
            });
        }
    },
    created() {
        this.$inertia.on("navigate", (event) => {
            console.log("ödeme navigate", event.detail.page.url);

            let cartProducts = [];
            this.products.map((item) => {
                let product = {};
                product.item_id = item.id;
                product.item_name = item.product.attribute_data.name.tr;
                product.price = item.total;
                // product.item_brand = item.product.brand.name;
                // product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                // product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                // (product.item_color = item.variants[0]?.options[0] ?? ""), (product.index = 0);
                product.discount = 0;
                product.item_variant = item.month?.name;
                product.quantity = item.quantity;
                cartProducts.push(product);
            });

            if (event.detail.page.url.startsWith("/odeme")) {
                dataLayer.push({
                    event: "add_shipping_info",
                    ecommerce: {
                        currency: "TRY",
                        value: this.total,
                        shipping_tier: "Address",
                        items: cartProducts
                    }
                });
            }

            console.log("dataLayer", dataLayer);

            // Destroy the interval before navigating to a new page. This is important!
            clearInterval(intervalCode); // this is important!
        });
    },
    layout: Layout
};
</script>

<template class="relative">

    <Head title="Ödeme Yöntemi" />

    <div class="fixed inset-0 hidden bg-gray-500 bg-opacity-75 z-80 transition-opacity md:block" v-if="paymnentValidationMessage || formSubmiting">
        <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div class="flex min-h-full items-stretch justify-center text-center md:items-center md:px-2 lg:px-4">
                <div aria-label="Loading..." class="flex items-center space-x-2 bg-white text-3xl m-4 p-10 rounded-2lg" role="status">
                    <svg class="h-20 w-20 animate-spin stroke-gray-500" viewBox="0 0 256 256">
                        <line x1="128" y1="32" x2="128" y2="64" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                        <line x1="195.9" y1="60.1" x2="173.3" y2="82.7" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                        <line x1="224" y1="128" x2="192" y2="128" stroke-linecap="round" stroke-linejoin="round" stroke-width="24">
                        </line>
                        <line x1="195.9" y1="195.9" x2="173.3" y2="173.3" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                        <line x1="128" y1="224" x2="128" y2="192" stroke-linecap="round" stroke-linejoin="round" stroke-width="24">
                        </line>
                        <line x1="60.1" y1="195.9" x2="82.7" y2="173.3" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                        <line x1="32" y1="128" x2="64" y2="128" stroke-linecap="round" stroke-linejoin="round" stroke-width="24"></line>
                        <line x1="60.1" y1="60.1" x2="82.7" y2="82.7" stroke-linecap="round" stroke-linejoin="round" stroke-width="24">
                        </line>
                    </svg>
                    <span class="text-4xl font-medium text-gray-500">{{ paymnentValidationMessage ?? "Bankadan cevap bekleniyor" }}...</span>
                </div>
            </div>
        </div>
    </div>

    <main class="my-3 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-4 mx-auto">
        <section class="flex justify-center items-center">
            <p class="px-2 py-1 font-santralextrabold text-base text-textgray border-2 border-textgray rounded-full">Teslimat</p>
            <svg xmlns="http://www.w3.org/2000/svg" width="142.787" height="1" viewBox="0 0 142.787 1">
                <path id="Path_2907" data-name="Path 2907" d="M10647.213,14347.5H10790" transform="translate(-10647.213 -14347)" fill="none" stroke="#d0d0d0" stroke-width="1" stroke-dasharray="5" />
            </svg>
            <p class="px-2 py-1 font-santralextrabold text-base border-2 border-kbgreen rounded-full">Ödeme</p>
        </section>
        <section class="flex w-full justify-center lg:justify-between mt-8 lg:mt-0">
            <div class="w-10/12 lg:w-3/12 flex justify-between items-end border-b-3 border-kbgreen">
                <div class="flex">
                    <svg id="wallet" xmlns="http://www.w3.org/2000/svg" width="28.904" height="25" viewBox="0 0 28.904 25">
                        <path id="Path_2913" data-name="Path 2913"
                            d="M29.609,7.645a1.432,1.432,0,0,0-1.021-.424h-1.02v-2.4a1.444,1.444,0,0,0-1.443-1.443H5.369A4.244,4.244,0,0,0,1.125,7.619V24.131a4.244,4.244,0,0,0,4.244,4.244H28.53a1.445,1.445,0,0,0,1.443-1.438l.056-18.269a1.432,1.432,0,0,0-.421-1.023ZM28.051,26.452H5.369a2.321,2.321,0,0,1-2.321-2.321V7.619A2.321,2.321,0,0,1,5.369,5.3H25.645V7.221H5.452V9.144H28.105Z"
                            transform="translate(-1.125 -3.375)" fill="#231f20" />
                        <path id="Path_2914" data-name="Path 2914" d="M27.563,18.563h1.923v1.923H27.563Z" transform="translate(-4.966 -5.582)" fill="#231f20" />
                    </svg>
                    <h3 class="p-0 pl-5 text-3xl text-left text-gray-900 box-border whitespace-no-wrap">Ödeme</h3>
                </div>
            </div>
        </section>
        <div class="w-full hidden lg:flex justify-start items-center mt-4">
            <p class="text-xl text-black">Ödeme Yöntemi</p>
        </div>
        <section class="mt-2 mb-12 flex flex-col lg:flex-row">
            <div class="w-full lg:w-9/12 pr-0 lg:pr-4 px-1 lg:px-0">
                <div class="w-full flex justify-center" v-if="Object.entries(errors).length > 0">
                    <div class="w-full md:w-2/4 text-xs text-center text-kbdvred font-sans" v-for="error in errors">{{ error }}</div>
                </div>
                <div class="w-full ts:w-11/12" v-if="!showCCForm && user?.tckn_verified_at && cards.length > 0">
                    <Splide class="w-full" :has-track="false" aria-label="" :options="{ rewind: true, perPage: 3, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 } } }">
                        <SplideTrack>
                            <SplideSlide class="px-4" v-for="(card, index) in cards" :key="index">
                                <div class="transition-all w-full mr-2 mb-10 mt-10 max-w-[314px] ts:max-w-[256px] md:h-[190px] ts:h-[168px] relative p-3 rounded-2lg bg-gradient-to-r from-[#5f4af4] to-[#423881] cursor-pointer"
                                    :class="[selectedCC == card ? 'border-4 border-red-400 scale-110' : '']" @click="creditCardSelected(card)">
                                    <div class="flex relative z-40">
                                        <div class="w-9/12 mx-auto">
                                            <p class="text-xs font-santralextrabold text-left text-white mt-2 md:mt-1 ts:mt-2">
                                                {{ card.alias }}
                                            </p>
                                            <p class="text-xs text-left text-white mt-4 md:mt-3 lg:mt-3">
                                                {{ card.holder }}
                                            </p>
                                            <p class="text-xs text-left text-white mt-4 md:mt-3 lg:mt-3">{{ card.bin_number }} **** {{ card.number }}</p>
                                            <p class="text-xs text-left text-white mt-7 md:mt-7 lg:mt-7">**/{{ card.year }}</p>
                                        </div>
                                        <div class="w-2/12 flex flex-col justify-around">
                                            <div class="text-right w-full mt-0 ts:mt-3">
                                                <img class="w-full" src="../../images/visa.png" alt="" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </SplideSlide>
                            <SplideSlide class="px-4" :key="-9999" v-if="tosla_active">
                                <div class="relative my-10 rounded-2lg transition-all cursor-pointer" :class="[selectedCC?.id == -9999 ? 'border-4 border-blue-400 scale-110' : '']" @click="creditCardSelected({ id: -9999 })">
                                    <picture>
                                        <source srcset="../../images/billing/tosla-card-desktop.png" type="image/png">
                                        <img class="hidden ts:block w-full max-w-[314px] p-0 m-0" src="../../images/billing/tosla-card-desktop.png" />
                                    </picture>
                                    <picture>
                                        <source srcset="../../images/billing/tosla-card-mobile.png" type="image/png">
                                        <img src="../../images/billing/tosla-card-mobile.png" class="block ts:hidden w-full max-w-[314px] p-0 m-0" />
                                    </picture>
                                    <!--                                    <div class="transition-all w-full h-full mr-2 p-3 absolute top-0 left-0">-->
                                    <!--                                        <div class="flex relative z-40">-->
                                    <!--                                            <div class="w-full mx-auto">-->
                                    <!--                                                <p class="text-xs font-santralextrabold text-left text-white mt-2 md:mt-1 ts:mt-2">-->
                                    <!--                                                    TOSLA-->
                                    <!--                                                </p>-->
                                    <!--                                                <p class="text-xs text-left text-white mt-4 md:mt-3 lg:mt-3">-->
                                    <!--                                                    TOSLA HOLDER-->
                                    <!--                                                </p>-->
                                    <!--                                                <p class="text-xs text-left text-white mt-4 md:mt-3 lg:mt-3">**** **** ****</p>-->
                                    <!--                                                <p class="text-xs text-left text-white mt-7 md:mt-7 lg:mt-7">**/24</p>-->
                                    <!--                                            </div>-->
                                    <!--                                        </div>-->
                                    <!--                                    </div>-->
                                </div>
                            </SplideSlide>
                        </SplideTrack>
                        <div class="splide__arrows">
                            <button class="splide__arrow splide__arrow--prev !-left-9">
                                <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                        transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                            <button class="splide__arrow splide__arrow--next !-right-9">
                                <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                        transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                        </div>
                    </Splide>
                    <div class="flex justify-center items-center w-full h-full">
                        <div @click="showCCForm = true" class="cursor-pointer bg-black text-white rounded-full py-3 px-5 self-center text-base font-santralextrabold whitespace-nowrap hover:bg-kbgreen">Yeni Kart ile Öde</div>
                    </div>
                </div>
                <!--                <div class="mt-3 flex flex-wrap justify-start mb-10 self-stretch max-h-[355px] overflow-y-scroll"-->
                <!--                     v-if="!showCCForm && user.tckn_verified_at && cards.length > 0">-->
                <!--                    <div v-for="(card, index) in cards" :key="index"-->
                <!--                         class="w-full md:w-5/12 mr-2 mb-2 max-w-[276px] ts:w-[266px] ts:h-[168px] relative p-3 rounded-2lg bg-gradient-to-r from-[#5f4af4] to-[#423881] cursor-pointer"-->
                <!--                         :class="[selectedCC == card ? 'border-4 border-red-400' : '']" @click="selectedCC = card">-->
                <!--                        <div class="flex relative z-40">-->
                <!--                            <div class="w-9/12 mx-auto">-->
                <!--                                <p class="text-xs font-bold text-left text-white mt-2 md:mt-1 ts:mt-2">{{ card.alias-->
                <!--                                    }}</p>-->
                <!--                                <p class="text-xs text-left text-white mt-4 md:mt-3 lg:mt-3">{{ card.holder }}</p>-->
                <!--                                <p class="text-xs text-left text-white mt-4 md:mt-3 lg:mt-3">{{ card.bin_number }} ****-->
                <!--                                    {{ card.number }}</p>-->
                <!--                                <p class="text-xs text-left text-white mt-7 md:mt-7 lg:mt-7">**/{{ card.year }}</p>-->
                <!--                            </div>-->
                <!--                            <div class="w-2/12 flex flex-col justify-around">-->
                <!--                                <div class="text-right w-full mt-0 ts:mt-3">-->
                <!--                                    <img class="w-full" src="../../images/visa.png" alt="" />-->
                <!--                                </div>-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                    </div>-->

                <!--                    <div-->
                <!--                        class="w-full md:w-5/12 mt-12 md:mt-0 h-44 md:h-[175px] lg:h-[195px] ts:w-[266px] ts:h-[168px] relative md:ml-3 border-3 border-bordergray rounded-lg hover:border-black cursor-pointer"-->
                <!--                        @click="showCCForm = true">-->
                <!--                        <div class="flex justify-center items-center w-full h-full">-->
                <!--                            <div-->
                <!--                                class="bg-black text-white rounded-full py-3 px-5 self-center text-base font-bold whitespace-nowrap hover:bg-kbgreen">-->
                <!--                                Yeni Kart ile Öde-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </div>-->
                <form class="mt-3" v-if="(showCCForm && user?.tckn_verified_at) || (cards.length == 0 && user?.tckn_verified_at)" @submit.prevent="submitCardSave">
                    <div class="flex flex-col-reverse lg:flex-row justify-around items-start w-full">
                        <div class="w-full lg:w-6/12 text-center relative">
                            <div class="relative group">
                                <input id="fullname"
                                    class="peer w-full mb-5 rounded-2lg hover:border-acordion-green group-hover:placeholder-white border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="holder" autofocus v-model="ccForm.holder" @keypress="preventNumericInput" />
                                <label for="fullname"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Ad
                                    Soyad*</label>
                            </div>
                            <div class="relative group">
                                <input id="cardnumber" v-maska data-maska="#### #### #### ####"
                                    class="peer w-full mb-5 hover:border-acordion-green group-hover:placeholder-white rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="number" v-model="ccForm.number" />
                                <label for="cardnumber"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Kart
                                    Numarası*</label>
                            </div>
                            <div class="flex w-full pb-1">
                                <label class="w-9/12 ts:w-7/12 pl-1 text-left font-medium text-sm"> Son Kullanma Tarihi: </label>
                                <label class="w-3/12 ts:w-5/12 ml-5 text-left font-medium text-sm"> CVV : </label>
                            </div>
                            <div class="flex">
                                <div class="relative group mr-4 flex space-x-3 w-2/3">
                                    <!--                                    <input-->
                                    <!--                                        id="month"-->
                                    <!--                                        v-maska-->
                                    <!--                                        data-maska="##"-->
                                    <!--                                        class="hover:border-acordion-green group-hover:placeholder-white w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"-->
                                    <!--                                        required-->
                                    <!--                                        type="text"-->
                                    <!--                                        placeholder="Ay*"-->
                                    <!--                                        name="month"-->
                                    <!--                                        v-model="ccForm.month"-->
                                    <!--                                    />-->

                                    <select name="month" id="month" required v-model="ccForm.month"
                                        class="hover:border-acordion-green group-hover:placeholder-white w-40 md:w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider">
                                        <option value="Ay" selected disabled>Ay</option>
                                        <option value="01">01</option>
                                        <option value="02">02</option>
                                        <option value="03">03</option>
                                        <option value="04">04</option>
                                        <option value="05">05</option>
                                        <option value="06">06</option>
                                        <option value="07">07</option>
                                        <option value="08">08</option>
                                        <option value="09">09</option>
                                        <option value="10">10</option>
                                        <option value="11">11</option>
                                        <option value="12">12</option>
                                    </select>
                                    <select name="year" id="year" required v-model="ccForm.year"
                                        class="hover:border-acordion-green group-hover:placeholder-white w-40 md:w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider">
                                        <option value="Ay" selected disabled>Yıl</option>
                                        <option value="2025">2025</option>
                                        <option value="2026">2026</option>
                                        <option value="2027">2027</option>
                                        <option value="2028">2028</option>
                                        <option value="2029">2029</option>
                                        <option value="2030">2030</option>
                                        <option value="2031">2031</option>
                                        <option value="2032">2032</option>
                                        <option value="2033">2033</option>
                                        <option value="2034">2034</option>
                                        <option value="2035">2035</option>
                                    </select>
                                </div>
                                <!--                                <div class="relative group mr-4">-->
                                <!--                                                                        <input-->
                                <!--                                                                            id="year"-->
                                <!--                                                                            v-maska-->
                                <!--                                                                            data-maska="20##"-->
                                <!--                                                                            class="w-full mb-5 mr-4 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white text-base tracking-wider"-->
                                <!--                                                                            required-->
                                <!--                                                                            type="text"-->
                                <!--                                                                            placeholder="Yıl*"-->
                                <!--                                                                            name="year"-->
                                <!--                                                                            v-model="ccForm.year"-->
                                <!--                                                                        />-->
                                <!--                                </div>-->
                                <div class="relative group ts:mr-4">
                                    <input id="cvc"
                                        class="w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white text-base tracking-wider"
                                        required type="number" placeholder="CVV*" name="cvc" v-model="ccForm.cvc" v-maska data-maska="####" />
                                </div>
                            </div>

                            <div class="flex mt-4 w-80 md:w-96 self-center justify-between" v-if="Object.entries(errors).length > 0">
                                <div class="text-xs mx-auto text-red-900" v-for="error in errors">{{ error }}</div>
                            </div>
                        </div>
                        <div class="w-11/12 md:w-5/12 mx-auto lg:w-4/12 text-center mb-16 lg:mb-0 relative min-h-[140px] md:minh-[]">
                            <img class="w-full max-w-[360px] mx-auto" src="../../images/kart.jpg" alt="" />

                            <!--                            <img class="absolute top-0 left-0 z-0 shadow hover:shadow-md rounded-2lg"-->
                            <!--                                 src="../../images/card-empty.png" alt="" />-->
                            <!--                            <div class="flex relative z-40">-->
                            <!--                                <div class="w-10/12 pl-3">-->
                            <!--                                    <p class="text-base font-bold text-left text-white mt-4 md:mt-4 lg:mt-4">Alışveriş Kartım</p>-->
                            <!--                                    <p class="text-sm text-left text-white mt-4 md:mt-4 lg:mt-4">{{ this.ccForm.holder-->
                            <!--                                        }}</p>-->
                            <!--                                    <p class="text-xs text-left text-white mt-4 md:mt-4 lg:mt-4">{{ this.ccForm.number-->
                            <!--                                        }}</p>-->
                            <!--                                    <p class="text-sm text-left text-white mt-5 mb-4 md:mt-4 lg:mt-4">-->
                            <!--                                        {{ this.ccForm.month }} {{ this.ccForm.year }}</p>-->
                            <!--                                </div>-->
                            <!--                                <div class="w-2/12 flex flex-col justify-around pr-4">-->
                            <!--                                    <div class="w-full flex justify-center">-->
                            <!--                                        <a href="">-->
                            <!--                                            <svg id="edit" width="18.888" height="20.462" viewBox="0 0 18.888 20.462">-->
                            <!--                                                <path id="Path_162" data-name="Path 162" d="M2.25,29.25H21.138v1.574H2.25Z" transform="translate(-2.25 -10.362)" fill="#fff" />-->
                            <!--                                                <path id="Path_163" data-name="Path 163" d="M21.341,7.759a1.521,1.521,0,0,0,0-2.2L18.508,2.722a1.521,1.521,0,0,0-2.2,0L4.5,14.527v5.037H9.537ZM17.407,3.824,20.24,6.657,17.879,9.018,15.046,6.185ZM6.074,17.99V15.157l7.87-7.87,2.833,2.833-7.87,7.87Z" transform="translate(-2.926 -2.25)" fill="#fff" />-->
                            <!--                                            </svg>-->
                            <!--                                        </a>-->
                            <!--                                    </div>-->
                            <!--                                    <div class="w-full flex justify-center">-->
                            <!--                                        <a href="">-->
                            <!--                                <svg id="delete" xmlns="http://www.w3.org/2000/svg" width="17.538" height="20.462" viewBox="0 0 17.538 20.462">-->
                            <!--                                    <path id="Path_164" data-name="Path 164" d="M13.5,13.5h1.462v8.769H13.5Z" transform="translate(-7.654 -6.192)" fill="#fff"/>-->
                            <!--                                    <path id="Path_165" data-name="Path 165" d="M20.25,13.5h1.462v8.769H20.25Z" transform="translate(-10.019 -6.192)" fill="#fff"/>-->
                            <!--                                    <path id="Path_166" data-name="Path 166" d="M4.5,6.75V8.212H5.962V22.827a1.462,1.462,0,0,0,1.462,1.462H19.115a1.462,1.462,0,0,0,1.462-1.462V8.212h1.462V6.75ZM7.423,22.827V8.212H19.115V22.827Z" transform="translate(-4.5 -3.827)" fill="#fff"/>-->
                            <!--                                    <path id="Path_167" data-name="Path 167" d="M13.5,2.25h5.846V3.712H13.5Z" transform="translate(-7.654 -2.25)" fill="#fff"/>-->
                            <!--                                </svg>-->
                            <!--                                        </a>-->
                            <!--                                    </div>-->
                            <!--                                    <div class="text-right w-full mt-5 md:mt-0 lg:mt-5">-->
                            <!--                                        <img class="w-full" src="../../images/visa.png" alt="" />-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                        </div>
                    </div>
                    <div class="w-full mt-0 relative">
                        <div class="flex justify-center items-center w-full h-full">
                            <div v-if="cards.length != 0" @click="showCCForm = false" class="cursor-pointer bg-black text-white rounded-full py-2 px-5 self-center text-base font-santralextrabold whitespace-nowrap hover:bg-kbgreen">Geri Dön</div>
                            <button type="submit" :disabled="ccForm.processing" class="ml-5 cursor-pointer bg-black text-white rounded-full py-2 px-5 self-center text-base font-santralextrabold whitespace-nowrap hover:bg-kbgreen">Kartı
                                Kaydet</button>
                            <!--                            <button class="w-full ts:w-auto bg-black text-white rounded-full py-2 px-5 text-base font-bold whitespace-nowrap" type="submit" :disabled="form.processing">Kartı Ekle</button>-->
                        </div>
                    </div>
                </form>

                <form @submit.prevent="submitTCKN" v-if="user?.tckn_verified_at == null">
                    <div class="w-full lg:w-8/12 text-center relative flex flex-wrap">
                        <p class="w-full text-center relative mb-5">Kiralamayı tamamlaman için kimlik doğrulama gerekli.</p>
                    </div>
                    <div class="flex flex-col-reverse lg:flex-row justify-start items-start w-full">
                        <div class="w-full lg:w-8/12 text-center relative flex flex-wrap">
                            <div class="relative group w-full md:w-6/12 px-1">
                                <input id="name"
                                    class="peer w-full mb-6 rounded-2lg hover:border-acordion-green group-hover:placeholder-white border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    type="text" placeholder="" name="name" v-model="form.first_name" required />
                                <label for="name"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-5 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Ad*</label>
                            </div>
                            <div class="relative group w-full md:w-6/12 px-1">
                                <input id="surname"
                                    class="peer w-full mb-6 rounded-2lg hover:border-acordion-green group-hover:placeholder-white border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="holder" v-model="form.last_name" />
                                <label for="surname"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-5 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Soyad*</label>
                            </div>
                            <div class="relative group w-full md:w-6/12 px-1">
                                <input id="tckn" v-maska data-maska="###########"
                                    class="peer w-full mb-6 rounded-2lg hover:border-acordion-green group-hover:placeholder-white border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="tckn" v-model="form.tckn" />
                                <label for="tckn" class="transform transition-all absolute -top-3.5 left-5 px-1 pl-0 text-2xs text-black">TC Kimlik No*</label>
                            </div>
                            <div class="relative group w-full md:w-6/12 px-1">
                                <Datepicker v-model="form.date_of_birth" locale="tr" :enable-time-picker="false" :format="`dd/MM/yyyy`" auto-apply required :max-date="new Date().setFullYear(new Date().getFullYear() - 18)"
                                    class="disabled:text-textgray peer w-full mb-5 rounded-2lg hover:border-acordion-green group-hover:placeholder-white border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider">
                                </Datepicker>
                                <label for="date_of_birth" class="transform transition-all absolute -top-3.5 left-5 px-1 pl-0 text-2xs text-black">Doğum Tarihi*</label>
                            </div>
                            <div class="mt-4 self-center justify-center flex" v-if="Object.entries(errors).length > 0">
                                <div class="text-xs mx-auto text-red-900" v-for="error in errors">{{ error }}</div>
                            </div>
                            <div class="mt-4 self-center justify-center flex" v-if="validationMessage != ''">
                                <div class="text-xs mx-auto text-red-900">{{ validationMessage }}</div>
                            </div>

                            <div class="relative group w-full flex justify-center">
                                <button type="submit" class="bg-black text-white text-center rounded-full py-2 text-lg px-2 self-center font-santralextrabold w-10/12 hover:bg-kbgreen" :disabled="form.processing">Doğrula</button>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="">
                    <div class="border-b-2 border-bordergray my-3"></div>
                    <CartProductPreview :products="products" />
                    <div class="border-b-2 border-bordergray my-3"></div>
                    <CartAddresses :user="user" :cities="cities" :errors="errors"></CartAddresses>
                </div>
            </div>

            <CheckoutPaymentInfo :total="total" :products="products" :discount="discount_amount" :sub_total="sub_total" :insurance="insurance" :non_mass_payment_total="non_mass_payment_total" :is_mass_payment_enabled="is_mass_payment_enabled">
                <template #action>
                    <template v-if="showCCForm || cards.length == 0">
                        <button type="submit" form="cc-form" class="bg-black text-white text-center rounded-full py-2 text-lg px-2 self-center font-santralextrabold w-full hover:bg-kbgreen"
                            v-if="onBilgilendirme && mesafeliKiralama && auth.user?.address">Kiralamayı Tamamla</button>
                        <div class="bg-black text-white text-center rounded-full py-2 text-lg px-2 self-center font-santralextrabold w-full hover:bg-kbgreen" v-else>Kiralamayı Tamamla</div>
                    </template>
                    <button type="button" class="bg-black text-white text-center rounded-full py-2 text-lg px-2 self-center font-santralextrabold w-full hover:bg-kbgreen" v-else @click="compeletePaymentWithRegisteredCard()">Kiralamayı
                        Tamamla</button>
                    <div v-if="user?.addresses.length == 0 && formSubmitted" class="mt-5 w-full text-center">Bir Adres Seçiniz</div>
                    <div v-if="errorText != ''" class="mt-5 w-full text-center">{{ errorText }}</div>

                    <div class="mt-5 border border-hopi-pink p-3 rounded text-sm w-full" v-if="hopi_bird">
                        <p>Hopi Bakiyeniz: {{ hopi_balance }} ₺</p>
                        <p v-if="selectedHopiCampaign">Hopi Seçili Kampanya: {{ selectedHopiCampaign && hopi_campaigns.filter((x) => x.code == selectedHopiCampaign)[0]?.name }}</p>
                        <p v-if="enteredHopiBalance > 0">Kullanılan Paracık: {{ enteredHopiBalance }}</p>
                    </div>
                </template>
                <template #aggrements>
                    <div class="w-full flex justify-start items-center mb-3">
                        <input v-model="onBilgilendirme" @input="$emit('update:onBilgilendirme', $event.target.value)" id="uyelik" class="cursor-pointer w-4 h-4 border-3 rounded-md focus:outline-none checked:bg-black focus:bg-black" type="checkbox"
                            required />
                        <label @click="openModal" class="cursor-pointer text-sm text-left text-checkoutgray pl-2" for="uyelik"> Ön bilgilendirme formunu kabul ediyorum.</label>
                    </div>
                    <div class="w-full flex justify-start items-center mb-3">
                        <input v-model="mesafeliKiralama" @input="$emit('update:mesafeliKiralama', $event.target.value)" id="aydinlatma" class="cursor-pointer w-4 h-4 border-3 rounded-md focus:outline-none checked:bg-black" type="checkbox"
                            required />
                        <label @click="openModal2" class="cursor-pointer text-sm text-left text-checkoutgray pl-2" for="aydinlatma"> Mesafeli kiralama sözleşmesini kabul ediyorum. </label>
                    </div>
                </template>
            </CheckoutPaymentInfo>
        </section>
    </main>

    <!--    Ön Bilgilendirme Formu-->
    <Dialog :open="isOpen" @close="closeModal" class="relative z-50">
        <!-- The backdrop, rendered as a fixed sibling to the panel container -->
        <div class="fixed inset-0 bg-black/30" aria-hidden="true" />

        <!-- Full-screen scrollable container -->
        <div class="fixed inset-0 overflow-y-auto">
            <!-- Container to center the panel -->
            <div class="flex min-h-full items-center justify-center p-4">
                <!-- The actual dialog panel -->
                <DialogPanel class="relative w-full max-w-[305px] md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl p-4 ts:p-6 rounded bg-white max-h-[65vh] overflow-y-scroll">
                    <DialogTitle :class="'mb-5'">ÖN BİLGİLENDİRME FORMU</DialogTitle>
                    <OnBilgilendirmeFormu :products="products" :user="user"></OnBilgilendirmeFormu>
                    <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal">x</div>
                </DialogPanel>
            </div>
        </div>
    </Dialog>
    <!--    Mesafeli Kiralama Sözleşmesi-->
    <Dialog :open="isOpen2" @close="closeModal2" class="relative z-50">
        <!-- The backdrop, rendered as a fixed sibling to the panel container -->
        <div class="fixed inset-0 bg-black/30" aria-hidden="true" />

        <!-- Full-screen scrollable container -->
        <div class="fixed inset-0 overflow-y-auto">
            <!-- Container to center the panel -->
            <div class="flex min-h-full items-center justify-center p-4">
                <!-- The actual dialog panel -->
                <DialogPanel class="relative w-full max-w-[305px] md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl p-4 ts:p-6 rounded bg-white max-h-[65vh] overflow-y-scroll">
                    <DialogTitle :class="'mb-5'">MESAFELİ KİRALAMA SÖZLEŞMESİ</DialogTitle>
                    <MesafeliKiralamaSozlesmesi :products="products" :user="user"></MesafeliKiralamaSozlesmesi>
                    <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal2">x</div>
                </DialogPanel>
            </div>
        </div>
    </Dialog>
</template>
