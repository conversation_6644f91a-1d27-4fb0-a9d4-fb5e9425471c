<template>
    <div class="flex shadow-aboutshadow absolute left-3 md:left-1/4 bottom-5 rounded-2lg checkoutaddpopup">
        <div class="w-[106px] md:w-[122px] bg-kbblue flex justify-center items-center h-[76px] md:h-[100px] rounded-l-2lg">
            <svg xmlns="http://www.w3.org/2000/svg" width="44.079" height="40.019" viewBox="0 0 44.079 40.019">
                <g id="Group_4963" data-name="Group 4963" transform="translate(-1418.489 -266.332)">
                    <g id="Group_4103" data-name="Group 4103" transform="translate(1420 267.832)">
                        <g id="Group_3491" data-name="Group 3491" transform="translate(0 0)">
                            <path id="Path_2958" data-name="Path 2958" d="M216.467,221.826H200.548c-2.615,0-4.822-1.312-5.155-3.064L192.4,203.011c-.4-2.1,2.022-3.957,5.155-3.957h21.9c3.133,0,5.554,1.858,5.156,3.957l-2.993,15.751C221.289,220.515,219.081,221.826,216.467,221.826Z" transform="translate(-192.356 -188.423)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="3"/>
                            <path id="Path_2959" data-name="Path 2959" d="M202.477,193.839l6.113-9.337c1.114-1.7,2.683-1.727,3.811-.061l6.366,9.4" transform="translate(-194.291 -183.208)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="3"/>
                        </g>
                    </g>
                    <g id="Group_4105" data-name="Group 4105" transform="translate(1436.15 279.934)">
                        <circle id="Ellipse_287" data-name="Ellipse 287" cx="10" cy="10" r="10" transform="translate(3.138 3.181)" fill="#5f4af4"/>
                        <g id="check-circle-fill" transform="translate(0 0)">
                            <path id="Path_173" data-name="Path 173" d="M26.417,13.209A13.209,13.209,0,1,1,13.209,0,13.209,13.209,0,0,1,26.417,13.209Zm-6.555-5a1.239,1.239,0,0,0-1.783.036l-5.734,7.306L8.889,12.091a1.239,1.239,0,0,0-1.75,1.75l4.368,4.37a1.239,1.239,0,0,0,1.782-.033l6.591-8.239A1.239,1.239,0,0,0,19.864,8.2Z" fill="#70d44b" fill-rule="evenodd"/>
                        </g>
                    </g>

                </g>
            </svg>
        </div>
        <div class="bg-kb-mid-grey w-[200px] md:w-[278px] h-[76px] md:h-[100px] flex items-center justify-between px-1 md:px-4 rounded-r-2lg">
            <div class="">
                <div class="text-sm md:text-base font-bold whitespace-nowrap mb-2">Ürün Sepete Eklendi</div>
                <div class="text-xs md:text-sm font-bold whitespace-nowrap text-[#1F7EFF]"><a href="sepetim">Sepete Git</a></div>
            </div>
            <button>
                <svg xmlns="http://www.w3.org/2000/svg" width="20.887" height="20.887" viewBox="0 0 20.887 20.887">
                    <path id="close-filled" d="M12.693,2.25A10.379,10.379,0,0,0,2.25,12.693,10.379,10.379,0,0,0,12.693,23.137,10.379,10.379,0,0,0,23.137,12.693,10.379,10.379,0,0,0,12.693,2.25Zm4.028,15.665-4.028-4.028L8.665,17.915,7.472,16.722,11.5,12.693,7.472,8.665,8.665,7.472,12.693,11.5l4.028-4.028,1.194,1.194-4.028,4.028,4.028,4.028Z" transform="translate(-2.25 -2.25)" fill="#c7c7c7"/>
                </svg>
            </button>
        </div>
    </div>
    <div class="shadow-aboutshadow px-4 pt-8 pb-6 absolute right-5 top-5 rounded-2lg checkoutpopup">
        <div class=" overflow-y-scroll max-h-[354px] md:max-h-[465px]">
            <div class="w-full relative flex-1 bg-white rounded-lg py-2 border-2 border-bordergray min-w-[303px] lg:max-w-[454px] mb-3">
                <div class="flex w-full">
                    <div class="w-3/12 hidden md:block">
                        <img src="../../images/ipad.png" alt="">
                    </div>
                    <div class="w-full md:w-9/12 flex flex-col justify-between items-start pl-3">
                        <div class="w-full flex items-center">
                            <div class="w-4/12 block md:hidden mr-2">
                                <img src="../../images/ipad.png" alt="">
                            </div>
                            <div>
                                <h2 class="text-base font-semibold"> Apple iPad Air 12 (2022)</h2>
                                <div class="text-sm text-textgray">6.1” Super Retina XDR display, Dual Rear Camera, 4GB RAM…</div>
                            </div>
                        </div>
                        <div class="w-full mt-4 pb-3 border-y-1 border-bordergray">
                            <div class="w-full flex flex-wrap mt-2">
                                <div class="w-4/12 pl-1">
                                    <div class="text-xs text-textgray font-thin lg:pb-2">Adet</div>
                                </div>
                                <div class="w-4/12 pl-1">
                                    <div class="text-xs  text-textgray font-thin lg:pb-2">Kiralama Süresi</div>
                                </div>
                                <div class="w-4/12 pl-1">
                                    <div class="text-xs text-textgray text-center font-thin lg:pb-2">Tutar</div>
                                </div>
                            </div>
                            <div class="w-full flex flex-wrap items-center">
                                <div class="w-4/12 pl-1 px-2 border-r-1 md:border-r-2 border-bordergray mt-1">
                                    <select class=" w-full px-2 py-2 rounded-lg border-1 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-sm lg:text-base tracking-wider" name="sirala" id="sirala">
                                        <option value="Sırala" selected>1</option>
                                    </select>
                                </div>
                                <div class="w-4/12 pl-1 mt-1 px-2  border-r-1 md:border-r-2 border-bordergray">
                                    <select class=" w-full px-2 py-2 rounded-lg border-1 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-sm lg:text-base tracking-wider" name="sirala" id="sirala2">
                                        <option value="Sırala" selected>1 Ay</option>
                                    </select>
                                </div>
                                <h2 class="w-4/12 text-base font-semibold pl-2 whitespace-nowrap leading-tight mt-2"> 540 TL / <br> <span class="text-xs font-light text-textgray whitespace-nowrap leading-tight">Aylık kiralama</span> </h2>
                            </div>
                        </div>

                        <div class="w-full flex justify-between mt-2 items-center">
                            <div class="flex space-x-18px">
                                <div class="border-1 border-black p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-black"></div>
                                </div>
                                <div class="hover:border-1 border-textgray p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-textgray"></div>
                                </div>
                                <div class="hover:border-1 border-kbred p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-kbred"></div>
                                </div>
                                <div class="hover:border-1 border-[#268602] p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-[#268602]"></div>
                                </div>
                                <div class="hover:border-1 border-[#074291] p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-[#074291]"></div>
                                </div>
                            </div>
                            <button class="w-[34px] h-[34px] rounded-full flex justify-center items-center mr-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 34 34">
                                    <circle id="Ellipse_362" data-name="Ellipse 362" cx="17" cy="17" r="17" fill="#ebebeb" opacity="0.276"/>
                                    <g id="Group_4879" data-name="Group 4879" transform="translate(10.468 9.742)">
                                        <g id="Group_3303" data-name="Group 3303">
                                            <g id="Group_3301" data-name="Group 3301">
                                                <g id="delete">
                                                    <path id="Path_164" data-name="Path 164" d="M13.5,13.5h1.037v6.221H13.5Z" transform="translate(-9.353 -8.316)" fill="#231f20"/>
                                                    <path id="Path_165" data-name="Path 165" d="M20.25,13.5h1.037v6.221H20.25Z" transform="translate(-12.992 -8.316)" fill="#231f20"/>
                                                    <path id="Path_166" data-name="Path 166" d="M4.5,6.75V7.787H5.537V18.155a1.037,1.037,0,0,0,1.037,1.037h8.294A1.037,1.037,0,0,0,15.9,18.155V7.787h1.037V6.75Zm2.074,11.4V7.787h8.294V18.155Z" transform="translate(-4.5 -4.676)" fill="#231f20"/>
                                                    <path id="Path_167" data-name="Path 167" d="M13.5,2.25h4.147V3.287H13.5Z" transform="translate(-9.353 -2.25)" fill="#231f20"/>
                                                </g>
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-full relative flex-1 bg-white rounded-lg py-2 border-2 border-bordergray min-w-[303px] lg:max-w-[454px] mb-3">
                <div class="flex w-full">
                    <div class="w-3/12 hidden md:block">
                        <img src="../../images/ipad.png" alt="">
                    </div>
                    <div class="w-full md:w-9/12 flex flex-col justify-between items-start pl-3">
                        <div class="w-full flex items-center">
                            <div class="w-4/12 block md:hidden mr-2">
                                <img src="../../images/ipad.png" alt="">
                            </div>
                            <div>
                                <h2 class="text-base font-semibold"> Apple iPad Air 12 (2022)</h2>
                                <div class="text-sm text-textgray">6.1” Super Retina XDR display, Dual Rear Camera, 4GB RAM…</div>
                            </div>
                        </div>
                        <div class="w-full mt-4 pb-3 border-y-1 border-bordergray">
                            <div class="w-full flex flex-wrap mt-2">
                                <div class="w-4/12 pl-1">
                                    <div class="text-xs text-textgray font-thin lg:pb-2">Adet</div>
                                </div>
                                <div class="w-4/12 pl-1">
                                    <div class="text-xs  text-textgray font-thin lg:pb-2">Kiralama Süresi</div>
                                </div>
                                <div class="w-4/12 pl-1">
                                    <div class="text-xs text-textgray text-center font-thin lg:pb-2">Tutar</div>
                                </div>
                            </div>
                            <div class="w-full flex flex-wrap items-center">
                                <div class="w-4/12 pl-1 px-2 border-r-1 md:border-r-2 border-bordergray mt-1">
                                    <select class=" w-full px-2 py-2 rounded-lg border-1 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-sm lg:text-base tracking-wider" name="sirala" id="sirala">
                                        <option value="Sırala" selected>1</option>
                                    </select>
                                </div>
                                <div class="w-4/12 pl-1 mt-1 px-2  border-r-1 md:border-r-2 border-bordergray">
                                    <select class=" w-full px-2 py-2 rounded-lg border-1 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-sm lg:text-base tracking-wider" name="sirala" id="sirala2">
                                        <option value="Sırala" selected>1 Ay</option>
                                    </select>
                                </div>
                                <h2 class="w-4/12 text-base font-semibold pl-2 whitespace-nowrap leading-tight mt-2"> 540 TL / <br> <span class="text-xs font-light text-textgray whitespace-nowrap leading-tight">Aylık kiralama</span> </h2>
                            </div>
                        </div>

                        <div class="w-full flex justify-between mt-2 items-center">
                            <div class="flex space-x-18px">
                                <div class="border-1 border-black p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-black"></div>
                                </div>
                                <div class="hover:border-1 border-textgray p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-textgray"></div>
                                </div>
                                <div class="hover:border-1 border-kbred p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-kbred"></div>
                                </div>
                                <div class="hover:border-1 border-[#268602] p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-[#268602]"></div>
                                </div>
                                <div class="hover:border-1 border-[#074291] p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-[#074291]"></div>
                                </div>
                            </div>
                            <button class="w-[34px] h-[34px] rounded-full flex justify-center items-center mr-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 34 34">
                                    <circle id="Ellipse_362" data-name="Ellipse 362" cx="17" cy="17" r="17" fill="#ebebeb" opacity="0.276"/>
                                    <g id="Group_4879" data-name="Group 4879" transform="translate(10.468 9.742)">
                                        <g id="Group_3303" data-name="Group 3303">
                                            <g id="Group_3301" data-name="Group 3301">
                                                <g id="delete">
                                                    <path id="Path_164" data-name="Path 164" d="M13.5,13.5h1.037v6.221H13.5Z" transform="translate(-9.353 -8.316)" fill="#231f20"/>
                                                    <path id="Path_165" data-name="Path 165" d="M20.25,13.5h1.037v6.221H20.25Z" transform="translate(-12.992 -8.316)" fill="#231f20"/>
                                                    <path id="Path_166" data-name="Path 166" d="M4.5,6.75V7.787H5.537V18.155a1.037,1.037,0,0,0,1.037,1.037h8.294A1.037,1.037,0,0,0,15.9,18.155V7.787h1.037V6.75Zm2.074,11.4V7.787h8.294V18.155Z" transform="translate(-4.5 -4.676)" fill="#231f20"/>
                                                    <path id="Path_167" data-name="Path 167" d="M13.5,2.25h4.147V3.287H13.5Z" transform="translate(-9.353 -2.25)" fill="#231f20"/>
                                                </g>
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-full relative flex-1 bg-white rounded-lg py-2 border-2 border-bordergray min-w-[303px] lg:max-w-[454px] mb-3">
                <div class="flex w-full">
                    <div class="w-3/12 hidden md:block">
                        <img src="../../images/ipad.png" alt="">
                    </div>
                    <div class="w-full md:w-9/12 flex flex-col justify-between items-start pl-3">
                        <div class="w-full flex items-center">
                            <div class="w-4/12 block md:hidden mr-2">
                                <img src="../../images/ipad.png" alt="">
                            </div>
                            <div>
                                <h2 class="text-base font-semibold"> Apple iPad Air 12 (2022)</h2>
                                <div class="text-sm text-textgray">6.1” Super Retina XDR display, Dual Rear Camera, 4GB RAM…</div>
                            </div>
                        </div>
                        <div class="w-full mt-4 pb-3 border-y-1 border-bordergray">
                            <div class="w-full flex flex-wrap mt-2">
                                <div class="w-4/12 pl-1">
                                    <div class="text-xs text-textgray font-thin lg:pb-2">Adet</div>
                                </div>
                                <div class="w-4/12 pl-1">
                                    <div class="text-xs  text-textgray font-thin lg:pb-2">Kiralama Süresi</div>
                                </div>
                                <div class="w-4/12 pl-1">
                                    <div class="text-xs text-textgray text-center font-thin lg:pb-2">Tutar</div>
                                </div>
                            </div>
                            <div class="w-full flex flex-wrap items-center">
                                <div class="w-4/12 pl-1 px-2 border-r-1 md:border-r-2 border-bordergray mt-1">
                                    <select class=" w-full px-2 py-2 rounded-lg border-1 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-sm lg:text-base tracking-wider" name="sirala" id="sirala">
                                        <option value="Sırala" selected>1</option>
                                    </select>
                                </div>
                                <div class="w-4/12 pl-1 mt-1 px-2  border-r-1 md:border-r-2 border-bordergray">
                                    <select class=" w-full px-2 py-2 rounded-lg border-1 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-sm lg:text-base tracking-wider" name="sirala" id="sirala2">
                                        <option value="Sırala" selected>1 Ay</option>
                                    </select>
                                </div>
                                <h2 class="w-4/12 text-base font-semibold pl-2 whitespace-nowrap leading-tight mt-2"> 540 TL / <br> <span class="text-xs font-light text-textgray whitespace-nowrap leading-tight">Aylık kiralama</span> </h2>
                            </div>
                        </div>

                        <div class="w-full flex justify-between mt-2 items-center">
                            <div class="flex space-x-18px">
                                <div class="border-1 border-black p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-black"></div>
                                </div>
                                <div class="hover:border-1 border-textgray p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-textgray"></div>
                                </div>
                                <div class="hover:border-1 border-kbred p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-kbred"></div>
                                </div>
                                <div class="hover:border-1 border-[#268602] p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-[#268602]"></div>
                                </div>
                                <div class="hover:border-1 border-[#074291] p-1 rounded-full cursor-pointer">
                                    <div class="w-5 h-5 rounded-full bg-[#074291]"></div>
                                </div>
                            </div>
                            <button class="w-[34px] h-[34px] rounded-full flex justify-center items-center mr-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 34 34">
                                    <circle id="Ellipse_362" data-name="Ellipse 362" cx="17" cy="17" r="17" fill="#ebebeb" opacity="0.276"/>
                                    <g id="Group_4879" data-name="Group 4879" transform="translate(10.468 9.742)">
                                        <g id="Group_3303" data-name="Group 3303">
                                            <g id="Group_3301" data-name="Group 3301">
                                                <g id="delete">
                                                    <path id="Path_164" data-name="Path 164" d="M13.5,13.5h1.037v6.221H13.5Z" transform="translate(-9.353 -8.316)" fill="#231f20"/>
                                                    <path id="Path_165" data-name="Path 165" d="M20.25,13.5h1.037v6.221H20.25Z" transform="translate(-12.992 -8.316)" fill="#231f20"/>
                                                    <path id="Path_166" data-name="Path 166" d="M4.5,6.75V7.787H5.537V18.155a1.037,1.037,0,0,0,1.037,1.037h8.294A1.037,1.037,0,0,0,15.9,18.155V7.787h1.037V6.75Zm2.074,11.4V7.787h8.294V18.155Z" transform="translate(-4.5 -4.676)" fill="#231f20"/>
                                                    <path id="Path_167" data-name="Path 167" d="M13.5,2.25h4.147V3.287H13.5Z" transform="translate(-9.353 -2.25)" fill="#231f20"/>
                                                </g>
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="w-full flex justify-between items-center pt-3 mt-1 border-t-2 border-bordergray">
            <div class="w-4/12 text-[18px] font-semibold pl-2 whitespace-nowrap leading-tight"> Ara Toplam <span class="text-sm font-light text-textgray whitespace-nowrap leading-tight">(KDV Dahil)</span> </div>
            <div class="text-xl font-bold whitespace-nowrap ">1.620 TL</div>
        </div>
        <div class="w-full mt-8"><a class="w-full block bg-black text-white rounded-full py-4 px-4 self-center font-semibold text-base text-center hover:bg-kbgreen" href="/sepetim">Kiralamayı Tamamla</a></div>
    </div>
</template>

<script>
    import { Link } from '@inertiajs/inertia-vue3';

    export default {
        data() {
            return {
                selectedMonth: 1,
                selectedClass: 'rounded-full -m-2 pt-4',
            }
        },
        components: {
            Link,
        },
        methods: {
            selectMonth(month) {
                this.selectedMonth = month;
            }
        }
    }
</script>

<style scoped>

</style>
