<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import { Video } from "@splidejs/splide-extension-video";
import "@splidejs/splide-extension-video/dist/css/splide-extension-video.min.css";
import { defineComponent, onMounted, reactive, ref } from "vue";
import Loader from "@/Pages/Shared/Loader.vue";
import CategoryBox from "@/Pages/Shared/CategoryBox.vue";
import Pagination from "@/Pages/Shared/Pagination.vue";
import { vMaska } from "maska";

export default defineComponent({
    name: "<PERSON><PERSON> + <PERSON><PERSON>",
    components: {
        CategoryBox,
        Loader,
        Pagination,
        <PERSON>,
        Head,
        Splide,
        SplideSlide,
        SplideTrack,
        Video,
        UserMenu
    },
    directives: { maska: vMaska },
    data() {
        return {
            isOpen: this.$page.props.success.success != null ? true : false,
            businessForm: this.$inertia.form({
                name: null,
                email: null,
                gsm: null,
                firmName: null,
                message: null,
                solutions: "Çözümlerimiz"
            })
        };
    },
    computed: {
        splidedDiscounted() {
            // Son Eklenen Ürünler için ayrı veri kullan (pagination'dan bağımsız)
            if (!this.latestProducts) return [];
            return this.splidedArray(this.latestProducts, 1);
        }
    },
    methods: {
        splidedArray(arr, size) {
            return arr.reduce((acc, e, i) => (i % size ? acc[acc.length - 1].push(e) : acc.push([e]), acc), []);
        }
    },
    props: {
        newtagged: Object,
        latestProducts: Array,
        errors: { type: Object, default: false }
    },
    watch: {
        "$page.props.success": function(val) {
            this.isOpen = val.success != null ? true : false;
            if (val.success == "Mesajınız başarıyla gönderildi.") {
                this.businessForm.reset();
            }
        }
    },
    setup() {
        const main = ref("<InstanceType<typeof Splide>>");
        const thumbs = ref("<InstanceType<typeof Splide>>");
        const videos = ["gG5sWlLQyIY", "gRXb2rCKjdk", "4JlAq0RDVRk", "1BzZB-vhCz8", "2O5-0sBhhKw"];

        const mainOptions = {
            type: "loop",
            perPage: 1,
            perMove: 1,
            gap: "1rem",
            pagination: true,
            arrows: true,
            rewind: true,
            autoPlay: true
        };

        const thumbsOptions = {
            type: "slide",
            rewind: true,
            gap: "1rem",
            pagination: false,
            fixedWidth: 110,
            fixedHeight: 70,
            cover: true,
            focus: "center",
            isNavigation: true,
            updateOnMove: true,
            arrows: false
        };

        onMounted(() => {
            const thumbsSplide = thumbs.value?.splide;
            if (thumbsSplide) {
                main.value?.sync(thumbsSplide);
            }
        });

        return {
            main,
            thumbs,
            thumbsOptions,
            mainOptions,
            videos,
            extensions: { Video }
        };
    },
    layout: Layout
});
</script>

<template>
    <Head>
        <title>Kiralabunu | Kirala + Satın Al</title>
        <meta name="description" content="Kiralabunu Ürünlerini Kirala + Satın Al" />
    </Head>
    <section class="mb-6">
        <div class="mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-4">
            <!--            <div class="my-4 text-2xl lg:text-4xl relative z-40 text-center">KİRALA SATIN AL</div>-->
            <div class="sliderclass rounded-2lg overflow-hidden">
                <splide :options="{ rewind: true, gap: '1rem', perPage: 1, arrows: false }" class="block md:hidden">
                    <splide-slide>
                        <picture>
                            <source src="../../images/rent-buy/rent-buy-slider-new-mobil.png" type="image/png" />
                            <source src="../../images/rent-buy/rent-buy-slider-new-mobil.webp" type="image/webp" />
                            <img class="w-full" src="../../images/rent-buy/rent-buy-slider-new-mobil.png" alt="" />
                        </picture>
                    </splide-slide>
                </splide>
                <splide :options="{ rewind: true, gap: '1rem', perPage: 1, arrows: false }" class="hidden md:block">
                    <splide-slide>
                        <picture>
                            <source src="../../images/rent-buy/rent-buy-slider-new-web.png" type="image/png" />
                            <source src="../../images/rent-buy/rent-buy-slider-new-web.webp" type="image/webp" />
                            <img class="w-full" src="../../images/rent-buy/rent-buy-slider-new-web.png" alt="" />
                        </picture>
                    </splide-slide>
                </splide>
            </div>
            <div class="font-santralregular text-base lg:text-lg text-black text-center my-4">
                Aylık ödemelerle ürününü şimdi kullan, istersen vadeli satın al. <br>
                Kredi yok, kefil yok, peşin ödeme yok, kredi kartına bloke yok.
            </div>
        </div>
    </section>
    <section class="mt-6 md:mt-4 py-1 md:py-9 bg-[#f8f8f8]">
        <div class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
            <div class="flex w-full justify-between">
                <div class="text-2xl md:text-3xl my-2 lg:my-0 mx-0 lg:mx-4 self-center text-center w-full md:w-auto">Son Eklenen Ürünler</div>
                <div class="flex-1 ml-6 hidden md:flex">
                    <div class="flex-1 self-center border border-gray-200"></div>
                    <a href="#rentbuyproducts" class="cursor-pointer text-sm font-santralextrabold flex justify-center items-center border-2 rounded-full px-4 border-2 whitespace-nowrap md:ml-4 bg-white hover:bg-kbgreen hover:text-white"> Tümünü Gör</a>
                </div>
            </div>
            <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0 lg:mt-9">
                <Splide :has-track="false" aria-label="" :options="{ rewind: true, perPage: 4, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 3 } } }" v-if="latestProducts">
                    <SplideTrack>
                        <SplideSlide v-for="(productGroup, groupIndex) in splidedDiscounted" :key="groupIndex" class="flex">
                            <category-box v-for="(product, index) in productGroup" :key="product.id" :product="product" :new-container-classes="`w-full`" :auth="auth" />
                        </SplideSlide>
                    </SplideTrack>
                    <div class="splide__arrows">
                        <button class="splide__arrow splide__arrow--prev !-left-9">
                            <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z" transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                        <button class="splide__arrow splide__arrow--next !-right-9">
                            <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z" transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                    </div>
                </Splide>
                <div v-else>
                    <loader :active="true" message="Please wait 5 seconds" />
                </div>
            </div>
            <div class="flex-1 ml-2 flex md:hidden justify-center">
                <a href="#rentbuyproducts" class="cursor-pointer border-3 rounded-full py-1.5 px-2 text-center mb-4 w-32 bg-white hover:bg-kbgreen hover:text-white font-santralextrabold text-sm"> Tümünü Gör</a>
            </div>
        </div>
    </section>

    <section class="mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl my-8 category-banner relative kiralasteps" v-if="false">
        <Splide :has-track="false" aria-label="" :options="{ gap: '30px', rewind: true, perPage: 3, pagination: false, arrows: false, breakpoints: { 640: { perPage: 1, arrows: true }, 1100: { perPage: 2, arrows: false }, 1270: { perPage: 3, arrows: false } } }">
            <div class="text-2xl md:text-3xl my-6 lg:my-0 mx-0 lg:mx-4 self-center w-full">Neden Kirala + Satın Al</div>
            <SplideTrack class="w-full">
                <SplideSlide class="rounded-32 shadow-kiralastep !my-4 overflow-hidden h-full !ml-2">
                    <div class="bg-white flex flex-col justify-between items-start h-full">
                        <div class="flex flex-col justify-start items-start px-8 pt-8">
                            <div class="font-santralextrabold text-lg md:text-xl mb-1 min-h-14">Kira Öder Gibi Satın Al</div>
                            <div class="font-santralregular text-xs md:text-sm min-h-[100px] lg:min-h-[160px]">Bazı ürünler için "kira öder gibi sahip olmak" çok daha mantıklı. Biz de bu yüzden, esnek, kolay ve bütçe dostu bir model sunmak istedik.</div>
                        </div>
                        <picture class="w-full">
                            <source srcset="../../images/rent-buy/neden-kirala-satin-al-1.webp" type="image/webp" />
                            <source srcset="../../images/rent-buy/neden-kirala-satin-al-1.png" type="image/png" />
                            <img src="../../images/rent-buy/neden-kirala-satin-al-1.png" class="w-full" />
                        </picture>
                    </div>
                </SplideSlide>
                <SplideSlide class="rounded-32 shadow-kiralastep !my-4 overflow-hidden h-full !ml-2">
                    <div class="bg-white flex flex-col justify-between items-start">
                        <div class="flex flex-col justify-start items-start px-8 pt-8">
                            <div class="font-santralextrabold text-lg md:text-xl mb-1 min-h-14">Limit Zorlamadan, Parça Parça Senin Olsun</div>
                            <div class="font-santralregular text-xs md:text-sm min-h-[100px] lg:min-h-[160px]">Peşinat yok, kart limiti bloke olmaz, taksit derdi yok. Aylık ödemelerle ürünü rahatça kullan, kullanırken sahip ol. Ne krediye ihtiyaç var ne kefile. Her şey sade, net ve erişilebilir.</div>
                        </div>
                        <picture class="w-full">
                            <source srcset="../../images/rent-buy/neden-kirala-satin-al-2.webp" type="image/webp" />
                            <source srcset="../../images/rent-buy/neden-kirala-satin-al-2.png" type="image/png" />
                            <img src="../../images/rent-buy/neden-kirala-satin-al-2.png" class="w-full" />
                        </picture>
                    </div>
                </SplideSlide>
                <SplideSlide class="rounded-32 shadow-kiralastep !my-4 overflow-hidden h-full !ml-2">
                    <div class="bg-white flex flex-col justify-between items-start">
                        <div class="flex flex-col justify-start items-start px-8 pt-8">
                            <div class="font-santralextrabold text-lg md:text-xl mb-1 min-h-14">Kira Süresi Biter, Ürün Senin Olur</div>
                            <div class="font-santralregular text-xs md:text-sm min-h-[100px] lg:min-h-[160px]">Kira dönemi sonunda ürün artık senindir. İade istemiyoruz çünkü zaten senin gibi hissettirdi. Kullandıkça bağ kurduğun şey sende kalmalı, biz de aynen öyle yapıyoruz.</div>
                        </div>
                        <picture class="w-full">
                            <source srcset="../../images/rent-buy/neden-kirala-satin-al-3.webp" type="image/webp" />
                            <source srcset="../../images/rent-buy/neden-kirala-satin-al-3.png" type="image/png" />
                            <img src="../../images/rent-buy/neden-kirala-satin-al-3.png" class="w-full" />
                        </picture>
                    </div>
                </SplideSlide>
            </SplideTrack>

            <div class="splide__arrows absolute bottom-4 lg:bottom-auto lg:top-4 right-4 z-70">
                <button class="splide__arrow splide__arrow--prev !-left-9 !bg-icon-gray !px-1">
                    <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                        <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z" transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                    </svg>
                </button>
                <button class="splide__arrow splide__arrow--next !-right-9 !bg-icon-gray !px-1">
                    <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                        <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z" transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                    </svg>
                </button>
            </div>
        </Splide>
    </section>
    <section class="mt-6 md:mt-4 py-1 md:py-9 bg-white" id="rentbuyproducts">
        <div class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
            <div class="flex w-full justify-between">
                <div class="text-2xl md:text-3xl my-2 lg:my-4 mx-0 lg:mx-4 self-center text-center w-full md:w-auto">
                    Tüm Kirala + Satın Al Ürünleri
                    <!--                    <span v-if="newtagged && newtagged.items.pagination" class="text-xs text-gray-500 font-normal">-->
                    <!--                        ({{ newtagged.items.pagination.total }} ürün)-->
                    <!--                    </span>-->
                </div>
            </div>
            <div v-if="newtagged" class="my-4">
                <div class="flex flex-wrap">
                    <category-box v-for="(product, index) in newtagged.items.data" :key="product.id" :product="product" :new-container-classes="`w-full lg:w-1/4 md:w-1/3 sm:w-1/2`" :auth="auth" />
                </div>
                <div class="w-full flex justify-center mt-5 font-santralregular">
                    <div v-if="newtagged.items.pagination && newtagged.items.pagination.links && newtagged.items.pagination.links.length > 3">
                        <div class="flex flex-wrap -mb-1">
                            <template v-for="(link, key) in newtagged.items.pagination.links">
                                <div v-if="link.url === null || link.url.startsWith('&') || link.url === '&'" :key="key" class="mb-1 mr-1 px-4 py-3 text-gray-400 text-sm leading-4 border rounded font-santralregular font-bold" v-html="link.label" />
                                <Link v-else :key="`link-${key}`" class="mb-1 mr-1 px-4 py-3 focus:text-indigo-500 text-sm leading-4 hover:bg-white border rounded font-santralregular font-bold" :class="{ 'border-kbgreen border-2': link.active }" :href="link.url" :preserve-state="true" :preserve-scroll="true" v-html="link.label" />
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto my-12 bg-bordergray" id="RentBuyForm">
        <div class="rounded-2lg w-full mts:px-4 mt-4">
            <div class="w-full h-full flex flex-col justify-center items-center rounded-2lg p-4 min-h-[160px]">
                <div class="font-bold text-2xl md:text-3xl w-full text-black font-santralextrabold text-center mb-2 pb-2">Hangi ürünü vadeli almak istersin?</div>

                <form class="w-full mx-auto my-6" action="#">
                    <div class="w-full flex justify-center items-center mb-5" v-if="Object.entries(errors).length > 0">
                        <div class="flex whitespace-nowrap self-center flex-col p-4 px-12 border-2 border-kbred rounded-2lg">
                            <div class="text-sm mx-auto text-white my-1"><span class="text-kbred">*</span> Gerekli alanları doldurunuz</div>
                        </div>
                    </div>
                    <div class="flex flex-wrap relative w-full">
                        <div class="flex flex-col relative w-full lg:w-1/2">
                            <div class="w-full lg:pr-2 group relative mb-3">
                                <input
                                    id="fullname"
                                    required=""
                                    type="text"
                                    name="AdSoyad*"
                                    v-model="businessForm.name"
                                    autocomplete="off"
                                    class="!py-2 !leading-none w-full h-12 rounded-lg border-2 border-bordergray placeholder:text-xs text-lg tracking-wider placeholder:text-placeholdergray focus:border-acordion-green hover:border-kbgreen focus:outline-none focus:ring-kbgreen group-hover:placeholder-black peer"
                                />
                                <label
                                    for="fullname"
                                    class="transform transition-all absolute top-4 group-focus-within:top-3 group-focus-within:bg-white peer-valid:top-3 peer-valid:bg-white left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-xs peer-valid:text-xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-kbgray group-focus-within:text-black peer-valid:text-black"
                                >
                                    İsim, Soyisim*</label
                                >
                                <div class="text-sm mx-auto text-white my-1" v-if="Object.entries(errors).length > 0 && errors.name"><span class="text-kbred">*</span>{{ errors.name }}</div>
                            </div>
                            <div class="w-full lg:pr-2 relative group mb-3">
                                <input
                                    id="email"
                                    type="email"
                                    v-model="businessForm.email"
                                    placeholder="E-posta"
                                    autocomplete="off"
                                    required=""
                                    class="!py-2 w-full h-12 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbgreen focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-transparent"
                                />
                                <label
                                    for="email"
                                    class="transform transition-all absolute top-4 group-focus-within:top-3 group-focus-within:bg-white peer-valid:top-3 peer-valid:bg-white left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-xs peer-valid:text-xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-kbgray group-focus-within:text-black peer-valid:text-black"
                                >E-Posta*</label
                                >
                                <div class="text-sm mx-auto text-white my-1" v-if="Object.entries(errors).length > 0 && errors.email"><span class="text-kbred">*</span>{{ errors.email }}</div>
                            </div>
                            <div class="w-full lg:pr-2 relative group mb-3">
                                <input
                                    v-maska
                                    data-maska="(5##) ### ## ##"
                                    id="telephone"
                                    class="!py-2 !leading-none w-full h-12 rounded-lg border-2 border-bordergray placeholder:text-xs text-lg tracking-wider placeholder:text-placeholdergray focus:border-acordion-green hover:border-kbgreen focus:outline-none focus:ring-kbgreen group-hover:placeholder-black peer"
                                    required
                                    type="text"
                                    placeholder=""
                                    name="telno"
                                    autocomplete="off"
                                    v-model="businessForm.gsm"
                                />
                                <label
                                    for="telephone"
                                    class="transform transition-all absolute top-4 group-focus-within:top-3 group-focus-within:bg-white peer-valid:top-3 peer-valid:bg-white left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-xs peer-valid:text-xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-kbgray group-focus-within:text-black peer-valid:text-black"
                                >Telefon*</label
                                >
                                <div class="text-sm mx-auto text-white my-1" v-if="Object.entries(errors).length > 0 && errors.gsm"><span class="text-kbred">*</span>{{ errors.gsm }}</div>
                            </div>
                        </div>
                        <div class="w-full lg:w-1/2 lg:pr-2 relative group mb-3">
                            <textarea
                                class="peer w-full bg-white rounded-lg border-2 border-bordergray placeholder:text-xs placeholder:text-placeholdergray text-lg tracking-wider placeholder:mt-2 focus:border-acordion-green hover:border-kbgreen focus:outline-none focus:ring-kbgreen group-hover:placeholder-black"
                                required=""
                                name="Size Nasıl Yardımcı Olabiliriz?*"
                                v-model="businessForm.message"
                                cols="10"
                                rows="5"
                                autocomplete="off"
                                id="message"
                            ></textarea>
                            <label
                                for="message"
                                class="transform transition-all absolute top-4 group-focus-within:top-3 group-focus-within:bg-white peer-valid:top-3 peer-valid:bg-white left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-xs peer-valid:text-xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-kbgray group-focus-within:text-black peer-valid:text-black"
                            >Size Nasıl Yardımcı Olabiliriz?*</label
                            >
                            <div class="text-sm mx-auto text-white my-1" v-if="Object.entries(errors).length > 0 && errors.message"><span class="text-kbred">*</span>{{ errors.message }}</div>
                        </div>
                        <div class="w-full text-center mt-4">
                            <Link
                                :data="businessForm"
                                :href="route('rentBuyRequest')"
                                method="post"
                                class="font-santralextrabold border-2 border-transparent bg-white text-base lg:text-lg text-black rounded-full py-2 px-4 md:px-12 self-center font-santralextrabold hover:bg-black hover:border-kbgreen hover:text-white transition-all ease-in-out duration-300"
                            >Gönder
                            </Link>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>
</template>
