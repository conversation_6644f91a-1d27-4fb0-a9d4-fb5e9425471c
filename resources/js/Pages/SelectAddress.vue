<script>
import {Head, <PERSON>} from '@inertiajs/inertia-vue3';
import Layout from '@/Pages/Shared/Layout.vue';
import UserMenu from '@/Pages/Shared/UserMenu.vue';

export default {
    components: {
        <PERSON>,
        Head,
        UserMenu,

    },
    layout: Layout,
}
</script>

<template>
    <Head title=" Adres Seç " />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <user-menu :active="SelectAddress" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 lg:px-4 pt-2">
                <div class="flex justify-between items-center mb-7">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap"> <PERSON><PERSON><PERSON> </h3>
                    <Link href="/adres-ekle" class="hidden md:block w-32 bg-black text-white text-base text-center rounded-full py-2 px-1 font-bold">Ad<PERSON></Link>
                </div>

                <div class="flex justify-start items-center w-full mb-3 bg-white border-2 border-kb-light-grey py-3 px-5 rounded-lg">
                    <input class=" border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" id="yanlis" name="yanlis" value="yanlis">
                    <div class="w-5/6 pl-4 flex flex-wrap">
                        <div class="w-full flex">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18.07" height="18.089" viewBox="0 0 18.07 18.089">
                                <path id="home" d="M17.727,9.82a1.131,1.131,0,0,1-.83.344,1.116,1.116,0,0,1-.821-.335l-.265-.265v7.394a1.1,1.1,0,0,1-.327.794,1.077,1.077,0,0,1-.8.335H11.294V13.005a.573.573,0,0,0-.565-.565H7.341a.573.573,0,0,0-.565.565v5.082H3.388a1.077,1.077,0,0,1-.8-.335,1.1,1.1,0,0,1-.326-.794V9.564l-.265.265a1.116,1.116,0,0,1-.821.335,1.131,1.131,0,0,1-.83-.344A1.131,1.131,0,0,1,0,8.99,1.116,1.116,0,0,1,.335,8.17L8.17.334A1.118,1.118,0,0,1,9.035,0,1.118,1.118,0,0,1,9.9.334L17.735,8.17a1.116,1.116,0,0,1,.335.821,1.131,1.131,0,0,1-.344.83Z" transform="translate(0 0.002)" fill="#139a52"/>
                            </svg>
                            <label class="pl-2 text-base text-black font-bold cursor-pointer" for="yanlis">Ev Adresim</label>
                        </div>
                        <p class="hidden md:block mt-1 text-sm text-textgray cursor-pointer max-w-[350px] ts:max-w-none">Küçükbakkalköy, Şht. Şakir Elkovan Cd No:20, 34750 Ataşehir/İstanbul</p>
                    </div>
                    <p class="w-1/6 mt-1 text-base text-black cursor-pointer font-bold text-right"><Link href="/adres-duzenle">Düzenle</Link></p>
                </div>
                <div class="flex justify-start items-center w-full mb-3 bg-white border-2 border-kb-light-grey py-3 px-5 rounded-lg">
                    <input class=" border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green" type="radio" id="is" name="yanlis" value="yanlis">
                    <div class="w-5/6 pl-4 flex flex-wrap">
                        <div class="w-full flex">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16.298" height="16.298" viewBox="0 0 16.298 16.298">
                                <path id="building" d="M15.789,16.3H.509a.489.489,0,0,1-.358-.151.5.5,0,0,1,0-.716.489.489,0,0,1,.358-.151h.509a.972.972,0,0,0,.724-.3.99.99,0,0,0,.294-.716V1.019A.979.979,0,0,1,2.34.3.979.979,0,0,1,3.056,0H13.242a.972.972,0,0,1,.724.3.99.99,0,0,1,.294.716V14.261a.972.972,0,0,0,.3.724.99.99,0,0,0,.716.294h.509a.509.509,0,0,1,0,1.019ZM6.112,2.547A.516.516,0,0,0,5.6,2.037H4.584a.516.516,0,0,0-.509.509V3.565a.516.516,0,0,0,.509.509H5.6a.516.516,0,0,0,.509-.509Zm0,3.056A.516.516,0,0,0,5.6,5.093H4.584a.516.516,0,0,0-.509.509V6.621a.516.516,0,0,0,.509.509H5.6a.516.516,0,0,0,.509-.509Zm0,3.056A.516.516,0,0,0,5.6,8.149H4.584a.516.516,0,0,0-.509.509V9.677a.516.516,0,0,0,.509.509H5.6a.516.516,0,0,0,.509-.509ZM9.168,2.547a.516.516,0,0,0-.509-.509H7.64a.516.516,0,0,0-.509.509V3.565a.516.516,0,0,0,.509.509H8.658a.516.516,0,0,0,.509-.509Zm0,3.056a.516.516,0,0,0-.509-.509H7.64A.516.516,0,0,0,7.13,5.6V6.621a.516.516,0,0,0,.509.509H8.658a.516.516,0,0,0,.509-.509Zm0,3.056a.516.516,0,0,0-.509-.509H7.64a.516.516,0,0,0-.509.509V9.677a.516.516,0,0,0,.509.509H8.658a.516.516,0,0,0,.509-.509Zm.509,3.565H6.621a.516.516,0,0,0-.509.509V14.77a.516.516,0,0,0,.509.509H9.677a.516.516,0,0,0,.509-.509V12.733a.516.516,0,0,0-.509-.509Zm2.547-9.677a.516.516,0,0,0-.509-.509H10.7a.516.516,0,0,0-.509.509V3.565a.516.516,0,0,0,.509.509h1.019a.516.516,0,0,0,.509-.509Zm0,3.056a.516.516,0,0,0-.509-.509H10.7a.516.516,0,0,0-.509.509V6.621a.516.516,0,0,0,.509.509h1.019a.516.516,0,0,0,.509-.509Zm0,3.056a.516.516,0,0,0-.509-.509H10.7a.516.516,0,0,0-.509.509V9.677a.516.516,0,0,0,.509.509h1.019a.516.516,0,0,0,.509-.509Z" fill="#139a52"/>
                            </svg>
                            <label class="pl-2 text-base text-black font-bold cursor-pointer" for="is">İş Adresim</label>
                        </div>
                        <p class="hidden md:block mt-1 text-sm text-textgray cursor-pointer max-w-[350px] ts:max-w-none">19 Mayıs, Ulya Engin İş Merkezi, Atatürk Cd. No:68/9, 34734 Kadıköy/İstanbul</p>
                    </div>
                    <p class="w-1/6 mt-1 text-base text-black cursor-pointer font-bold text-right"><Link href="/adres-duzenle">Düzenle</Link></p>
                </div>
                <a class="block md:hidden w-32 mt-5 bg-black text-white text-base text-center rounded-full py-2 px-1 font-bold" href="">Adres Ekle</a>

            </div>
        </section>
    </main>

</template>
