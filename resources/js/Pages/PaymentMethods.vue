<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";

export default {
    components: {
        <PERSON>,
        Head,
        UserMenu,
    },
    props: {
        cards: Array,
        order_count: Number,
    },
    layout: Layout,
};
</script>

<template>

    <Head title="Ödeme Yöntemleri" />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <user-menu :order_count="order_count" :active="PaymentMethods" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 pt-2 lg:px-4">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7">Ödeme <PERSON></h3>
                </div>
                <div class="flex flex-col justify-center items-center mb-10" v-if="cards.length == 0">
                    <div class="flex justify-center items-center bg-kb-light-grey rounded-full w-32 h-32 mb-5">
                        <div class="flex justify-center items-center bg-black rounded-full w-20 h-20">
                            <svg id="credit-card" xmlns="http://www.w3.org/2000/svg" width="40.909" height="30.681" viewBox="0 0 40.909 30.681">
                                <path id="Path_3339" data-name="Path 3339"
                                    d="M0,9.614A5.114,5.114,0,0,1,5.114,4.5H35.795a5.114,5.114,0,0,1,5.114,5.114V30.068a5.114,5.114,0,0,1-5.114,5.114H5.114A5.114,5.114,0,0,1,0,30.068ZM5.114,7.057A2.557,2.557,0,0,0,2.557,9.614V12.17H38.352V9.614a2.557,2.557,0,0,0-2.557-2.557ZM38.352,17.284H2.557V30.068a2.557,2.557,0,0,0,2.557,2.557H35.795a2.557,2.557,0,0,0,2.557-2.557Z"
                                    transform="translate(0 -4.5)" fill="#fff" fill-rule="evenodd" />
                                <path id="Path_3340" data-name="Path 3340" d="M4.5,22.807A2.557,2.557,0,0,1,7.057,20.25H9.614a2.557,2.557,0,0,1,2.557,2.557v2.557A2.557,2.557,0,0,1,9.614,27.92H7.057A2.557,2.557,0,0,1,4.5,25.364Z"
                                    transform="translate(0.614 -2.352)" fill="#fff" />
                            </svg>
                        </div>
                    </div>
                    <p class="p-0 text-lg font-bold text-center text-gray-900 box-border whitespace-no-wrap mb-5">Ödeme yöntemin bulunmamaktadır.</p>
                    <p class="p-0 text-base text-center text-kbgray box-border whitespace-no-wrap mb-5">Kiralama yapmak için en az bir adet ödeme yöntemi eklemelisin.</p>
                    <Link href="odeme-yontemi-ekle" class="bg-black text-white rounded-full py-2 px-4 self-center text-lg font-bold">Ödeme Yöntemi Ekle </Link>
                </div>
                <div v-else class="flex flex-wrap justify-start mb-10 self-stretch">
                    <div v-for="(card, index) in cards" :key="index" class="w-full md:w-5/12 mr-2 mb-2 max-w-[276px] ts:w-[266px] ts:h-[168px] relative p-3 rounded-2lg bg-gradient-to-r from-[#5f4af4] to-[#423881]">
                        <div class="flex relative z-40">
                            <div class="w-9/12 mx-auto">
                                <p class="text-xs font-bold text-left text-white mt-2 md:mt-1 ts:mt-2">{{ card.alias }}</p>
                                <p class="text-xs text-left text-white mt-4 md:mt-3 lg:mt-3">{{ card.holder }}</p>
                                <p class="text-xs text-left text-white mt-4 md:mt-3 lg:mt-3">{{ card.bin_number }} **** {{ card.number }}</p>
                                <p class="text-xs text-left text-white mt-7 md:mt-7 lg:mt-7">**/{{ card.year }}</p>
                            </div>
                            <div class="w-2/12 flex flex-col justify-around">
                                <!--                                <div class="w-full flex justify-end md:justify-center">
                                                                    <a href="">
                                                                        <svg id="edit" width="18.888" height="20.462" viewBox="0 0 18.888 20.462">
                                                                            <path id="Path_162" data-name="Path 162" d="M2.25,29.25H21.138v1.574H2.25Z" transform="translate(-2.25 -10.362)" fill="#fff"/>
                                                                            <path id="Path_163" data-name="Path 163" d="M21.341,7.759a1.521,1.521,0,0,0,0-2.2L18.508,2.722a1.521,1.521,0,0,0-2.2,0L4.5,14.527v5.037H9.537ZM17.407,3.824,20.24,6.657,17.879,9.018,15.046,6.185ZM6.074,17.99V15.157l7.87-7.87,2.833,2.833-7.87,7.87Z" transform="translate(-2.926 -2.25)" fill="#fff"/>
                                                                        </svg>
                                                                    </a>
                                                                </div>
                                                                <div class="w-full flex  justify-end md:justify-center mb-6">
                                                                    <a href="">
                                                                        <svg id="delete" xmlns="http://www.w3.org/2000/svg" width="17.538" height="20.462" viewBox="0 0 17.538 20.462">
                                                                            <path id="Path_164" data-name="Path 164" d="M13.5,13.5h1.462v8.769H13.5Z" transform="translate(-7.654 -6.192)" fill="#fff"/>
                                                                            <path id="Path_165" data-name="Path 165" d="M20.25,13.5h1.462v8.769H20.25Z" transform="translate(-10.019 -6.192)" fill="#fff"/>
                                                                            <path id="Path_166" data-name="Path 166" d="M4.5,6.75V8.212H5.962V22.827a1.462,1.462,0,0,0,1.462,1.462H19.115a1.462,1.462,0,0,0,1.462-1.462V8.212h1.462V6.75ZM7.423,22.827V8.212H19.115V22.827Z" transform="translate(-4.5 -3.827)" fill="#fff"/>
                                                                            <path id="Path_167" data-name="Path 167" d="M13.5,2.25h5.846V3.712H13.5Z" transform="translate(-7.654 -2.25)" fill="#fff"/>
                                                                        </svg>
                                                                    </a>
                                                                </div>-->
                                <div class="text-right w-full mt-0 ts:mt-3">
                                    <img class="w-full" src="../../images/visa.png" alt="" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="w-full md:w-5/12 mt-12 md:mt-0 h-44 md:h-[175px] lg:h-[195px] ts:w-[266px] ts:h-[168px] relative md:ml-3 border-3 border-bordergray rounded-lg hover:border-black">
                        <div class="flex justify-center items-center w-full h-full">
                            <Link href="/odeme-yontemi-ekle" class="bg-black text-white rounded-full py-3 px-5 self-center text-base font-bold whitespace-nowrap hover:bg-kbgreen"> Yeni Kart Ekle </Link>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
</template>
