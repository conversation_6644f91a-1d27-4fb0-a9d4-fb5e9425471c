<script>
import { Inertia } from "@inertiajs/inertia";
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import ProductBox from "@/Pages/Shared/ProductBox.vue";
import VerticalProductBox from "@/Pages/Shared/VerticalProductBox.vue";
import Loader from "@/Pages/Shared/Loader.vue";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import Comments from "@/Pages/Shared/Comments.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import CategoryBox from "@/Pages/Shared/CategoryBox.vue";
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from "@headlessui/vue";
import { vMaska } from "maska";

export default {
    components: {
        ProductBox,
        VerticalProductBox,
        Link,
        Head,
        Splide,
        SplideSlide,
        SplideTrack,
        CategoryBox,
        Loader,
        Comments,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle
    },
    directives: { maska: vMaska },

    props: {
        sliders: Object,
        allProducts: Object,
        discounted: Object,
        mostRentedProducts: Object,
        newtagged: Object,
        category_telefon: Object,
        category_bilgisayar: Object,
        category_tablet: Object,
        category_saat: Object,
        category_oyun: Object,
        category_kameralar: Object,
        category_ses: Object,
        category_evaletleri: Object,
        category_emobilite: Object,
        category_saglik: Object,
        category_spor: Object,
        category_eveglence: Object,
        auth: Object
    },
    layout: Layout,
    data() {
        return {
            selectedSpeedCategory: 1,
            loaderActive: false,
            selectedSpeedCategoryProducts: this.category_telefon,
            isOpen: true,
            isNKolayVisitor: false,
            isMigrosVisitor: false
        };
    },
    setup() {
        const options = {
            rewind: true,
            gap: "1rem",
            perPage: 1,
            type: "loop",
            autoplay: true,
            pagination: false
        };

        return { options };
    },
    methods: {
        preventNumericInput($event) {
            console.log($event.keyCode); //will display the keyCode value
            //console.log($event.key); will show the key value

            var charCode = $event.keyCode ? $event.keyCode : $event.which;
            if ((charCode <= 93 && charCode >= 65) || (charCode <= 122 && charCode >= 97) || charCode == 32 || charCode == 8 || charCode == 350 || charCode == 351 || charCode == 304 || charCode == 286 || charCode == 287 || charCode == 231 || charCode == 199 || charCode == 305 || charCode == 214 || charCode == 246 || charCode == 220 || charCode == 252) {
                if (charCode != 33 || charCode != 43) {
                    return true;
                } else {
                    $event.preventDefault();
                }
            } else {
                $event.preventDefault();
            }
        },
        splidedArray(arr, size) {
            return arr.reduce((acc, e, i) => (i % size ? acc[acc.length - 1].push(e) : acc.push([e]), acc), []);
        },
        changeSpeedCategory(id) {
            // if not changed, return
            if (this.selectedSpeedCategory === id) return;

            this.selectedSpeedCategory = id;
            this.loaderActive = true;
            Inertia.reload({
                only: ["category_" + this.selectedSpeedCategory],
                onFinish: () => {
                    this.loaderActive = false;
                    this.selectedSpeedCategoryProducts = this["category_" + this.selectedSpeedCategory];
                },
                onSuccess: () => {
                    let categoryProducts = this["category_" + this.selectedSpeedCategory].items.element.products.data;
                    let categoryProductsDL = [];
                    categoryProducts.map((item, keys) => {
                        let product = {};
                        let subscribetionMonthsOrdered = [];
                        if (item.variants[0]?.prices.length > 0) subscribetionMonthsOrdered = Object.entries(item.variants[0]?.prices).sort(([, a], [, b]) => a.subscription_months.value - b.subscription_months.value);
                        let nonZeroPrices = item.variants[0]?.prices.filter((price) => price.price.value > 0);
                        subscribetionMonthsOrdered = subscribetionMonthsOrdered.filter((price) => price[1].price.value > 0);
                        let productPrice = subscribetionMonthsOrdered[+Object.keys(nonZeroPrices).length - 1]?.[1]?.price?.value / 100;

                        product.item_id = item.id;
                        product.item_name = item.attribute_data.name.tr;
                        product.price = productPrice;
                        product.item_brand = item.brand.name;
                        product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                        product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                        product.item_list_id = "HOME-KG";
                        product.item_list_name = "Homepage - Kategoriler " + this.selectedSpeedCategory + " List";
                        product.index = keys;
                        categoryProductsDL.push(product);
                    });
                    dataLayer.push({
                        event: "view_item_list",
                        ecommerce: {
                            item_list_id: "HOME-KG",
                            item_list_name: "Homepage - Kategoriler " + this.selectedSpeedCategory + " List",
                            items: categoryProductsDL
                        }
                    });
                }
            });
        },
        closeMigrosModal() {
            this.isMigrosVisitor = false;
        },
        closeModal() {
            this.isOpen = false;
        },
        openMigrosModal() {
            this.isMigrosVisitor = true;
        }
    },
    created() {
        Inertia.reload({
            only: ["allProducts", "mostRentedProducts", "discounted", "category_telefon", "newtagged"],
            onSuccess: () => {
                let lastAddedProducts = this.newtagged.items.data;
                let lastAddedProductsDL = [];
                lastAddedProducts.map((item, keys) => {
                    let product = {};
                    let subscribetionMonthsOrdered = [];
                    if (item.variants[0]?.prices.length > 0) subscribetionMonthsOrdered = Object.entries(item.variants[0]?.prices).sort(([, a], [, b]) => a.subscription_months.value - b.subscription_months.value);
                    let nonZeroPrices = item.variants[0]?.prices.filter((price) => price.price.value > 0);
                    subscribetionMonthsOrdered = subscribetionMonthsOrdered.filter((price) => price[1].price.value > 0);
                    let productPrice = subscribetionMonthsOrdered[+Object.keys(nonZeroPrices).length - 1]?.[1]?.price?.value / 100;

                    product.item_id = item.id;
                    product.item_name = item.attribute_data.name.tr;
                    product.price = productPrice;
                    product.item_brand = item.brand.name;
                    product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                    product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                    product.item_list_id = "HOME-SEU";
                    product.item_list_name = "Homepage - Son Eklenen Ürünler List";
                    product.index = keys;
                    lastAddedProductsDL.push(product);
                });
                dataLayer.push({
                    event: "view_item_list",
                    ecommerce: {
                        item_list_id: "HOME-SEU",
                        item_list_name: "Homepage - Son Eklenen Ürünler List",
                        items: lastAddedProductsDL
                    }
                });

                let discountedProducts = this.discounted.items.data;
                let discountedProductsDL = [];
                discountedProducts.map((item, keys) => {
                    let product = {};
                    let subscribetionMonthsOrdered = [];
                    if (item.variants[0]?.prices.length > 0) subscribetionMonthsOrdered = Object.entries(item.variants[0]?.prices).sort(([, a], [, b]) => a.subscription_months.value - b.subscription_months.value);
                    let nonZeroPrices = item.variants[0]?.prices.filter((price) => price.price.value > 0);
                    subscribetionMonthsOrdered = subscribetionMonthsOrdered.filter((price) => price[1].price.value > 0);
                    let productPrice = subscribetionMonthsOrdered[+Object.keys(nonZeroPrices).length - 1]?.[1]?.price?.value / 100;

                    product.item_id = item.id;
                    product.item_name = item.attribute_data.name.tr;
                    product.price = productPrice;
                    product.item_brand = item.brand.name;
                    product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                    product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                    product.item_list_id = "HOME-IUL";
                    product.item_list_name = "Homepage - İndirimli Ürünler List";
                    product.index = keys;
                    discountedProductsDL.push(product);
                });
                dataLayer.push({
                    event: "view_item_list",
                    ecommerce: {
                        item_list_id: "HOME-IUL",
                        item_list_name: "Homepage - İndirimli Ürünler List",
                        items: discountedProductsDL
                    }
                });

                let mostRentedProducts = this.mostRentedProducts.items.data;
                let mostRentedProductsDL = [];
                mostRentedProducts.map((item, keys) => {
                    let product = {};
                    let subscribetionMonthsOrdered = [];
                    if (item.variants[0]?.prices.length > 0) subscribetionMonthsOrdered = Object.entries(item.variants[0]?.prices).sort(([, a], [, b]) => a.subscription_months.value - b.subscription_months.value);
                    let nonZeroPrices = item.variants[0]?.prices.filter((price) => price.price.value > 0);
                    subscribetionMonthsOrdered = subscribetionMonthsOrdered.filter((price) => price[1].price.value > 0);
                    let productPrice = subscribetionMonthsOrdered[+Object.keys(nonZeroPrices).length - 1]?.[1]?.price?.value / 100;

                    product.item_id = item.id;
                    product.item_name = item.attribute_data.name.tr;
                    product.price = productPrice;
                    product.item_brand = item.brand.name;
                    product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                    product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                    product.item_list_id = "HOME-ECKU";
                    product.item_list_name = "Homepage - En Çok Kiralanan Ürünler List";
                    product.index = keys;
                    mostRentedProductsDL.push(product);
                });
                dataLayer.push({
                    event: "view_item_list",
                    ecommerce: {
                        item_list_id: "HOME-ECKU",
                        item_list_name: "Homepage - En Çok Kiralanan Ürünler List",
                        items: mostRentedProductsDL
                    }
                });
            }
        });
    },
    computed: {
        splidedAllProducts() {
            return this.splidedArray(this.allProducts.items.data, 1);
        },
        splidedNewProducts() {
            return this.splidedArray(this.newtagged.items.data, 1);
        },
        splidedDiscounted() {
            // Take first 8 items than split them into 2
            return this.splidedArray(this.discounted.items.data.slice(0, 16), 1);
        }
    },
    watch: {
        // whenever question changes, this function will run
        category_telefon(newRes, oldRes) {
            this.selectedSpeedCategoryProducts = newRes;
        }
    },
    mounted() {
        // calculate isOpen with cookie content
        // let cookies = document.cookie.split(";");
        // let isOpenCookie = null;
        // for (let i = 0; i < cookies.length; i++) {
        //     let cookie = cookies[i].split("=");
        //     if (cookie[0].trim() === "isOpen") {
        //         isOpenCookie = true;
        //     }
        // }
        //
        // if (isOpenCookie) {
        //     this.isOpen = false;
        // } else {
        //     this.isOpen = true;
        //     let date = new Date();
        //     date.setTime(date.getTime() + 1 * 24 * 60 * 60 * 1000);
        //     const expires = "expires=" + date.toUTCString();
        //     document.cookie = "isOpen" + "=" + "true" + "; " + expires + "; path=/";
        // }

        // read props test
        // console.log(JSON.parse(JSON.stringify(this.$parent)).affiliate);
        // console.log(this.$parent.affiliate);

        // this.isNKolayVisitor = (JSON.parse(JSON.stringify(this.$parent)).affiliate === "aktifbank");
        // const res = JSON.stringify(this.$parent, (key, value) => {
        //     if (typeof value === 'object' && value !== null) {
        //         if (value instanceof Array) {
        //             return value.map(
        //                 (item, index) =>
        //                     (index === value.length - 1 ?
        //                         'circular reference' : item));
        //         }
        //         return {...value, circular: 'circular reference'};
        //     }
        //     return value;
        // });
        console.log("affiliate", this.$parent.affiliate === "aktifbank");
        this.isNKolayVisitor = this.$parent.affiliate === "aktifbank";
        this.isMigrosVisitor = this.$parent.affiliate === "migros";
        console.log("affiliate", this.$parent.affiliate === "migros");

        // console.log('isNKolayVisitor', this.isNKolayVisitor);
        // console.log('this.$parent.affiliate', res);
    }
};
</script>

<template>

    <Head title="Kiralabunu | Yüzlerce Son Teknoloji Ürünü Hemen Kirala">
        <meta name="description" content="Telefondan Akıllı Süpürgeye Birçok Teknoloji Ürününü Kolayca Kiralayabildiğin Kiralabunu! Satın Almadan, Kredi Kartını Bloke Etmeden Kiralama Özgürlüğü." />
        <template v-for="item in sliders">
            <link rel="preload" as="image" :href="item.media[0].original_url" />
        </template>
    </Head>

    <main class="my-7 lg:my-12">
        <div class="mt-5 mb-[37px] hidden mts:block lg:hidden w-full mx-auto">
            <ul class="flex w-full space-x-3 justify-center text-xl leading-6.5 font-medium">
                <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                    <Link href="/nasil-calisir">Nasıl Çalışır?</Link>
                </li>
                <li>
                    <svg viewBox="0 0 1 18.297" class="h-full">
                        <path class="stroke-neutral-400" d="M 0 0 L 0 18.296875"></path>
                    </svg>
                </li>
                <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                    <Link href="/kurumsal">Kurumsal</Link>
                </li>
                <li>
                    <svg viewBox="0 0 1 18.297" class="h-full">
                        <path class="stroke-neutral-400" d="M 0 0 L 0 18.296875"></path>
                    </svg>
                </li>
                <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">
                    <Link href="/kategoriler/tum-urunler">İndirimli Ürünler</Link>
                </li>
                <!--                <li class="p-0 m-0 text-base text-left text-gray-900 box-border whitespace-no-wrap">-->
                <!--                    <Link href="/kategoriler/tum-urunler">Yaz Kampanyası</Link>-->
                <!--                </li>-->
            </ul>
        </div>

        <section class="-mt-7 lg:-mt-12 mb-4" v-if="!isNKolayVisitor">
            <!-- Slider -->
            <div class="w-full mx-auto">
                <div class="sliderclass">
                    <splide :options="{ rewind: true, gap: '1rem', perPage: 1, arrows: false, type: 'loop', autoplay: true, pagination: false }" class="block mts:hidden ts:hidden">
                        <splide-slide v-for="item in sliders.filter((item) => item.type === 'mobil')">
                            <Link :href="item.url">
                                <img class="w-full" :src="item.media[0].original_url" alt="" />
                            </Link>
                        </splide-slide>
                    </splide>

                    <splide :options="options" class="hidden mts:block ts:hidden">
                        <splide-slide v-for="item in sliders.filter((item) => item.type === 'tablet')">
                            <Link :href="item.url">
                                <img class="w-full" :src="item.media[0].original_url" alt="" />
                            </Link>
                        </splide-slide>
                    </splide>

                    <splide :options="options" class="hidden mts:hidden ts:block">
                        <splide-slide v-for="item in sliders.filter((item) => item.type === 'desktop')">
                            <Link :href="item.url">
                                <img class="w-full" :src="item.media[0].original_url" alt="" />
                            </Link>
                        </splide-slide>
                    </splide>

                    <!--                    <div>-->
                    <!--                        <svg class="w-full overflow-visible h-96 bg-[#61ba3f]">-->
                    <!--                            <rect rx="0" ry="0" x="0" y="0" width="1920" class="fill-current hidden md:block h-96"></rect>-->
                    <!--                        </svg>-->
                    <!--                        <img id="Mask_Group_70" src="../../images/Mask_Group_70.png" srcset="../../images/Mask_Group_70.png 1x, ../../images/<EMAIL> 2x">-->
                    <!--                        <div id="Group_115">-->
                    <!--                            <svg class="Ellipse_18">-->
                    <!--                                <ellipse id="Ellipse_18" rx="200" ry="200" cx="200" cy="200">-->
                    <!--                                </ellipse>-->
                    <!--                            </svg>-->
                    <!--                        </div>-->
                    <!--                        <img id="Mask_Group_63" src="../../images/Mask_Group_63.png" srcset="../../images/Mask_Group_63.png 1x, ../../images/<EMAIL> 2x">-->
                    <!--                    </div>-->
                </div>
            </div>
        </section>
        <section class="-mt-7 lg:-mt-12 mb-4" v-if="isNKolayVisitor">
            <!-- Slider -->
            <div class="w-full mx-auto">
                <div class="sliderclass">
                    <splide :options="{ rewind: true, gap: '1rem', perPage: 1, arrows: false, type: 'loop', autoplay: true }" class="block mts:hidden ts:hidden">
                        <splide-slide>
                            <Link href="/kategoriler/tum-urunler">
                                <picture>
                                    <source srcset="../../images/nkolay/kb-nkolay-mobil.webp" type="image/webp">
                                    <source srcset="../../images/nkolay/kb-nkolay-mobil.png" type="image/png">
                                    <img class="w-full" src="../../images/nkolay/kb-nkolay-mobil.png" />
                                </picture>
                            </Link>
                        </splide-slide>
                    </splide>

                    <splide :options="options" class="hidden mts:block ts:hidden">
                        <splide-slide>
                            <Link href="/kategoriler/tum-urunler">
                                <picture>
                                    <source srcset="../../images/nkolay/kb-nkolay-web.webp" type="image/webp">
                                    <source srcset="../../images/nkolay/kb-nkolay-web.png" type="image/png">
                                    <img class="w-full" src="../../images/nkolay/kb-nkolay-web.png" />
                                </picture>
                            </Link>
                        </splide-slide>
                    </splide>

                    <splide :options="options" class="hidden mts:hidden ts:block">
                        <splide-slide>
                            <Link href="/kategoriler/tum-urunler">
                                <picture>
                                    <source srcset="../../images/nkolay/kb-nkolay-web.webp" type="image/webp">
                                    <source srcset="../../images/nkolay/kb-nkolay-web.png" type="image/png">
                                    <img class="w-full" src="../../images/nkolay/kb-nkolay-web.png" />
                                </picture>
                            </Link>
                        </splide-slide>
                    </splide>

                    <!--                    <div>-->
                    <!--                        <svg class="w-full overflow-visible h-96 bg-[#61ba3f]">-->
                    <!--                            <rect rx="0" ry="0" x="0" y="0" width="1920" class="fill-current hidden md:block h-96"></rect>-->
                    <!--                        </svg>-->
                    <!--                        <img id="Mask_Group_70" src="../../images/Mask_Group_70.png" srcset="../../images/Mask_Group_70.png 1x, ../../images/<EMAIL> 2x">-->
                    <!--                        <div id="Group_115">-->
                    <!--                            <svg class="Ellipse_18">-->
                    <!--                                <ellipse id="Ellipse_18" rx="200" ry="200" cx="200" cy="200">-->
                    <!--                                </ellipse>-->
                    <!--                            </svg>-->
                    <!--                        </div>-->
                    <!--                        <img id="Mask_Group_63" src="../../images/Mask_Group_63.png" srcset="../../images/Mask_Group_63.png 1x, ../../images/<EMAIL> 2x">-->
                    <!--                    </div>-->
                </div>
            </div>

        </section>
        <section class="flex flex-row mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
            <div class="w-12/12 md:w-3/4 lg:w-7/12 mx-auto">
                <ul class="flex text-xs space-x-1 lg:space-x-4 text-center mt-1">
                    <li class="w-1/5 md:w-1/3 lg:w-1/5">
                        <div class="flex flex-col">
                            <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 107.74 78.83">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-2" x="17.71" y="1" width="89.04" height="61.66" rx="14.15" ry="14.15" />
                                                <rect x="18.71" y="15.15" width="86.99" height="14.34" />
                                                <circle class="cls-1" cx="85.86" cy="44.17" r="8.66" />
                                                <circle class="cls-1" cx="74.62" cy="44.17" r="8.66" />
                                            </g>
                                            <g>
                                                <circle class="cls-3" cx="20.12" cy="58.71" r="19.12" />
                                                <polyline class="cls-4" points="10.55 58.71 17.71 68.53 29.75 47.27" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-2xs xl:text-xs leading-none">Kart Limitin Sana Kalsın</div>
                        </div>
                    </li>
                    <li class="w-1/5 md:w-1/3 lg:w-1/5">
                        <div class="flex flex-col">
                            <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 126.16 126.7">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-2tr" x="40.92" y="19.93" width="44.97" height="86.95" rx="5.71" ry="5.71" />
                                                <rect class="cls-1tr" x="56.1" y="23.35" width="14.63" height="3.66" rx="1.83" ry="1.83" />
                                            </g>
                                            <g>
                                                <path
                                                    d="m63.4,126.7c-2.79,0-5.62-.19-8.46-.57-16.77-2.25-31.66-10.89-41.93-24.34S-1.68,71.71.57,54.94C2.82,38.17,11.46,23.28,24.91,13.01,38.36,2.74,55.01-1.67,71.77.57c18.15,2.43,34.36,12.6,44.46,27.9.61.92.35,2.16-.57,2.77-.92.61-2.16.35-2.77-.57-9.46-14.33-24.65-23.86-41.65-26.14-15.71-2.11-31.3,2.03-43.9,11.65C14.74,25.81,6.64,39.76,4.54,55.47c-2.11,15.71,2.03,31.3,11.65,43.9,9.62,12.6,23.57,20.7,39.28,22.8,32.43,4.35,62.36-18.5,66.7-50.94.15-1.1,1.17-1.86,2.25-1.72,1.09.15,1.86,1.15,1.72,2.25-4.26,31.78-31.52,54.94-62.74,54.94Z" />
                                                <polygon points="124.28 56.09 94.42 25.17 113.03 27.91 124.62 13.11 124.28 56.09" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-2xs xl:text-xs leading-none">Yenileme Opsiyonu</div>
                        </div>
                    </li>
                    <li class="w-1/5 md:w-1/3 lg:w-1/5">
                        <div class="flex flex-col">
                            <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 217.86 118.79">
                                    <g id="Layer_1-2">
                                        <g>
                                            <polyline class="cls-5eq"
                                                      points="110.17 64.25 159.33 64.25 159.33 9.13 156.46 5.14 159.33 16.92 184.09 16.92 188.93 19.41 215.36 50.87 215.36 90.92 209.27 98.15 199.4 98.15 195.2 88.03 184.37 82.31 172.49 85.32 165.49 96.61 165.29 99.24 110.17 99.24 106.81 89.07 101.96 84.65 93.11 82.18 81.1 87.13 76.06 99.24 62.2 98.85 55.91 94.11 54.29 85.93 54.29 62.21" />
                                            <g>
                                                <path class="cls-4eq" d="m31.1,2.5h118.85c5.18,0,9.38,4.2,9.38,9.38v87.36" />
                                                <path class="cls-4eq" d="m33.68,48.6h9.83c5.96,0,10.79,4.83,10.79,10.79v29.06c0,5.96,4.83,10.79,10.79,10.79h10.98" />
                                                <path class="cls-4eq" d="m159.33,16.92h21.55c4.3,0,8.37,1.93,11.08,5.26l23.41,28.69v37.58c0,5.96-4.83,10.79-10.79,10.79h-5.17" />
                                                <polyline class="cls-4eq" points="165.29 99.24 159.33 99.24 110.17 99.24" />
                                                <line class="cls-2eq" x1="54.29" y1="64.25" x2="159.33" y2="64.25" />
                                                <line class="cls-2eq" x1="54.29" y1="78.83" x2="159.33" y2="78.83" />
                                                <circle class="cls-1eq" cx="93.11" cy="99.24" r="17.06" />
                                                <circle class="cls-1eq" cx="182.35" cy="99.24" r="17.06" />
                                                <line class="cls-3eq" x1="18.84" y1="16.92" x2="62.2" y2="16.92" />
                                                <line class="cls-3eq" y1="32.28" x2="43.36" y2="32.28" />
                                                <polyline class="cls-4eq" points="171.1 16.92 171.1 50.87 215.36 50.87" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-2xs xl:text-xs leading-none">Ücretsiz Kargo</div>
                        </div>
                    </li>
                    <li class="w-1/5 md:w-1/3 lg:w-1/5">
                        <div class="flex flex-col">
                            <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 148.11 137.57">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-1re" x="0" y="79.35" width="20.8" height="54.14" rx="2.9" ry="2.9" />
                                                <path class="cls-2" d="m11.13,128.75c-2.61.49-4.85-1.74-4.37-4.36.27-1.46,1.45-2.65,2.91-2.93,2.62-.5,4.89,1.76,4.38,4.37-.28,1.46-1.46,2.63-2.92,2.91Z" />
                                                <path class="cls-2"
                                                      d="m108.64,106.42l30.07-12.44c5.69-2.35,10.59,4.85,6.22,9.19-.07.07-.14.14-.22.21-.37.35-.78.65-1.23.89l-57.41,31.1c-1.22.66-2.65.85-4,.53l-56.9-13.48c-2.56-.61-4.36-2.89-4.36-5.51v-20.81c0-2.65,1.83-4.94,4.41-5.53l25.11-5.69c.93-.21,1.9-.18,2.81.08l46.7,13.37c.08.02.17.05.25.07.96.23,7.9,2.06,8.51,7.26.06.52.02,1.04-.08,1.55-.4,2.06-2.06,7.24-8.54,5.59l-26.8-6.4" />
                                            </g>
                                            <path class="cls-2re"
                                                  d="m114.96,42.8c0-.5-.01-.99-.04-1.48-.04-.94.57-1.79,1.49-2.03l3.35-.88c1.07-.28,1.71-1.37,1.43-2.44l-2.07-7.91c-.28-1.07-1.37-1.71-2.44-1.43l-3.52.92c-.88.23-1.82-.15-2.25-.96-1.12-2.09-2.45-4.04-3.96-5.85-.6-.71-.6-1.75-.03-2.48l2.28-2.95c.68-.87.51-2.13-.36-2.81l-6.47-4.99c-.87-.67-2.13-.51-2.81.36l-2.34,3.04c-.56.73-1.55.99-2.38.61-2.1-.95-4.3-1.7-6.59-2.22-.9-.2-1.52-1.02-1.52-1.94v-3.87c0-1.1-.9-2-2-2h-8.18c-1.1,0-2,.9-2,2v3.87c0,.92-.63,1.74-1.53,1.94-2.12.48-4.17,1.16-6.12,2.01-.84.37-1.82.09-2.37-.64l-2.31-3.08c-.66-.88-1.92-1.06-2.8-.4l-6.54,4.9c-.88.66-1.06,1.92-.4,2.8l2.24,2.99c.56.74.54,1.78-.07,2.48-.93,1.07-1.79,2.2-2.58,3.38-.51.75-1.48,1.05-2.33.73l-3.45-1.27c-1.04-.38-2.18.15-2.57,1.19l-2.83,7.68c-.38,1.04.15,2.19,1.19,2.57l3.29,1.21c.88.32,1.42,1.21,1.29,2.14-.22,1.57-.34,3.17-.34,4.8,0,.22,0,.45,0,.67.02.92-.59,1.74-1.49,1.97l-3.32.87c-1.07.28-1.71,1.37-1.43,2.44l2.07,7.92c.28,1.07,1.37,1.71,2.44,1.43l3.13-.82c.9-.24,1.85.17,2.27,1,1.12,2.2,2.46,4.28,4.01,6.18.58.71.56,1.73,0,2.45l-1.93,2.5c-.68.87-.51,2.13.36,2.81l6.47,5c.87.67,2.13.51,2.81-.36l1.85-2.4c.57-.73,1.57-1,2.4-.6,2.23,1.06,4.6,1.88,7.06,2.44.9.2,1.52,1.02,1.52,1.94v3c0,1.1.9,2,2,2h8.18c1.1,0,2-.9,2-2v-3c0-.92.62-1.74,1.52-1.94,2.29-.52,4.5-1.27,6.6-2.22.84-.38,1.84-.11,2.4.63l1.81,2.43c.66.89,1.92,1.07,2.8.4l6.55-4.9c.88-.66,1.06-1.92.4-2.8l-1.89-2.52c-.55-.73-.55-1.75.04-2.45.97-1.16,1.87-2.39,2.69-3.68.5-.78,1.49-1.09,2.36-.77l3,1.11c1.04.38,2.19-.15,2.57-1.19l2.82-7.67c.38-1.04-.15-2.19-1.19-2.57l-3.17-1.17c-.86-.32-1.4-1.17-1.3-2.08.16-1.32.24-2.67.24-4.03Zm-22.17,12.18c-18,13.39-38.17-6.8-24.77-24.78.12-.17.28-.32.45-.45,17.99-13.38,38.16,6.79,24.77,24.78-.12.17-.28.32-.45.45Z" />
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-2xs xl:text-xs leading-none">Hasarda %70 Garanti</div>
                        </div>
                    </li>
                    <li class="w-1/5 md:w-1/3 lg:w-1/5">
                        <div class="flex flex-col">
                            <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                <svg class="w-[44px] ts:w-[55px] h-auto ts:h-[55px]" id="Layer_2" viewBox="0 0 80.08 80.08">
                                    <g id="Layer_1-2">
                                        <g>
                                            <!--                                            <rect class="cls-1we" x="1" y="1" width="78.08" height="78.08" rx="11.14" ry="11.14" />-->
                                            <g>
                                                <path class="cls-3we" d="m9.99,17.19h6.06c1.35,0,2.56.86,2.99,2.15l10.99,32.56c.43,1.28,1.64,2.15,2.99,2.15h25.07c1.12,0,2.15-.59,2.72-1.56l9.11-15.48c1.24-2.1-.28-4.76-2.72-4.76H24.65" />
                                                <circle class="cls-2we" cx="37.38" cy="61.99" r="4.18" />
                                                <circle class="cls-2we" cx="53.66" cy="61.99" r="4.18" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-2xs xl:text-xs leading-none">Kirala, Beğenirsen Satın Al</div>
                        </div>
                    </li>
                </ul>
            </div>
        </section>
        <section class="mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl my-8 category-banner relative">
            <Splide :has-track="false" aria-label="" :options="{ gap: '16px', rewind: true, perPage: 6, pagination: false, breakpoints: { 640: { perPage: 2 }, 1100: { perPage: 4 }, 1270: { perPage: 5 } } }" v-if="allProducts">
                <div class="text-xl md:text-2xl my-6 lg:my-0 mx-0 lg:mx-4 self-center w-full pb-3">Kategoriler</div>
                <SplideTrack class="w-full">
                    <SplideSlide>
                        <Link href="/kategoriler/telefon" class=" bg-bordergray rounded-lg py-4 px-4 flex flex-col justify-center items-center">
                            <img src="../../images/welcome-webp/homepage-category-images/telefon.png" class="w-full">
                            <span class="font-santralregular text-xs md:text-sm"> Telefon </span>
                        </Link>
                    </SplideSlide>
                    <SplideSlide>
                        <Link href="/kategoriler/bilgisayar-tablet" class=" bg-bordergray rounded-lg py-4 px-4 flex flex-col justify-center items-center">
                            <img src="../../images/welcome-webp/homepage-category-images/bilgisayar-tablet.png" class="w-full">
                            <span class="font-santralregular text-xs md:text-sm"> Bilgisayar & Tablet</span>
                        </Link>
                    </SplideSlide>
                    <SplideSlide>
                        <Link href="/kategoriler/kamera" class=" bg-bordergray rounded-lg py-4 px-4 flex flex-col justify-center items-center">
                            <img src="../../images/welcome-webp/homepage-category-images/kameralar.png" class="w-full">
                            <span class="font-santralregular text-xs md:text-sm"> Kameralar </span>
                        </Link>
                    </SplideSlide>
                    <SplideSlide>
                        <Link href="/kategoriler/oyun-konsolu-vr" class=" bg-bordergray rounded-lg py-4 px-4 flex flex-col justify-center items-center">
                            <img src="../../images/welcome-webp/homepage-category-images/oyun-konsolu-vr.png" class="w-full">
                            <span class="font-santralregular text-xs md:text-sm"> Oyun Konsolu & VR </span>
                        </Link>
                    </SplideSlide>
                    <SplideSlide>
                        <Link href="/kategoriler/ses-muzik" class=" bg-bordergray rounded-lg py-4 px-4 flex flex-col justify-center items-center">
                            <img src="../../images/welcome-webp/homepage-category-images/ses-muzik.png" class="w-full">
                            <span class="font-santralregular text-xs md:text-sm"> Ses & Müzik </span>
                        </Link>
                    </SplideSlide>
                    <SplideSlide>
                        <Link href="/kategoriler/saat" class=" bg-bordergray rounded-lg py-4 px-4 flex flex-col justify-center items-center">
                            <img src="../../images/welcome-webp/homepage-category-images/saat.png" class="w-full">
                            <span class="font-santralregular text-xs md:text-sm"> Saat </span>
                        </Link>
                    </SplideSlide>
                    <SplideSlide>
                        <Link href="/kategoriler/ev-eglence" class=" bg-bordergray rounded-lg py-4 px-4 flex flex-col justify-center items-center">
                            <img src="../../images/welcome-webp/homepage-category-images/akilli-ev.png" class="w-full">
                            <span class="font-santralregular text-xs md:text-sm"> Ev & Ofis </span>
                        </Link>
                    </SplideSlide>
                    <SplideSlide>
                        <Link href="/kategoriler/spor" class=" bg-bordergray rounded-lg py-4 px-4 flex flex-col justify-center items-center">
                            <img src="../../images/welcome-webp/homepage-category-images/saglik-spor.png" class="w-full">
                            <span class="font-santralregular text-xs md:text-sm"> Sağlık & Spor </span>
                        </Link>
                    </SplideSlide>
                    <SplideSlide>
                        <Link href="/kategoriler/kiralamobil" class=" bg-bordergray rounded-lg py-4 px-4 flex flex-col justify-center items-center">
                            <img src="../../images/welcome-webp/homepage-category-images/kiralamobil.png" class="w-full">
                            <span class="font-santralregular text-xs md:text-sm"> Kiralamobil </span>
                        </Link>
                    </SplideSlide>
                    <SplideSlide>
                        <a href="https://kiralamini.com" target="_blank" class=" bg-bordergray rounded-lg py-4 px-4 flex flex-col justify-center items-center">
                            <img src="../../images/welcome-webp/homepage-category-images/anne-ve-bebek.png" class="w-full">
                            <span class="font-santralregular text-xs md:text-sm"> Anne & Bebek </span>
                        </a>
                    </SplideSlide>
                </SplideTrack>

                <div class="splide__arrows absolute top-4 right-4">
                    <button class="splide__arrow splide__arrow--prev !-left-9">
                        <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                            <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                  transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                        </svg>
                    </button>
                    <button class="splide__arrow splide__arrow--next !-right-9">
                        <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                            <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                  transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                        </svg>
                    </button>
                </div>
            </Splide>
        </section>
        <section class="py-1 md:py-9 bg-[#f8f8f8] mt-6 md:mt-4" id="en-yeni-urunler">
            <div class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
                <div class="flex w-full justify-between">
                    <div class="text-xl md:text-2xl my-6 lg:my-0 mx-0 lg:mx-4 self-center text-center w-full md:w-auto">Son Eklenen Ürünler</div>
                    <div class="flex-1 ml-6 hidden md:flex">
                        <div class="flex-1 self-center border border-gray-200"></div>
                        <Link href="/tags/yeni"
                              class="cursor-pointer text-sm font-santralextrabold flex justify-center items-center border-2 rounded-full px-4 border-2 whitespace-nowrap md:ml-4 bg-white hover:bg-kbgreen hover:text-white">
                            Tümünü Gör
                        </Link>
                    </div>
                </div>
                <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0 lg:mt-9">
                    <Splide :has-track="false" aria-label="" :options="{ rewind: true, perPage: 4, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 3 } } }" v-if="newtagged">
                        <SplideTrack>
                            <SplideSlide v-for="(productGroup, index) in splidedNewProducts" :key="index" class="flex">
                                <category-box v-for="(product, index) in productGroup" :product="product" :new-container-classes="`w-full`" :auth="auth" />
                            </SplideSlide>
                        </SplideTrack>

                        <div class="splide__arrows">
                            <button class="splide__arrow splide__arrow--prev !-left-9">
                                <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                          transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                            <button class="splide__arrow splide__arrow--next !-right-9">
                                <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                          transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                        </div>
                    </Splide>
                    <div v-else>
                        <loader :active="true" message="Please wait 5 seconds" />
                    </div>
                </div>
                <div class="flex-1 ml-2 flex md:hidden justify-center">
                    <Link href="/tags/yeni" class="cursor-pointer border-3 rounded-full py-1.5 px-2 text-center mb-4 w-32 bg-white hover:bg-kbgreen hover:text-white font-santralextrabold text-sm"> Tümünü Gör</Link>
                </div>
            </div>
        </section>

        <!--        <section class="mt-0 flex flex-col-reverse lg:flex-row mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl justify-center items-center">-->

        <!--            <div class="lg:pl-6 lg:pr-12 w-full lg:w-2/4 self-center">-->
        <!--                <div class="mt-4 font-semibold leading-tight lg:leading-normal text-sm md:text-xl ts:text-2xl text-center lg:text-left text-kbblue">-->
        <!--                    Karşınızda Galaxy AI <br>-->
        <!--                    Telefon, saat ve daha fazlasını keşfet; istediğin süre boyunca kullanmanın keyfini çıkar.-->
        <!--                </div>-->
        <!--                <div class="mb-3 md:mb-0 mt-5 md:mt-9 text-center lg:text-left">-->
        <!--                    <Link href="/ara?term=samsung"-->
        <!--                          class="bg-kbgreen text-base lg:text-lg text-white rounded-full py-1.5 px-4 self-center font-santralextrabold hover:bg-white hover:text-kbgreen pt-2 border-1 border-transparent hover:border-kbgreen transition-all duration-200 ease-in-out">-->
        <!--                        Ürünlere Git-->
        <!--                    </Link>-->
        <!--                </div>-->
        <!--            </div>-->
        <!--            <div class="w-full lg:w-2/4 rounded-full">-->
        <!--                <picture>-->
        <!--                    <source srcset="../../images/welcome-webp/galaxy-ai-banner.webp" type="image/webp" />-->
        <!--                    <img src="../../images/welcome-webp/galaxy-ai-banner.png" alt="" loading="lazy" />-->
        <!--                </picture>-->
        <!--            </div>-->
        <!--        </section>-->

        <section class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12">
            <div class="text-xl md:text-3xl text-center w-full md:w-auto">Kiralamak</div>
            <div class="text-xl md:text-2xl font-santralregular text-center w-full md:w-auto">Esnek ve Sürdürülebilir</div>
            <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0 lg:mt-9">
                <Splide class="surdurebilirlik-slider" :has-track="false" aria-label=""
                        :options="{ arrows: false, autoplay: true, rewind: true, perPage: 4, pagination: true, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 4 } } }">
                    <SplideTrack>
                        <SplideSlide class="">
                            <div class="py-4 px-4 flex flex-col justify-center items-center">
                                <div class="relative">
                                    <img src="../../images/welcome-webp/esnek-surdurulebilir-yeni-1.png" class="w-full z-50 relative max-w-[200px]">
                                </div>
                                <span class="font-santralextrabold text-sm md:text-base text-center"> Ürünler yeni veya yeni gibi </span>
                                <span class="font-santralregular text-xs md:text-sm text-center"> Kalite kontrolü tamamlanmış tüm teknoloji ürünlerini kiralayabilirsin. </span>
                            </div>
                        </SplideSlide>
                        <SplideSlide class="">
                            <div class="py-4 px-4 flex flex-col justify-center items-center">
                                <div class="relative">
                                    <img src="../../images/welcome-webp/esnek-surdurulebilir-yeni-2.png" class="w-full z-50 relative max-w-[200px]">
                                </div>
                                <span class="font-santralextrabold text-sm md:text-base text-center"> Hasar onarım garantisi </span>
                                <span class="font-santralregular text-xs md:text-sm text-center"> %70 hasar onarım garantisi bizden, seçili ürünlerde istersen %100’e sen tamamlayabilirsin. </span>
                            </div>
                        </SplideSlide>
                        <SplideSlide class="">
                            <div class="py-4 px-4 flex flex-col justify-center items-center">
                                <div class="relative">
                                    <img src="../../images/welcome-webp/esnek-surdurulebilir-yeni-3.png" class="w-full z-50 relative max-w-[200px]">
                                </div>
                                <span class="font-santralextrabold text-sm md:text-base text-center"> Esnek kira süreleri </span>
                                <span class="font-santralregular text-xs md:text-sm text-center"> 1 aydan 24 aya varan kira sürelerinden sana en uygun olanı seçebilirsin. </span>
                            </div>
                        </SplideSlide>
                        <SplideSlide class="">
                            <div class="py-4 px-4 flex flex-col justify-center items-center">
                                <div class="relative">
                                    <img src="../../images/welcome-webp/esnek-surdurulebilir-yeni-4.png" class="w-full z-50 relative max-w-[200px]">
                                </div>
                                <span class="font-santralextrabold text-sm md:text-base text-center"> Üst modele geç veya iade et </span>
                                <span class="font-santralregular text-xs md:text-sm text-center"> Kiralaman devam ederken ürününü üst modeli ile değiştirebilir ya da kira sürenin sonunda iade edebilirsin. </span>
                            </div>
                        </SplideSlide>
                    </SplideTrack>
                </Splide>
            </div>
        </section>


        <section class="py-1 md:py-9 bg-[#f8f8f8]">
            <div class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
                <div class="flex w-full justify-between">
                    <div class="text-xl md:text-2xl my-2 lg:my-0 mx-0 lg:mx-4 self-center text-center w-full md:w-auto">İndirimli Ürünler</div>
                    <div class="flex-1 ml-6 hidden md:flex">
                        <div class="flex-1 self-center border border-gray-200"></div>
                        <Link href="indirimli-urunler" class="cursor-pointer text-sm font-santralextrabold flex justify-center items-center border-2 rounded-full px-4 border-2 whitespace-nowrap md:ml-4 bg-white hover:bg-kbgreen hover:text-white">
                            Tümünü
                            Gör
                        </Link>
                    </div>
                </div>
                <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0 lg:mt-9">
                    <Splide :has-track="false" aria-label="" :options="{ rewind: true, perPage: 4, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 3 } } }" v-if="discounted">
                        <SplideTrack>
                            <SplideSlide v-for="(productGroup, index) in splidedDiscounted" :key="index" class="flex">
                                <category-box v-for="(product, index) in productGroup" :product="product" :new-container-classes="`w-full`" :auth="auth" />
                            </SplideSlide>
                        </SplideTrack>
                        <div class="splide__arrows">
                            <button class="splide__arrow splide__arrow--prev !-left-9">
                                <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                          transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                            <button class="splide__arrow splide__arrow--next !-right-9">
                                <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                          transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                        </div>
                    </Splide>
                    <div v-else>
                        <loader :active="true" message="Please wait 5 seconds" />
                    </div>
                </div>
                <div class="flex-1 ml-2 flex md:hidden justify-center">
                    <Link href="/indirimli-urunler" class="cursor-pointer border-3 rounded-full py-1.5 px-2 text-center mb-4 w-32 bg-white hover:bg-kbgreen hover:text-white font-santralextrabold text-sm"> Tümünü Gör</Link>
                </div>
            </div>
        </section>
        <section class="mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl my-8 category-banner relative kiralasteps">
            <Splide :has-track="false" aria-label="" :options="{ gap: '30px', rewind: true, perPage: 3, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 3 } } }">
                <div class="text-2xl md:text-3xl my-6 lg:my-0 mx-0 lg:mx-4 self-center w-full">5 Adımda Kirala</div>
                <SplideTrack class="w-full">
                    <SplideSlide class="rounded-32 shadow-kiralastep !my-4 overflow-hidden h-full !ml-2">
                        <div class="bg-white flex flex-col justify-between items-start h-full">
                            <div class="flex flex-col justify-start items-start px-8 pt-8">
                                <div class="py-3.5 px-6 bg-icon-gray rounded-full text-lg font-santralextrabold leading-0 tracking-0 mb-3">1</div>
                                <div class="font-santralextrabold text-lg md:text-xl mb-1 min-h-14">Ürününü seç</div>
                                <div class="font-santralregular text-xs md:text-sm min-h-[100px] lg:min-h-[160px]">İhtiyacın olan veya kullanmak ürünü seç ve kira süreni belirle.</div>
                            </div>
                            <picture class="w-full">
                                <source srcset="../../images/welcome-webp/kiralastep/kiralastep-1.webp" type="image/webp" />
                                <source srcset="../../images/welcome-webp/kiralastep/kiralastep-1.png" type="image/png" />
                                <img src="../../images/welcome-webp/kiralastep/kiralastep-1.png" class="w-full">
                            </picture>
                        </div>
                    </SplideSlide>
                    <SplideSlide class="rounded-32 shadow-kiralastep !my-4 overflow-hidden h-full !ml-2">
                        <div class="bg-white flex flex-col justify-between items-start ">
                            <div class="flex flex-col justify-start items-start px-8 pt-8">
                                <div class="py-3.5 px-6 bg-icon-gray rounded-full text-lg font-santralextrabold leading-0 tracking-0 mb-3">2</div>
                                <div class="font-santralextrabold text-lg md:text-xl mb-1 min-h-14">Sepeti onayla</div>
                                <div class="font-santralregular text-xs md:text-sm min-h-[100px] lg:min-h-[160px]">Kredi/banka kartını kaydet, ilk kira bedelini öde ve siparişini oluştur.</div>
                            </div>
                            <picture class="w-full">
                                <source srcset="../../images/welcome-webp/kiralastep/kiralastep-2.webp" type="image/webp" />
                                <source srcset="../../images/welcome-webp/kiralastep/kiralastep-2.png" type="image/png" />
                                <img src="../../images/welcome-webp/kiralastep/kiralastep-2.png" class="w-full">
                            </picture>
                        </div>
                    </SplideSlide>
                    <SplideSlide class="rounded-32 shadow-kiralastep !my-4 overflow-hidden h-full !ml-2">
                        <div class="bg-white flex flex-col justify-between items-start ">
                            <div class="flex flex-col justify-start items-start px-8 pt-8">
                                <div class="py-3.5 px-6 bg-icon-gray rounded-full text-lg font-santralextrabold leading-0 tracking-0 mb-3">3</div>
                                <div class="font-santralextrabold text-lg md:text-xl mb-1 min-h-14">Siparişin değerlendirilsin</div>
                                <div class="font-santralregular text-xs md:text-sm min-h-[100px] lg:min-h-[160px]">Siparişini verdikten sonra 24 saat içinde ücretsiz findeks kontrolü yapıyoruz. Kontrolü tamamlamamız için tarafına iletilen findeks sms’ini onayla.</div>
                            </div>
                            <picture class="w-full">
                                <source srcset="../../images/welcome-webp/kiralastep/kiralastep-3.webp" type="image/webp" />
                                <source srcset="../../images/welcome-webp/kiralastep/kiralastep-3.png" type="image/png" />
                                <img src="../../images/welcome-webp/kiralastep/kiralastep-3.png" class="w-full">
                            </picture>
                        </div>
                    </SplideSlide>
                    <SplideSlide class="rounded-32 shadow-kiralastep !my-4 overflow-hidden h-full !ml-2">
                        <div class="bg-white flex flex-col justify-between items-start ">
                            <div class="flex flex-col justify-start items-start px-8 pt-8">
                                <div class="py-3.5 px-6 bg-icon-gray rounded-full text-lg font-santralextrabold leading-0 tracking-0 mb-3">4</div>
                                <div class="font-santralextrabold text-lg md:text-xl mb-1 min-h-14">Ürününü teslim al, kullanmaya başla</div>
                                <div class="font-santralregular text-xs md:text-sm min-h-[100px] lg:min-h-[160px]">Onaylanan siparişini en kısa sürede sana ulaştırıyoruz. Sana da ürününü keyifle kullanmak kalıyor.</div>
                            </div>
                            <picture class="w-full">
                                <source srcset="../../images/welcome-webp/kiralastep/kiralastep-4.webp" type="image/webp" />
                                <source srcset="../../images/welcome-webp/kiralastep/kiralastep-4.png" type="image/png" />
                                <img src="../../images/welcome-webp/kiralastep/kiralastep-4.png" class="w-full">
                            </picture>
                        </div>
                    </SplideSlide>
                    <SplideSlide class="rounded-32 shadow-kiralastep !my-4 overflow-hidden h-full !ml-2">
                        <div class="bg-white flex flex-col justify-between items-start ">
                            <div class="flex flex-col justify-start items-start px-8 pt-8">
                                <div class="py-3.5 px-6 bg-icon-gray rounded-full text-lg font-santralextrabold leading-0 tracking-0 mb-3">5</div>
                                <div class="font-santralextrabold text-lg md:text-xl mb-1 min-h-14">Kira sürenin sonunda iade et, ürün yeni kullanıcısına gitsin</div>
                                <div class="font-santralregular text-xs md:text-sm min-h-[100px] lg:min-h-[160px]">İade edilen cihazlar titizlikle incelenir, tüm veriler silinir, gerekli bakım ve onarımlar yapılır. Ardından, en iyi performansla yeniden kiralanmaya sunulur. Bu sayede teknoloji sürdürülebilir şekilde döngüde kalır.</div>
                            </div>
                            <picture class="w-full">
                                <source srcset="../../images/welcome-webp/kiralastep/kiralastep-5.webp" type="image/webp" />
                                <source srcset="../../images/welcome-webp/kiralastep/kiralastep-5.png" type="image/png" />
                                <img src="../../images/welcome-webp/kiralastep/kiralastep-5.png" class="w-full">
                            </picture>
                        </div>
                    </SplideSlide>
                </SplideTrack>

                <div class="splide__arrows absolute top-4 right-4">
                    <button class="splide__arrow splide__arrow--prev !-left-9 !bg-icon-gray !px-1">
                        <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                            <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                  transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                        </svg>
                    </button>
                    <button class="splide__arrow splide__arrow--next !-right-9 !bg-icon-gray !px-1">
                        <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                            <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                  transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                        </svg>
                    </button>
                </div>
            </Splide>
        </section>

        <section class="mt-14">
            <div class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
                <div class="flex">
                    <div class="text-xl ts:text-2xl self-center md:text-left ts:mx-4 w-full lg:w-auto">En Çok Kiralanan Ürünler</div>
                    <div class="hidden md:flex md:w-2/3 flex-1 ml-6">
                        <div class="flex-1 self-center border-1 border-bordergray"></div>
                        <Link href="#" class="font-santralextrabold border text-sm whitespace-nowrap rounded-full py-1 flex justify-center items-center px-4 border-2 md:w-32 ml-4 hover:bg-kbgreen hover:text-white"> Tümünü Gör</Link>
                    </div>
                </div>
                <div class="w-full">
                    <Splide :has-track="false" aria-label="" class="w-full mt-5" :options="{ rewind: true, perPage: 2, pagination: false, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 } } }" v-if="mostRentedProducts">
                        <SplideTrack class="w-full">
                            <SplideSlide class="flex w-full" v-for="(product, index) in mostRentedProducts.items.data.slice(0, 4)">
                                <vertical-product-box :class="'!mx-2'" :product="product"></vertical-product-box>
                            </SplideSlide>
                        </SplideTrack>
                        <div class="splide__arrows">
                            <button class="splide__arrow splide__arrow--prev !-left-9">
                                <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                          transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                            <button class="splide__arrow splide__arrow--next !-right-9">
                                <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                          transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                        </div>
                    </Splide>
                    <div v-else>
                        <loader :active="true" message="Please wait 5 seconds" />
                    </div>
                </div>
            </div>
        </section>
        <section class="bg-[#f8f8f8] py-5 mt-5 ">
            <div v-if="false" class="flex flex-col lg:flex-row mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl justify-center items-center">
                <div class="w-full lg:w-2/3 rounded-full lg:pr-10">
                    <picture>
                        <source srcset="../../images/welcome-webp/main-new-product-banner.webp" type="image/webp" />
                        <img src="../../images/welcome-webp/main-new-product-banner.png" alt="" class="w-full" loading="lazy" />
                    </picture>
                </div>
                <div class="lg:ml-12 w-full lg:w-1/3 self-center">
                    <div class="w-full flex lg:block justify-center md:justify-start mt-4 lg:mt-0">
                        <!--                    <img src="../../images/brands/samsung.png" alt="" />-->
                    </div>
                    <div class="mt-4 font-semibold text-xl ts:text-2xl text-center lg:text-left">
                        Aramıza Yeni Katılanlar
                        <!--                    <span class="hidden md:block lg:hidden"> </span>-->
                        <!--                    <span class="text-kbgreen">Samsung S23</span>'leri incele, hemen kirala.-->
                    </div>
                    <div class="mt-4 text-black text-center md:text-left">
                        Süpürgeden klimaya, oyun konsolundan tornavida setine ihtiyacın olan tüm ürünler burada!
                    </div>
                    <div class="mt-5 md:mt-9 text-center lg:text-left">
                        <Link href="/kategoriler/tum-urunler?filter[brand]=&filter[price]=&filter[collections]=&orderBy=yeniden-eskiye"
                              class="bg-kbgreen text-base lg:text-lg text-white rounded-full py-1.5 px-4 self-center font-santralextrabold hover:bg-white hover:text-kbgreen pt-2 border-1 border-transparent hover:border-kbgreen transition-all duration-200 ease-in-out">
                            Ürünlere Git
                        </Link>
                    </div>
                </div>
            </div>
            <div class=" flex flex-col lg:flex-row mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl justify-center items-center">
                <div class="lg:pl-6 lg:pr-12 w-full lg:w-2/3 self-center">
                    <div class="mt-4 font-semibold leading-tight lg:leading-normal text-xl md:text-3xl ts:text-4xl text-center lg:text-left text-kbblue">
                        Evdeki Büyük Yardımcılar Kiralabunu'da!
                    </div>
                    <div class="mt-4 font-semibold leading-tight lg:leading-normal text-sm md:text-xl ts:text-2xl text-center lg:text-left text-kbgreen">
                        Çamaşır makinesi, bulaşık makinesi, kurutma makinesi ve buzdolabı, ihtiyacına göre kirala, süre sonunda satın al.
                    </div>
                    <div class="mb-3 md:mb-0 mt-5 md:mt-9 text-center lg:text-left">
                        <a class="bg-kbgreen text-base lg:text-lg text-white rounded-full py-1.5 px-4 self-center font-santralextrabold hover:bg-white hover:text-kbgreen pt-2 border-1 border-transparent hover:border-kbgreen transition-all duration-200 ease-in-out"
                           href="/kategoriler/ev-eglence/beyaz-esya">
                            Şimdi Keşfet
                        </a>
                    </div>
                </div>
                <div class="w-full lg:w-1/3 rounded-full">
                    <picture>
                        <source srcset="../../images/welcome-webp/beyaz-esya-banner.webp" type="image/webp">
                        <img src="../../images/welcome-webp/beyaz-esya-banner.png" alt="" loading="lazy">
                    </picture>
                </div>
            </div>
        </section>

        <section class="hidden mt-3 bg-[#f8f8f8] py-5">
            <div class="flex mx-auto flex-col max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
                <div class="flex px-4 md:px-0">
                    <div class="text-3xl self-center text-center w-full lg:w-auto">Kategorilere Göz At</div>
                    <div class="hidden md:flex md:w-2/3 flex-1 ml-6">
                        <div class="flex-1 self-center border border-gray-200"></div>
                        <!--                        <div class="flex">-->
                        <!--                            <div class="border rounded-full border-3 w-12 h-12 flex justify-center items-center mx-3 bg-white">-->
                        <!--                                <svg xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">-->
                        <!--                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z" transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />-->
                        <!--                                </svg>-->
                        <!--                            </div>-->
                        <!--                            <div class="border rounded-full border-3 w-12 h-12 flex justify-center items-center rotate-180 bg-white">-->
                        <!--                                <svg xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">-->
                        <!--                                    <path id="Path_18" data-name="Path 18" d="M.456.456a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2L11.469,11.47a1.558,1.558,0,0,1-2.2,0L.456,2.659A1.558,1.558,0,0,1,.456.456Z" transform="translate(11.926) rotate(90)" fill="#231f20" />-->
                        <!--                                </svg>-->
                        <!--                            </div>-->
                        <!--                        </div>-->
                    </div>
                </div>
                <div class="flex flex-wrap">
                    <div class="w-1/4 mt-4 max-h-[462px] overflow-y-scroll rounded-2lg">
                        <ul class="flex flex-col text-sm bg-white mx-4 rounded-2lg">
                            <li class="flex justify-between items-center group" @click="changeSpeedCategory('telefon')">
                                <a :class="[selectedSpeedCategory == 1 ? 'bg-kbgreen' : '']"
                                   class="cursor-pointer w-full py-3 px-3 rounded-t-2lg active:bg-kbgreen focus:bg-kbgreen group-hover:bg-kbgreen group-hover:text-white focus:text-white active:text-white">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20.058" height="29.741" viewBox="0 0 20.058 29.741" class="group-hover:text-white group-hover:fill-white stroke-kbgreen group-hover:stroke-white min-w-[45px]">
                                            <g id="Group_4184" data-name="Group 4184" transform="translate(0.5 -161.9)">
                                                <rect id="Rectangle_955" data-name="Rectangle 955" width="19.058" height="28.741" rx="1.826" transform="translate(0 162.4)" fill="none" stroke-miterlimit="10" stroke-width="1" />
                                                <line id="Line_71" data-name="Line 71" x2="3.781" transform="translate(7.786 187.79)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                            </g>
                                        </svg>
                                        <span class="ml-3 whitespace-nowrap text-base">Telefon</span>
                                    </div>
                                </a>
                            </li>
                            <li class="flex justify-between items-center group" @click="changeSpeedCategory('bilgisayar')">
                                <a class="cursor-pointer w-full py-3 px-3 active:bg-kbgreen focus:bg-kbgreen group-hover:bg-kbgreen group-hover:text-white focus:text-white active:text-white">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="34.003" height="22.617" viewBox="0 0 34.003 22.617" class="group-hover:text-white group-hover:fill-white stroke-kbgreen group-hover:stroke-white min-w-[45px]">
                                            <g id="Group_4191" data-name="Group 4191" transform="translate(0.5 -267.359)">
                                                <rect id="Rectangle_963" data-name="Rectangle 963" width="28.521" height="17.297" rx="2.835" transform="translate(2.168 267.859)" fill="none" stroke-miterlimit="10" stroke-width="1" />
                                                <path id="Rectangle_964" data-name="Rectangle 964" d="M0,0H33a0,0,0,0,1,0,0V1.32a3,3,0,0,1-3,3H3a3,3,0,0,1-3-3V0A0,0,0,0,1,0,0Z" transform="translate(0 285.157)" fill="none" stroke-linecap="round"
                                                      stroke-linejoin="round" stroke-width="1" />
                                                <line id="Line_83" data-name="Line 83" x2="2.818" transform="translate(15.092 270.532)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                            </g>
                                        </svg>
                                        <span class="ml-3 whitespace-nowrap text-base">Bilgisayar</span>
                                    </div>
                                </a>
                            </li>
                            <li class="flex justify-between items-center group" @click="changeSpeedCategory('tablet')">
                                <a class="cursor-pointer w-full py-3 px-3 active:bg-kbgreen focus:bg-kbgreen group-hover:bg-kbgreen group-hover:text-white focus:text-white active:text-white">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="#000000" width="32" height="32" class="group-hover:text-white group-hover:fill-white fill-kbgreen group-hover:stroke-white min-w-[45px]" viewBox="0 0 30 30">
                                            <path
                                                d="M6.5 24h17c.67 0 .65 1 0 1h-17c-.65 0-.662-1 0-1zm8.5 2c.552 0 1 .448 1 1s-.448 1-1 1-1-.448-1-1 .448-1 1-1zM4 28.5c0 .822.678 1.5 1.5 1.5h19c.822 0 1.5-.678 1.5-1.5v-27c0-.822-.678-1.5-1.5-1.5h-19C4.678 0 4 .678 4 1.5zm1 0v-27c0-.286.214-.5.5-.5h19c.286 0 .5.214.5.5v27c0 .286-.214.5-.5.5h-19c-.286 0-.5-.214-.5-.5z" />
                                        </svg>
                                        <span class="ml-3 whitespace-nowrap text-base">Tablet</span>
                                    </div>
                                </a>
                            </li>
                            <li class="flex justify-between items-center group" @click="changeSpeedCategory('saat')">
                                <a class="cursor-pointer w-full py-3 px-3 active:bg-kbgreen focus:bg-kbgreen group-hover:bg-kbgreen group-hover:text-white focus:text-white active:text-white">
                                    <div class="flex items-center">
                                        <svg class="group-hover:text-white group-hover:fill-white stroke-kbgreen group-hover:stroke-white min-w-[45px]" width="30px" height="30px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8.5 19H15.5C17.83 19 19 17.83 19 15.5V8.5C19 6.17 17.83 5 15.5 5H8.5C6.17 5 5 6.17 5 8.5V15.5C5 17.83 6.17 19 8.5 19Z" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round"
                                                  stroke-linejoin="round" />
                                            <path d="M16 2H8" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M16 22H8" stroke-width="1" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M11.5 9.5V12.5H14.5" stroke-width="1" stroke-miterlimit="10" stroke="#70d44b" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <span class="ml-3 whitespace-nowrap text-base">Saat</span>
                                    </div>
                                </a>
                            </li>
                            <li class="flex justify-between items-center group" @click="changeSpeedCategory('oyun')">
                                <a class="cursor-pointer w-full py-3 px-3 active:bg-kbgreen focus:bg-kbgreen group-hover:bg-kbgreen group-hover:text-white focus:text-white active:text-white active">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="45.508" height="29.806" viewBox="0 0 45.508 29.806" class="group-hover:text-white group-hover:fill-white stroke-kbgreen group-hover:stroke-white min-w-[45px]">
                                            <g id="Group_4190" data-name="Group 4190" transform="translate(0.545 -161.848)">
                                                <path id="Path_3732" data-name="Path 3732"
                                                      d="M603.987,168.7h0a1.3,1.3,0,0,1-1.3-1.3h0a1.3,1.3,0,0,0-1.3-1.3H598.63a1.305,1.305,0,0,0-1.306,1.3h0a1.3,1.3,0,0,1-1.3,1.3h0a1.305,1.305,0,0,0-1.3,1.306v2.748a1.3,1.3,0,0,0,1.3,1.3h0a1.3,1.3,0,0,1,1.3,1.3h0a1.306,1.306,0,0,0,1.306,1.306h2.748a1.306,1.306,0,0,0,1.3-1.306h0a1.3,1.3,0,0,1,1.3-1.3h0a1.306,1.306,0,0,0,1.306-1.3v-2.748A1.306,1.306,0,0,0,603.987,168.7Z"
                                                      transform="translate(-588.879 2.312)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <path id="Path_3733" data-name="Path 3733"
                                                      d="M602.143,169.885h0a.711.711,0,0,1-.712-.712h0a.712.712,0,0,0-.712-.712h-1.5a.713.713,0,0,0-.713.712h0a.711.711,0,0,1-.712.712h0a.712.712,0,0,0-.712.713v1.5a.711.711,0,0,0,.712.712h0a.711.711,0,0,1,.712.712h0a.712.712,0,0,0,.713.712h1.5a.711.711,0,0,0,.712-.712h0a.711.711,0,0,1,.712-.712h0a.711.711,0,0,0,.712-.712v-1.5A.712.712,0,0,0,602.143,169.885Z"
                                                      transform="translate(-588.846 2.345)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_309" data-name="Ellipse 309" cx="1.717" cy="1.717" r="1.717" transform="translate(30.497 168.058)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_310" data-name="Ellipse 310" cx="1.717" cy="1.717" r="1.717" transform="translate(34.483 172.045)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_311" data-name="Ellipse 311" cx="1.717" cy="1.717" r="1.717" transform="translate(30.587 175.942)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_312" data-name="Ellipse 312" cx="1.717" cy="1.717" r="1.717" transform="translate(26.635 171.92)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <path id="Path_3734" data-name="Path 3734"
                                                      d="M626.438,161.361a6.294,6.294,0,0,0-4.649-1.107l-7.67,1.545a3.7,3.7,0,0,0-.814.266,5.037,5.037,0,0,1-4.272,0,3.7,3.7,0,0,0-.814-.266l-7.67-1.545a6.292,6.292,0,0,0-4.649,1.107c-6.127,5.158-8.2,18.032-6.208,26.078.337,1.359,1.962,1.642,3.7,1.388a9.67,9.67,0,0,0,4.919-2.348l3.232-2.822a7.739,7.739,0,0,1,1.186-.855c.181-.105.383-.223.6-.345a9.215,9.215,0,0,1,4.315-1.207,3.614,3.614,0,0,0,1.558-.4,4.252,4.252,0,0,1,3.939,0,3.606,3.606,0,0,0,1.558.4,9.222,9.222,0,0,1,4.315,1.207c.213.122.416.239.6.345a7.791,7.791,0,0,1,1.186.855l3.232,2.822a9.674,9.674,0,0,0,4.92,2.348c1.738.253,3.363-.029,3.7-1.388C634.637,179.393,632.565,166.519,626.438,161.361Z"
                                                      transform="translate(-588.959 2.23)" fill="none" stroke-miterlimit="10" stroke-width="1" />
                                            </g>
                                        </svg>
                                        <span class="ml-3 whitespace-nowrap text-base">Oyun Konsolu & VR</span>
                                    </div>
                                </a>
                            </li>
                            <li class="flex justify-between items-center group" @click="changeSpeedCategory('kameralar')">
                                <a class="cursor-pointer w-full py-3 px-3 active:bg-kbgreen focus:bg-kbgreen group-hover:bg-kbgreen group-hover:text-white focus:text-white active:text-white">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="32.534" height="25.543" viewBox="0 0 32.534 25.543" class="group-hover:text-white group-hover:fill-white stroke-kbgreen group-hover:stroke-white min-w-[45px]">
                                            <g id="Group_4185" data-name="Group 4185" transform="translate(0.5 -161.9)">
                                                <path id="Path_3723" data-name="Path 3723"
                                                      d="M244.031,163.145h-7.079v-1.086a1.889,1.889,0,0,0-1.889-1.889h-9.609a1.889,1.889,0,0,0-1.889,1.889v1.086h-7.079a1.994,1.994,0,0,0-1.994,1.994v17.58a1.994,1.994,0,0,0,1.994,1.994h27.546a1.994,1.994,0,0,0,1.994-1.994v-17.58A1.994,1.994,0,0,0,244.031,163.145Z"
                                                      transform="translate(-214.491 2.23)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_295" data-name="Ellipse 295" cx="6.956" cy="6.956" r="6.956" transform="translate(8.548 169.203)" fill="none" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_296" data-name="Ellipse 296" cx="1.413" cy="1.413" r="1.413" transform="translate(25.51 168.275)" fill="none" stroke-miterlimit="10" stroke-width="1" />
                                            </g>
                                        </svg>
                                        <span class="ml-3 whitespace-nowrap text-base">Kameralar</span>
                                    </div>
                                </a>
                            </li>
                            <li class="flex justify-between items-center group" @click="changeSpeedCategory('ses')">
                                <a class="cursor-pointer w-full py-3 px-3 active:bg-kbgreen focus:bg-kbgreen group-hover:bg-kbgreen group-hover:text-white focus:text-white active:text-white">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="29.334" height="29.741" viewBox="0 0 29.334 29.741" class="group-hover:text-white group-hover:fill-white stroke-kbgreen group-hover:stroke-white min-w-[45px]">
                                            <g id="Group_4186" data-name="Group 4186" transform="translate(0.5 -161.9)">
                                                <rect id="Rectangle_956" data-name="Rectangle 956" width="17.218" height="23.725" rx="1.792" transform="translate(5.491 162.4)" fill="none" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_297" data-name="Ellipse 297" cx="1.695" cy="1.695" r="1.695" transform="translate(12.505 167.077)" fill="none" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_298" data-name="Ellipse 298" cx="3.746" cy="3.746" r="3.746" transform="translate(10.453 165.025)" fill="none" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_299" data-name="Ellipse 299" cx="1.695" cy="1.695" r="1.695" transform="translate(12.505 177.806)" fill="none" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_300" data-name="Ellipse 300" cx="3.746" cy="3.746" r="3.746" transform="translate(10.453 175.754)" fill="none" stroke-miterlimit="10" stroke-width="1" />
                                                <rect id="Rectangle_957" data-name="Rectangle 957" width="3.389" height="2.508" transform="translate(12.505 186.125)" fill="none" stroke-miterlimit="10" stroke-width="1" />
                                                <rect id="Rectangle_958" data-name="Rectangle 958" width="17.346" height="2.508" rx="1.237" transform="translate(5.525 188.633)" fill="none" stroke-miterlimit="10" stroke-width="1" />
                                                <line id="Line_72" data-name="Line 72" y2="6.202" transform="translate(0 170.873)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <line id="Line_73" data-name="Line 73" y2="3.627" transform="translate(2.949 172.161)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <line id="Line_74" data-name="Line 74" y2="6.202" transform="translate(28.334 170.873)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <line id="Line_75" data-name="Line 75" y2="3.627" transform="translate(25.386 172.161)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                            </g>
                                        </svg>
                                        <span class="ml-3 whitespace-nowrap text-base">Ses ve Müzik</span>
                                    </div>
                                </a>
                            </li>
                            <li class="flex justify-between items-center group" @click="changeSpeedCategory('evaletleri')">
                                <a class="cursor-pointer w-full py-3 px-3 active:bg-kbgreen focus:bg-kbgreen group-hover:bg-kbgreen group-hover:text-white focus:text-white active:text-white active">
                                    <div class="flex items-center">
                                        <svg version="1.0" xmlns="http://www.w3.org/2000/svg" class="group-hover:text-white group-hover:fill-white fill-kbgreen group-hover:stroke-white min-w-[45px]" width="30" height="30"
                                             viewBox="0 0 596.000000 595.000000" preserveAspectRatio="xMidYMid meet">
                                            <g transform="translate(0.000000,595.000000) scale(0.100000,-0.100000)" stroke="none">
                                                <path d="M2727 5819 c-504 -39 -1015 -225 -1424 -519 l-83 -59 0 63 c0 80 -27
124 -87 145 -35 13 -46 13 -78 0 -67 -25 -80 -53 -83 -191 l-4 -120 -97 96
c-95 93 -100 96 -144 96 -55 0 -100 -33 -117 -84 -18 -56 -3 -85 98 -188 l96
-98 -118 0 c-135 0 -164 -10 -191 -66 -25 -52 -15 -101 30 -141 32 -31 40 -33
105 -33 l71 0 -46 -60 c-168 -218 -342 -570 -429 -867 -80 -273 -111 -496
-110 -813 0 -201 4 -271 23 -390 103 -675 422 -1267 919 -1709 435 -386 886
-596 1497 -698 158 -26 622 -26 780 0 200 34 344 67 493 116 824 270 1493 920
1784 1734 56 158 113 387 139 557 19 119 23 189 23 390 1 317 -30 540 -110
813 -87 297 -261 649 -429 867 l-46 60 71 0 c65 0 73 2 105 33 45 40 55 89 30
141 -27 56 -56 66 -191 66 l-118 0 96 98 c101 103 116 132 98 188 -17 51 -62
84 -117 84 -44 0 -49 -3 -144 -96 l-97 -96 -4 120 c-4 139 -16 167 -85 191
-35 13 -46 13 -79 0 -58 -22 -84 -67 -84 -145 l0 -63 -82 59 c-533 383 -1203
569 -1861 519z m448 -249 c630 -59 1199 -334 1642 -793 414 -431 678 -1031
709 -1615 4 -73 4 -132 0 -132 -7 0 -111 26 -207 51 l-36 9 -7 101 c-26 377
-183 817 -404 1129 -393 556 -973 908 -1637 994 -245 32 -562 16 -808 -40
-568 -130 -1064 -465 -1409 -954 -221 -312 -378 -752 -404 -1129 l-7 -101 -36
-9 c-96 -25 -200 -51 -207 -51 -12 0 6 256 26 377 132 769 575 1416 1242 1814
456 272 1014 398 1543 349z m-85 -491 c283 -20 535 -88 781 -211 441 -220 791
-582 984 -1019 36 -82 85 -214 85 -231 0 -4 -898 -8 -1995 -8 -1097 0 -1995 4
-1995 8 0 43 103 282 177 411 100 174 173 270 308 410 180 187 357 316 589
431 278 138 528 199 896 218 14 1 90 -3 170 -9z m1924 -1761 c59 -409 13 -775
-144 -1151 -224 -538 -703 -977 -1266 -1163 -818 -269 -1714 -14 -2266 646
-389 464 -551 1051 -462 1668 l6 42 2063 0 2063 0 6 -42z m-4396 -586 c31
-265 111 -535 228 -771 310 -626 862 -1072 1534 -1241 201 -51 322 -65 565
-65 161 0 251 5 335 18 670 104 1236 463 1614 1025 200 298 335 668 378 1034
7 56 16 98 22 98 6 -1 61 -13 121 -28 l110 -27 -3 -30 c-7 -88 -26 -204 -53
-322 -163 -719 -627 -1337 -1277 -1698 -269 -150 -556 -246 -887 -297 -199
-30 -521 -30 -720 0 -331 51 -618 147 -887 297 -570 317 -1001 834 -1208 1449
-58 173 -109 410 -122 571 l-3 30 105 26 c58 15 112 27 121 28 12 1 18 -19 27
-97z" />
                                                <path d="M2833 4826 c-127 -31 -262 -133 -321 -244 -149 -280 -1 -627 303
-708 68 -19 200 -17 266 2 136 41 241 129 303 255 51 105 64 193 44 296 -23
114 -60 187 -137 264 -125 125 -294 175 -458 135z m216 -256 c199 -101 181
-385 -29 -455 -36 -12 -68 -16 -101 -12 -234 28 -301 334 -99 455 47 28 63 32
122 32 49 0 80 -6 107 -20z" />
                                                <path d="M2822 1860 c-104 -38 -184 -116 -223 -218 -17 -47 -20 -73 -17 -147
3 -79 8 -98 38 -153 68 -128 179 -194 325 -195 70 -1 89 3 146 30 86 40 138
88 180 165 33 60 34 68 34 168 0 94 -3 111 -27 160 -37 75 -100 137 -178 174
-84 40 -195 46 -278 16z m187 -245 c28 -14 61 -68 61 -99 0 -61 -65 -126 -125
-126 -60 0 -125 65 -125 126 0 30 32 85 59 99 34 19 95 19 130 0z" />
                                            </g>
                                        </svg>
                                        <span class="ml-3 whitespace-nowrap text-base">Ev Aletleri</span>
                                    </div>
                                </a>
                            </li>
                            <li class="flex justify-between items-center group" @click="changeSpeedCategory('emobilite')">
                                <a class="cursor-pointer w-full py-3 px-3 active:bg-kbgreen focus:bg-kbgreen group-hover:bg-kbgreen group-hover:text-white focus:text-white active:text-white active">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="30.304" height="29.741" viewBox="0 0 30.304 29.741" class="group-hover:text-white group-hover:fill-white stroke-kbgreen group-hover:stroke-white min-w-[45px]">
                                            <g id="Group_4189" data-name="Group 4189" transform="translate(0.641 -161.9)">
                                                <path id="Path_3729" data-name="Path 3729" d="M540.019,160.17h2.052a2.26,2.26,0,0,1,2.25,2.049l1.778,19.02" transform="translate(-520.582 2.23)" fill="none" stroke-linecap="round" stroke-miterlimit="10"
                                                      stroke-width="1" />
                                                <circle id="Ellipse_305" data-name="Ellipse 305" cx="3.646" cy="3.646" r="3.646" transform="translate(21.871 183.848)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_306" data-name="Ellipse 306" cx="1.671" cy="1.671" r="1.671" transform="translate(23.847 185.824)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_307" data-name="Ellipse 307" cx="1.671" cy="1.671" r="1.671" transform="translate(1.976 185.824)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <circle id="Ellipse_308" data-name="Ellipse 308" cx="3.646" cy="3.646" r="3.646" transform="translate(0 183.848)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <path id="Path_3730" data-name="Path 3730" d="M545.974,177.871a7.623,7.623,0,0,0-6.08,5.376,1.483,1.483,0,0,1-1.439,1.086H528.042" transform="translate(-520.749 2.476)" fill="none"
                                                      stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                                <path id="Path_3731" data-name="Path 3731" d="M530.95,184.33s-1.615-8.65-10.1-5.566" transform="translate(-520.849 2.479)" fill="none" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1" />
                                            </g>
                                        </svg>

                                        <span class="ml-3 whitespace-nowrap text-base">E-Mobilite</span>
                                    </div>
                                </a>
                            </li>
                            <li class="flex justify-between items-center group" @click="changeSpeedCategory('saglik')">
                                <a class="cursor-pointer w-full py-3 px-3 active:bg-kbgreen focus:bg-kbgreen group-hover:bg-kbgreen group-hover:text-white focus:text-white active:text-white">
                                    <div class="flex items-center">
                                        <svg width="32" height="32" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg" class="group-hover:text-white group-hover:fill-white stroke-kbgreen group-hover:stroke-white min-w-[45px]">
                                            <path
                                                d="M16 3C16 2.44772 15.5523 2 15 2H9C8.44772 2 8 2.44772 8 3V8L3 8C2.44772 8 2 8.44771 2 9V15C2 15.5523 2.44772 16 3 16H8V21C8 21.5523 8.44772 22 9 22H15C15.5523 22 16 21.5523 16 21V16H21C21.5523 16 22 15.5523 22 15V9C22 8.44772 21.5523 8 21 8L16 8V3Z"
                                                stroke-width="1" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <span class="ml-3 whitespace-nowrap text-base">Sağlık</span>
                                    </div>
                                </a>
                            </li>
                            <li class="flex justify-between items-center group" @click="changeSpeedCategory('spor')">
                                <a class="cursor-pointer w-full py-3 px-3 active:bg-kbgreen focus:bg-kbgreen group-hover:bg-kbgreen group-hover:text-white focus:text-white active:text-white active">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="35px" height="35px" class="group-hover:text-white group-hover:fill-white stroke-kbgreen fill-kbgreen group-hover:stroke-white min-w-[45px]" viewBox="0 0 64 64"
                                             data-name="Layer 1" id="Layer_1" stroke-width="1">
                                            <path class="cls-1w" stroke-width="1" d="M11.67,37.36h-3a1,1,0,0,1-1-1V27.64a1,1,0,0,1,1-1h3a1,1,0,0,1,1,1v8.72A1,1,0,0,1,11.67,37.36Zm-2-2h1V28.64h-1Z" />
                                            <path class="cls-1w" stroke-width="1" d="M15.67,39.59h-4a1,1,0,0,1-1-1V25.41a1,1,0,0,1,1-1h4a1,1,0,0,1,1,1V38.59A1,1,0,0,1,15.67,39.59Zm-3-2h2V26.41h-2Z" />
                                            <path class="cls-1w" stroke-width="1" d="M22.32,42H15.67a1,1,0,0,1-1-1V23a1,1,0,0,1,1-1h6.65a1,1,0,0,1,1,1V41A1,1,0,0,1,22.32,42Zm-5.65-2h4.65V24H16.67Z" />
                                            <path class="cls-1w" stroke-width="1" d="M55.33,37.36h-3a1,1,0,0,1-1-1V27.64a1,1,0,0,1,1-1h3a1,1,0,0,1,1,1v8.72A1,1,0,0,1,55.33,37.36Zm-2-2h1V28.64h-1Z" />
                                            <path class="cls-1w" stroke-width="1" d="M52.32,39.59h-4a1,1,0,0,1-1-1V25.41a1,1,0,0,1,1-1h4a1,1,0,0,1,1,1V38.59A1,1,0,0,1,52.32,39.59Zm-3-2h2V26.41h-2Z" />
                                            <path class="cls-1w" stroke-width="1" d="M48.32,42H41.67a1,1,0,0,1-1-1V23a1,1,0,0,1,1-1h6.65a1,1,0,0,1,1,1V41A1,1,0,0,1,48.32,42Zm-5.65-2h4.65V24H42.67Z" />
                                            <path class="cls-1w" stroke-width="1" d="M41.67,35H22.32a1,1,0,0,1-1-1V30a1,1,0,0,1,1-1H41.67a1,1,0,0,1,1,1V34A1,1,0,0,1,41.67,35ZM23.32,33H40.67V31H23.32Z" />
                                        </svg>
                                        <span class="ml-3 whitespace-nowrap text-base">Spor</span>
                                    </div>
                                </a>
                            </li>
                            <li class="flex justify-between items-center group" @click="changeSpeedCategory('eveglence')">
                                <a class="cursor-pointer w-full py-3 px-3 active:bg-kbgreen focus:bg-kbgreen group-hover:bg-kbgreen group-hover:text-white focus:text-white active:text-white active rounded-b-2lg">
                                    <div class="flex items-center">
                                        <svg class="group-hover:text-white group-hover:fill-white fill-kbgreen group-hover:stroke-white min-w-[45px]" width="30" height="30" viewBox="0 0 512 512" xml:space="preserve">
                                            <g>
                                                <g>
                                                    <path
                                                        d="M0,39.184v329.143h216.816v51.2h-33.959V435.2h146.286v-15.673h-33.959v-51.2h80.98v75.755    c0,15.845,12.891,28.735,28.735,28.735h52.245c15.844,0,28.735-12.89,28.735-28.735v-75.755H512V39.184H0z M279.51,419.527h-47.02    v-51.2h47.02V419.527z M470.204,444.082c0,7.203-5.859,13.061-13.061,13.061h-52.245c-7.202,0-13.061-5.859-13.061-13.061V236.669    h78.367V444.082z M496.327,352.653h-10.449V220.996H376.163v131.657H15.673V54.857h480.653V352.653z" />
                                                </g>
                                            </g>
                                            <g>
                                                <g>
                                                    <polygon
                                                        points="31.347,70.531 31.347,336.98 363.102,336.98 363.102,321.306 47.02,321.306 47.02,86.204 265.403,86.204     167.954,183.654 179.038,194.737 287.57,86.204 320.672,86.204 223.222,183.654 234.305,194.737 342.838,86.204 464.98,86.204     464.98,207.935 480.653,207.935 480.653,70.531   " />
                                                </g>
                                            </g>
                                            <g>
                                                <g>
                                                    <rect x="406.465" y="331.755" width="15.673" height="15.673" />
                                                </g>
                                            </g>
                                            <g>
                                                <g>
                                                    <rect x="443.037" y="331.755" width="15.673" height="15.673" />
                                                </g>
                                            </g>
                                            <g>
                                                <g>
                                                    <rect x="406.465" y="300.408" width="15.673" height="15.673" />
                                                </g>
                                            </g>
                                            <g>
                                                <g>
                                                    <rect x="443.037" y="300.408" width="15.673" height="15.673" />
                                                </g>
                                            </g>
                                            <g>
                                                <g>
                                                    <rect x="406.465" y="269.061" width="15.673" height="15.673" />
                                                </g>
                                            </g>
                                            <g>
                                                <g>
                                                    <rect x="443.037" y="269.061" width="15.673" height="15.673" />
                                                </g>
                                            </g>
                                        </svg>

                                        <span class="ml-3 whitespace-nowrap text-base">Ev ve Eğlence</span>
                                    </div>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="w-3/4 mt-4">
                        <div class="pl-12 lg:pl-0 p-3 lg:p-0" :class="[loaderActive ? 'grid h-[462px] place-items-center' : '']">
                            <loader :active="loaderActive" message="Please wait 5 seconds" />
                            <Splide :has-track="false" aria-label="" :options="{ rewind: true, drag: true, arrows: false, perPage: 3, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 3 } } }">
                                <SplideTrack>
                                    <SplideSlide v-for="(product, index) in selectedSpeedCategoryProducts?.items?.element?.products?.data" class="flex">
                                        <category-box :product="product" :new-container-classes="`w-[100%]`" :auth="auth" v-if="!loaderActive" />
                                    </SplideSlide>
                                </SplideTrack>
                            </Splide>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="flex flex-wrap mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-5xl mt-3 w-full">
            <div class="w-3/4 md:w-1/2 mt-4 md:p-4 rounded-2lg group mx-auto md:mx-0">
                <div class="h-full rounded-2lg w-full p-5">
                    <a href="/kurumsal">
                        <picture>
                            <source srcset="../../images/welcome-webp/institutional-category-banner.webp" type="image/webp" />
                            <img class="hover:shadow-searchshadow scale-110 transition-all ease-in-out duration-75 w-full rounded-2lg" src="../../images/welcome-webp/institutional-category-banner.png" alt="" loading="lazy" />
                        </picture>
                    </a>
                </div>
            </div>
            <div class="w-3/4 md:w-1/2 mt-4 md:p-4 rounded-2lg group mx-auto md:mx-0">
                <div class="h-full rounded-2lg w-full p-5">
                    <a href="/kategoriler/tum-urunler?filter[brand]=1&filter[price]=&filter[collections]=&orderBy=">
                        <picture>
                            <source srcset="../../images/welcome-webp/apple-category-banner.webp" type="image/webp" />
                            <img class="hover:shadow-searchshadow scale-110 transition-all ease-in-out duration-75 w-full rounded-2lg" src="../../images/welcome-webp/apple-category-banner.png" alt="" loading="lazy" />
                        </picture>
                    </a>
                </div>
            </div>
        </section>
        <section class="flex flex-wrap mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-16 w-full">
            <div class="text-3xl self-center text-center w-full">Büyük markaların ürünlerini kiralamaya başlayın.</div>
            <!--
                        <div class="text-base mt-2 text-textgray self-center text-center w-full">Sed ut perspiciatis unde omnis iste natus error sit voluptatem</div>
            -->
            <div class="w-full text-center hidden lg:block">
                <button class="bg-white text-sm md:text-base text-black border-1 border-black rounded-full py-1 px-2 md:px-4 self-center font-santralextrabold mt-5 hover:bg-kbgreen hover:text-white hover:border-white">
                    <Link href="/kategoriler/tum-urunler"> Tümünü Gör</Link>
                </button>
            </div>
            <div class="w-full flex flex-wrap mt-8 relative lg:max-w-none o">
                <div class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24">
                    <Link href="/marka/apple">
                        <picture>
                            <source srcset="../../images/brands-webp/apple.webp" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300" src="../../images/brands/apple.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24">
                    <Link href="/marka/xiaomi">
                        <picture>
                            <source srcset="../../images/brands-webp/mi.webp" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300" src="../../images/brands/mi.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24 px-6 ts:px-18">
                    <Link href="/marka/dell">
                        <picture>
                            <source srcset="../../images/brands-webp/dell_logo.webp" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300" src="../../images/brands/dell_logo.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24">
                    <Link href="/marka/samsung">
                        <picture>
                            <source srcset="../../images/brands-webp/samsung.webp" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300" src="../../images/brands/samsung.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24">
                    <Link href="/marka/lenovo">
                        <picture>
                            <source srcset="../../images/brands-webp/lenovo.webp" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300" src="../../images/brands/lenovo.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24">
                    <Link href="/marka/asus">
                        <picture>
                            <source srcset="../../images/brands-webp/asusu.webp" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300" src="../../images/brands/asusu.jpeg" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24">
                    <Link href="/marka/casper">
                        <picture>
                            <source srcset="../../images/brands-webp/casper-logo-lacivert.webp" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300" src="../../images/brands/casper-logo-lacivert.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24 ts:px-15">
                    <Link href="/marka/monster">
                        <picture>
                            <source srcset="../../images/brands-webp/monster-notebook-logo.webp" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300" src="../../images/brands/monster-notebook-logo.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24 ts:px-15">
                    <Link href="/marka/nespresso">
                        <picture>
                            <source srcset="../../images/brands-webp/nespresso-logo.webp" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300" src="../../images/brands/nespresso-logo.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div v-if="false" class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24 ">
                    <Link href="/marka/philips">
                        <picture>
                            <source srcset="../../images/brands-webp/philips.webp" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300" src="../../images/brands/philips.jpeg" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div v-if="false" class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24">
                    <Link href="/marka/vespa">
                        <picture>
                            <source srcset="../../images/category-banner/brands/1.png" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 h-24" src="../../images/category-banner/brands/1.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24">
                    <Link href="/marka/honda">
                        <picture>
                            <source srcset="../../images/category-banner/brands/5.png" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 h-24" src="../../images/category-banner/brands/5.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div v-if="false" class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24">
                    <Link href="/marka/tesla">
                        <picture>
                            <source srcset="../../images/category-banner/brands/9.png" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-24" src="../../images/category-banner/brands/9.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div v-if="false" class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24">
                    <Link href="/marka/zes">
                        <picture>
                            <source srcset="../../images/category-banner/brands/16.png" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 h-24" src="../../images/category-banner/brands/16.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
                <div v-if="false" class="w-1/2 mts:w-1/3 lg:w-1/5 flex justify-center items-center h-24">
                    <Link href="/marka/jeep">
                        <picture>
                            <source srcset="../../images/category-banner/brands/11.png" type="image/webp" />
                            <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 h-24" src="../../images/category-banner/brands/11.png" alt="" loading="lazy" />
                        </picture>
                    </Link>
                </div>
            </div>
        </section>
        <div class="w-full text-center mt-10">
            <div class="text-xl md:text-2xl self-center text-center w-full mb-4">Doğaya Değer Veriyoruz</div>
            <Link href="/surdurulebilirlik" class="">
                <picture>
                    <source srcset="../../images/sustainability/surdurulebilirlik-homepage-new.webp" type="image/webp" />
                    <img src="../../images/sustainability/surdurulebilirlik-homepage-new.png" class="w-full hover:shadow-productshadow duration-300 ease-out delay-100" alt="">
                </picture>
            </Link>
        </div>
        <section class="flex flex-wrap mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-16 w-full">
            <div class="text-xl text-2xl self-center text-center w-full">Müşterilerin Yorumları</div>
            <!--            <div class="text-2xl md:text-xl self-center text-center w-full mt-4">Bugüne Kadar Toplam <span-->
            <!--                class=""></span> Yorum </div>-->
            <div class="w-full flex justify-center mt-4">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="142.533 79.652 189.594 30.345" width="182.594" height="30.345">
                    <g id="Group_568" data-name="Group 568" transform="matrix(1, 0, 0, 1, 146.708847, 82.000595)">
                        <g id="star-solid" transform="translate(153.538 -2.432)" fill="none" stroke-linejoin="round">
                            <path
                                d="M28.647,14.4a1.852,1.852,0,0,0-1.076-3.336l-7.506-.284a.192.192,0,0,1-.167-.125L17.3,3.648a1.852,1.852,0,0,0-3.478,0l-2.586,7.031a.192.192,0,0,1-.167.125l-7.506.284a1.852,1.852,0,0,0-1.076,3.336L8.38,19.053a.192.192,0,0,1,.067.2L6.42,26.434a1.852,1.852,0,0,0,2.819,2.043l6.222-4.17a.184.184,0,0,1,.209,0l6.222,4.17a1.829,1.829,0,0,0,2.819-2L22.667,19.27a.183.183,0,0,1,.067-.2Z"
                                stroke="none" />
                            <path
                                d="M 15.56581592559814 4.712684631347656 L 13.11771202087402 11.36995697021484 C 12.81991195678711 12.17903709411621 12.07173156738281 12.74015617370605 11.21163177490234 12.79948616027832 C 11.19094085693359 12.80091667175293 11.17024230957031 12.80201721191406 11.14951133728027 12.80279731750488 L 4.008689880371094 13.07256507873535 L 9.616781234741211 17.48119735717773 C 10.30588150024414 18.02341651916504 10.60516166687012 18.92123794555664 10.37922096252441 19.76847839355469 C 10.37673187255859 19.77779769897461 10.37418174743652 19.7870979309082 10.37156105041504 19.79637718200684 L 8.45207405090332 26.5975456237793 L 14.34182357788086 22.64998245239258 C 14.70442581176758 22.40457725524902 15.12701416015625 22.27497673034668 15.56535148620605 22.27497673034668 C 16.00387954711914 22.27497673034668 16.42667579650879 22.40470314025879 16.78940200805664 22.65032577514648 L 22.66341590881348 26.58734130859375 L 20.74460220336914 19.82059097290039 C 20.4981746673584 18.96349334716797 20.80209732055664 18.03650093078613 21.50568389892578 17.49121475219727 L 27.13162803649902 13.04759216308594 L 19.98955154418945 12.77777671813965 C 19.96883010864258 12.77699661254883 19.9481315612793 12.7758960723877 19.92745208740234 12.77446746826172 C 19.06741142272949 12.71515655517578 18.31924057006836 12.15408706665039 18.02266120910645 11.34850692749023 L 15.56581592559814 4.712684631347656 M 15.56536102294922 2.432346343994141 C 16.3427619934082 2.432346343994141 17.03738021850586 2.917966842651367 17.30434036254883 3.648096084594727 L 19.89824104309082 10.65409660339355 C 19.92435073852539 10.72501754760742 19.98965072631836 10.77400779724121 20.0650520324707 10.77920722961426 L 27.57149124145508 11.06278610229492 C 28.36390113830566 11.07572746276855 29.06021118164062 11.5915470123291 29.30345153808594 12.34580612182617 C 29.54671096801758 13.10007667541504 29.28295135498047 13.92551612854004 28.64740180969238 14.39897727966309 L 22.73400115966797 19.06964683532715 C 22.67178153991699 19.11562728881836 22.64510154724121 19.19569778442383 22.66728210449219 19.26981735229492 L 24.71070098876953 26.47599792480469 C 24.92720031738281 27.2225170135498 24.64986038208008 28.02444648742676 24.0184326171875 28.47771644592285 C 23.38178253173828 28.93046569824219 22.52827453613281 28.93046760559082 21.89161109924316 28.47771644592285 L 15.66961097717285 24.30746650695801 C 15.60686111450195 24.26414680480957 15.52384185791016 24.26414680480957 15.46110153198242 24.30746650695801 L 9.239101409912109 28.47771644592285 C 8.594064712524414 28.9152660369873 7.744083404541016 28.90202713012695 7.112991333007812 28.44457626342773 C 6.481901168823242 27.98713684082031 6.204860687255859 27.18345642089844 6.420021057128906 26.43428802490234 L 8.446750640869141 19.25313568115234 C 8.466501235961914 19.1790771484375 8.440271377563477 19.10036659240723 8.380031585693359 19.0529670715332 L 2.49165153503418 14.42399787902832 C 1.856111526489258 13.95053672790527 1.592351913452148 13.12509727478027 1.835601806640625 12.37083625793457 C 2.078851699829102 11.61656761169434 2.775161743164062 11.10074615478516 3.567581176757812 11.08780670166016 L 11.07401084899902 10.80422782897949 C 11.14940071105957 10.79902648925781 11.21471214294434 10.75003623962402 11.24081230163574 10.67912673950195 L 13.82636070251465 3.648096084594727 C 14.09332084655762 2.917966842651367 14.78795051574707 2.432346343994141 15.56536102294922 2.432346343994141 Z"
                                style="fill: rgb(112, 212, 75); stroke: rgba(112, 212, 75, 0)" />
                        </g>
                        <path id="star-solid-2" data-name="star-solid"
                              d="M28.647,14.4a1.852,1.852,0,0,0-1.076-3.336l-7.506-.284a.192.192,0,0,1-.167-.125L17.3,3.648a1.852,1.852,0,0,0-3.478,0l-2.586,7.031a.192.192,0,0,1-.167.125l-7.506.284a1.852,1.852,0,0,0-1.076,3.336L8.38,19.053a.192.192,0,0,1,.067.2L6.42,26.434a1.852,1.852,0,0,0,2.819,2.043l6.222-4.17a.184.184,0,0,1,.209,0l6.222,4.17a1.829,1.829,0,0,0,2.819-2L22.667,19.27a.183.183,0,0,1,.067-.2Z"
                              transform="translate(114.717 -2.432)" fill="#70d44b" />
                        <path id="star-solid-3" data-name="star-solid"
                              d="M28.647,14.4a1.852,1.852,0,0,0-1.076-3.336l-7.506-.284a.192.192,0,0,1-.167-.125L17.3,3.648a1.852,1.852,0,0,0-3.478,0l-2.586,7.031a.192.192,0,0,1-.167.125l-7.506.284a1.852,1.852,0,0,0-1.076,3.336L8.38,19.053a.192.192,0,0,1,.067.2L6.42,26.434a1.852,1.852,0,0,0,2.819,2.043l6.222-4.17a.184.184,0,0,1,.209,0l6.222,4.17a1.829,1.829,0,0,0,2.819-2L22.667,19.27a.183.183,0,0,1,.067-.2Z"
                              transform="translate(75.896 -2.432)" fill="#70d44b" />
                        <path id="star-solid-4" data-name="star-solid"
                              d="M28.647,14.4a1.852,1.852,0,0,0-1.076-3.336l-7.506-.284a.192.192,0,0,1-.167-.125L17.3,3.648a1.852,1.852,0,0,0-3.478,0l-2.586,7.031a.192.192,0,0,1-.167.125l-7.506.284a1.852,1.852,0,0,0-1.076,3.336L8.38,19.053a.192.192,0,0,1,.067.2L6.42,26.434a1.852,1.852,0,0,0,2.819,2.043l6.222-4.17a.184.184,0,0,1,.209,0l6.222,4.17a1.829,1.829,0,0,0,2.819-2L22.667,19.27a.183.183,0,0,1,.067-.2Z"
                              transform="translate(37.075 -2.432)" fill="#70d44b" />
                        <path id="star-solid-5" data-name="star-solid"
                              d="M28.647,14.4a1.852,1.852,0,0,0-1.076-3.336l-7.506-.284a.192.192,0,0,1-.167-.125L17.3,3.648a1.852,1.852,0,0,0-3.478,0l-2.586,7.031a.192.192,0,0,1-.167.125l-7.506.284a1.852,1.852,0,0,0-1.076,3.336L8.38,19.053a.192.192,0,0,1,.067.2L6.42,26.434a1.852,1.852,0,0,0,2.819,2.043l6.222-4.17a.184.184,0,0,1,.209,0l6.222,4.17a1.829,1.829,0,0,0,2.819-2L22.667,19.27a.183.183,0,0,1,.067-.2Z"
                              transform="translate(-1.746 -2.432)" fill="#70d44b" />
                    </g>
                    <path id="path-2" data-name="star-solid"
                          d="M 263.007 35.705 C 263.029 35.772 258.516 32.905 259.685 33.722 C 263.176 36.164 259.58 14.045 261.27 14.016 C 260.758 14.078 264.616 20.627 264.59 20.556 L 261.992 13.549 C 261.502 12.21 259.746 11.904 258.832 12.998 C 258.695 13.162 258.587 13.349 258.514 13.549 L 255.928 20.58 C 255.902 20.651 255.836 20.7 255.761 20.705 L 248.255 20.989 C 246.83 21.013 245.965 22.572 246.699 23.794 C 246.823 24.001 246.986 24.181 247.179 24.325 L 253.072 28.954 C 253.132 29.001 253.159 29.08 253.139 29.154 L 251.112 36.335 C 250.72 37.706 251.958 38.987 253.342 38.641 C 253.552 38.589 253.751 38.5 253.931 38.378 L 260.153 34.208 C 260.216 34.165 260.299 34.165 260.362 34.208 C 260.362 34.208 258.858 33.152 263.007 35.705 Z"
                          fill="#70d44b" transform="matrix(1, 0, 0, 1, 55.493668, 69.600685)" />
                </svg>
            </div>
            <div class="text-xl md:text-base self-center text-kbgreen text-center w-full mt-4 md:mt-2">Mükemmel</div>
            <Comments></Comments>
        </section>
    </main>
    <TransitionRoot appear :show="isMigrosVisitor" as="template">
        <Dialog as="div" @close="closeMigrosModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>
            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="max-w-[350px] md:max-w-[750px] transform rounded-sm bg-white z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center px-4 lg:px-12 py-3 lg:py-8">
                            <div class="flex items-center justify-center space-x-4 mb-2 pb-2">
                                <img src="../../images/logo-large.png" class="w-1/3 md:w-full max-h-[31px]" alt="">
                                <span class="text-5xl font-santralregular text-textgray">X</span>
                                <img src="../../images/migros-icon/migros-logo.png" class="w-1/3 md:w-full max-h-[31px]" alt="">
                            </div>
                            <div class="font-santralregular text-sm 2xl:text-base text-black text-center mb-4">
                                Kiralabunu Migros iş birliği ile kiralabunu.com üzerinden istediğin
                                ürünü kirala, 1-5 iş günü içerisinde kapına gelsin!
                            </div>
                            <ul class="flex text-xs space-x-1 lg:space-x-4 text-center mt-1">
                                <li class="w-1/4 md:w-1/4 lg:w-1/4">
                                    <div class="flex flex-col">
                                        <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                            <img src="../../images/migros-icon/migros-icon-4.png" class="w-10/12 lg:w-full max-h-14 mx-auto">
                                        </div>
                                        <div class="text-3xs mts:text-2xs xl:text-xs leading-none">Ürünü ve kira süreni seç</div>
                                    </div>
                                </li>
                                <li class="w-1/4 md:w-1/4 lg:w-1/4">
                                    <div class="flex flex-col">
                                        <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                            <img src="../../images/migros-icon/migros-icon-3.png" class="w-10/12 lg:w-full max-h-14 mx-auto">
                                        </div>
                                        <div class="text-3xs mts:text-2xs xl:text-xs leading-none">Sepete ekle ve üyelik oluştur</div>
                                    </div>
                                </li>
                                <li class="w-1/4 md:w-1/4 lg:w-1/4">
                                    <div class="flex flex-col">
                                        <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                            <img src="../../images/migros-icon/migros-icon-2.png" class="w-10/12 lg:w-full max-h-14 mx-auto">
                                        </div>
                                        <div class="text-3xs mts:text-2xs xl:text-xs leading-none">İlk ay kira ücretini öde kiralamanı tamamla</div>
                                    </div>
                                </li>
                                <li class="w-1/4 md:w-1/4 lg:w-1/4">
                                    <div class="flex flex-col">
                                        <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                            <img src="../../images/migros-icon/migros-icon-1.png" class="w-10/12 lg:w-full max-h-14 mx-auto">
                                        </div>
                                        <div class="text-3xs mts:text-2xs xl:text-xs leading-none">Onaylanan kiralaman kapına gelsin</div>
                                    </div>
                                </li>
                            </ul>
                            <div class="mt-12 text-center">
                                <a class="bg-kbgreen text-base lg:text-lg text-white rounded-md py-1.5 px-8 self-center font-santralextrabold hover:bg-white hover:text-kbgreen pt-2 border-1 border-transparent hover:border-kbgreen transition-all duration-200 ease-in-out"
                                   href="/kategoriler/tum-urunler"> Tüm Ürünler</a>
                            </div>
                            <div class="font-santralextrabold absolute top-0 right-0 cursor-pointer px-1 text-base bg-white rounded-sm" @click="closeMigrosModal">X</div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
    <TransitionRoot appear :show="false" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>
            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="max-w-[350px] md:max-w-[550px] transform rounded-sm bg-white z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center px-4 lg:px-5 py-3 lg:py-5">
                            <div class="font-santralextrabold text-lg md:text-xl text-black text-center mb-4">
                                Kiralabunu ailesi olarak <br> Kurban Bayramınızı kutluyoruz!
                            </div>
                            <div class="font-santralregular text-sm 2xl:text-base text-black text-center mb-4">
                                Müşteri Hizmetleri ekibimiz çağrılarınıza ve maillerinize 10.06.2025 tarihinden itibaren destek vermeye devam edecek.<br><br>

                                05.06.2025 tarihinden itibaren vereceğiniz siparişler 10.06.2025 tarihinden itibaren işleme alınacaktır.<br><br>

                                Onaylanan siparişler için teslimat sürelerinin ortalama 5 iş günü olduğunu hatırlatır, bugünden itibaren vereceğiniz tüm siparişlerde bu sürenin 10.06.2025 tarihinde başlayacağını belirtmek isteriz.<br><br>

                                Herkese iyi bayramlar!
                            </div>


                            <div class="font-santralextrabold absolute top-2 right-2 cursor-pointer px-1 text-base bg-white rounded-sm" @click="closeModal">X</div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>
