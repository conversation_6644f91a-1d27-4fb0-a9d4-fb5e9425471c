<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import { Video } from "@splidejs/splide-extension-video";
import "@splidejs/splide-extension-video/dist/css/splide-extension-video.min.css";
import { defineComponent, onMounted, reactive, ref } from "vue";
import Loader from "@/Pages/Shared/Loader.vue";
import CategoryBox from "@/Pages/Shared/CategoryBox.vue";

export default defineComponent({
    name: "Kampanyalar",
    components: {
        CategoryBox, Loader,
        Link,
        Head,
        Splide,
        SplideSlide,
        SplideTrack,
        Video,
        UserMenu
    },
    computed: {
        splidedAllProducts() {
            return this.splidedArray(this.allProducts.items.data, 1);
        },
        splidedDiscounted() {
            // Take first 8 items than split them into 2
            return this.splidedArray(this.discounted.items.data.slice(0, 16), 1);
        }
    },
    methods: {
        splidedArray(arr, size) {
            return arr.reduce((acc, e, i) => (i % size ? acc[acc.length - 1].push(e) : acc.push([e]), acc), []);
        }
    },
    props: {
        discounted: Object
    },
    setup() {
        const main = ref("<InstanceType<typeof Splide>>");
        const thumbs = ref("<InstanceType<typeof Splide>>");
        const videos = ["gG5sWlLQyIY", "gRXb2rCKjdk", "4JlAq0RDVRk", "1BzZB-vhCz8", "2O5-0sBhhKw"];

        const mainOptions = {
            type: "loop",
            perPage: 1,
            perMove: 1,
            gap: "1rem",
            pagination: true,
            arrows: true,
            rewind: true,
            autoPlay: true
        };

        const thumbsOptions = {
            type: "slide",
            rewind: true,
            gap: "1rem",
            pagination: false,
            fixedWidth: 110,
            fixedHeight: 70,
            cover: true,
            focus: "center",
            isNavigation: true,
            updateOnMove: true,
            arrows: false
        };

        onMounted(() => {
            const thumbsSplide = thumbs.value?.splide;
            if (thumbsSplide) {
                main.value?.sync(thumbsSplide);
            }
        });

        return {
            main,
            thumbs,
            thumbsOptions,
            mainOptions,
            videos,
            extensions: { Video }
        };
    },
    layout: Layout

});
</script>

<template>

    <Head>
        <title>Kiralabunu | Kampanyalar</title>
        <meta name="description" content="Kiralabunu Kampanyalarını İncele. Seç, Beğen, Kirala.">
    </Head>
    <div class="">

        <section class="mt-12 bg-white py-10">
            <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
                <div class="flex flex-wrap justify-center items-center">
                    <div class="w-full lg:w-1/2 xl:pr-12 pb-6 lg:pb-0 ">
                        <h2 class="text-3xl font-semibold mb-3 lg:mb-6 text-kbgreen">Kiralabunu Fırsatları</h2>
                        <p class="text-base text-black font-santralregular">
                            Teknolojiyi herkesin erişimine açmayı
                            seviyoruz!
                            <br><br>
                            İster ilk defa kiralayın ister teknoloji ustası,
                            bir sonraki kiralamanda tasarruf etmenin
                            keyfini çıkar. Kupon kodlarıyla fırsatlar seni
                            bekliyor!
                        </p>
                    </div>
                    <div class="w-full lg:w-1/2">
                        <picture>
                            <source srcset="../../images/campaign/campaign-b1.webp" type="image/webp">
                            <source srcset="../../images/campaign/campaign-b1.png" type="image/png">
                            <img class="w-full rounded-xl my-2" src="../../images/campaign/campaign-b1.png" />
                        </picture>
                    </div>
                </div>
            </div>
        </section>
        <section class=" bg-kb-mid-grey py-10">
            <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
                <div class="flex flex-wrap justify-center items-center pt-12">
                    <!--                    <div class="w-full lg:w-1/2 my-4 px-8">-->
                    <!--                        <div class="bg-white rounded-xl p-12 flex flex-col justify-center items-center">-->
                    <!--                            <div class="text-black font-bold text-2xl lg:text-4xl leading-relaxed text-center font-santralextrabold border-3 border-kbblue rounded-lg mx-auto mb-4 px-4">kbyaz50</div>-->
                    <!--                            <h2 class="text-2xl lg:text-3xl font-semibold mb-6 text-kbgreen text-center">Yaz Kampanyası</h2>-->
                    <!--                            <p class="text-base text-black font-santralregular text-center leading-tight lg:leading-relaxed">-->
                    <!--                                3 ay ve üzeri kiralamaların ilk ayına <br>-->
                    <!--                                %50 indirim-->
                    <!--                            </p>-->
                    <!--                        </div>-->
                    <!--                    </div>-->
                    <div class="w-full lg:w-1/2 my-4 px-8">
                        <div class="bg-white rounded-xl p-8">
                            <div class="flex justify-center rounded-full mb-3 px-3 bg-white">
                                <img class=" h-[62px]" src="../../images/svg/discount.svg" />
                            </div>
                            <h2 class="text-2xl lg:text-3xl font-semibold mb-6 text-kbgreen text-center">Tek Fiyat Kampanyası</h2>
                            <p class="text-base text-black font-santralregular text-center leading-tight lg:leading-relaxed">
                                Seçili ürünlerde geçerli 3 ay ve üzeri kiralamalarında en düşük kira fiyatından kiralama fırsatını kaçırma
                            </p>
                        </div>
                    </div>
                    <div class="w-full lg:w-1/2 my-4 px-8" v-if="false">
                        <div class="bg-white rounded-xl p-10">
                            <div class="flex justify-center rounded-full mb-3 px-4 bg-white">
                                <img class="h-[62px] w-[62px]" src="../../images/campaign/campaign-i1.svg" />
                            </div>
                            <h2 class="text-2xl lg:text-3xl font-semibold mb-6 text-kbgreen text-center">İlk kiralamana özel</h2>
                            <p class="text-base text-black font-santralregular text-center leading-tight lg:leading-relaxed">
                                Yeni üyelere özel 6 ay ve üzeri kiralamaların ilk ayında 500 tl indirim Kiralabunu’dan hediye! İndirim kodu: <b>merhaba500</b>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="py-6 flex flex-row mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
            <div class="w-12/12 md:w-3/4 lg:w-7/12 mx-auto">
                <ul class="flex text-xs space-x-1 lg:space-x-4 text-center mt-1">
                    <li class="w-1/5 md:w-1/3 lg:w-1/5">
                        <div class="flex flex-col">
                            <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 107.74 78.83">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-2" x="17.71" y="1" width="89.04" height="61.66" rx="14.15" ry="14.15" />
                                                <rect x="18.71" y="15.15" width="86.99" height="14.34" />
                                                <circle class="cls-1" cx="85.86" cy="44.17" r="8.66" />
                                                <circle class="cls-1" cx="74.62" cy="44.17" r="8.66" />
                                            </g>
                                            <g>
                                                <circle class="cls-3" cx="20.12" cy="58.71" r="19.12" />
                                                <polyline class="cls-4" points="10.55 58.71 17.71 68.53 29.75 47.27" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs leading-none">Kart Limitin Sana Kalsın</div>
                        </div>
                    </li>
                    <li class="w-1/5 md:w-1/3 lg:w-1/5">
                        <div class="flex flex-col">
                            <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 126.16 126.7">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-2tr" x="40.92" y="19.93" width="44.97" height="86.95" rx="5.71" ry="5.71" />
                                                <rect class="cls-1tr" x="56.1" y="23.35" width="14.63" height="3.66" rx="1.83" ry="1.83" />
                                            </g>
                                            <g>
                                                <path
                                                    d="m63.4,126.7c-2.79,0-5.62-.19-8.46-.57-16.77-2.25-31.66-10.89-41.93-24.34S-1.68,71.71.57,54.94C2.82,38.17,11.46,23.28,24.91,13.01,38.36,2.74,55.01-1.67,71.77.57c18.15,2.43,34.36,12.6,44.46,27.9.61.92.35,2.16-.57,2.77-.92.61-2.16.35-2.77-.57-9.46-14.33-24.65-23.86-41.65-26.14-15.71-2.11-31.3,2.03-43.9,11.65C14.74,25.81,6.64,39.76,4.54,55.47c-2.11,15.71,2.03,31.3,11.65,43.9,9.62,12.6,23.57,20.7,39.28,22.8,32.43,4.35,62.36-18.5,66.7-50.94.15-1.1,1.17-1.86,2.25-1.72,1.09.15,1.86,1.15,1.72,2.25-4.26,31.78-31.52,54.94-62.74,54.94Z" />
                                                <polygon points="124.28 56.09 94.42 25.17 113.03 27.91 124.62 13.11 124.28 56.09" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs leading-none">Yenileme Opsiyonu</div>
                        </div>
                    </li>
                    <li class="w-1/5 md:w-1/3 lg:w-1/5">
                        <div class="flex flex-col">
                            <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 217.86 118.79">
                                    <g id="Layer_1-2">
                                        <g>
                                            <polyline class="cls-5eq"
                                                      points="110.17 64.25 159.33 64.25 159.33 9.13 156.46 5.14 159.33 16.92 184.09 16.92 188.93 19.41 215.36 50.87 215.36 90.92 209.27 98.15 199.4 98.15 195.2 88.03 184.37 82.31 172.49 85.32 165.49 96.61 165.29 99.24 110.17 99.24 106.81 89.07 101.96 84.65 93.11 82.18 81.1 87.13 76.06 99.24 62.2 98.85 55.91 94.11 54.29 85.93 54.29 62.21" />
                                            <g>
                                                <path class="cls-4eq" d="m31.1,2.5h118.85c5.18,0,9.38,4.2,9.38,9.38v87.36" />
                                                <path class="cls-4eq" d="m33.68,48.6h9.83c5.96,0,10.79,4.83,10.79,10.79v29.06c0,5.96,4.83,10.79,10.79,10.79h10.98" />
                                                <path class="cls-4eq" d="m159.33,16.92h21.55c4.3,0,8.37,1.93,11.08,5.26l23.41,28.69v37.58c0,5.96-4.83,10.79-10.79,10.79h-5.17" />
                                                <polyline class="cls-4eq" points="165.29 99.24 159.33 99.24 110.17 99.24" />
                                                <line class="cls-2eq" x1="54.29" y1="64.25" x2="159.33" y2="64.25" />
                                                <line class="cls-2eq" x1="54.29" y1="78.83" x2="159.33" y2="78.83" />
                                                <circle class="cls-1eq" cx="93.11" cy="99.24" r="17.06" />
                                                <circle class="cls-1eq" cx="182.35" cy="99.24" r="17.06" />
                                                <line class="cls-3eq" x1="18.84" y1="16.92" x2="62.2" y2="16.92" />
                                                <line class="cls-3eq" y1="32.28" x2="43.36" y2="32.28" />
                                                <polyline class="cls-4eq" points="171.1 16.92 171.1 50.87 215.36 50.87" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs leading-none">Ücretsiz Kargo</div>
                        </div>
                    </li>
                    <li class="w-1/5 md:w-1/3 lg:w-1/5">
                        <div class="flex flex-col">
                            <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                <svg class="w-10/12 lg:w-full max-h-14 mx-auto" id="Layer_2" viewBox="0 0 148.11 137.57">
                                    <g id="Layer_1-2">
                                        <g>
                                            <g>
                                                <rect class="cls-1re" x="0" y="79.35" width="20.8" height="54.14" rx="2.9" ry="2.9" />
                                                <path class="cls-2" d="m11.13,128.75c-2.61.49-4.85-1.74-4.37-4.36.27-1.46,1.45-2.65,2.91-2.93,2.62-.5,4.89,1.76,4.38,4.37-.28,1.46-1.46,2.63-2.92,2.91Z" />
                                                <path class="cls-2"
                                                      d="m108.64,106.42l30.07-12.44c5.69-2.35,10.59,4.85,6.22,9.19-.07.07-.14.14-.22.21-.37.35-.78.65-1.23.89l-57.41,31.1c-1.22.66-2.65.85-4,.53l-56.9-13.48c-2.56-.61-4.36-2.89-4.36-5.51v-20.81c0-2.65,1.83-4.94,4.41-5.53l25.11-5.69c.93-.21,1.9-.18,2.81.08l46.7,13.37c.08.02.17.05.25.07.96.23,7.9,2.06,8.51,7.26.06.52.02,1.04-.08,1.55-.4,2.06-2.06,7.24-8.54,5.59l-26.8-6.4" />
                                            </g>
                                            <path class="cls-2re"
                                                  d="m114.96,42.8c0-.5-.01-.99-.04-1.48-.04-.94.57-1.79,1.49-2.03l3.35-.88c1.07-.28,1.71-1.37,1.43-2.44l-2.07-7.91c-.28-1.07-1.37-1.71-2.44-1.43l-3.52.92c-.88.23-1.82-.15-2.25-.96-1.12-2.09-2.45-4.04-3.96-5.85-.6-.71-.6-1.75-.03-2.48l2.28-2.95c.68-.87.51-2.13-.36-2.81l-6.47-4.99c-.87-.67-2.13-.51-2.81.36l-2.34,3.04c-.56.73-1.55.99-2.38.61-2.1-.95-4.3-1.7-6.59-2.22-.9-.2-1.52-1.02-1.52-1.94v-3.87c0-1.1-.9-2-2-2h-8.18c-1.1,0-2,.9-2,2v3.87c0,.92-.63,1.74-1.53,1.94-2.12.48-4.17,1.16-6.12,2.01-.84.37-1.82.09-2.37-.64l-2.31-3.08c-.66-.88-1.92-1.06-2.8-.4l-6.54,4.9c-.88.66-1.06,1.92-.4,2.8l2.24,2.99c.56.74.54,1.78-.07,2.48-.93,1.07-1.79,2.2-2.58,3.38-.51.75-1.48,1.05-2.33.73l-3.45-1.27c-1.04-.38-2.18.15-2.57,1.19l-2.83,7.68c-.38,1.04.15,2.19,1.19,2.57l3.29,1.21c.88.32,1.42,1.21,1.29,2.14-.22,1.57-.34,3.17-.34,4.8,0,.22,0,.45,0,.67.02.92-.59,1.74-1.49,1.97l-3.32.87c-1.07.28-1.71,1.37-1.43,2.44l2.07,7.92c.28,1.07,1.37,1.71,2.44,1.43l3.13-.82c.9-.24,1.85.17,2.27,1,1.12,2.2,2.46,4.28,4.01,6.18.58.71.56,1.73,0,2.45l-1.93,2.5c-.68.87-.51,2.13.36,2.81l6.47,5c.87.67,2.13.51,2.81-.36l1.85-2.4c.57-.73,1.57-1,2.4-.6,2.23,1.06,4.6,1.88,7.06,2.44.9.2,1.52,1.02,1.52,1.94v3c0,1.1.9,2,2,2h8.18c1.1,0,2-.9,2-2v-3c0-.92.62-1.74,1.52-1.94,2.29-.52,4.5-1.27,6.6-2.22.84-.38,1.84-.11,2.4.63l1.81,2.43c.66.89,1.92,1.07,2.8.4l6.55-4.9c.88-.66,1.06-1.92.4-2.8l-1.89-2.52c-.55-.73-.55-1.75.04-2.45.97-1.16,1.87-2.39,2.69-3.68.5-.78,1.49-1.09,2.36-.77l3,1.11c1.04.38,2.19-.15,2.57-1.19l2.82-7.67c.38-1.04-.15-2.19-1.19-2.57l-3.17-1.17c-.86-.32-1.4-1.17-1.3-2.08.16-1.32.24-2.67.24-4.03Zm-22.17,12.18c-18,13.39-38.17-6.8-24.77-24.78.12-.17.28-.32.45-.45,17.99-13.38,38.16,6.79,24.77,24.78-.12.17-.28.32-.45.45Z" />
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs leading-none">Hasarda %70 Garanti</div>
                        </div>
                    </li>
                    <li class="w-1/5 md:w-1/3 lg:w-1/5">
                        <div class="flex flex-col">
                            <div class="h-10 md:h-14 self-center mb-3 flex items-end">
                                <svg class="w-[44px] ts:w-[55px] h-auto ts:h-[55px]" id="Layer_2" viewBox="0 0 80.08 80.08">
                                    <g id="Layer_1-2">
                                        <g>
                                            <!--                                            <rect class="cls-1we" x="1" y="1" width="78.08" height="78.08" rx="11.14" ry="11.14" />-->
                                            <g>
                                                <path class="cls-3we" d="m9.99,17.19h6.06c1.35,0,2.56.86,2.99,2.15l10.99,32.56c.43,1.28,1.64,2.15,2.99,2.15h25.07c1.12,0,2.15-.59,2.72-1.56l9.11-15.48c1.24-2.1-.28-4.76-2.72-4.76H24.65" />
                                                <circle class="cls-2we" cx="37.38" cy="61.99" r="4.18" />
                                                <circle class="cls-2we" cx="53.66" cy="61.99" r="4.18" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </div>
                            <div class="text-3xs mts:text-xs leading-none">Kirala, Beğenirsen Satın Al</div>
                        </div>
                    </li>
                </ul>
            </div>
            <!--            <div class="w-3/12 hidden md:flex md:w-1/4 lg:w-5/12 flex-col items-start lg:ml-28">-->
            <!--                <div class="flex">-->
            <!--                    <h1 class=" text-xl md:text-2xl md:text-right lg:text-left lg:text-3xl  md:self-center">Seç! Beğen! Kirala!</h1>-->
            <!--                </div>-->
            <!--                <div class="flex md:w-full lg:w-auto md:justify-end lg:justify-start mt-2">-->
            <!--                    <button class="bg-black text-sm md:text-base text-white rounded-full py-1 md:py-2 px-2 md:px-4 self-center  whitespace-nowrap">Ürünlere Git</button>-->
            <!--                </div>-->
            <!--            </div>-->
        </section>
        <section class=" bg-kb-mid-grey py-10">
            <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
                <div class="flex flex-wrap justify-center items-center pt-12">
                    <div class="w-full lg:w-1/2 my-4 px-8" v-if="false">
                        <div class="bg-white rounded-xl p-10">
                            <div class="flex justify-center rounded-full mb-3 px-4 bg-white">
                                <img class="h-[62px] w-[62px]" src="../../images/campaign/campaign-i2.svg" />
                            </div>
                            <h2 class="text-2xl lg:text-3xl font-semibold mb-6 text-kbgreen text-center">Toplu Öde, %15 İndirim Kazan</h2>
                            <p class="text-base text-black font-santralregular text-center leading-tight lg:leading-relaxed">
                                Seçili kategorilerde kira ücretini toplu
                                öde %15’ varan indirim fırsatlarını
                                kaçırma
                            </p>
                        </div>
                    </div>
                    <div class="w-full my-4 px-8">
                        <div class="bg-white rounded-xl p-12 flex flex-col justify-center items-center">
                            <div class="text-black font-bold text-2xl lg:text-4xl leading-relaxed text-center font-santralextrabold border-3 border-kbblue rounded-lg mx-auto mb-4 px-4">kibu10</div>
                            <h2 class="text-2xl lg:text-3xl font-semibold mb-6 text-kbgreen text-center">Kiralamini’de %10 İndirim</h2>
                            <p class="text-base text-black font-santralregular text-center leading-tight lg:leading-relaxed">
                                Kiralabunu üyelerine özel
                                kiralamini’den yapacağınız
                                kiralamalarda
                                %10 indirim
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="mt-12 bg-white py-10">
            <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
                <div class="flex flex-col-reverse lg:flex-row flex-wrap justify-center items-center">
                    <div class="w-full lg:w-1/2 xl:pr-12">
                        <h2 class="text-3xl font-semibold mb-3 lg:mb-6 text-kbgreen">Öğrenciler her ay %10 tasarruf ediyor!</h2>
                        <p class="text-base text-black font-santralregular">
                            İş hayatın başlayana kadar kiralabunu
                            desteği yanında!
                            <br><br>
                            İndirim Kodu: <b>okuladonus10</b>
                        </p>
                        <div class="mt-6">
                            <a href="/indirimli-urunler" class="bg-kbblue hover:opacity-80 transition-all duration-300 delay-100 ease-in-out text-white text-lg font-santralextrabold rounded-full py-2 px-4 self-center">İndirimli Kirala</a>
                        </div>
                    </div>
                    <div class="w-full lg:w-1/2 mb-6 lg:mb-0">
                        <picture>
                            <source srcset="../../images/campaign/campaign-b2.webp" type="image/webp">
                            <source srcset="../../images/campaign/campaign-b2.png" type="image/png">
                            <img class="w-full rounded-xl my-2" src="../../images/campaign/campaign-b1.png" />
                        </picture>
                    </div>
                </div>
            </div>
        </section>
        <section class="mt-6 md:mt-4 py-1 md:py-9 bg-[#f8f8f8]">
            <div class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
                <div class="flex w-full justify-between">
                    <div class="text-2xl md:text-3xl my-2 lg:my-0 mx-0 lg:mx-4 self-center text-center w-full md:w-auto">İndirimli Ürünler</div>
                    <div class="flex-1 ml-6 hidden md:flex">
                        <div class="flex-1 self-center border border-gray-200"></div>
                        <Link href="indirimli-urunler" class="cursor-pointer text-sm font-santralextrabold flex justify-center items-center border-2 rounded-full px-4 border-2 whitespace-nowrap md:ml-4 bg-white hover:bg-kbgreen hover:text-white">
                            Tümünü Gör
                        </Link>
                    </div>
                </div>
                <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0 lg:mt-9">
                    <Splide :has-track="false" aria-label="" :options="{ rewind: true, perPage: 4, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 3 } } }" v-if="discounted">
                        <SplideTrack>
                            <SplideSlide v-for="(productGroup, index) in splidedDiscounted" :key="index" class="flex">
                                <category-box v-for="(product, index) in productGroup" :product="product" :new-container-classes="`w-full`" :auth="auth" />
                            </SplideSlide>
                        </SplideTrack>
                        <div class="splide__arrows">
                            <button class="splide__arrow splide__arrow--prev !-left-9">
                                <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                          transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                            <button class="splide__arrow splide__arrow--next !-right-9">
                                <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                          transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                        </div>
                    </Splide>
                    <div v-else>
                        <loader :active="true" message="Please wait 5 seconds" />
                    </div>
                </div>
                <div class="flex-1 ml-2 flex md:hidden justify-center">
                    <Link href="/indirimli-urunler" class="cursor-pointer border-3 rounded-full py-1.5 px-2 text-center mb-4 w-32 bg-white hover:bg-kbgreen hover:text-white font-santralextrabold text-sm"> Tümünü Gör</Link>
                </div>
            </div>
        </section>

    </div>
</template>
