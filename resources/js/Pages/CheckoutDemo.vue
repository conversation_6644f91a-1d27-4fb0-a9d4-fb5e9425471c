<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import CheckoutPaymentInfo from "@/Components/CheckoutPaymentInfoDemo.vue";
import HopiModal from "@/Pages/Shared/HopiModal.vue";
import coupons from "@/Pages/Coupons.vue";
import HopiCampaignModal from "@/Pages/Shared/HopiCampaignModal.vue";
import PegasusModal from "@/Pages/Shared/PegasusModal.vue";
import { Popover, PopoverButton, PopoverPanel, Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from "@headlessui/vue";

export default {
    computed: {
        coupons() {
            return coupons;
        },
        productsWithoutInstallment() {
            return this.products.filter(product => !product.is_installment);
        },
        productsWithInstallment() {
            return this.products.filter(product => product.is_installment);
        }
    },
    components: {
        PegasusModal,
        HopiModal,
        HopiCampaignModal,
        Link,
        Head,
        UserMenu,
        Popover,
        PopoverButton,
        PopoverPanel,
        Dialog,
        DialogPanel,
        DialogTitle,
        TransitionChild,
        TransitionRoot,
        CheckoutPaymentInfo
    },
    data() {
        return {
            selectedItems: [],
            form: this.$inertia.form({
                coupon: ""
            }),
            isHopiModelOpen: false,
            isPegasusModalOpen: false,
            isHopiCampaignModelOpen: false,
            selectedHopiCampaign: localStorage.getItem("selectedHopiCampaign"),
            enteredHopiBalance: localStorage.getItem("enteredHopiBalance") ?? 0,
            is_mass_payment_local_enable: false,
            campaignsModalIsOpen: false,
            couponSection: true,
            couponsectionColor: "border-kbblue hover:bg-purple-50",
            showMixedCartError: false
        };
    },
    props: {
        products: Array,
        total: Number,
        discount_amount: Number,
        sub_total: Number,
        insurance: Number,
        errors: { type: Object, default: false },
        coupon: { type: Object, default: false },
        hopi_campaigns: { type: Object, default: false },
        hopi_bird: { type: Number, default: 0 },
        hopi_balance: { type: Number, default: 0 },
        non_mass_payment_total: { type: Number, default: 0 },
        hopi_active: { type: Boolean, default: false },
        pgs_active: { type: Boolean, default: false },
        is_mass_payment_enabled: { type: Boolean, default: false }
    },
    layout: Layout,
    mounted() {
        this.is_mass_payment_local_enable = this.is_mass_payment_enabled;

        // URL'den mixed-cart-error parametresini kontrol et
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get("mixed-cart-error") === "true") {
            this.showMixedCartError = true;
            // URL'den parametreyi temizle
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    },
    methods: {
        closeCampaignModal() {
            this.campaignsModalIsOpen = false;
        },
        openCampaignModal() {
            this.campaignsModalIsOpen = true;
        },
        closePegasusModal(value) {
            this.isPegasusModalOpen = false;
            this.campaignsModalIsOpen = true;
        },
        submitCoupon() {
            this.form.post("coupon", {
                onError: () => {
                    this.couponsectionColor = "border-kbred bg-kbred/10 hover:bg-kbred/10";
                },
                onSuccess: () => {
                    this.couponsectionColor = "border-kbgreen bg-kbgreen/10 hover:bg-kbgreen/10";
                    this.couponSection = true;
                    // setTimeout(() => {
                    //     this.closeCampaignModal();
                    // }, 1000);
                }
            });
            this.form.coupon = "";
        },
        itemSelected(itemId, monthId) {
            if (this.selectedItems == null) {
                this.selectedItems = [];
            }

            if (this.selectedItems.filter((item) => item.product_id == itemId && item.month_id == monthId).length > 0) this.selectedItems = this.selectedItems.filter((item) => item.product_id != itemId || item.month_id != monthId);
            else this.selectedItems.push({ product_id: itemId, month_id: monthId });

        },
        selectAll() {
            this.products.map((item) => {
                this.itemSelected(item.product_id, item.month_id);
            });
        },
        closeHopi(value) {
            console.log("closeHopi", value);
            this.isHopiModelOpen = false;
            this.campaignsModalIsOpen = true;
        },
        openHopi() {
            this.isHopiModelOpen = true;
            this.campaignsModalIsOpen = false;
        },
        openPegasusModal() {
            this.isPegasusModalOpen = true;
            this.campaignsModalIsOpen = false;
        },
        closeHopiCampaign(value) {
            console.log("closeHopiCampaign", value);
            this.isHopiCampaignModelOpen = false;
        },
        changeSelectedHopiCampaign(value) {
            console.log("selectedHopiCampaign", value);
            this.selectedHopiCampaign = value;
        },
        updateEnteredBalance(value) {
            console.log("enteredHopiBalance", value);
            this.enteredHopiBalance = value;
        },
        clearSelected() {
            let removedProducts = [];
            removedProducts = this.products.filter((item) => {
                return this.selectedItems.filter((item2) => {
                    return item2.product_id == item.product_id && item2.month_id == item.month_id;
                });
            });

            this.selectedItems = [];

            console.log(removedProducts);

            let removedCartProducts = [];
            removedProducts.map((item, keys) => {
                let product = {};
                product.item_id = item.id;
                product.item_name = item.product.attribute_data.name.tr;
                product.price = item.total;
                //product.item_brand = item.brand.name;
                //product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                //product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                //product.item_color = item.variants[0]?.options[0] ?? ""; // Kategori verisidne yok
                //product.item_list_id = "HOME-1";
                //product.item_list_name = "Homepage - Kampanya Ürünleri List";
                product.item_variant = item.month?.name;
                product.index = keys;
                product.quantity = item.quantity;
                removedCartProducts.push(product);
            });

            // Sum removed products price
            let removedProductsPrice = 0;
            removedProducts.map((item) => {
                removedProductsPrice += item.total;
            });

            dataLayer.push({
                event: "remove_from_cart",
                ecommerce: {
                    currency: "TRY",
                    value: removedProductsPrice,
                    items: removedCartProducts
                }
            });
        },
        getMassPaymentDiscount(subscriptionMonth) {
            let discount = 0;
            switch (subscriptionMonth) {
                case 3:
                    discount = 10;
                    break;
                case 4:
                    discount = 15;
                    break;
                case 5:
                    discount = 20;
                    break;
                default:
                    discount = 20;
            }
            return discount;
        },
        updateCartItem(cartLine, monthId, productVariantId = null) {
            console.log("updateCartItem", cartLine, monthId, productVariantId);
            this.$inertia.post(
                route("updateCart"),
                {
                    cart_item_id: cartLine.id,
                    month: monthId,
                    insurance: cartLine.is_insurance_requested,
                    product_variant_id: productVariantId
                },
                {
                    preserveState: true,
                    preserveScroll: true,
                    onSuccess: () => { /* Notification */
                    }
                }
            );
        },
        insuranceChanged(cartLine, monthId) {
            // Toggle insurance
            cartLine.is_insurance_requested = !cartLine.is_insurance_requested;
            // call updateCartItem
            this.updateCartItem(cartLine, monthId);
        },
        massPaymentChanged() {
            this.$inertia.post(
                "/sepet-toplu-odeme",
                {
                    is_mass_payment_enabled: this.is_mass_payment_local_enable
                },
                {
                    preserveState: true,
                    preserveScroll: true
                }
            );
        },
        checkEligibleForInsurance(cartLine) {
            const EligibleCategories = [47, 49, 54, 84];
            let status = cartLine.categories.filter((x) => EligibleCategories.includes(x));
            return status.length > 0;
        },
        paymentStep() {
            let cartProducts = [];
            this.products.map((item) => {
                let product = {};
                product.item_id = item.id;
                product.item_name = item.product.attribute_data.name.tr;
                product.price = item.total;
                // product.item_brand = item.product.brand.name;
                // product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                // product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                // (product.item_color = item.variants[0]?.options[0] ?? ""), (product.index = 0);
                product.discount = 0;
                product.item_variant = item.month?.name;
                product.quantity = item.quantity;
                cartProducts.push(product);
            });

            dataLayer.push({
                event: "begin_checkout",
                ecommerce: {
                    currency: "TRY",
                    value: this.total,
                    items: cartProducts
                }
            });
        },
        clearHopiSelections() {
            console.log("clearHopiSelections");
            this.selectedHopiCampaign = "";
            this.enteredHopiBalance = 0;
        }
    },
    created() {
        this.$inertia.on("navigate", (event) => {
            console.log("sepet navigate", event.detail.page.url);

            let cartProducts = [];
            this.products.map((item) => {
                let product = {};
                //product.item_id = item.id; cart_item_id idi
                product.item_id = item.product_id; // varyant id yaptım
                //product.product_id = item.product_id; // varyant id idi
                product.product_id = item.product.product_id; // ürün id yaptım
                product.item_name = item.product.attribute_data.name.tr;
                product.price = item.total;
                // product.item_brand = item.product.brand.name;
                // product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                // product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                // (product.item_color = item.variants[0]?.options[0] ?? ""), (product.index = 0);
                product.discount = 0;
                product.item_variant = item.month?.name;
                product.quantity = item.quantity;
                cartProducts.push(product);
            });

            if (event.detail.page.url.startsWith("/sepetim")) {
                dataLayer.push({
                    event: "view_cart",
                    ecommerce: {
                        currency: "TRY",
                        value: this.total,
                        items: cartProducts
                    }
                });
            }
        });
    },
    watch: {
        selectedHopiCampaign: function(val) {
            localStorage.setItem("selectedHopiCampaign", val);
        },
        enteredHopiBalance: function(val) {
            localStorage.setItem("enteredHopiBalance", val);
        },
        hopi_campaigns: function(val) {
            // console.log("hopi_campaigns", val);
            if (this.hopi_campaigns.length > 0) {
                this.isHopiModelOpen = false;
                this.isHopiCampaignModelOpen = true;
            }
            //if (val) this.isHopiCampaignModelOpen = true;
            //localStorage.setItem("isHopiCampaignModelOpen", val);
        }
    }
};
</script>

<template>

    <Head title="Sepetim" />

    <main class="my-3 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-4 mx-auto">
        <!-- Mixed cart error bildirim -->
        <div v-if="showMixedCartError" class="mb-4 bg-red-50 border-2 border-red-400 rounded-lg p-4 mb-10">
            <p class="text-sm md:text-base text-red-800 font-semibold text-center">
                Sepetinizde hem vadeli hem de normal kiralama ürünü bulunmaktadır. Devam etmek için sepetinizde sadece vadeli ya da normal kiralama ürünü olması gerekmektedir.
            </p>
        </div>
        <section class="flex w-full justify-center md:justify-between" v-if="productsWithoutInstallment.length > 0">
            <div class="w-full md:w-12/12 lg:w-5/12 flex flex-wrap md:flex-nowrap justify-center lg:justify-between items-end mt-8 lg:mt-0 border-b-none lg:border-b-3 border-bordergray">
                <div class="w-full md:w-8/12 lg:w-9/12 flex border-b-3 md:border-none border-kbgreen pb-2">
                    <h3 class="text-2xl ts:text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap">
                        Sepetim ({{ productsWithoutInstallment.length }} ürün)</h3>
                </div>
            </div>
        </section>
        <section class="mt-2 mb-12 flex flex-col lg:flex-row relative" v-if="products.length > 0">
            <!--            <div class="w-full lg:w-9/12 pr-0 lg:pr-4 px-0 flex flex-col justify-between">-->
            <div class="w-full lg:w-9/12 pr-0 lg:pr-4 px-0">
                <!-- Normal ürünler (is_installment = false) -->
                <div class="flex flex-wrap lg:flex-wrap relative" v-for="product in productsWithoutInstallment" :key="`${product.product.id}_${product.month_id}`">
                    <div class="w-full flex flex-wrap lg:flex-wrap border-2 border-bordergray rounded-lg p-2 lg:p-3 mb-5">
                        <div class="w-2/12 md:w-2/12 lg:w-2/12 flex items-center">
                            <div class="flex justify-center items-center w-full bg-white">
                                <picture class="">
                                    <source :srcset="product.product.imagesWebP?.thumb_webp" type="image/webp" />
                                    <source :srcset="product.product.imagesWebP?.zoom_webp" type="image/jpeg" />
                                    <img :src="product.product.imagesWebP?.thumb" alt="Alt Text!" />
                                </picture>
                            </div>
                        </div>
                        <div class="w-10/12 md:w-10/12 flex flex-wrap">
                            <div class="w-full lg:w-full pl-3 ts:pl-4">
                                <div class="flex justify-between w-full items-start">
                                    <div class="text-sm ts:text-lg text-black font-santralregular font-bold w-[calc(100%-100px)]">
                                        {{ product.product.attribute_data.name.tr }}
                                        <pre v-if="$page.props.auth?.user?.id == 1">Cat ID List: {{ product.categories.map(x => x).join(", ") }}</pre>
                                    </div>
                                    <div>
                                        <div class=" ts:text-xl text-black font-bold font-santralextrabold leading-none">
                                            {{ product.total }} TL
                                        </div>
                                        <div class="font-kiralabunuthin text-xs lg:text-sm text-black font-santralregular ts:pb-2 -mt-1">
                                            Aylık Tutar
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs md:text-sm text-checkoutgray font-santralregular font-semibold leading-tight" v-html="product.product.attribute_data.excerpt?.tr"></div>
                                <div class="text-xs md:text-sm text-black font-santralregular leading-tight flex items-center">
                                    <img src="../../images/svg/delivery.svg">
                                    {{ product.product.min_delivery_time }}-{{ product.product.max_delivery_time }} İş
                                    Günü
                                </div>
                            </div>
                        </div>
                        <div class="w-10/12 pl-3 pr-1 py-2 md:pt-0 relative" :class="product.alternative_variants.filter((v) => v.rentable).length > 0 ? 'md:w-5/12' : 'md:w-6/12'">
                            <img src="../../images/svg/chronometer.svg" class="absolute top-3 md:top-2 left-5 md:left-4 w-6 h-6">
                            <select class="w-full px-2 py-2 pl-[30px] mr-0 md:mr-1 lg:mr-4 rounded-2lg border-2 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-xs md:text-sm ts:text-base tracking-wider" name="sirala"
                                    id="sirala" v-model="product.month.id" @change="updateCartItem(product, product.month.id)">
                                <option :value="month.id" v-for="month in product.product.monthOptions" :key="month.id">
                                    {{ month.name }}
                                </option>
                            </select>
                        </div>
                        <div class="w-10/12 pl-3 pr-1 py-2 md:pt-0 relative" :class="product.alternative_variants.filter((v) => v.rentable).length > 0 ? 'md:w-5/12' : ''" v-if="product.alternative_variants.filter((v) => v.rentable).length > 0">
                            <img src="../../images/svg/colors.svg" class="absolute top-3 md:top-2 left-5 md:left-4 w-7 h-7">
                            <select class="w-full px-2 py-2 pl-[30px] mr-0 md:mr-1 lg:mr-4 rounded-2lg border-2 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-xs md:text-sm ts:text-base tracking-wider"
                                    @change="updateCartItem(product, product.month.id, $event.target.value)">
                                <option class="flex text-base cursor-pointer text-gray-500 border border-checkoutgray rounded-full h-12 content-center p-2 mt-5" value="0">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full">&nbsp;</div>
                                    <div><a href="#" class="w-full rounded-full py-2 px-4 lg:font-bold text-black">Renk
                                        seçiniz</a></div>
                                </option>
                                <option class="flex text-base cursor-pointer text-gray-500 border border-checkoutgray rounded-full h-12 content-center p-2 mt-5" v-for="variant in product.alternative_variants.filter((v) => v.rentable)"
                                        :key="variant.id" :selected="variant.id == product.product_id" :value="variant.id">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full">&nbsp;</div>
                                    <div><a href="#" class="w-full rounded-full py-2 px-4 lg:font-bold text-black">{{
                                            variant.option_value_name }}</a></div>
                                </option>
                            </select>
                        </div>
                        <div class="w-2/12 pl-3 md:pl-2 py-2 md:pt-0 flex items-center justify-end" :class="product.alternative_variants.filter((v) => v.rentable).length > 0 ? 'md:w-2/12' : 'md:w-6/12'">
                            <Link class="pl-4" href="/sepetden-kaldir" :data="{ itemIds: [{ product_id: product.product_id, month_id: product.month_id }] }" method="post" as="button" @click="clearSelected()">
                                <img src="../../images/svg/trash.svg" class="h-[23px]">
                            </Link>
                        </div>

                        <div class="flex justify-between items-center w-full pt-4 mt-2 border-t-2 border-bordergray w-full">
                            <!--                                ///// Sepet Sırasında Adet artırma alanı-->
                            <!--                                <div class="w-6/12 md:w-2/12 lg:w-2/12 pl-2 pr-1 border-r-none md:border-r-2 border-bordergray pt-2 md:pt-0">-->
                            <!--                                    <div class="flex items-center space-x-2">-->
                            <!--                                        <button class=" hover:bg-gray-100 active:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500">-->
                            <!--                                            <img src="../../images/svg/minus.svg">-->
                            <!--                                        </button>-->
                            <!--                                        <input type="number" value="1" min="1" class="w-12 text-center border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500" />-->
                            <!--                                        <button class="hover:bg-gray-100 active:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500">-->
                            <!--                                            <img src="../../images/svg/plus.svg">-->
                            <!--                                        </button>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <div class="font-kiralabunuthin text-sm text-black w-9/12 lg:w-10/12 flex items-center space-x-1 md:space-x-3 h-3/4" v-if="checkEligibleForInsurance(product)">
                                <div class="font-kiralabunuthin text-xs md:text-sm text-black font-normal">Sigorta:
                                </div>
                                <div class="text-xs md:text-sm ts:text-base text-black font-bold lg:text-center pr-3">
                                    <input @change="insuranceChanged(product, product.month.id)" :checked="product.is_insurance_requested" class="w-5 h-5 border-3 rounded-md focus:outline-none checked:bg-black" type="checkbox" />
                                </div>
                                <span class="font-santralregular text-sm leading-none tracking-tighter">Hasar onarımının %100'ü kapsaması için ek sigorta paket ücreti <b>bir seferlik</b> kredi kartından tahsil edilir.</span>
                            </div>
                            <div v-if="false" class="pl-3 md:pl-2 mt-2 md:mt-0 flex flex-col items-end justify-end" :class="checkEligibleForInsurance(product) ? 'w-3/12 lg:w-2/12' : 'w-full'">
                                <Link class="pl-4" href="/sepetden-kaldir" :data="{ itemIds: [{ product_id: product.product_id, month_id: product.month_id }] }" method="post" as="button" @click="clearSelected()">
                                    <img src="../../images/svg/trash.svg" class="h-[23px]">
                                </Link>
                            </div>
                            <div v-if="checkEligibleForInsurance(product)" class="pl-3 md:pl-2 mt-2 md:mt-0 flex items-end justify-end" :class="checkEligibleForInsurance(product) ? 'w-3/12 lg:w-2/12' : 'w-full'">
                                <p class="text-base font-bold tracking-wide text-black w-full whitespace-nowrap text-end">{{ product.insurance_price.toFixed(2) }} TL</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vadeli ürünler (is_installment = true) -->
                <div v-if="productsWithInstallment.length > 0" class="mt-8">
                    <h3 class="text-2xl ts:text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">
                        Vadeli Ürün Sepetim ({{ productsWithInstallment.length }} ürün)
                    </h3>
                    <div class="flex flex-wrap lg:flex-wrap relative" v-for="product in productsWithInstallment" :key="`${product.product.id}_${product.month_id}_installment`">
                        <div class="w-full flex flex-wrap lg:flex-wrap border-2 border-bordergray rounded-lg p-2 lg:p-3 mb-5">
                            <div class="w-2/12 md:w-2/12 lg:w-2/12 flex items-center">
                                <div class="flex justify-center items-center w-full bg-white">
                                    <picture class="">
                                        <source :srcset="product.product.imagesWebP?.thumb_webp" type="image/webp" />
                                        <source :srcset="product.product.imagesWebP?.zoom_webp" type="image/jpeg" />
                                        <img :src="product.product.imagesWebP?.thumb" alt="Alt Text!" />
                                    </picture>
                                </div>
                            </div>
                            <div class="w-10/12 md:w-10/12 flex flex-wrap">
                                <div class="w-full lg:w-full pl-3 ts:pl-4">
                                    <div class="flex justify-between w-full items-start">
                                        <div class="text-sm ts:text-lg text-black font-santralregular font-bold w-[calc(100%-100px)]">
                                            {{ product.product.attribute_data.name.tr }}
                                            <pre v-if="$page.props.auth?.user?.id == 1">Cat ID List: {{ product.categories.map(x => x).join(", ") }}</pre>
                                        </div>
                                        <div>
                                            <div class=" ts:text-xl text-black font-bold font-santralextrabold leading-none">
                                                {{ product.total }} TL
                                            </div>
                                            <div class="font-kiralabunuthin text-xs lg:text-sm text-black font-santralregular ts:pb-2 -mt-1">
                                                Aylık Tutar
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-xs md:text-sm text-checkoutgray font-santralregular font-semibold leading-tight" v-html="product.product.attribute_data.excerpt?.tr"></div>
                                    <div class="text-xs md:text-sm text-black font-santralregular leading-tight flex items-center">
                                        <img src="../../images/svg/delivery.svg">
                                        {{ product.product.min_delivery_time }}-{{ product.product.max_delivery_time }} İş
                                        Günü
                                    </div>
                                </div>
                            </div>
                            <div class="w-10/12 pl-3 pr-1 py-2 md:pt-0 relative" :class="product.alternative_variants.filter((v) => v.rentable).length > 0 ? 'md:w-5/12' : 'md:w-6/12'">
                                <img src="../../images/svg/chronometer.svg" class="absolute top-3 md:top-2 left-5 md:left-4 w-6 h-6">
                                <select class="w-full px-2 py-2 pl-[30px] mr-0 md:mr-1 lg:mr-4 rounded-2lg border-2 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-xs md:text-sm ts:text-base tracking-wider" name="sirala"
                                        id="sirala" v-model="product.month.id" @change="updateCartItem(product, product.month.id)">
                                    <option :value="month.id" v-for="month in product.product.monthOptions" :key="month.id">
                                        {{ month.name }}
                                    </option>
                                </select>
                            </div>
                            <div class="w-10/12 pl-3 pr-1 py-2 md:pt-0 relative" :class="product.alternative_variants.filter((v) => v.rentable).length > 0 ? 'md:w-5/12' : ''" v-if="product.alternative_variants.filter((v) => v.rentable).length > 0">
                                <img src="../../images/svg/colors.svg" class="absolute top-3 md:top-2 left-5 md:left-4 w-7 h-7">
                                <select class="w-full px-2 py-2 pl-[30px] mr-0 md:mr-1 lg:mr-4 rounded-2lg border-2 border-bordergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-xs md:text-sm ts:text-base tracking-wider"
                                        @change="updateCartItem(product, product.month.id, $event.target.value)">
                                    <option class="flex text-base cursor-pointer text-gray-500 border border-checkoutgray rounded-full h-12 content-center p-2 mt-5" value="0">
                                        <div class="w-8 h-8 bg-blue-500 rounded-full">&nbsp;</div>
                                        <div><a href="#" class="w-full rounded-full py-2 px-4 lg:font-bold text-black">Renk
                                            seçiniz</a></div>
                                    </option>
                                    <option class="flex text-base cursor-pointer text-gray-500 border border-checkoutgray rounded-full h-12 content-center p-2 mt-5" v-for="variant in product.alternative_variants.filter((v) => v.rentable)"
                                            :key="variant.id" :selected="variant.id == product.product_id" :value="variant.id">
                                        <div class="w-8 h-8 bg-blue-500 rounded-full">&nbsp;</div>
                                        <div><a href="#" class="w-full rounded-full py-2 px-4 lg:font-bold text-black">{{
                                                variant.option_value_name }}</a></div>
                                    </option>
                                </select>
                            </div>
                            <div class="w-2/12 pl-3 md:pl-2 py-2 md:pt-0 flex items-center justify-end" :class="product.alternative_variants.filter((v) => v.rentable).length > 0 ? 'md:w-2/12' : 'md:w-6/12'">
                                <Link class="pl-4" href="/sepetden-kaldir" :data="{ itemIds: [{ product_id: product.product_id, month_id: product.month_id }] }" method="post" as="button" @click="clearSelected()">
                                    <img src="../../images/svg/trash.svg" class="h-[23px]">
                                </Link>
                            </div>

                            <div class="flex justify-between items-center w-full pt-4 mt-2 border-t-2 border-bordergray w-full">
                                <div class="font-kiralabunuthin text-sm text-black w-9/12 lg:w-10/12 flex items-center space-x-1 md:space-x-3 h-3/4">
                                    <div class="font-kiralabunuthin text-xs md:text-sm text-black font-bold">Bu ürün kira süresi sonunda senin olur
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bilgilendirme mesajı -->
                <div v-if="productsWithoutInstallment.length > 0 && productsWithInstallment.length > 0" class="w-full mt-6 mb-4">
                    <div class="bg-yellow-50 border-2 border-yellow-400 rounded-lg p-4">
                        <p class="text-sm md:text-base text-yellow-800 font-semibold text-center">
                            Devam etmek için sepetinizde sadece vadeli ya da normal kiralama ürünü olması gerekmektedir.
                        </p>
                    </div>
                </div>
            </div>
            <div class="w-full lg:w-3/12 sticky top-4 h-fit z-10" id="checkout-payment-info" style="position: -webkit-sticky; position: sticky;">
                <CheckoutPaymentInfo :campaignmodal="true" :campaignsModalIsOpen="campaignsModalIsOpen" :total="total" :products="products" :discount="discount_amount" :sub_total="sub_total" :coupon="coupon" :insurance="insurance"
                                     :non_mass_payment_total="non_mass_payment_total" :is_mass_payment_enabled="is_mass_payment_enabled" :is_mass_payment_local_enable="is_mass_payment_local_enable" @openCampaignModal="openCampaignModal">

                    <template #action>
                        <div class="w-full">
                            <a v-if="!(productsWithoutInstallment.length > 0 && productsWithInstallment.length > 0)"
                               href="/odeme"
                               class="relative block bg-kbgreen border-2 border-transparent text-white text-center rounded-full py-2 lg:py-3 text-xl px-2 lg:px-4 font-bold hover:bg-white hover:text-black hover:border-black transition-all">
                                Sepeti
                                Onayla</a>
                            <div v-else
                                 class="relative block bg-gray-400 border-2 border-transparent text-white text-center rounded-full py-2 lg:py-3 text-xl px-2 lg:px-4 font-bold cursor-not-allowed">
                                Sepeti
                                Onayla
                            </div>
                        </div>

                    </template>
                </CheckoutPaymentInfo>
            </div>
            <HopiModal :isOpen="isHopiModelOpen" @update:isOpen="closeHopi" />
            <PegasusModal :isOpen="isPegasusModalOpen" @update:isOpen="closePegasusModal" />
            <HopiCampaignModal :isOpen="isHopiCampaignModelOpen" :campaigns="hopi_campaigns" :balance="hopi_balance" :selectedHopiCampaign="selectedHopiCampaign" @update:isOpenCampaignModal="closeHopiCampaign"
                               @update:selectedCampaign="changeSelectedHopiCampaign" @update:enteredBalance="updateEnteredBalance" />
        </section>
        <section class="mt-6 mb-12 flex flex-col lg:flex-row" v-else>
            <div class="w-full max-w-[90%] mx-auto border-2 border-bordergray rounded-lg text-black text-base">
                <div class="p-3 text-center">Sepetinde ürün bulunmamaktadır. Kiralamaya hemen başlamak için <a href="/kategoriler/tum-urunler" class="underline">tıkla.</a></div>
            </div>
        </section>
        <TransitionRoot appear :show="campaignsModalIsOpen" as="template">
            <Dialog as="div" @close="closeCampaignModal" class="relative z-50">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>
                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel class="max-w-[350px] lg:max-w-[750px] transform overflow-hidden rounded-sm bg-white px-1 py-1 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                                <div class="p-4 mx-auto bg-white rounded-lg shadow-md">
                                    <div class="flex justify-center items-center">
                                        <h2 class="text-2xl font-semibold text-kbblue text-center mb-3">Kampanya Seç</h2>
                                        <button class="absolute right-4 top-4 text-black hover:text-gray-700 focus:outline-none px-2 py-1 bg-bordergray rounded transition-all hover:bg-white hover:shadow-aboutshadow" @click="closeCampaignModal">
                                            <span class="font-santralregular font-bold">X</span>
                                        </button>
                                    </div>
                                    <div class="relative">
                                        <template v-if="!coupon.code">
                                            <button @click="couponSection = !couponSection" v-if="couponSection"
                                                    class="w-full flex items-center justify-center p-3 mb-4 border border-kbblue rounded-lg text-kbblue font-medium hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-purple-400 transition-all">
                                                <span class="mr-2 px-2 py-1 shadow-aboutshadow">
                                                    <img src="../../images/svg/plus-purple.svg">
                                                </span>
                                                <span class="text-lg">Kupon Kodu Ekle</span>
                                            </button>
                                        </template>
                                        <template v-if="coupon.code">
                                            <div
                                                class="relative border-kbgreen text-kbgreen bg-kbgreen/10 hover:bg-kbgreen/10 w-full text-center p-3 mb-4 border rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-purple-400 transition-all">
                                                <div class="text-base md:text-lg text-black mt-3 md:mt-0">Kupon Kodu : <span class="text-kbblue">{{ coupon.code }}</span></div>
                                                <div class="font-santralregular text-xs md:text-sm text-black">{{ coupon.description }}</div>
                                                <Link href="/coupon" :data="{ coupon: null }" method="post" as="button" class="text-sm absolute top-2 text-kbred right-2 px-1 rounded-lg leading-none">
                                                    Kaldır
                                                </Link>
                                            </div>
                                        </template>
                                        <div v-if="!couponSection" class="relative px-3 mb-4 border rounded-lg text-kbblue font-medium focus:outline-none focus:ring-2 focus:ring-purple-400 transition-all border-kbblue bg-purple-50">
                                            <form class="w-full flex items-center justify-between my-2" @submit.prevent="submitCoupon">
                                                <input
                                                    class="w-full mr-3 text-black rounded-md border-none placeholder:text-base placeholder:text-placeholdergray focus:border-none focus:outline-none focus:ring-white text-lg tracking-wide placeholder:font-kiralabunuthin"
                                                    required type="text" placeholder="Kupon Kodu" name="couponCode" v-model="form.coupon" :disabled="is_mass_payment_local_enable" />
                                                <button class=" whitespace-nowrap inline-block md:hidden" type="submit" :disabled="form.processing">
                                                    Ekle
                                                </button>
                                                <button class=" whitespace-nowrap hidden md:inline-block" type="submit" :disabled="form.processing">
                                                    Kupon Kodu Ekle
                                                </button>
                                            </form>
                                            <div class="flex self-center justify-center flex-col absolute w-full bottom-0 left-0 " v-if="is_mass_payment_local_enable">
                                                <div class="flex text-sm text-kbred mr-[2px] tracking-normal">Toplu
                                                    ödemede
                                                    kupon kullanılamaz
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-center flex-col w-full" v-if="Object.entries(errors).length > 0">
                                            <div class="flex text-sm text-kbred tracking-normal py-1 px-4 border rounded" :class="couponsectionColor" v-for="error in errors">
                                                {{ error }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <!--                                        <p class="text-base font-medium text-gray-500 text-center">Kullanıma Hazır Kampanyalar</p>-->
                                        <div class="mt-2 flex items-center justify-center text-gray-700 w-full text-xl">
                                            <span class="mr-2 text-xl">ℹ️</span> Tüm kampanyalar aşağıda!
                                        </div>
                                    </div>

                                    <!--                                    <div class="mb-4 border border-gray-200 rounded-lg p-4 bg-gray-50 hover:bg-kbgreen/10 transition-all">-->
                                    <!--                                        <div class="flex justify-between items-center">-->
                                    <!--                                            <div class="flex items-center space-x-3">-->
                                    <!--                                                <img src="../../images/svg/giftbox-purple.svg">-->
                                    <!--                                                <div>-->
                                    <!--                                                    <p class="font-semibold text-black text-base">Kampanya Başlık</p>-->
                                    <!--                                                    <p class="text-xs text-gray-700 font-santralregular">Kampanya detay</p>-->
                                    <!--                                                </div>-->
                                    <!--                                            </div>-->
                                    <!--                                            <button class="text-kbblue font-medium hover:underline">Sepete Ekle</button>-->
                                    <!--                                        </div>-->
                                    <!--                                    </div>-->

                                    <div class="mb-4 px-4 lg:px-6 py-2 bg-[#FCF3EC] hover:bg-kbyellow transition-all rounded-2lg mt-2 cursor-pointer" id="merhaba500" v-if="false" @click="() => { form.coupon = 'merhaba500'; submitCoupon(); }">
                                        <div class="flex justify-start items-center">
                                            <img class="w-[30px] h-[30px]" src="../../images/svg/discount.svg" alt="">
                                            <div class="ml-4 text-sm mt-1 ts:mt-0 2xl:mt-1">
                                                Yeni üyelere özel 6 ay ve üzeri kiralamaların ilk ayında 500 tl indirim Kiralabunu'dan hediye! İndirim
                                                kodu: merhaba500
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-4 bg-[#f4fbf1] hover:bg-[#d5ebcc] transition-all py-2 px-2 md:px-4 rounded-lg md:rounded-2lg" v-if="false && products.length == 1 && products[0].month_id >= 3">
                                        <div class="text-xs md:text-base text-black leading-none flex flex-col-reverse justify-between items-start">
                                            <div class="w-full">
                                                <input type="checkbox" id="massPayment" @change="massPaymentChanged" v-model="is_mass_payment_local_enable"
                                                       class="cursor-pointer bg-green-50 border-2 w-5 h-5 md:w-6 md:h-6 text-kbgreen border-black rounded focus:ring-kbgreen" />
                                                <label for="massPayment" class="cursor-pointer leading-loose ml-2">
                                                    Toplu öde %{{ this.getMassPaymentDiscount(products[0].month_id) }}
                                                    indirimle
                                                    {{ (non_mass_payment_total * products[0].month.value).toFixed(2) }}
                                                    TL
                                                    yerine toplam {{ ((non_mass_payment_total / 100) *
                                                    (100 -
                                                        this.getMassPaymentDiscount(products[0].month_id)) *
                                                    products[0].month.value).toFixed(2) }} TL</label>
                                            </div>
                                            <div class="flex items-center justify-between w-full md:w-auto">
                                                <div class="text-lg whitespace-nowrap line-through mr-2">{{
                                                        non_mass_payment_total }} TL
                                                </div>
                                                <div class="text-lg whitespace-nowrap">{{ ((non_mass_payment_total /
                                                    100) * (100
                                                    - this.getMassPaymentDiscount(products[0].month_id))).toFixed(2) }}
                                                    TL / Ay
                                                </div>
                                                <Popover v-slot="{ open }" class="relative">
                                                    <PopoverButton :class="open ? 'text-white' : 'text-white/90'" class="group inline-flex items-center px-3 py-2 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75">
                                                        <svg id="question-circle-fill" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 md:w-6 md:h-6" viewBox="0 0 14.141 14.141">
                                                            <path id="Path_92" data-name="Path 92"
                                                                  d="M14.141,7.07A7.07,7.07,0,1,1,7.07,0a7.07,7.07,0,0,1,7.07,7.07ZM5.807,5.332H4.64A2.168,2.168,0,0,1,7.076,3.093c1.235,0,2.362.645,2.362,1.98a2.157,2.157,0,0,1-1.1,1.818c-.651.494-.892.679-.892,1.314v.314H6.29l-.006-.409A2.014,2.014,0,0,1,7.316,6.353c.521-.392.853-.65.853-1.212A1.027,1.027,0,0,0,7.007,4.109a1.128,1.128,0,0,0-1.2,1.223Zm1.106,5.694a.828.828,0,1,1,0-1.65.829.829,0,1,1,0,1.65Z"
                                                                  transform="translate(0 0)" fill="#ace697" fill-rule="evenodd" />
                                                        </svg>
                                                    </PopoverButton>

                                                    <transition enter-active-class="transition duration-200 ease-out" enter-from-class="translate-y-1 opacity-0" enter-to-class="translate-y-0 opacity-100"
                                                                leave-active-class="transition duration-150 ease-in" leave-from-class="translate-y-0 opacity-100" leave-to-class="translate-y-1 opacity-0">
                                                        <PopoverPanel class="absolute -left-8 z-10 mt-3 w-screen max-w-sm -translate-x-3/4 -translate-y-full px-4 sm:px-0 z-[99999]">
                                                            <div class="overflow-hidden rounded-lg shadow-lg ring-1 ring-black/5 border-2 border-kbgreen">
                                                                <div class="bg-gray-50 p-4">
                                                                    <span class="block text-sm text-gray-500">
                                                                        kiraladığınız
                                                                        sürenin toplam kira bedeli %{{
                                                                            this.getMassPaymentDiscount(products[0].month_id)
                                                                        }}
                                                                        indirim ile tek seferde tahsil
                                                                        edilecektir.
                                                                        Böyleliklekiralama süren bitene kadar kira
                                                                        bedeli tahsil
                                                                        edilmez. </span>
                                                                </div>
                                                            </div>
                                                        </PopoverPanel>
                                                    </transition>
                                                </Popover>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="mb-4 border border-gray-200 rounded-lg flex justify-between items-center">
                                        <!--                                        <div class="cursor-pointer rounded-lg"><img src="/images/hopi/button.png" alt=""></div>-->
                                        <div class="cursor-pointer" @click="openHopi" v-if="hopi_active && hopi_bird === null && hopi_campaigns.length == 0">
                                            <img src="/images/hopi/button.png" alt="" />
                                        </div>
                                        <div class="mt-5 border border-hopi-pink p-3 rounded text-sm w-full" v-if="hopi_active && hopi_bird && hopi_campaigns && hopi_campaigns.length > 0">
                                            <p>Hopi Bakiyeniz: {{ hopi_balance }} ₺</p>
                                            <!--                        <p>Hopi Seçili Kampanya: {{ selectedHopiCampaign }} ₺</p>-->
                                            <p v-if="selectedHopiCampaign">Hopi Seçili Kampanya: {{ selectedHopiCampaign
                                            &&
                                            hopi_campaigns.filter((x) => x.code == selectedHopiCampaign)[0]?.name }}
                                            </p>
                                            <p v-if="enteredHopiBalance > 0">Kullanılan Paracık: {{ enteredHopiBalance
                                                }}</p>
                                            <!--                        <p v-if="hopi_balance > 0" class="mt-2"><input class="p-1" type="text" name="balance" id="" v-model="enteredHopiBalance" /></p>-->
                                            <!--                        <p v-if="hopi_balance > 0" class="text-sm mt-2">Kullanmak istediğiniz hopi bakiyenizi giriniz</p>-->
                                            <div class="flex justify-around items-center mt-3">
                                                <div @click="isHopiCampaignModelOpen = true"
                                                     class="cursor-pointer bg-white text-xs text-hopi-pink border-1 border-hopi-pink rounded-full py-1 px-2 self-center font-santralextrabold mt-2 hover:bg-hopi-pink hover:text-white hover:border-white">
                                                    Değiştir
                                                </div>
                                                <Link href="/hopi-user-logout"
                                                      class="cursor-pointer bg-white text-xs text-hopi-pink border-1 border-hopi-pink rounded-full py-1 px-2 self-center font-santralextrabold mt-2 hover:bg-hopi-pink hover:text-white hover:border-white"
                                                      @click="clearHopiSelections" as="button" method="post">Kaldır
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-4 border border-gray-200 rounded-lg flex justify-between items-center">
                                        <div class="px-4 cursor-pointer flex items-center space-x-2 w-full max-w-full" @click="openPegasusModal" v-if="pgs_active">
                                            <img src="../../images/brands/pegasus-airlines-logo.jpeg" alt="" class="w-16" />
                                            <span class="border-l-3 border-bordergray h-[40px] px-1 lg:px-3"></span>
                                            <span class="whitespace-nowrap text-base text-gray-500"><span class="text-orange-500">BOLBOL Puan</span> için tıkla</span>
                                        </div>
                                    </div>

                                    <button class="w-full p-3 border border-purple-400 text-kbblue rounded-lg hover:bg-purple-50 font-medium focus:outline-none focus:ring-2 focus:ring-purple-400" @click="closeCampaignModal">
                                        Kampanyasız Devam Et
                                    </button>
                                </div>

                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </main>
</template>
