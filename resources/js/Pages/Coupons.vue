<script>
import {Head, <PERSON>} from '@inertiajs/inertia-vue3';
import Layout from '@/Pages/Shared/Layout.vue';
import UserMenu from '@/Pages/Shared/UserMenu.vue';

export default {
    components: {
        Link,
        Head,
        UserMenu,
    },
    layout: Layout,
    props: {
        order_count: Number,
    }
}
</script>

<template>
    <Head title="Kuponlar"/>
    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <user-menu :order_count="order_count" :active="`Coupons`" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 px-4 pt-2 mx-auto">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7">
                        Kuponlar</h3>
                </div>
                <div class="flex flex-col justify-center items-center mb-10 max-w-[454px] mx-auto">
                    <div class="flex justify-center items-center bg-kb-light-grey rounded-full w-32 h-32 mb-5">
                        <div class="flex justify-center items-center bg-black rounded-full w-20 h-20">
                            <svg xmlns="http://www.w3.org/2000/svg" width="50.812" height="43.674"
                                 viewBox="0 0 50.812 43.674">
                                <g id="noun-coupon-1047062" transform="translate(0 18.142) rotate(-25)">
                                    <path id="Path_3341" data-name="Path 3341"
                                          d="M8.049,13.415H18.781v1.341H8.049Zm0,4.025H16.1V16.1H8.049Zm0-5.366H18.781V10.732H8.049Zm19.787,1.409h.671V12.275h-.671Zm0,7.445h.671V19.72h-.671Zm0-12.409h.671V7.312h-.671Zm0,2.482h.671V9.793h-.671Zm0,7.445h.671V17.238h-.671Zm0-2.482h.671V14.757h-.671ZM42.928,4.025V24.147A4.037,4.037,0,0,1,38.9,28.172H32.2a1.345,1.345,0,0,1-1.341-1.341,2.683,2.683,0,0,0-5.365,0,1.345,1.345,0,0,1-1.341,1.341H4.025A4.037,4.037,0,0,1,0,24.147V4.025A4.037,4.037,0,0,1,4.025,0H24.147a1.345,1.345,0,0,1,1.341,1.341,2.683,2.683,0,1,0,5.365,0A1.345,1.345,0,0,1,32.2,0H38.9a4.037,4.037,0,0,1,4.025,4.025ZM40.915,24.147V4.025A1.977,1.977,0,0,0,38.9,2.012H32.8c-.858,5.493-8.6,5.267-9.257,0H4.025A1.977,1.977,0,0,0,2.013,4.025V24.147a1.977,1.977,0,0,0,2.012,2.012H23.544c.858-5.493,8.6-5.267,9.257,0h6.1a1.976,1.976,0,0,0,2.012-2.012Z"
                                          transform="translate(0)" fill="#fff"/>
                                </g>
                            </svg>
                        </div>
                    </div>
                    <p class="p-0 text-lg font-bold text-center lg:text-left  text-gray-900 box-border whitespace-no-wrap mb-5">
                        Geçerli kupon ya da kredin bulunmamaktadır.</p>
                    <p class="p-0 text-base text-center text-kbgray box-border whitespace-no-wrap mb-5">Kupon indirimi,
                        yalnızca sepette en az bir ürün bulunduğunda görünür olacaktır.</p>
                    <form
                        class="w-full lg:w-5/12 ts:w-full flex items-center justify-center md:justify-between border-b-2 border-black pb-1"
                        action="#">
                        <input
                            class="max-w-[170px] rounded-md border-none placeholder:text-xs placeholder:text-placeholdergray focus:border-none focus:outline-none focus:ring-white text-lg tracking-wider"
                            required="" type="text" placeholder="Kupon kodu gir." name="couponCode" autofocus="">
                        <Link href="/kuponlar-dolu"
                              class="flex justify-center items-center bg-black text-white rounded-full py-2 px-2 lg:px-4 self-center text-base font-bold min-w-[120px]">
                            Kupon Ekle
                        </Link>
                    </form>
                </div>
            </div>
        </section>
    </main>
</template>
