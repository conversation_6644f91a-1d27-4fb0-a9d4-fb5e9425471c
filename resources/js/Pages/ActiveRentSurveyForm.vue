<template>
    <Head title="Aktif <PERSON> Anketi" />

    <div class="min-h-screen bg-gray-50 py-8">
        <div class="max-w-4xl mx-auto px-4">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-6">Aktif <PERSON>a Deneyim Anketi</h1>

                <div class="bg-white p-6 rounded-lg shadow-sm mb-8 text-left">
                    <p class="text-black font-santralregular text-sm lg:text-base leading-relaxed">
                        Mer<PERSON><PERSON>,<br /><br />
                        <PERSON><PERSON><PERSON>ğ<PERSON>n ürün şu anda sende ve umarız her şey yolunda gidiyordur. Bu süreçte destek olabileceğimiz herhangi bir konu varsa bizim için çok önemli. Kısa bir anketle bize geri veriler sen veriniz.
                        <br /><br />
                        Cevapların sadece 1 dakikanı alacak ama senin deneyimini iyileştirmemiz için büyük fark yaratacak.
                    </p>
                </div>
            </div>

            <!-- Hash Status Error Modal -->
            <TransitionRoot as="template" :show="hashStatus !== 'valid'">
                <Dialog as="div" class="fixed z-50 inset-0 overflow-y-auto" @close="goToHomepage">
                    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                        <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
                            <DialogOverlay class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                        </TransitionChild>

                        <!-- This element is to trick the browser into centering the modal contents. -->
                        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                        <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" enter-to="opacity-100 translate-y-0 sm:scale-100" leave="ease-in duration-200" leave-from="opacity-100 translate-y-0 sm:scale-100" leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                            <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                                <div class="sm:flex sm:items-start">
                                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                    </div>
                                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                        <DialogTitle as="h3" class="text-lg leading-6 font-medium text-gray-900">
                                            {{ getModalTitle() }}
                                        </DialogTitle>
                                        <div class="mt-2">
                                            <p class="text-sm text-gray-500">
                                                {{ errorMessage || getDefaultErrorMessage() }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-5 sm:mt-4 sm:flex sm:justify-end">
                                    <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm" @click="goToHomepage">
                                        Anasayfaya Git
                                    </button>
                                </div>
                            </div>
                        </TransitionChild>
                    </div>
                </Dialog>
            </TransitionRoot>

            <!-- API Error - Gömülü UX -->
            <div v-if="hasAPIError" class="bg-white rounded-lg shadow-lg p-6 md:p-8 mb-8">
                <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-medium text-red-800">Anket Yüklenemedi</h3>
                            <div class="mt-2 text-sm text-red-700">
                                <p>{{ errorMessage || "Anket soruları yüklenirken bir hata oluştu. Lütfen sayfayı yenilemeyi deneyin." }}</p>
                            </div>
                            <div class="mt-4">
                                <button type="button" @click="window.location.reload()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium mr-3">Sayfayı Yenile</button>
                                <button type="button" @click="goToHomepage" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">Anasayfaya Dön</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form (only show if hash is valid and questions loaded) -->
            <div v-if="hashStatus === 'valid' && hasValidQuestions" class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                <form @submit.prevent="submitSurvey">
                    <!-- Success Message -->
                    <TransitionRoot as="template" :show="showSuccessMessage">
                        <Dialog as="div" class="fixed z-10 inset-0 overflow-y-auto" @close="closeSuccessMessage">
                            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                                <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
                                    <DialogOverlay class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                                </TransitionChild>

                                <!-- This element is to trick the browser into centering the modal contents. -->
                                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                                <TransitionChild
                                    as="template"
                                    enter="ease-out duration-300"
                                    enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                                    enter-to="opacity-100 translate-y-0 sm:scale-100"
                                    leave="ease-in duration-200"
                                    leave-from="opacity-100 translate-y-0 sm:scale-100"
                                    leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                                >
                                    <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                                        <div class="sm:flex sm:items-start">
                                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                                                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            </div>
                                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                                <DialogTitle as="h3" class="text-lg leading-6 font-medium text-gray-900">Teşekkürler!</DialogTitle>
                                                <div class="mt-2">
                                                    <p class="text-sm text-gray-500">Anket yanıtlarınız başarıyla gönderildi. Geri bildiriminiz bizim için çok değerli.</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-5 sm:mt-4 sm:flex sm:justify-end">
                                            <button
                                                type="button"
                                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                                                @click="closeSuccessMessage"
                                            >
                                                Kapat
                                            </button>
                                        </div>
                                    </div>
                                </TransitionChild>
                            </div>
                        </Dialog>
                    </TransitionRoot>

                    <!-- Error Modal -->
                    <TransitionRoot as="template" :show="showErrorMessage">
                        <Dialog as="div" class="fixed z-10 inset-0 overflow-y-auto" @close="closeErrorMessage">
                            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                                <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
                                    <DialogOverlay class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
                                </TransitionChild>

                                <!-- This element is to trick the browser into centering the modal contents. -->
                                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                                <TransitionChild
                                    as="template"
                                    enter="ease-out duration-300"
                                    enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                                    enter-to="opacity-100 translate-y-0 sm:scale-100"
                                    leave="ease-in duration-200"
                                    leave-from="opacity-100 translate-y-0 sm:scale-100"
                                    leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                                >
                                    <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                                        <div class="sm:flex sm:items-start">
                                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </div>
                                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                                <DialogTitle as="h3" class="text-lg leading-6 font-medium text-gray-900">Hata Oluştu</DialogTitle>
                                                <div class="mt-2">
                                                    <p class="text-sm text-gray-500">{{ errorMessage }}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-5 sm:mt-4 sm:flex sm:justify-end">
                                            <button
                                                type="button"
                                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                                                @click="closeErrorMessage"
                                            >
                                                Tamam
                                            </button>
                                        </div>
                                    </div>
                                </TransitionChild>
                            </div>
                        </Dialog>
                    </TransitionRoot>

                    <!-- Global Error Message -->
                    <div v-if="Object.entries(errors).length > 0" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Lütfen gerekli alanları doldurunuz</h3>
                            </div>
                        </div>
                    </div>

                    <!-- Dinamik Form Alanları - API'den gelen sorular -->
                    <div v-for="(question, questionKey) in questionData" :key="questionKey" class="mb-8">
                        <!-- Question Title -->
                        <h3 class="text-lg font-medium mb-4" :class="errors[questionKey] && touched[questionKey] ? 'text-red-600' : 'text-gray-900'">
                            {{ question.title }}
                            <span v-if="isFieldRequired(questionKey)" class="text-red-500">*</span>
                        </h3>

                        <!-- Radio Button Questions -->
                        <div v-if="question.type === 'radio'" class="space-y-3">
                            <label v-for="option in question.options" :key="option.value" class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors" :class="{ 'bg-blue-50 border-blue-500': form[questionKey] === option.value }">
                                <input type="radio" :value="option.value" v-model="form[questionKey]" :name="questionKey" class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500" @change="handleInputChange(questionKey)" />
                                <span class="ml-3 text-gray-700">{{ option.label }}</span>
                            </label>
                        </div>

                        <!-- Emoji Rating Questions -->
                        <div v-if="question.type === 'emoji'" class="flex flex-wrap gap-3 justify-center lg:justify-start">
                            <label
                                v-for="option in question.options"
                                :key="option.value"
                                class="relative flex flex-col items-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200"
                                :class="form[questionKey] === option.value ? 'border-blue-500 bg-blue-50 scale-105' : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'"
                                tabindex="0"
                            >
                                <input type="radio" :value="option.value" v-model="form[questionKey]" :name="questionKey" class="sr-only" @change="handleInputChange(questionKey)" />
                                <span class="text-3xl mb-2">{{ option.icon }}</span>
                                <span class="text-sm font-medium text-gray-700 text-center">{{ option.label }}</span>
                            </label>
                        </div>

                        <!-- Score Rating Questions -->
                        <div v-if="question.type === 'score'" class="flex flex-wrap gap-2 justify-center lg:justify-start">
                            <button
                                v-for="score in Array.from({ length: question.max - question.min + 1 }, (_, i) => question.min + i)"
                                :key="score"
                                type="button"
                                @click="
                                    form[questionKey] = score;
                                    handleInputChange(questionKey);
                                "
                                class="w-12 h-12 rounded-full border-2 font-semibold text-white transition-all duration-200 hover:scale-110"
                                :class="getScoreButtonClass(score, form[questionKey])"
                            >
                                {{ score }}
                            </button>
                        </div>

                        <!-- Textarea Questions -->
                        <div v-if="question.type === 'textarea'">
                            <textarea
                                v-model="form[questionKey]"
                                rows="4"
                                class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                                :class="errors[questionKey] && touched[questionKey] ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'"
                                placeholder="Görüşlerinizi paylaşabilirsiniz..."
                                @blur="handleInputBlur(questionKey)"
                                @input="handleInputChange(questionKey)"
                            >
                            </textarea>
                        </div>

                        <!-- Checkbox Questions -->
                        <div v-if="question.type === 'checkbox'">
                            <label class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors" :class="{ 'bg-blue-50 border-blue-500': form[questionKey] }">
                                <input type="checkbox" v-model="form[questionKey]" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" @change="handleInputChange(questionKey)" />
                                <span class="ml-3 text-gray-700">Evet, kampanyalardan haberdar olmak istiyorum</span>
                            </label>
                        </div>

                        <!-- Field Error Message -->
                        <div v-if="errors[questionKey] && touched[questionKey]" class="text-red-500 text-sm mt-2">
                            {{ errors[questionKey] }}
                        </div>

                        <!-- Detail Field (Conditional) -->
                        <div v-if="hasDetailField(questionKey) && isFieldRequired(`${questionKey}Details`)" class="mt-4">
                            <label class="block text-sm font-medium mb-2" :class="errors[`${questionKey}Details`] && touched[`${questionKey}Details`] ? 'text-red-600' : 'text-gray-700'"> Detay verir misiniz? <span class="text-red-500">*</span> </label>
                            <textarea
                                v-model="form[`${questionKey}Details`]"
                                rows="3"
                                class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                                :class="errors[`${questionKey}Details`] && touched[`${questionKey}Details`] ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'"
                                placeholder="Detayları paylaşabilirsiniz..."
                                @blur="handleInputBlur(`${questionKey}Details`)"
                                @input="handleInputChange(`${questionKey}Details`)"
                            >
                            </textarea>
                            <div v-if="errors[`${questionKey}Details`] && touched[`${questionKey}Details`]" class="text-red-500 text-sm mt-1">
                                {{ errors[`${questionKey}Details`] }}
                            </div>
                        </div>

                        <!-- Other Field (Conditional) -->
                        <div v-if="hasOtherField(questionKey) && isFieldRequired(`${questionKey}Other`)" class="mt-4">
                            <label class="block text-sm font-medium mb-2" :class="errors[`${questionKey}Other`] && touched[`${questionKey}Other`] ? 'text-red-600' : 'text-gray-700'"> Lütfen belirtiniz <span class="text-red-500">*</span> </label>
                            <input
                                type="text"
                                v-model="form[`${questionKey}Other`]"
                                class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                :class="errors[`${questionKey}Other`] && touched[`${questionKey}Other`] ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'"
                                placeholder="Lütfen belirtiniz..."
                                @blur="handleInputBlur(`${questionKey}Other`)"
                                @input="handleInputChange(`${questionKey}Other`)"
                            />
                            <div v-if="errors[`${questionKey}Other`] && touched[`${questionKey}Other`]" class="text-red-500 text-sm mt-1">
                                {{ errors[`${questionKey}Other`] }}
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" :disabled="form.processing" :class="form.processing ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'" class="px-8 py-3 text-white font-medium rounded-lg transition-colors duration-200 min-w-[200px]">
                            <span v-if="form.processing" class="flex items-center justify-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Gönderiliyor...
                            </span>
                            <span v-else>Anketi Gönder</span>
                        </button>
                    </div>

                    <!-- Teşekkür Mesajı -->
                    <div class="text-center mt-8">
                        <p class="text-gray-600 text-sm leading-relaxed">
                            Her zaman buradayız,<br />
                            <span class="font-medium">Kiralabunu Ekibi 💚 / Kiralamini Ekibi 🧡</span>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
import { Head } from "@inertiajs/inertia-vue3";
import { vMaska } from "maska";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import { Dialog, DialogPanel, DialogTitle, DialogOverlay, TransitionChild, TransitionRoot } from "@headlessui/vue";
import { useForm } from "@inertiajs/inertia-vue3";

export default {
    name: "ActiveRentSurveyForm",
    components: {
        Head,
        Dialog,
        DialogPanel,
        DialogTitle,
        DialogOverlay,
        TransitionChild,
        TransitionRoot,
    },
    layout: Layout,

    directives: {
        maska: vMaska,
    },

    props: {
        hash: {
            type: String,
            default: null,
        },
        surveyData: {
            type: Object,
            default: () => ({}),
        },
        hashStatus: {
            type: String,
            default: "valid", // valid | completed | invalid | error
        },
        errorMessage: {
            type: String,
            default: "",
        },
    },

    setup(props) {
        // Dinamik form initialization
        const initFormData = () => {
            const baseForm = { hash: props.hash };

            // API'den gelen sorular varsa form field'larını oluştur
            if (props.surveyData?.questions) {
                Object.keys(props.surveyData.questions).forEach((questionKey) => {
                    const question = props.surveyData.questions[questionKey];

                    // Ana field
                    if (question.type === "checkbox") {
                        baseForm[questionKey] = false;
                    } else if (question.type === "emoji" || question.type === "score") {
                        baseForm[questionKey] = null;
                    } else {
                        baseForm[questionKey] = "";
                    }

                    // Detail field (conditional fields için)
                    baseForm[`${questionKey}Details`] = "";

                    // Other field (Diğer seçeneği için)
                    baseForm[`${questionKey}Other`] = "";
                });
            }

            return baseForm;
        };

        const form = useForm(initFormData());

        return {
            form,
        };
    },
    data() {
        return {
            showSuccessMessage: false,
            showErrorMessage: false,
            errorMessage: "",
            successMessage: "",
            errors: {},
            touched: {},
        };
    },

    computed: {
        questionData() {
            // Tamamen API'den gelen soru yapısını kullan
            return this.surveyData?.questions || {};
        },

        // Dinamik form field'ları - API'den gelen sorulara göre
        formFields() {
            const fields = {};
            Object.keys(this.questionData).forEach((questionKey) => {
                fields[questionKey] = "";

                // Detail field'ları için de default değer
                if (this.hasDetailField(questionKey)) {
                    fields[`${questionKey}Details`] = "";
                }

                // Other field'ları için de default değer
                if (this.hasOtherField(questionKey)) {
                    fields[`${questionKey}Other`] = "";
                }
            });
            return fields;
        },

        // API verisi mevcut mu kontrolü
        hasValidQuestions() {
            return Object.keys(this.questionData).length > 0;
        },

        // Error durumu kontrolü
        hasAPIError() {
            return this.hashStatus === "error" || (!this.hasValidQuestions && this.hashStatus === "valid");
        },
    },

    mounted() {
        // Initialize touched state for all form fields
        this.initializeTouchedState();

        // Check for flash messages on component mount
        const flashSuccess = this.$page.props.flash?.success;
        const flashError = this.$page.props.flash?.error;

        if (flashSuccess) {
            this.successMessage = flashSuccess;
            this.showSuccessMessage = true;
        }

        if (flashError) {
            this.showError(flashError);
        }
    },

    methods: {
        // Initialize touched state for all form fields
        initializeTouchedState() {
            this.touched = {};
            Object.keys(this.formFields).forEach((field) => {
                this.touched[field] = false;
            });
        },

        // Check if a question requires detail field
        hasDetailField(questionKey) {
            const question = this.questionData[questionKey];
            if (!question || !question.options) return false;

            // Bu sorular için detay alanı gerekebilir
            return question.options.some((option) => option.value === "Memnun değilim" || option.value === "Evet, hala çözülmedi");
        },

        // Check if a question has "Other" option
        hasOtherField(questionKey) {
            const question = this.questionData[questionKey];
            if (!question || !question.options) return false;

            return question.options.some((option) => option.value === "Diğer");
        },

        // Get question by key
        getQuestion(questionKey) {
            return this.questionData[questionKey] || {};
        },

        // Check if field should be required based on conditions
        isFieldRequired(questionKey) {
            const question = this.getQuestion(questionKey);

            // Detay alanları için koşullu zorunluluk
            if (questionKey.endsWith("Details")) {
                const baseKey = questionKey.replace("Details", "");
                const baseValue = this.form[baseKey];

                return baseValue === "Memnun değilim" || baseValue === "Evet, hala çözülmedi";
            }

            // Other alanları için koşullu zorunluluk
            if (questionKey.endsWith("Other")) {
                const baseKey = questionKey.replace("Other", "");
                const baseValue = this.form[baseKey];

                return baseValue === "Diğer";
            }

            // Ana alanlar için zorunluluk (checkbox hariç)
            return question.type !== "checkbox" && question.type !== "textarea";
        },

        validateForm() {
            const newErrors = {};

            // Dinamik validasyon - API'den gelen her soru için
            Object.keys(this.questionData).forEach((questionKey) => {
                const question = this.getQuestion(questionKey);
                const fieldValue = this.form[questionKey];

                // Ana alan validasyonu
                if (this.isFieldRequired(questionKey)) {
                    if (question.type === "emoji" || question.type === "score") {
                        if (fieldValue === null || fieldValue === "") {
                            newErrors[questionKey] = `${question.title} alanı zorunludur`;
                        }
                    } else {
                        if (!fieldValue || (typeof fieldValue === "string" && !fieldValue.trim())) {
                            newErrors[questionKey] = `${question.title} alanı zorunludur`;
                        }
                    }
                }

                // Detail field validasyonu
                const detailKey = `${questionKey}Details`;
                if (this.form.hasOwnProperty(detailKey) && this.isFieldRequired(detailKey)) {
                    if (!this.form[detailKey] || !this.form[detailKey].trim()) {
                        newErrors[detailKey] = "Bu alanı doldurmanız gereklidir";
                    }
                }

                // Other field validasyonu
                const otherKey = `${questionKey}Other`;
                if (this.form.hasOwnProperty(otherKey) && this.isFieldRequired(otherKey)) {
                    if (!this.form[otherKey] || !this.form[otherKey].trim()) {
                        newErrors[otherKey] = "Lütfen açıklama yapınız";
                    }
                }
            });

            // Clear old errors and set new ones
            this.errors = newErrors;

            return Object.keys(newErrors).length === 0;
        },
        handleInputBlur(field) {
            this.touched[field] = true;
            this.validateForm();
        },
        handleInputChange(field) {
            if (this.touched[field]) {
                this.validateForm();
            }
        },
        submitSurvey() {
            // DEBUG: hash kontrolü
            console.log("submitSurvey() hash:", this.form.hash);
            // Hash geçerli değilse form submit etme
            if (this.hashStatus !== "valid") {
                this.showError("Bu anket doldurulabilir durumda değil.");
                return;
            }

            // Mark all fields as touched
            Object.keys(this.touched).forEach((key) => {
                this.touched[key] = true;
            });

            if (!this.validateForm()) {
                // Focus on first error field
                this.focusFirstErrorField();
                return;
            }

            if (!this.form.hash) {
                this.showError("Anket hash değeri bulunamadı. Lütfen geçerli bir anket linki kullanın.");
                return;
            }

            const submitUrl = `/aktif-kiralama-anketi/${this.form.hash}`;
            console.log(submitUrl);
            this.form.post(submitUrl, {
                preserveScroll: true,
                onSuccess: (page) => {
                    // Check for success flash message
                    const successMsg = page.props.flash?.success;

                    if (successMsg) {
                        this.successMessage = successMsg;
                        this.showSuccessMessage = true;
                        this.form.reset();
                        this.resetForm();
                    }
                },
                onError: (errors) => {
                    // Handle validation errors from backend
                    if (errors) {
                        this.errors = {};
                        Object.keys(errors).forEach((key) => {
                            this.errors[key] = Array.isArray(errors[key]) ? errors[key][0] : errors[key];
                        });

                        // Focus on first error field
                        this.$nextTick(() => {
                            this.focusFirstErrorField();
                        });
                    }
                },
                onFinish: () => {
                    // Check for flash messages in different locations
                    this.$nextTick(() => {
                        const flashError = this.$page.props.flash?.error;
                        const flashSuccess = this.$page.props.flash?.success;
                        const directSuccess = this.$page.props.success;

                        // Handle success message (can be in flash.success or direct success prop)
                        const successMsg = flashSuccess || (directSuccess?.success ? directSuccess.success : directSuccess);

                        if (flashError && !this.showErrorMessage) {
                            this.showError(flashError);
                        } else if (successMsg && !this.showSuccessMessage) {
                            this.successMessage = successMsg;
                            this.showSuccessMessage = true;
                            this.form.reset();
                            this.resetForm();
                        }
                    });
                },
            });
        },

        resetForm() {
            // Reset touched state
            this.touched = {
                rentalExperience: false,
                rentalExperienceDetails: false,
                technicalIssues: false,
                technicalIssuesDetails: false,
                supportNeeded: false,
                communicationRating: false,
                howDidYouHear: false,
                howDidYouHearOther: false,
                additionalComments: false,
                wantsSpecialOffers: false,
            };

            // Reset errors
            this.errors = {};
        },
        closeSuccessMessage() {
            this.showSuccessMessage = false;
            this.successMessage = "";
        },

        closeErrorMessage() {
            this.showErrorMessage = false;
            this.errorMessage = "";
        },

        showError(message) {
            this.errorMessage = message;
            this.showErrorMessage = true;
        },
        getScoreButtonClass(score, currentScore) {
            if (score === currentScore) {
                return "bg-blue-500 border-blue-500";
            }
            if (score <= 3) {
                return "bg-red-500 hover:bg-red-600";
            } else if (score <= 6) {
                return "bg-yellow-500 hover:bg-yellow-600";
            } else {
                return "bg-green-500 hover:bg-green-600";
            }
        },
        focusFirstErrorField() {
            console.log("focusFirstErrorField called, errors:", this.errors);

            // Dinamik field order - API'den gelen soru sırasına göre
            const questionKeys = Object.keys(this.questionData);
            const allFieldNames = [];

            // Önce ana question field'ları ekle
            questionKeys.forEach((questionKey) => {
                allFieldNames.push(questionKey);
                allFieldNames.push(`${questionKey}Details`);
                allFieldNames.push(`${questionKey}Other`);
            });

            for (const fieldName of allFieldNames) {
                if (this.errors[fieldName]) {
                    console.log(`Found error for field: ${fieldName}`, this.errors[fieldName]);

                    this.$nextTick(() => {
                        let element = null;

                        // Dinamik element bulma
                        if (fieldName.endsWith("Details")) {
                            element = document.querySelector(`textarea[v-model*="${fieldName}"]`) || document.querySelector(`textarea[placeholder*="etay"]`);
                        } else if (fieldName.endsWith("Other")) {
                            element = document.querySelector(`input[v-model*="${fieldName}"]`) || document.querySelector(`input[placeholder*="belirt"]`);
                        } else {
                            // Ana field için question type'a göre element bul
                            const question = this.getQuestion(fieldName);

                            if (question.type === "radio") {
                                element = document.querySelector(`input[name="${fieldName}"]:first-of-type`);
                            } else if (question.type === "emoji" || question.type === "score") {
                                element = document.querySelector(`label[tabindex="0"]`);
                            } else if (question.type === "textarea") {
                                element = document.querySelector(`textarea[v-model*="${fieldName}"]`);
                            } else if (question.type === "checkbox") {
                                element = document.querySelector(`input[type="checkbox"][v-model*="${fieldName}"]`);
                            }
                        }

                        console.log(`Element found for ${fieldName}:`, element);

                        if (element) {
                            // Scroll to element first
                            element.scrollIntoView({
                                behavior: "smooth",
                                block: "center",
                                inline: "nearest",
                            });

                            // Focus after scroll animation completes
                            setTimeout(() => {
                                const question = this.getQuestion(fieldName.replace("Details", "").replace("Other", ""));

                                // For emoji/score ratings, focus the label directly
                                if (question?.type === "emoji" || question?.type === "score" || (element.type === "radio" && element.classList.contains("sr-only"))) {
                                    const parentLabel = element.closest("label") || element;
                                    if (parentLabel && parentLabel.focus) {
                                        parentLabel.focus();
                                        console.log(`Focused on label for ${fieldName}`);
                                    }
                                } else {
                                    element.focus();
                                    console.log(`Focused on element for ${fieldName}`);
                                }
                            }, 500);
                        } else {
                            console.log(`No element found for field: ${fieldName}`);
                        }
                    });
                    break; // Focus on first error only
                }
            }
        },
        getModalTitle() {
            switch (this.hashStatus) {
                case "completed":
                    return "Anket Daha Önce Doldurulmuş";
                case "invalid":
                    return "Geçersiz Anket Bağlantısı";
                case "error":
                    return "Bir Hata Oluştu";
                default:
                    return "Anket Erişim Hatası";
            }
        },
        getDefaultErrorMessage() {
            switch (this.hashStatus) {
                case "completed":
                    return "Bu anket daha önce doldurulmuş. Aynı anket sadece bir kez doldurulabilir.";
                case "invalid":
                    return "Anket bağlantısı geçersiz veya bulunamadı.";
                case "error":
                    return "Anket yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.";
                default:
                    return "Ankete erişimde bir sorun oluştu.";
            }
        },
        goToHomepage() {
            window.location.href = "/";
        },
    },
};
</script>

<style scoped>
/* Custom animations */
@keyframes slide-down {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.animate-slide-down {
    animation: slide-down 0.3s ease-out;
}

.animate-fade-in {
    animation: fade-in 0.3s ease-out;
}

/* Improve hover effects */
.hover\:scale-105:hover {
    transform: scale(1.05);
}

.active\:scale-95:active {
    transform: scale(0.95);
}

/* Focus styles for better accessibility */
input:focus,
select:focus,
textarea:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Custom checkbox and radio styles */
input[type="checkbox"]:checked,
input[type="radio"]:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .grid-cols-2 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .grid-cols-3 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .grid-cols-4 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}
</style>
