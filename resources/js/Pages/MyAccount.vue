<script>
import { Head, <PERSON>, router } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";

export default {
    components: {
        <PERSON>,
        Head,
        router
    },
    props: {
        errors: { type: Object, default: false },
        intended: { type: String, default: false }
    },
    data() {
        return {
            form: this.$inertia.form({
                email: null,
                password: null,
                remember: false,
                passwordFieldType: "password",
                intended: this.intended
            })
        };
    },
    methods: {
        submit() {
            this.form.post("/giris-yap");
        },
        switchVisibility() {
            this.form.passwordFieldType = this.form.passwordFieldType === "password" ? "text" : "password";
        }
    },
    layout: Layout
};
</script>

<template>

    <Head title="Giriş Yap" />

    <main class="my-6">
        <section class="mt-6">
            <form class="flex mx-auto flex-col max-w-[350px] md:max-w-lg py-8 rounded-2lg bg-white shadow-searchshadow" @submit.prevent="submit">
                <img src="../../images/logo.png" class="hidden mx-auto w-60 mb-10" alt="Logo" />
                <div class="font-bold text-xl self-center">Giriş Yap</div>
                <div class="self-center mt-6 relative">
                    <input id="email" type="email" v-model="form.email" placeholder="E-posta adresi*" autocomplete="off" autofill="off"
                        class="peer placeholder-transparent border-2 rounded-lg border-kb-light-grey w-80 md:w-96 hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" required />
                    <!--
                                        <label for="text" class="transform transition-all absolute top-3 group-focus-within:top-0 peer-placeholder-shown:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-placeholder-shown:text-2xs group-focus-within:-translate-y-full peer-placeholder-shown:-translate-y-full group-focus-within:pl-0 peer-placeholder-shown:pl-0 text-placeholdergray group-focus-within:text-black peer-placeholder-shown:text-black">E-posta adresi*</label>
                    -->
                    <label for="email"
                        class="absolute left-4 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">E-posta
                        adresi*</label>
                </div>
                <div class="self-center mt-5 group relative">
                    <input id="password" :type="form.passwordFieldType" v-model="form.password" placeholder="Şifre*" autocomplete="off"
                        class="peer placeholder-transparent border-2 rounded-lg border-kb-light-grey w-80 md:w-96 hover:border-acordion-green focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" required />
                    <!--                    <label
                                            for="password"
                                            class="transform transition-all absolute top-3 group-focus-within:top-0 peer-placeholder-shown:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-placeholder-shown:text-2xs group-focus-within:-translate-y-full peer-placeholder-shown:-translate-y-full group-focus-within:pl-0 peer-placeholder-shown:pl-0 text-placeholdergray group-focus-within:text-black peer-placeholder-shown:text-black"
                                            >Şifre*</label
                                        >-->
                    <label for="password"
                        class="absolute left-4 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">Şifre*</label>
                    <div @click="switchVisibility" class="absolute mt-3 top-0 right-5">
                        <svg id="eye-slash" xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 17.749 15">
                            <path id="Path_2948" data-name="Path 2948"
                                d="M20.762,15.939a16.033,16.033,0,0,0,2.93-3.822s-3.328-6.492-8.874-6.492a7.4,7.4,0,0,0-3.095.694l.854.91a6.24,6.24,0,0,1,2.241-.424,8.088,8.088,0,0,1,5.733,2.9,15.359,15.359,0,0,1,1.841,2.412c-.065.1-.135.216-.216.34a15.4,15.4,0,0,1-1.625,2.072c-.183.195-.374.387-.573.573Z"
                                transform="translate(-5.943 -4.617)" fill="#231f20" />
                            <path id="Path_2949" data-name="Path 2949"
                                d="M17.661,15.641a4.323,4.323,0,0,0-.91-4.312A3.717,3.717,0,0,0,12.7,10.36l.913.972a2.672,2.672,0,0,1,2.354.835,3.069,3.069,0,0,1,.784,2.5l.912.97ZM14.4,17.175l.912.97a3.717,3.717,0,0,1-4.053-.969,4.323,4.323,0,0,1-.91-4.312l.913.972a3.069,3.069,0,0,0,.784,2.5,2.672,2.672,0,0,0,2.354.835Z"
                                transform="translate(-5.13 -6.753)" fill="#231f20" />
                            <path id="Path_2950" data-name="Path 2950"
                                d="M3.716,11.55q-.3.283-.574.575A15.359,15.359,0,0,0,1.3,14.537l.216.34a15.4,15.4,0,0,0,1.625,2.072,8.09,8.09,0,0,0,5.733,2.9,6.219,6.219,0,0,0,2.241-.425l.854.911a7.4,7.4,0,0,1-3.095.694C3.328,21.029,0,14.537,0,14.537a15.978,15.978,0,0,1,2.93-3.822l.785.837Z"
                                transform="translate(0 -7.036)" fill="#231f20" />
                            <path id="Path_2951" data-name="Path 2951" d="M17.015,18.7,3.7,4.539,4.489,3.7,17.8,17.867Z" transform="translate(-1.878 -3.703)" fill="#231f20" fill-rule="evenodd" />
                        </svg>
                    </div>
                </div>
                <div class="flex mt-4 w-80 md:w-96 self-center justify-between">
                    <label for="rememberMe" class="cursor-pointer">
                        <input type="checkbox" id="rememberMe" class="border-2 border-black rounded-md outline-none" />
                        <span class="font-semibold text-xs ml-3">Beni Hatırla</span>
                    </label>
                    <div>
                        <Link href="/sifremi-unuttum" class="text-xs text-kb-light-blue underline">Şifremi Unuttum!</Link>
                    </div>
                </div>
                <div class="flex mt-4 w-80 md:w-96 self-center justify-between" v-if="Object.entries(errors).length > 0">
                    <div class="text-xs mx-auto text-red-900" v-for="error in errors">{{ error[0] }}</div>
                </div>
                <div class="flex mt-4 w-80 md:w-96 self-center">
                    <button class="bg-black text-white rounded-full py-2 px-4 self-center font-bold w-full hover:bg-kbgreen flex items-center justify-center" type="submit" :disabled="form.processing">
                        <span v-if="form.processing" class="h-5 w-5 border-t-transparent border-solid animate-spin rounded-full border-white border-4 mr-2"></span>
                        <span>Giriş Yap</span>
                    </button>
                </div>
                <div class="flex w-80 md:w-96 self-center mt-4 flex-col">
                    <div class="text-xs mx-auto"><b>Kiralabunu.com</b>'a üye değil misin?</div>
                    <div class="mx-auto mt-2 flex mt-4 w-80 md:w-96">
                        <Link href="/hesap-olustur" class="text-white rounded-full py-2 px-4 self-center font-bold w-full bg-kbgreen text-center" type="submit" :disabled="form.processing">Üyelik Oluştur!</Link>
                    </div>
                </div>
            </form>
        </section>
    </main>
</template>
