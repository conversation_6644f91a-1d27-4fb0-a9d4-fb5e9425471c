<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import ProductBox from "@/Pages/Shared/ProductBox.vue";
import CategoryBox from "@/Pages/Shared/CategoryBox.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        ProductBox,
        CategoryBox,
        Splide,
        SplideSlide,
        SplideTrack,
    },
    props: {
        products: Object,
        auth: Object,
        order_count: Number,
        allProducts: Object,
    },
    methods: {
        splidedArray(arr, size) {
            return arr.reduce((acc, e, i) => (i % size ? acc[acc.length - 1].push(e) : acc.push([e]), acc), []);
        },
    },
    computed: {
        splidedAllProducts() {
            return this.splidedArray(this.allProducts.items.data, 1);
        },
    },
    layout: Layout,
};
</script>

<template>

    <Head title="İstek Listem" />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row items-start">
            <user-menu :order_count="order_count" :active="`MyWishlists`" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 lg:px-4 pt-2">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7">İstek Listem</h3>
                </div>
                <div class="flex flex-col justify-center items-center mb-10" v-if="products.length == 0">
                    <div class="flex justify-center items-center bg-kb-light-grey rounded-full w-32 h-32 mb-5">
                        <div class="flex justify-center items-center bg-black rounded-full w-20 h-20">
                            <svg xmlns="http://www.w3.org/2000/svg" width="39.104" height="42.629" viewBox="0 0 39.104 42.629">
                                <g id="noun-list-1322460" transform="translate(-93.065 0)">
                                    <path id="Path_3768" data-name="Path 3768" d="M236.391,19.285h23.228A2.409,2.409,0,0,0,262.2,17.1a2.409,2.409,0,0,0-2.581-2.184H236.391A2.409,2.409,0,0,0,233.81,17.1a2.409,2.409,0,0,0,2.581,2.184Z"
                                        transform="translate(-130.031 -13.782)" fill="#fff" />
                                    <path id="Path_3769" data-name="Path 3769" d="M236,105.352h14.2a2.184,2.184,0,0,0,0-4.368H236a2.184,2.184,0,0,0,0,4.368Z" transform="translate(-130.033 -93.297)" fill="#fff" />
                                    <path id="Path_3770" data-name="Path 3770" d="M259.623,215.74H236.391a2.214,2.214,0,1,0,0,4.368h23.228a2.214,2.214,0,1,0,0-4.368Z" transform="translate(-130.031 -199.318)" fill="#fff" />
                                    <path id="Path_3771" data-name="Path 3771" d="M236,306.172h14.2a2.184,2.184,0,0,0,0-4.368H236a2.184,2.184,0,0,0,0,4.368Z" transform="translate(-130.033 -278.83)" fill="#fff" />
                                    <path id="Path_3772" data-name="Path 3772" d="M259.623,416.56H236.391a2.214,2.214,0,1,0,0,4.368h23.228a2.214,2.214,0,1,0,0-4.368Z" transform="translate(-130.031 -384.85)" fill="#fff" />
                                    <path id="Path_3773" data-name="Path 3773" d="M250.192,502.62H236a2.184,2.184,0,0,0,0,4.368h14.2a2.184,2.184,0,0,0,0-4.368Z" transform="translate(-130.033 -464.359)" fill="#fff" />
                                    <path id="Path_3774" data-name="Path 3774" d="M96.354.982a2.3,2.3,0,1,1-.013,0m0-.983a3.261,3.261,0,1,0,.013,0Z" fill="#fff" />
                                    <path id="Path_3775" data-name="Path 3775" d="M96.354,202.372a2.29,2.29,0,1,1-.013,0m0-.983a3.307,3.307,0,1,0,.013,0Z" transform="translate(0 -186.059)" fill="#fff" />
                                    <path id="Path_3776" data-name="Path 3776" d="M96.354,403.192a2.3,2.3,0,1,1-.013,0m0-.983h0a3.3,3.3,0,1,0,.013,0Z" transform="translate(0 -371.592)" fill="#fff" />
                                </g>
                            </svg>
                        </div>
                    </div>
                    <p class="p-0 ts:text-lg font-bold text-center text-gray-900 box-border whitespace-no-wrap mb-4">
                        Beğendiğin ürünleri istek listene ekle <br />
                        indirime girdiğinde haberin olsun.
                    </p>
                    <Link href="/kategoriler/tum-urunler" class="font-santralextrabold bg-black text-white rounded-full py-2 px-4 self-center text-lg font-bold"> Ürünlere Git</Link>
                </div>
                <div class="flex flex-wrap" v-if="products.length != 0">
                    <template v-for="(p, index) in products" :key="index">
                        <category-box class="!w-full mts:!w-6/12 lg:!w-4/12" :product="p.product" :auth="auth"></category-box>
                    </template>
                </div>
            </div>
        </section>
    </main>
    <section class="mt-6 md:mt-4 py-1 md:py-9 bg-[#f8f8f8]">
        <div class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
            <div class="flex w-full justify-between">
                <div class="font-bold text-2xl md:text-3xl my-2 lg:my-0 mx-0 lg:mx-4 self-center text-center w-full md:w-auto">Son Eklenen Ürünler</div>
                <div class="flex-1 ml-6 hidden md:flex">
                    <div class="flex-1 self-center border border-gray-200"></div>
                    <Link href="/kategoriler/tum-urunler" class="cursor-pointer text-sm font-bold flex justify-center items-center border-2 rounded-full px-4 border-2 whitespace-nowrap md:ml-4 hover:bg-kbgreen hover:text-white"> Tümünü Gör</Link>
                </div>
            </div>
            <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0 lg:mt-9">
                <Splide :has-track="false" aria-label="" :options="{ rewind: true, perPage: 4, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 3 } } }">
                    <SplideTrack>
                        <SplideSlide v-for="(productGroup, index) in splidedAllProducts" :key="index" class="flex">
                            <category-box v-for="(product, index) in productGroup" :product="product" :new-container-classes="`w-full`" />
                        </SplideSlide>
                    </SplideTrack>

                    <div class="splide__arrows">
                        <button class="splide__arrow splide__arrow--prev !-left-9">
                            <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                    transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                        <button class="splide__arrow splide__arrow--next !-right-9">
                            <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                    transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                    </div>
                </Splide>
            </div>
            <div class="flex-1 ml-2 flex md:hidden justify-center">
                <Link href="/kategoriler/tum-urunler" class="cursor-pointer border-3 rounded-full py-1.5 px-2 text-center mb-4 w-32 hover:bg-kbgreen hover:text-white font-bold text-sm"> Tümünü Gör</Link>
            </div>
        </div>
    </section>
</template>
