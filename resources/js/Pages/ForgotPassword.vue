<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import { TransitionRoot, TransitionChild, Dialog, DialogPanel, DialogTitle } from "@headlessui/vue";

export default {
    components: {
        Link,
        Head,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
    },
    layout: Layout,
    data() {
        return {
            isOpen: false,
            passReset: this.$inertia.form({
                mail: null,
            }),
        };
    },
    props: {
        errors: { type: Object, default: false },
    },
    methods: {
        closeModal() {
            console.log(this.isOpen);
            this.isOpen = false;
        },
        openModal() {
            if (this.passReset.mail != null) {
                this.passReset.post(route("resetPassword"));
                //this.isOpen = true;
            }
        },
    },
};
</script>

<template>

    <Head title="Şifremi Unuttum" />
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white px-5 py-5 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 62.051 62.051">
                                <g id="Group_4884" data-name="Group 4884" transform="translate(-73.036 -250.295)">
                                    <circle id="Ellipse_199" data-name="Ellipse 199" cx="31.025" cy="31.025" r="31.025" transform="translate(73.036 250.295)" fill="#70d44b" />
                                    <path id="Path_3299" data-name="Path 3299"
                                        d="M351.649,476.232a3.45,3.45,0,0,1-2.761-1.381l-8.464-11.286a3.451,3.451,0,0,1,5.522-4.141l5.538,7.383,14.223-21.334a3.451,3.451,0,1,1,5.743,3.829l-16.928,25.393a3.453,3.453,0,0,1-2.776,1.536Z"
                                        transform="translate(-251.82 -178.764)" fill="#fff" />
                                </g>
                            </svg>
                            <DialogTitle>
                                <h2 class="text-base font-semibold text-center mb-2 mt-2">Başarılı</h2>
                            </DialogTitle>
                            <p class="text-xs font-kiralabunuthin ts:text-sm text-center">Şifre sıfırlama linki e-posta adresine yönlendirildi.</p>
                            <!--                            <div class="mt-4">-->
                            <!--                                <button-->
                            <!--                                    type="button"-->
                            <!--                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"-->
                            <!--                                    @click="closeModal"-->
                            <!--                                >-->
                            <!--                                    Got it, thanks!-->
                            <!--                                </button>-->
                            <!--                            </div>-->
                            <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal">x</div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>

    <main class="my-6">
        <section class="mt-6">
            <div class="flex mx-auto flex-col max-w-[350px] md:max-w-lg py-8 rounded-2lg bg-white shadow-searchshadow">
                <!--                <img src="../../images/logo.png" class="block lg:hidden mx-auto w-60 mb-10" alt="Logo">-->
                <div class="font-bold text-2xl self-center">Şifremi Unuttum</div>
                <div class="flex w-80 md:w-auto self-center mt-4">
                    <span class="text-base mx-auto text-center lg:text-left">Kayıtlı olduğun e-posta adresi'ni gir.</span>
                </div>
                <div class="self-center mt-10 relative group">
                    <input v-model="passReset.mail" id="id" type="text"
                        class="peer text-lg border-2 rounded-lg border-kb-light-grey w-80 md:w-96 focus:border-acordion-green hover:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" required />
                    <label for="id"
                        class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">E-Posta
                        adresi ya da telefon gir
                    </label>
                </div>
                <div class="flex mt-4 w-80 md:w-96 self-center flex-col" v-if="Object.entries(errors).length > 0">
                    <div class="text-xs mx-auto text-kbred my-1">{{ errors[0] }}</div>
                </div>
                <div class="flex mt-8 w-80 md:w-96 self-center">
                    <Link :href="route('resetPassword')" method="post" class="text-center bg-black text-white lg:text-lg rounded-full py-2 lg:py-3 px-4 self-center font-bold w-full hover:bg-kbgreen hover:text-white" :data="passReset">Gönder</Link>
                    <!--                    <button class="bg-black text-white lg:text-lg rounded-full py-2 lg:py-3 px-4 self-center font-bold w-full hover:bg-kbgreen hover:text-white" @click="openModal">Gönder</button>-->
                </div>
                <div class="flex w-80 md:w-96 self-center mt-8 flex-col">
                    <div class="text-xs lg:text-base mx-auto"><b>Kiralabunu.com</b>'a üye değil misin?</div>
                    <div class="mx-auto lg:mt-1">
                        <Link href="/hesap-olustur" class="underline pb-1 font-semibold text-md text-kbgreen">Üyelik Oluştur!</Link>
                    </div>
                </div>
            </div>
        </section>
    </main>
</template>
