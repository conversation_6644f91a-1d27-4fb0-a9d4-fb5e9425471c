<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";

export default {
    components: {
        <PERSON>,
        Head
    },
    data() {
        return {};
    },
    methods: {
        toogleBlock(block) {
            block.showBlock = !block.showBlock;
        }
    },
    layout: Layout
};
</script>

<template>

    <Head title="Çerez (Cookie) Politikası" />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <!--        <section class="flex justify-center items-center flex-col w-full mb-12">-->
        <!--            <div class="flex justify-center items-center mb-10">-->
        <!--                <div class="flex justify-center items-center bg-kbgreen rounded-full w-28 h-28">-->
        <!--                    <svg xmlns="http://www.w3.org/2000/svg" width="43.015" height="78.955" viewBox="0 0 43.015 78.955">-->
        <!--                        <g id="Group_4141" data-name="Group 4141" transform="translate(0 0)">-->
        <!--                            <path-->
        <!--                                id="Path_3349"-->
        <!--                                data-name="Path 3349"-->
        <!--                                d="M473.847,415.2H472.2a5.587,5.587,0,0,1-5.581-5.581v-1.387a17.076,17.076,0,0,1,1.934-8.548,44.963,44.963,0,0,1,5.417-7.278,32.075,32.075,0,0,0,3.683-4.439,7.467,7.467,0,0,0,1.163-4.13c0-3.445-2.245-5.121-6.863-5.121a27.868,27.868,0,0,0-7.564,1.135,6.368,6.368,0,0,1-8.116-6.123v-2.807a6.246,6.246,0,0,1,3.685-5.725,36.563,36.563,0,0,1,27-.235,21.4,21.4,0,0,1,9.074,6.877,16.563,16.563,0,0,1,3.255,10.039,16.327,16.327,0,0,1-3.147,10.04,57.393,57.393,0,0,1-8.8,8.966,62.707,62.707,0,0,0-5.642,5.315,9.811,9.811,0,0,0-2.346,4.367,5.566,5.566,0,0,1-5.5,4.636Z"-->
        <!--                                transform="translate(-456.268 -362.491)"-->
        <!--                                fill="#fff"-->
        <!--                            />-->
        <!--                            <path id="Path_3350" data-name="Path 3350"-->
        <!--                                  d="M480.951,518.1h-.218a10.344,10.344,0,0,1,0-20.688h.218a10.344,10.344,0,0,1,0,20.688Z"-->
        <!--                                  transform="translate(-464.291 -439.141)" fill="#fff" />-->
        <!--                        </g>-->
        <!--                    </svg>-->
        <!--                </div>-->
        <!--            </div>-->
        <!--            <button class="bg-black text-white rounded-full py-2 px-4 self-center text-2xl">Sıkça Sorulan Sorular-->
        <!--            </button>-->
        <!--        </section>-->
        <section class="mt-6 flex flex-col items-center justify-center mb-20">
            <div class="text-2xl font-semibold text-center mb-7">Çerez (Cookie) Politikası
            </div>
            <div class="flex justify-between w-full lg:w-full py-4 rounded-xl mb-4">
                <div>
                    <p class="text-sm text-justify">
                        Çerez politikamız, Kiralabunu Elektronik Pazarlama ve Ticaret Anonim Şirketi’nin (“KiralaBunu”)
                        içerik sağlayıcı olduğu <a href="https://kiralabunu.com">www.kiralabunu.com </a> internet sitesi
                        için geçerlidir.
                        <br>
                        <br>
                        Çerezler bir web sitesini ziyaret ettiğinizde cihazınıza (örneğin; bilgisayar veya cep telefonu)
                        depolanan küçük bir metin dosyalarıdır. Bir web sitesini ilk ziyaretiniz sırasında tarayıcınız
                        aracılığıyla cihazınıza çerezler depolanabilirler. Aynı siteyi aynı cihazla tekrar ziyaret
                        ettiğinizde tarayıcınız cihazınızda site adına kayıtlı bir çerez olup olmadığını kontrol eder.
                        Eğer kayıt var ise, kaydın içindeki veriyi ziyaret etmekte olduğunuz web sitesine iletir. Bu
                        sayede web sitesi, sizin siteyi daha önce ziyaret ettiğinizi anlar ve size iletilecek içeriği de
                        ona göre tayin eder. <br>
                        <br>
                        <span class="font-bold">Çerezler Neden Kullanılır?</span>
                        <br>
                        <br>
                        Çerezler, internet sitelerimizi kullanmamız sırasında size kişiselleştirilmiş bir deneyim
                        sunmak, hizmetlerimizi geliştirmek ve deneyimlerimizi iyileştirmek için kullanılır. Ayrıca, web
                        sitesinde bulunan üçüncü taraflara ait linkler, bu üçüncü taraflara ait gizlilik politikalarına
                        tabi olmakla birlikte, gizlilik uygulamalarına ait sorumluluk KiralaBunu’ya ait olmamaktadır.
                        <br><br>
                        Çerez Türleri
                        <br><br>
                        Esas kullanım amacı kullanıcılara kolaylık sağlamak olan çerezler, temel olarak 4 ana grupta
                        toplanmaktadır:
                        <br>
                    </p>
                    <ul class="ml-6 list-outside text-sm mt-5 space-y-4">
                        <li>Oturum Çerezleri: İnternet sayfaları arasında bilgi taşınması ve kullanıcı tarafından
                            girilen bilgilerin sistemsel olarak hatırlanması gibi çeşitli özelliklerden faydalanmaya
                            olanak sağlayan geçici çerezlerdir ve internet sitesine ait fonksiyonların düzgün bir
                            şekilde işleyebilmesi için gereklidir.
                        </li>
                        <li>Performans Çerezleri: Sayfaların ziyaret edilme frekansı, olası hata iletileri,
                            kullanıcıların ilgili sayfada harcadıkları toplam zaman ile birlikte siteyi kullanım
                            desenleri konularında bilgi toplayan çerezlerdir ve internet sitesinin performansını
                            arttırma amacıyla kullanılmaktadır.
                        </li>
                        <li>Fonksiyonel Çerezler: Kullanıcıya kolaylık sağlanması amacıyla önceden seçili olan
                            seçeneklerin hatırlatılmasını sağlayan çerezlerdir ve internet sitesi kapsamında
                            kullanıcılara gelişmiş internet özellikleri sağlanmasını hedeflemektedir.
                        </li>
                        <li>Üçüncü Taraf Çerezler: Üçüncü kişi tedarikçilere ait çerezlerdir ve internet sitesindeki
                            bazı fonksiyonların kullanımına olanak sağlamaktadır.
                        </li>
                    </ul>
                    <p class="text-sm text-justify">
                        <br>
                        Çerezlerin Kullanım Amaçları
                        <br>
                        <br>
                        KiralaBunu çerezleri, hizmetlerimizin kullanımını kolaylaştırmak, hizmetlerimizi ilgi ve
                        ihtiyaçlarınız doğrultusunda daha iyi bir şekilde özelleştirmek amacıyla kullanmaktadır.
                        <br>
                        Çerezler aynı zamanda gelecekte hizmetlerimiz doğrultusunda gerçekleştireceğiniz faaliyet ve
                        deneyimleri hızlandırmak amacıyla da kullanılabilir. Ayrıca çerezleri, insanların sitemizi nasıl
                        kullandıklarını anlamamızı sağlayan isimsiz ve toplu istatistiki verileri bir araya getirmemize,
                        performansımızın ölçülmesi ve artırılması ve sitelerimizin yapılarını ve içeriklerini
                        geliştirmemize yardımcı olmaları amacıyla da kullanmaktayız. Bu veriler, sizin kimliğinizi
                        tanımlamamızı sağlayabilecek bilgiler değildir.

                        <br><br>
                        Çerezleri Kontrol Etme ve Silme

                        <br><br>
                        Çerezlerin kullanımına ilişkin tercihlerinizi değiştirmek ya da çerezleri engellemek veya silmek
                        için tarayıcınızın ayarlarını değiştirmeniz yeterlidir. Aynı zamanda daha önce tarayıcınıza
                        kaydedilmiş çerezlerin silinmesi de mümkündür. Çerezleri kontrol edilmesine veya silinmesine
                        ilişkin işlemler kullandığınız tarayıcıya göre değişebilmektedir. Çerez kullanım seçiminin
                        değiştirilmesine ait yöntem, tarayıcı tipine bağlı olarak değişmekte olup, ilgili hizmet
                        sağlayıcıdan dilendiği zaman öğrenilebilmektedir. Tarayıcınızdan çerez ayarlarınızı
                        değiştirmediğiniz sürece bu sitede çerez kullanımını kabul ettiğinizi varsayacağız.

                        <br>
                    </p>
                </div>
            </div>

        </section>
    </main>
</template>
