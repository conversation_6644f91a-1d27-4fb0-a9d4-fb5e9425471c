<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { vMaska } from "maska";

export default {
    components: {
        Link,
        Head,
        UserMenu
    },
    directives: { maska: vMaska },
    props: {
        address: Object,
        cities: Array,
        id: Number,
        errors: { type: Object, default: false },
        order_count: Number
    },
    data() {
        return {
            form: this.$inertia.form({
                id: this.address?.id ?? null,
                first_name: this.address?.first_name ?? null,
                last_name: this.address?.last_name ?? null,
                city: this.address?.city ?? null,
                city_id: this.address?.city_id ?? null,
                district: this.address?.district ?? null,
                county_id: this.address?.county_id ?? null,
                // birtdate: false,
                tckn: this.address?.tckn ?? null,
                phone: this.address?.phone ?? null,
                name: this.address?.name ?? null,
                address: this.address?.address ?? null,
                bill_type: this.address?.bill_type ?? "",
                firn_name: this.address?.firn_name ?? null,
                tax_no: this.address?.tax_no ?? null,
                tax_office: this.address?.tax_office ?? null
            })
        };
    },
    methods: {
        submit() {
            this.form.post("/adres-duzenle");
        },
        cityChanged() {
            this.form.county_id = null;
        },
        preventNumericInput($event) {
            //console.log($event.keyCode); will display the keyCode value
            //console.log($event.key); will show the key value

            var charCode = $event.keyCode ? $event.keyCode : $event.which;
            if ((charCode <= 93 && charCode >= 65) || (charCode <= 122 && charCode >= 97) || charCode == 32 || charCode == 8 || charCode == 350 || charCode == 351 || charCode == 304 || charCode == 286 || charCode == 287 || charCode == 231 || charCode == 199 || charCode == 305 || charCode == 214 || charCode == 246 || charCode == 220 || charCode == 252) {
                if (charCode != 33 || charCode != 43) {
                    return true;
                } else {
                    $event.preventDefault();
                }
            } else {
                $event.preventDefault();
            }
        }
    },
    layout: Layout
};
</script>

<template>

    <Head title="Adres Ekle " />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <user-menu :order_count="order_count" :active="`Addresses`" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 pt-2">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7 ts:pl-4">
                        {{ id != null ? "Adres Düzenle" : "Adres Ekle" }}
                    </h3>
                </div>
                <form @submit.prevent="submit">
                    <div class="flex flex-row justify-center items-start w-full">
                        <div class="block mts:hidden w-full text-center mts:px-2 lg:px-3">
                            <div class="relative group">
                                <input id="name" v-model="form.first_name" @keypress="preventNumericInput"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="AdSoyad" autofocus />
                                <label for="name"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adın*</label>
                            </div>
                            <div class="relative group">
                                <input id="surname" v-model="form.last_name" @keypress="preventNumericInput"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="Soyad" />
                                <label for="surname"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Soyadın*</label>
                            </div>
                            <select v-model="form.city_id" @change="cityChanged"
                                class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider" name="city">
                                <option value="Sırala">İl Seç*</option>
                                <template v-for="(city, index) in [...new Map(cities.map((m) => [m.city_id, m])).values()]" :key="index">
                                    <option :value="city.city_id">{{ city.city }}</option>
                                </template>
                            </select>
                            <select v-model="form.county_id" class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-placeholdergray tracking-wider"
                                name="county">
                                <option value="Sırala" selected>İlçe seç*</option>
                                <template v-for="(county, index) in cities.filter((c) => c.city_id == this.form.city_id).sort((a, b) => a.county.localeCompare(b.county, 'tr'))" :key="index">
                                    <option :value="county.county_id">{{ county.county }}</option>
                                </template>
                            </select>
                            <div class="relative group">
                                <input v-model="form.phone" v-maska data-maska="(5##) ### ## ##" id="telephone"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="telno" />
                                <label for="telephone"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Telefon*</label>
                            </div>
                            <div class="relative group hidden">
                                <input v-model="form.tckn" id="idnumber2"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    type="number" placeholder="" name="adresadi" />
                                <label v-maska data-maska="###########" for="idnumber2"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Tc
                                    Kimlik Numaran*</label>
                            </div>
                            <div class="relative group">
                                <input v-model="form.name" id="addressname"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="adresadi" />
                                <label for="addressname"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adres
                                    Başlığı*</label>
                            </div>
                        </div>
                        <div class="hidden mts:block w-6/12 text-center mts:px-2 lg:px-3">
                            <div class="group relative">
                                <input id="name2" v-model="form.first_name" @keypress="preventNumericInput"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="AdSoyad" autofocus />
                                <label for="name2"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adın*</label>
                            </div>
                            <select v-model="form.city_id" @change="cityChanged"
                                class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="sirala" id="sirala">
                                <option value="Sırala">İl Seç*</option>
                                <template v-for="(city, index) in [...new Map(cities.map((m) => [m.city_id, m])).values()]" :key="index">
                                    <option :value="city.city_id">{{ city.city }}</option>
                                </template>
                            </select>
                            <div class="relative group">
                                <input id="telephone2" v-model="form.phone"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="telno" />
                                <label for="telephone2"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Telefon*</label>
                            </div>
                            <div class="relative group">
                                <input id="addressname2" v-model="form.name"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="adresadi" />
                                <label for="addressname2"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adres
                                    Başlığı*</label>
                            </div>
                        </div>
                        <div class="hidden mts:block w-6/12 text-center mts:px-2 lg:px-3">
                            <div class="relative group">
                                <input id="surname2" v-model="form.last_name" @keypress="preventNumericInput"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="" name="Soyad" />
                                <label for="surname2"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Soyadın*</label>
                            </div>
                            <select v-model="form.county_id" class="w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="sirala"
                                id="sirala">
                                <option value="Sırala" selected>İlçe seç*</option>
                                <template v-for="(county, index) in cities.filter((c) => c.city_id == this.form.city_id).sort((a, b) => a.county.localeCompare(b.county, 'tr'))" :key="index">
                                    <option :value="county.county_id">{{ county.county }}</option>
                                </template>
                            </select>
                            <div class="relative group hidden">
                                <input v-maska data-maska="###########" id="idnumber" v-model="form.tckn"
                                    class="peer w-full mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    type="number" placeholder="" name="adresadi" />
                                <label for="idnumber"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Tc
                                    Kimlik Numaran *</label>
                            </div>
                        </div>
                    </div>
                    <div class="w-full mts:px-2 lg:px-3 relative group">
                        <textarea v-model="form.address" id="address2"
                            class="peer w-full mb-4 rounded-md border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-lg tracking-wider placeholder:mt-2 h-32"
                            required name="Adres" placeholder="" cols="20" rows="10"></textarea>
                        <label for="address2"
                            class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-5 group-focus-within:left-5 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Adresin*</label>
                        <div class="flex justify-between flex-col ts:flex-row">
                            <select v-model="form.bill_type"
                                class="w-6/12 lg:w-3/12 mr-2 w-full px-2 py-2 mb-5 rounded-2lg border-2 border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="sirala"
                                id="sirala" required>
                                <option value="">Fatura Türü*</option>
                                <option value="Bireysel">Bireysel</option>
                                <option value="Kurumsal">Kurumsal</option>
                            </select>
                            <div class="w-full ts:w-8/12 flex" v-if="form.bill_type == 'Kurumsal'">
                                <input v-model="form.firn_name"
                                    class="w-full ts:w-1/3 mb-5 mr-2 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="Firma Adı" name="Firma" />
                                <input v-model="form.tax_no"
                                    class="w-full ts:w-1/3 mb-5 mr-2 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="Vergi Numarası" name="Numarası" />
                                <input v-model="form.tax_office"
                                    class="w-full ts:w-1/3 mb-5 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="Vergi Dairesi" name="Dairesi" />
                            </div>
                            <div class="w-full ts:w-8/12 flex" v-if="form.bill_type == 'Bireysel'">
                                <input v-model="form.tckn" v-maska data-maska="###########"
                                    class="w-full ts:w-1/3 mb-5 mr-2 rounded-2lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                    required type="text" placeholder="Tc Kimlik Numaran *" name="tckn" />
                            </div>
                        </div>
                        <div class="flex mt-4 self-center justify-center flex-col" v-if="Object.entries(errors).length > 0">
                            <div class="flex text-sm text-kbred mr-[2px] tracking-normal" v-for="error in errors">
                                {{ error }}
                            </div>
                        </div>
                        <div class="text-right">
                            <button class="w-full lg:w-3/12 mt-3 bg-black text-white rounded-full py-2 lg:py-3 text-lg px-4 self-center font-bold w-full" type="submit" :disabled="form.processing">Kaydet
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </section>
    </main>
</template>
