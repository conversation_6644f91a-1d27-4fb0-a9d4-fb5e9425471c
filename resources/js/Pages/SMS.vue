<script>
import { <PERSON>, <PERSON> } from '@inertiajs/inertia-vue3';
import Layout from '@/Pages/Shared/Layout.vue';

export default {
    components: {
        <PERSON>,
        Head,
    },
    layout: Layout,
}
</script>

<template>
    <Head title="SMS Onayı" />

    <main class="my-20">
        <section class="mt-6">
            <div class="flex mx-auto flex-col max-w-[350px] md:max-w-lg py-8 rounded-2lg bg-white shadow-searchshadow">
                <img src="../../images/logo.png" class="block lg:hidden mx-auto w-60 mb-10" alt="Logo">
                <div class="font-bold text-xl self-center">SMS Onayı </div>
                <div class="flex w-80 md:w-[394px] lg:w-auto self-center mt-4">
                    <span class="text-sm mx-auto text-center md:text-left px-4">Telefona gönderilen onay kodunu aşa<PERSON><PERSON><PERSON>i kutucuğa gir.</span>
                </div>
                <div class="self-center flex mt-6">
                    <input type="text" class="border-2 rounded-lg border-kb-light-grey w-8 p-2 mx-2 font-bold">
                    <input type="text" class="border-2 rounded-lg border-kb-light-grey w-8 p-2 mx-2 font-bold">
                    <input type="text" class="border-2 rounded-lg border-kb-light-grey w-8 p-2 mx-2 font-bold">
                    <input type="text" class="border-2 rounded-lg border-kb-light-grey w-8 p-2 mx-2 font-bold">
                </div>
                <div class="self-center flex mt-6">
                    <input type="text" class="border-2 rounded-lg border-kbred w-8 p-2 mx-2 font-bold">
                    <input type="text" class="border-2 rounded-lg border-kbred w-8 p-2 mx-2 font-bold">
                    <input type="text" class="border-2 rounded-lg border-kbred w-8 p-2 mx-2 font-bold">
                    <input type="text" class="border-2 rounded-lg border-kbred w-8 p-2 mx-2 font-bold">
                </div>
                <div class="self-center flex mt-6">
                    <input type="text" class="border-2 rounded-lg border-kbgreen w-8 p-2 mx-2 font-bold">
                    <input type="text" class="border-2 rounded-lg border-kbgreen w-8 p-2 mx-2 font-bold">
                    <input type="text" class="border-2 rounded-lg border-kbgreen w-8 p-2 mx-2 font-bold">
                    <input type="text" class="border-2 rounded-lg border-kbgreen w-8 p-2 mx-2 font-bold">
                </div>
                <div class="flex flex-col w-80 md:w-96 self-center mt-4">
                    <p class="text-xs text-kbred mx-auto">Geçersiz onay kodu. </p>
                </div>
                <div class="flex flex-col w-80 md:w-96 self-center mt-4">
                    <p class="text-base mx-auto">Kalan süre</p>
                    <p class="text-base font-bold mx-auto">02:58 </p>
                </div>
                <div class="flex mt-4 w-80 self-center">
                    <button class="bg-[#d2d1d1] hover:bg-black text-white rounded-full py-2 px-4 self-center font-bold w-full">Üyeliği Tamamla</button>
                </div>

            </div>
        </section>
    </main>

</template>
