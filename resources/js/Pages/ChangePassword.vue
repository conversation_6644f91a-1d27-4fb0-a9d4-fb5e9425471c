<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";

import { TransitionRoot, TransitionChild, Dialog, DialogPanel, DialogTitle } from "@headlessui/vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle
    },
    layout: Layout,
    data() {
        return {
            password: "",
            passwordFieldType: "password",
            password_confirmation: "",
            passwordFieldType2: "password",
            isOpen: false,
            rules: [
                {
                    message: "<PERSON><PERSON>ren en az bir küçük harf içermelidir.\n",
                    regex: /[a-z]+/
                },
                {
                    message: "<PERSON><PERSON>ren en az bir büyük harf içermelidir.\n",
                    regex: /[A-Z]+/
                },
                {
                    message: "<PERSON><PERSON><PERSON> en az 8 karakter içermelidir.\n",
                    regex: /.{8,}/
                },
                {
                    message: "<PERSON><PERSON><PERSON> en az bir rakam içermelidir.\n",
                    regex: /[0-9]+/
                }
            ],
            submitted: false,
            isPassSame: false,
            form: this.$inertia.form({
                password: "",
                password_confirmation: ""
            })
        };
    },
    methods: {
        submit() {
            let errors = [];
            let res = this.passwordValidation;
            console.log(res);
            if (res.valid == true) {
                if (this.form.password == this.form.password_confirmation) {
                    this.openModal();
                    this.form.post("/sifremi-yenile");
                }
            }

            // this.form.post("/sifremi-yenile");
        },
        switchVisibility() {
            this.passwordFieldType = this.passwordFieldType === "password" ? "text" : "password";
        },
        switchVisibility2() {
            this.passwordFieldType2 = this.passwordFieldType2 === "password" ? "text" : "password";
        },
        closeModal() {
            this.isOpen = false;
        },
        openModal() {
            this.isOpen = true;
        },
        resetPasswords() {
            this.form.password = "";
            this.form.password_confirmation = "";
            this.submitted = true;
            setTimeout(() => {
                this.submitted = false;
            }, 2000);
        },
        EditPass() {
            // if (!this.notSamePasswords && this.passwordsFilled) {
            //     this.isOpen = true;
            // }
        }
    },
    computed: {
        notSamePasswords() {
            if (this.passwordsFilled) {
                return this.form.password !== this.form.password_confirmation;
            } else {
                this.isPassSame = false;
                return false;
            }
        },
        passwordsFilled() {
            return this.form.password !== "" && this.form.password_confirmation !== "";
        },
        passwordValidation() {
            let errors = [];
            if (this.form.password == null || this.form.password == "") return (errors = []);
            for (let condition of this.rules) {
                if (!condition.regex.test(this.form.password)) {
                    errors.push(condition.message);
                }
            }
            if (errors.length === 0) {
                this.isPassSame = true;
                return { valid: true, errors };
            } else {
                return { valid: false, errors };
            }
        }
    },
    watch: {
        saveStatus: function (newVal, oldVal) {
            // watch it
            console.log("Prop changed: ", newVal, " | was: ", oldVal);
            this.isOpen = newVal;
        }
    },
    props: {
        order_count: Number,
        saveStatus: { type: Boolean, default: false }
    }
};
</script>

<template>

    <Head title="Şifremi Yenile" />
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white px-5 py-5 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 62.051 62.051">
                                <g id="Group_4884" data-name="Group 4884" transform="translate(-73.036 -250.295)">
                                    <circle id="Ellipse_199" data-name="Ellipse 199" cx="31.025" cy="31.025" r="31.025" transform="translate(73.036 250.295)" fill="#70d44b" />
                                    <path id="Path_3299" data-name="Path 3299"
                                        d="M351.649,476.232a3.45,3.45,0,0,1-2.761-1.381l-8.464-11.286a3.451,3.451,0,0,1,5.522-4.141l5.538,7.383,14.223-21.334a3.451,3.451,0,1,1,5.743,3.829l-16.928,25.393a3.453,3.453,0,0,1-2.776,1.536Z"
                                        transform="translate(-251.82 -178.764)" fill="#fff" />
                                </g>
                            </svg>
                            <DialogTitle>
                                <h2 class="text-base font-semibold text-center mb-2 mt-2">Başarılı</h2>
                            </DialogTitle>
                            <p class="text-xs font-kiralabunuthin ts:text-sm text-center">Yeni Şifreniz başarıyla kayıt edildi</p>
                            <!--                            <div class="mt-4">-->
                            <!--                                <button-->
                            <!--                                    type="button"-->
                            <!--                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"-->
                            <!--                                    @click="closeModal"-->
                            <!--                                >-->
                            <!--                                    Got it, thanks!-->
                            <!--                                </button>-->
                            <!--                            </div>-->
                            <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal">x</div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex flex-row">
            <user-menu :order_count="order_count" :active="`EditProfile`" class="hidden lg:flex"></user-menu>
            <div class="w-full lg:w-9/12 lg:px-4 pt-2">
                <div class="flex justify-between">
                    <h3 class="p-0 text-3xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-7">Şifremi Yenile</h3>
                </div>
                <section class="mt-6 mb-20">
                    <div class="flex mx-auto flex-col max-w-[350px] md:max-w-2xl py-8 rounded-2lg bg-white">
                        <transition name="hint" appear>
                            <div v-if="passwordValidation.errors && passwordValidation.errors.length > 0 && !submitted" class="hints">
                                <div class="flex flex-col w-full self-center mt-4">
                                    <span class="text-base mx-auto text-center md:text-left mb-1" v-for="error in passwordValidation.errors">{{ error }}</span>
                                </div>
                            </div>
                        </transition>
                        <form @submit.prevent="submit" class="w-full flex flex-col">
                            <div class="self-center mt-6 relative group w-80 md:w-96">
                                <input type="hidden" id="pass3" />
                                <input required id="pass" :type="passwordFieldType" v-model="form.password" placeholder=""
                                    class="w-full peer placeholder:text-placeholdergray border-2 rounded-2lg border-kb-light-grey focus:border-acordion-green hover:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white"
                                    autocomplete="off" />
                                <label for="pass"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Yeni
                                    Şifre</label>

                                <div @click="switchVisibility" class="absolute mt-3 top-0 right-5">
                                    <svg id="eye-slash" xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 17.749 15">
                                        <path id="Path_2948" data-name="Path 2948"
                                            d="M20.762,15.939a16.033,16.033,0,0,0,2.93-3.822s-3.328-6.492-8.874-6.492a7.4,7.4,0,0,0-3.095.694l.854.91a6.24,6.24,0,0,1,2.241-.424,8.088,8.088,0,0,1,5.733,2.9,15.359,15.359,0,0,1,1.841,2.412c-.065.1-.135.216-.216.34a15.4,15.4,0,0,1-1.625,2.072c-.183.195-.374.387-.573.573Z"
                                            transform="translate(-5.943 -4.617)" fill="#231f20" />
                                        <path id="Path_2949" data-name="Path 2949"
                                            d="M17.661,15.641a4.323,4.323,0,0,0-.91-4.312A3.717,3.717,0,0,0,12.7,10.36l.913.972a2.672,2.672,0,0,1,2.354.835,3.069,3.069,0,0,1,.784,2.5l.912.97ZM14.4,17.175l.912.97a3.717,3.717,0,0,1-4.053-.969,4.323,4.323,0,0,1-.91-4.312l.913.972a3.069,3.069,0,0,0,.784,2.5,2.672,2.672,0,0,0,2.354.835Z"
                                            transform="translate(-5.13 -6.753)" fill="#231f20" />
                                        <path id="Path_2950" data-name="Path 2950"
                                            d="M3.716,11.55q-.3.283-.574.575A15.359,15.359,0,0,0,1.3,14.537l.216.34a15.4,15.4,0,0,0,1.625,2.072,8.09,8.09,0,0,0,5.733,2.9,6.219,6.219,0,0,0,2.241-.425l.854.911a7.4,7.4,0,0,1-3.095.694C3.328,21.029,0,14.537,0,14.537a15.978,15.978,0,0,1,2.93-3.822l.785.837Z"
                                            transform="translate(0 -7.036)" fill="#231f20" />
                                        <path id="Path_2951" data-name="Path 2951" d="M17.015,18.7,3.7,4.539,4.489,3.7,17.8,17.867Z" transform="translate(-1.878 -3.703)" fill="#231f20" fill-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                            <div class="self-center mt-6 relative group w-80 md:w-96">
                                <input id="pass2" :type="passwordFieldType2" v-model="form.password_confirmation" placeholder=""
                                    class="peer placeholder:text-placeholdergray border-2 rounded-2lg border-kb-light-grey w-full focus:border-acordion-green hover:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white"
                                    autocomplete="off" required />
                                <label for="pass2"
                                    class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Yeni
                                    Şifre Tekrar*
                                </label>
                                <div @click="switchVisibility2" class="absolute mt-3 top-0 right-5">
                                    <svg id="eye-slash" xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 17.749 15">
                                        <path id="Path_2948" data-name="Path 2948"
                                            d="M20.762,15.939a16.033,16.033,0,0,0,2.93-3.822s-3.328-6.492-8.874-6.492a7.4,7.4,0,0,0-3.095.694l.854.91a6.24,6.24,0,0,1,2.241-.424,8.088,8.088,0,0,1,5.733,2.9,15.359,15.359,0,0,1,1.841,2.412c-.065.1-.135.216-.216.34a15.4,15.4,0,0,1-1.625,2.072c-.183.195-.374.387-.573.573Z"
                                            transform="translate(-5.943 -4.617)" fill="#231f20" />
                                        <path id="Path_2949" data-name="Path 2949"
                                            d="M17.661,15.641a4.323,4.323,0,0,0-.91-4.312A3.717,3.717,0,0,0,12.7,10.36l.913.972a2.672,2.672,0,0,1,2.354.835,3.069,3.069,0,0,1,.784,2.5l.912.97ZM14.4,17.175l.912.97a3.717,3.717,0,0,1-4.053-.969,4.323,4.323,0,0,1-.91-4.312l.913.972a3.069,3.069,0,0,0,.784,2.5,2.672,2.672,0,0,0,2.354.835Z"
                                            transform="translate(-5.13 -6.753)" fill="#231f20" />
                                        <path id="Path_2950" data-name="Path 2950"
                                            d="M3.716,11.55q-.3.283-.574.575A15.359,15.359,0,0,0,1.3,14.537l.216.34a15.4,15.4,0,0,0,1.625,2.072,8.09,8.09,0,0,0,5.733,2.9,6.219,6.219,0,0,0,2.241-.425l.854.911a7.4,7.4,0,0,1-3.095.694C3.328,21.029,0,14.537,0,14.537a15.978,15.978,0,0,1,2.93-3.822l.785.837Z"
                                            transform="translate(0 -7.036)" fill="#231f20" />
                                        <path id="Path_2951" data-name="Path 2951" d="M17.015,18.7,3.7,4.539,4.489,3.7,17.8,17.867Z" transform="translate(-1.878 -3.703)" fill="#231f20" fill-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                            <div class="w-full text-center mt-3 text-kbred transition-all" v-if="notSamePasswords">
                                <p>Girdiğiniz Şifreler Aynı Değil</p>
                            </div>
                            <div class="flex mt-4 w-80 md:w-96 self-center">
                                <button type="submit" class="cursor-pointer font-santralextrabold text-center bg-black text-white rounded-full py-3 px-4 self-center font-bold w-full leading-none">Gönder</button>
                            </div>
                        </form>
                    </div>
                </section>
            </div>
        </section>
    </main>
</template>
