<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import CategoryBox from "@/Pages/Shared/CategoryBox.vue";
import VerticalProductBox from "@/Pages/Shared/VerticalProductBox.vue";
import ProductBox from "@/Pages/Shared/ProductBox.vue";
import VerticalProductList from "@/Pages/Shared/VerticalProductList.vue";
import Pagination from "@/Pages/Shared/Pagination.vue";
import Loader from "@/Pages/Shared/Loader.vue";
import { Inertia } from "@inertiajs/inertia";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import { Disclosure, DisclosureButton, DisclosurePanel } from "@headlessui/vue";
import { vMaska } from "maska";

var ev;
export default {
    components: {
        Loader,
        Pagination,
        Link,
        Head,
        CategoryBox,
        ProductBox,
        VerticalProductBox,
        VerticalProductList,
        Splide,
        SplideSlide,
        SplideTrack,
        Disclosure,
        DisclosureButton,
        DisclosurePanel
    },
    directives: { maska: vMaska },
    props: {
        discounted: Object,
        category: Object,
        brands: Object,
        filters: Object,
        categories: Object,
        pageType: String,
        paginateType: String,
        auth: Object,
        mobilefilter: false,
        mostRentedProducts: Object,
        canonical: String,
        showCategoryNote: Boolean,
        newTypeSeoDefinition: {
            type: Boolean,
            default: false
        },
        title: String,
        meta_description: String,
        meta_keywords: String,
        errors: { type: Object, default: false }

    },
    data() {
        return {
            selectedGridType: "category-box",
            selectedFilters: {
                brand: this.filters.brand?.split(",") ?? [],
                price: this.filters.price?.split(",") ?? [],
                category: this.filters.collections?.split(",") ?? [],
                color: [],
                storage: [],
                os: []
            },
            categoryItems: this.category,
            hiddenSeoText: true,
            isOpen: this.$page.props.success.success != null ? true : false,
            kiralamobilForm: this.$inertia.form({
                fullName: null,
                email: null,
                gsm: null,
                city: null,
                brand: null,
                amount: null,
                leasePeriod: "Kiralama Süresi",
                leaseType: "Kiralama Tipi"
            })
        };
    },
    methods: {
        addToFilter(type, value) {
            // check if value is already in array
            if (this.selectedFilters[type].find((e) => e == value)) {
                // remove value from array
                this.selectedFilters[type] = this.selectedFilters[type].filter((e) => e != value);
            } else {
                // add value to array
                this.selectedFilters[type].push(value);
            }

            this.$inertia.get(`/kategoriler/${this.category.items.slug ?? "tum-urunler"}`, {
                "filter[brand]": this.selectedFilters.brand.join(","), // join array to string
                "filter[price]": this.selectedFilters.price.join(","), // join array to string,
                "filter[collections]": this.selectedFilters.category.join(",") // join array to string,
            });
        },
        splidedArray(arr, size) {
            return arr.reduce((acc, e, i) => (i % size ? acc[acc.length - 1].push(e) : acc.push([e]), acc), []);
        },
        orderCategories() {
            console.log("orderCategories");
            //console.log(this.categories);
            //return this.categories;
            window.cats = this.categories;
            return Object.values(this.categories).sort((a, b) => {
                return a.name?.name?.tr.localeCompare(b.name?.name?.tr);
            });
        },
        closeModal() {
            this.isOpen = false;
        },
        openModal() {
            this.isOpen = true;
        },
        goToForm() {
            this.$refs.input.focus();
        }
    },
    computed: {
        splidedDiscounted() {
            // Take first 8 items than split them into 2
            return this.splidedArray(this.discounted.items.data.slice(0, 16), 1);
        }
    },
    created() {
        console.log("CategoryGrid created");

        Inertia.reload({
            only: ["discounted"]
        });

        ev = this.$inertia.on("success", (event) => {
            //console.log("Kategpri", event.detail.page.url);

            let datas = this.categoryItems.items.data;
            if (this.pageType === "category") {
                datas = this.categoryItems.items.element.products.data;
            }

            let categoryProducts = [];
            datas.map((item, keys) => {
                let product = {};

                let subscribetionMonthsOrdered = [];
                if (item.variants[0]?.prices.length > 0) subscribetionMonthsOrdered = Object.entries(item.variants[0]?.prices).sort(([, a], [, b]) => a.subscription_months.value - b.subscription_months.value);
                let nonZeroPrices = item.variants[0]?.prices.filter((price) => price.price.value > 0);
                subscribetionMonthsOrdered = subscribetionMonthsOrdered.filter((price) => price[1].price.value > 0);
                let productPrice = subscribetionMonthsOrdered[+Object.keys(nonZeroPrices).length - 1]?.[1]?.price?.value / 100;

                product.item_id = item.id;
                product.item_name = item.attribute_data.name.tr;
                product.price = productPrice;
                product.item_brand = item.brand.name;
                product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                //product.item_color = item.variants[0]?.options[0] ?? ""; // Kategori verisidne yok
                product.item_list_id = "HOME-1";
                product.item_list_name = "Homepage - E Mobilite List";
                product.index = keys;
                categoryProducts.push(product);
            });

            if (event.detail.page.url.startsWith("/kategoriler")) {
                //console.log(this.category);
                dataLayer.push({
                    event: "view_item_list",
                    ecommerce: {
                        item_list_id: "HOME-" + this.category.items.element?.id,
                        item_list_name: "Category - " + this.category.items.element?.attribute_data?.name?.tr + " List",
                        items: categoryProducts
                    }
                });
            }
        });
    },
    layout: Layout,
    mounted() {
        console.log("CategoryGrid mounted");
    },
    setup() {
        const options = {
            rewind: true,
            gap: "1rem",
            perPage: 1,
            arrows: false
        };

        return { options };
    },
    watch: {
        "$page.props.success": function(val) {
            this.isOpen = val.success != null ? true : false;
            if (val.success == "Mesajınız başarıyla gönderildi.") {
                this.kiralamobilForm.reset();
            }
        }
    }
};
</script>

<template>
    <templete v-if="!newTypeSeoDefinition">

        <Head :title="category?.items?.element?.title">
            <meta name="description" :content="category?.items?.element?.meta_description" />
            <link rel="canonical" :href="canonical" />
        </Head>
    </templete>
    <templete v-else>

        <Head :title="title">
            <meta name="description" :content="meta_description" />
            <link rel="canonical" :href="canonical" />
        </Head>
    </templete>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-50">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white px-5 py-5 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 62.051 62.051">
                                <g id="Group_4884" data-name="Group 4884" transform="translate(-73.036 -250.295)">
                                    <circle id="Ellipse_199" data-name="Ellipse 199" cx="31.025" cy="31.025" r="31.025" transform="translate(73.036 250.295)" fill="#70d44b" />
                                    <path id="Path_3299" data-name="Path 3299" d="M351.649,476.232a3.45,3.45,0,0,1-2.761-1.381l-8.464-11.286a3.451,3.451,0,0,1,5.522-4.141l5.538,7.383,14.223-21.334a3.451,3.451,0,1,1,5.743,3.829l-16.928,25.393a3.453,3.453,0,0,1-2.776,1.536Z" transform="translate(-251.82 -178.764)" fill="#fff" />
                                </g>
                            </svg>
                            <DialogTitle>
                                <h2 class="text-base font-semibold text-center mb-2 mt-2">Başarılı</h2>
                            </DialogTitle>
                            <p class="text-xs font-kiralabunuthin ts:text-sm text-center">Formunuz Başarıyla Gönderildi.</p>
                            <!--                            <div class="mt-4">-->
                            <!--                                <button-->
                            <!--                                    type="button"-->
                            <!--                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"-->
                            <!--                                    @click="closeModal"-->
                            <!--                                >-->
                            <!--                                    Got it, thanks!-->
                            <!--                                </button>-->
                            <!--                            </div>-->
                            <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal">x</div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>


    <main class="my-3 mx-auto max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-4 mx-auto">
        <div class="promotion-bar w-full px-5 py-1 bg-[#FCF3EC] flex justify-center rounded-lg border-1 border-[#FCF3EC] mb-3" v-if="false">
            <div class="font-bold text-base">Yeni üyelere özel 6 ay ve üzeri kiralamaların ilk ayında 500 tl indirim Kiralabunu’dan hediye! İndirim kodu: merhaba500</div>
        </div>
        <section class="mb-6 flex flex-row">
            <div class="w-full lg:pt-2">
                <div class="sliderclass">
                    <splide :options="{ rewind: true, gap: '1rem', perPage: 1, arrows: false }" class="block mts:hidden ts:hidden mt-4">
                        <splide-slide>
                            <picture>
                                <source src="../../images/category-banner/kiralamobil-new-slider-mobile.webp" type="image/webp" />
                                <source src="../../images/category-banner/kiralamobil-new-slider-mobile.png" type="image/png" />
                                <img class="w-full" src="../../images/category-banner/kiralamobil-new-slider-mobile.png" alt="" />
                            </picture>
                        </splide-slide>

                    </splide>

                    <splide :options="options" class="hidden mts:block ts:hidden">
                        <splide-slide>
                            <picture>
                                <source src="../../images/category-banner/kiralamobil-new-slider-desktop.webp" type="image/webp" />
                                <source src="../../images/category-banner/kiralamobil-new-slider-desktop.png" type="image/png" />
                                <img class="w-full" src="../../images/category-banner/kiralamobil-new-slider-desktop.png" alt="" />
                            </picture>
                        </splide-slide>

                    </splide>

                    <splide :options="options" class="hidden mts:hidden ts:block">
                        <splide-slide>
                            <picture>
                                <source src="../../images/category-banner/kiralamobil-new-slider-desktop.webp" type="image/webp" />
                                <source src="../../images/category-banner/kiralamobil-new-slider-desktop.png" type="image/png" />
                                <img class="w-full" src="../../images/category-banner/kiralamobil-new-slider-desktop.png" alt="" />
                            </picture>
                        </splide-slide>
                    </splide>
                </div>
            </div>
        </section>
        <section class="pb-3 overflow-x-scroll">
            <div class="w-full flex space-x-8 relative w-[1000px]">
                <div class="bg-kbgreen text-white text-sm lg:text-base py-2 w-[200px] rounded-full mt-2 hover:opacity-[90%] whitespace-nowrap leading-tight font-santralextrabold flex justify-center"><a
                    href="/kategoriler/elektrikli-scooter">Elektrikli Scooter</a></div>
                <button class="bg-kbgreen text-white text-sm lg:text-base py-2 w-[200px] rounded-full mt-2 hover:opacity-[90%] whitespace-nowrap leading-tight font-santralextrabold flex justify-center"><a href="/kategoriler/elektrikli-kaykay">
                    Elektrikli Kaykay</a></button>
                <button class="bg-kbgreen text-white text-sm lg:text-base py-2 w-[200px] rounded-full mt-2 hover:opacity-[90%] whitespace-nowrap leading-tight font-santralextrabold flex justify-center"><a href="/kategoriler/elektrikli-bisiklet">
                    Elektrikli Bisiklet</a></button>
                <button class="bg-kbgreen text-white text-sm lg:text-base py-2 w-[200px] rounded-full mt-2 hover:opacity-[90%] whitespace-nowrap leading-tight font-santralextrabold flex justify-center"><a href="/kategoriler/elektrikli-motor">
                    Elektrikli Motor</a></button>
                <button class="bg-kbgreen text-white text-sm lg:text-base py-2 w-[200px] rounded-full mt-2 hover:opacity-[90%] whitespace-nowrap leading-tight font-santralextrabold flex justify-center"><a href="/kategoriler/sarj-istasyonlari"> Şarj
                    İstasyonları</a></button>
            </div>
        </section>
        <div class="mt-6 md:mt-4 py-1 md:py-9 bg-[#f8f8f8] w-full">
            <div class="flex w-full justify-between">
                <div class="text-2xl md:text-3xl my-2 lg:my-0 mx-0 lg:mx-4 self-center text-center w-full md:w-auto">Dikkat Çeken Kategoriler</div>
            </div>
            <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0 lg:mt-9">
                <Splide :has-track="false" aria-label="" :options="{ rewind: true, perPage: 3, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 3 } } }">
                    <SplideTrack>
                        <SplideSlide>
                            <div class="px-5 my-2 w-full">
                                <picture>
                                    <source srcset="../../images/category-banner/kategori-motosiklet.webp" type="image/webp" />
                                    <source srcset="../../images/category-banner/kategori-motosiklet.png" type="image/png" />
                                    <img src="../../images/category-banner/kategori-motosiklet.png" alt="" loading="lazy" />
                                </picture>
                                <div class="text-lg md:text-xl my-4 text-center w-full">
                                    <a href="/tags/kiralamotor"> Motosiklet</a>
                                </div>
                                <div class="flex justify-center">
                                    <div class="bg-kbgreen text-white text-sm lg:text-base py-2 w-[200px] rounded-full mt-2 hover:opacity-[90%] flex justify-center">
                                        <a class="leading-tight font-santralextrabold whitespace-nowrap" href="/tags/kiralamotor">Ürünlere Git</a>
                                    </div>
                                </div>
                            </div>
                        </SplideSlide>
                        <SplideSlide>
                            <div class="px-5 my-2 w-full">
                                <picture>
                                    <source srcset="../../images/category-banner/elektrikli-motosiklet.webp" type="image/webp" />
                                    <img src="../../images/category-banner/elektrikli-motosiklet.png" alt="" loading="lazy" />
                                </picture>
                                <div class="text-lg md:text-xl my-4 text-center w-full">
                                    <a href="/kategoriler/elektrikli-motor">Elektrikli Motorlar</a>
                                </div>
                                <div class="flex justify-center">
                                    <div class="bg-kbgreen text-white text-sm lg:text-base py-2 w-[200px] rounded-full mt-2 hover:opacity-[90%] flex justify-center">
                                        <a class="leading-tight font-santralextrabold whitespace-nowrap" href="/kategoriler/elektrikli-motor">Ürünlere Git</a>
                                    </div>
                                </div>
                            </div>
                        </SplideSlide>
                        <SplideSlide>
                            <div class="px-5 my-2 w-full">
                                <picture>
                                    <source srcset="../../images/category-banner/wallbox.webp" type="image/webp" />
                                    <img src="../../images/category-banner/wallbox.png" alt="" loading="lazy" />
                                </picture>
                                <div class="text-lg md:text-xl my-4 text-center w-full">
                                    <a class="leading-tight font-santralextrabold whitespace-nowrap" href="/kategoriler/sarj-istasyonlari">Duvar Tipi Sarj İstasyonları</a>
                                </div>
                                <div class="flex justify-center">
                                    <div class="bg-kbgreen text-white text-sm lg:text-base py-2 w-[200px] rounded-full mt-2 hover:opacity-[90%] flex justify-center">
                                        <a class="leading-tight font-santralextrabold whitespace-nowrap" href="/kategoriler/sarj-istasyonlari">Ürünlere Git</a>
                                    </div>
                                </div>
                            </div>
                        </SplideSlide>
                    </SplideTrack>
                    <div class="splide__arrows">
                        <button class="splide__arrow splide__arrow--prev !-left-9">
                            <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                      transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                        <button class="splide__arrow splide__arrow--next !-right-9">
                            <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                      transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                    </div>
                </Splide>

            </div>
        </div>
        <div class="flex flex-wrap mx-auto justify-center items-center relative z-40 w-full mt-12">
            <div class="w-full mts:w-6/12 ">
                <picture>
                    <source srcset="../../images/category-banner/suzuki-avenis.webp" type="image/webp" />
                    <source srcset="../../images/category-banner/suzuki-avenis.png" type="image/png" />
                    <img class="rounded-sm" src="../../images/category-banner/suzuki-avenis.png" alt="" />
                </picture>
            </div>
            <div class="w-full mts:w-6/12 mts:pl-6">
                <div class="my-4 text-4xl font-santralregular relative z-40">
                    Motorunu bizden kirala, kazandıkça öde!
                </div>
                <div class="font-santralregular text-base text-black">
                    Esnek kira süreleri, hızlı teslimat ve tek tuşla kolay başvuru ile hemen yola çık.
                </div>
                <div class="my-4">
                    <div class="bg-black max-w-[200px] text-white text-sm lg:text-base py-2 px-6 rounded-full mt-2 hover:opacity-[90%] whitespace-nowrap leading-tight font-santralextrabold flex justify-center"><a
                        href="/kiralamotor">Ürünlere Git</a></div>
                </div>
            </div>
        </div>
        <section class="mt-6 md:mt-4 py-1 md:py-9 bg-[#f8f8f8]">
            <div class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
                <div class="flex w-full justify-between">
                    <div class="text-2xl md:text-3xl my-2 lg:my-0 mx-0 lg:mx-4 self-center text-center w-full md:w-auto">İndirimli Ürünler</div>
                    <div class="flex-1 ml-6 hidden md:flex">
                        <div class="flex-1 self-center border border-gray-200"></div>
                        <Link href="/indirimli-urunler" class="cursor-pointer text-sm font-santralextrabold flex justify-center items-center border-2 rounded-full px-4 border-2 whitespace-nowrap md:ml-4 bg-white hover:bg-kbgreen hover:text-white">
                            Tümünü Gör
                        </Link>
                    </div>
                </div>
                <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0 lg:mt-9">
                    <Splide :has-track="false" aria-label="" :options="{ rewind: true, perPage: 4, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 3 } } }" v-if="discounted">
                        <SplideTrack>
                            <SplideSlide v-for="(productGroup, index) in splidedDiscounted" :key="index" class="flex">
                                <category-box v-for="(product, index) in productGroup" :product="product" :new-container-classes="`w-full`" :auth="auth" />
                            </SplideSlide>
                        </SplideTrack>
                        <div class="splide__arrows">
                            <button class="splide__arrow splide__arrow--prev !-left-9">
                                <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                          transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                            <button class="splide__arrow splide__arrow--next !-right-9">
                                <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                    <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                          transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                                </svg>
                            </button>
                        </div>
                    </Splide>
                    <div v-else>
                        <loader :active="true" message="Please wait 5 seconds" />
                    </div>
                </div>
                <div class="flex-1 ml-2 flex md:hidden justify-center">
                    <Link href="/indirimli-urunler" class="cursor-pointer border-3 rounded-full py-1.5 px-2 text-center mb-4 w-32 bg-white hover:bg-kbgreen hover:text-white font-santralextrabold text-sm"> Tümünü Gör</Link>
                </div>
            </div>
        </section>
        <section class="flex flex-wrap mx-auto justify-center items-center relative z-40 w-full mt-12" v-if="false">
            <div class="w-full mts:w-4/12">
                <Link :href="'/kategoriler/elektrikli-motor'">
                    <picture>
                        <source srcset="../../images/category-banner/honda.webp" type="image/webp" />
                        <img class="w-full mts:pr-4 mt-4" src="../../images/category-banner/honda.png" alt="" />
                    </picture>
                </Link>
            </div>
            <div class="w-full mts:w-4/12">
                <Link :href="'/kategoriler/elektrikli-motor'">
                    <picture>
                        <source srcset="../../images/category-banner/slience.webp" type="image/webp" />
                        <img class="w-full mts:pr-4 mt-4" src="../../images/category-banner/slience.png" alt="" />
                    </picture>
                </Link>
            </div>
            <div class="w-full mts:w-4/12">
                <Link :href="'/kategoriler/elektrikli-motor'">
                    <picture>
                        <source srcset="../../images/category-banner/vespa.webp" type="image/webp" />
                        <img class="w-full mts:pl-4 mt-4" src="../../images/category-banner/vespa.png" alt="" />
                    </picture>
                </Link>
            </div>
        </section>

        <section class="mt-6 md:mt-4 py-1 md:py-9 bg-[#f8f8f8]" v-if="false">
            <div class="text-2xl md:text-3xl my-2 lg:my-0 mx-0 lg:mx-4 self-center text-center w-full md:w-auto">Markalar</div>
            <div class="w-full flex flex-wrap mt-8 relative lg:max-w-none o">
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/vespa">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/1.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/piaggio">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/2.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/silence">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/3.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/kymco">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/4.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/honda">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/5.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/xev">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/6.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/mg">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/7.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/byd">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/8.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/tesla">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/9.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/citroen">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/10.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/jeep">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/11.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="#">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-32" src="../../images/category-banner/brands/12.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="#">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/13.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="#">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/14.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="#">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/15.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="#">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/16.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="#">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/17.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/xiaomi">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/18.png" alt="" loading="lazy" />
                    </Link>
                </div>
                <div class="w-1/2 mts:w-1/3 lg:w-1/6 flex justify-center items-center h-24">
                    <Link href="/marka/rks">
                        <img class="grayscale hover:grayscale-0 transition-all ease-in-out duration-300 max-h-28" src="../../images/category-banner/brands/19.png" alt="" loading="lazy" />
                    </Link>
                </div>
            </div>
        </section>
        <section class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto my-12" id="institutionalForm" v-if="false">
            <div class="rounded-2lg w-full mts:px-4 mt-4 items-center bg-kbblue border-2 hover:border-transparent shadow-institutional hover:shadow-searchshadow border-kbblue">
                <div class=" w-full h-full flex flex-col justify-center items-center rounded-2lg p-4 min-h-[160px]">
                    <form class="w-full  mx-auto my-6" action="#">
                        <div class="w-full flex justify-center items-center mb-5" v-if="Object.entries(errors).length > 0">
                            <div class="flex whitespace-nowrap self-center flex-col p-4 px-12 border-2 border-kbred rounded-2lg">
                                <div class="text-sm mx-auto text-white my-1"><span class="text-kbred">*</span> Gerekli alanları doldurunuz</div>
                            </div>
                        </div>
                        <div class="flex flex-wrap relative w-full">
                            <div class="w-full lg:w-1/3 lg:pr-2 group relative mb-5 ts:mb-7">
                                <input
                                    ref="input"
                                    id="fullName"
                                    required=""
                                    type="text"
                                    name="fullName"
                                    v-model="kiralamobilForm.fullName"
                                    autocomplete="off"
                                    class="!py-2 !leading-none w-full h-12 rounded-lg border-2 border-bordergray placeholder:text-xs text-lg tracking-wider placeholder:text-placeholdergray focus:border-acordion-green hover:border-kbblue focus:outline-none focus:ring-kbgreen group-hover:placeholder-white peer"
                                />
                                <label
                                    for="fullName"
                                    class="transform transition-all absolute top-4 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-xs peer-valid:text-xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-kbgray group-focus-within:text-white peer-valid:text-white"
                                >
                                    İsim, Soyisim*</label>
                                <div class="text-sm mx-auto text-white my-1" v-if="Object.entries(errors).length > 0 && errors.fullName"><span class="text-kbred">*</span>{{ errors.fullName }}</div>

                            </div>
                            <div class="w-full lg:w-1/3 lg:pr-2 relative group mb-5 ts:mb-7">
                                <input
                                    v-maska
                                    data-maska="(5##) ### ## ##"
                                    id="telephone"
                                    class="!py-2 !leading-none w-full h-12 rounded-lg border-2 border-bordergray placeholder:text-xs text-lg tracking-wider placeholder:text-placeholdergray focus:border-acordion-green hover:border-kbblue focus:outline-none focus:ring-kbgreen group-hover:placeholder-white peer"
                                    required
                                    type="text"
                                    placeholder=""
                                    name="telno"
                                    autocomplete="off"
                                    v-model="kiralamobilForm.gsm"
                                />
                                <label
                                    for="telephone"
                                    class="transform transition-all absolute top-4 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-xs peer-valid:text-xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-kbgray group-focus-within:text-white peer-valid:text-white"
                                >Telefon*</label>
                                <div class="text-sm mx-auto text-white my-1" v-if="Object.entries(errors).length > 0 && errors.gsm"><span class="text-kbred">*</span>{{ errors.gsm }}</div>

                            </div>
                            <div class="w-full lg:w-1/3 lg:pr-2 relative group mb-5 ts:mb-7">
                                <input
                                    id="email"
                                    type="email"
                                    v-model="kiralamobilForm.email"
                                    placeholder="E-posta"
                                    autocomplete="off"
                                    required=""
                                    class="!py-2 w-full h-12 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white"
                                />
                                <label
                                    for="email"
                                    class="transform transition-all absolute top-4 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-xs peer-valid:text-xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-kbgray group-focus-within:text-white peer-valid:text-white"
                                >E-Posta*</label>
                                <div class="text-sm mx-auto text-white my-1" v-if="Object.entries(errors).length > 0 && errors.email"><span class="text-kbred">*</span>{{ errors.email }}</div>

                            </div>
                            <div class="w-full lg:w-1/3 lg:pr-2 group relative mb-5 ts:mb-7">
                                <input

                                    id="city"
                                    required=""
                                    type="text"
                                    name="city"
                                    v-model="kiralamobilForm.city"
                                    autocomplete="off"
                                    class="!py-2 !leading-none w-full h-12 rounded-lg border-2 border-bordergray placeholder:text-xs text-lg tracking-wider placeholder:text-placeholdergray focus:border-acordion-green hover:border-kbblue focus:outline-none focus:ring-kbgreen group-hover:placeholder-white peer"
                                />
                                <label
                                    for="city"
                                    class="transform transition-all absolute top-4 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-xs peer-valid:text-xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-kbgray group-focus-within:text-white peer-valid:text-white"
                                >
                                    Şehir*</label>
                                <div class="text-sm mx-auto text-white my-1" v-if="Object.entries(errors).length > 0 && errors.city"><span class="text-kbred">*</span>{{ errors.city }}</div>

                            </div>
                            <div class="w-full lg:w-1/3 lg:pr-2 relative group mb-5 ts:mb-7">
                                <input
                                    id="brand"
                                    name="brand"
                                    type="text"
                                    v-model="kiralamobilForm.brand"
                                    placeholder="Marka/Model*"
                                    autocomplete="off"
                                    required=""
                                    class="!py-1.5 w-full h-12  peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white"
                                />
                                <label
                                    for="brand"
                                    class="transform transition-all absolute top-4 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-xs peer-valid:text-xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-kbgray group-focus-within:text-white peer-valid:text-white"
                                >
                                    Marka/Model*</label>
                                <div class="text-sm mx-auto text-white my-1" v-if="Object.entries(errors).length > 0 && errors.brand"><span class="text-kbred">*</span>{{ errors.brand }}</div>
                            </div>
                            <div class="w-full lg:w-1/3 lg:pr-2 relative group mb-5 ts:mb-7">
                                <input
                                    id="amount"
                                    name="amount"
                                    type="number"
                                    v-model="kiralamobilForm.amount"
                                    placeholder="Adet*"
                                    autocomplete="off"
                                    required=""
                                    class="!py-1.5 w-full h-12  peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white"
                                />
                                <label
                                    for="amount"
                                    class="transform transition-all absolute top-4 group-focus-within:top-0 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-xs peer-valid:text-xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-kbgray group-focus-within:text-white peer-valid:text-white"
                                >
                                    Adet*</label>
                                <div class="text-sm mx-auto text-white my-1" v-if="Object.entries(errors).length > 0 && errors.amount"><span class="text-kbred">*</span>{{ errors.amount }}</div>
                            </div>
                            <div class="w-full lg:w-1/3 lg:pr-2 relative group mb-5 ts:mb-7">
                                <select v-model="kiralamobilForm.leasePeriod" required
                                        autocomplete="off"
                                        class="h-12 w-full px-2 py-2 rounded-lg border border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="leaseType"
                                >
                                    <option disabled selected>Kiralama Süresi</option>
                                    <option value="12">12 Ay</option>
                                    <option value="24">24 Ay</option>
                                    <option value="36">36 Ay</option>
                                </select>
                                <div class="text-sm mx-auto text-white my-1" v-if="Object.entries(errors).length > 0 && errors.leasePeriod"><span class="text-kbred">*</span>{{ errors.leasePeriod }}</div>

                            </div>
                            <div class="w-full lg:w-1/3 lg:pr-2 relative group mb-5 ts:mb-7">
                                <select v-model="kiralamobilForm.leaseType" required
                                        autocomplete="off"
                                        class="h-12 w-full px-2 py-2 rounded-lg border border-bordergray focus:border-acordion-green focus:outline-none focus:ring-acordion-green text-sm text-black tracking-wider" name="leaseType"
                                >
                                    <option disabled selected>Kiralama Tipi</option>
                                    <option value="Bireysel">Bireysel</option>
                                    <option value="Kurumsal">Kurumsal</option>
                                </select>
                                <div class="text-sm mx-auto text-white my-1" v-if="Object.entries(errors).length > 0 && errors.solutions"><span class="text-kbred">*</span>{{ errors.leaseType }}</div>

                            </div>

                            <div class="w-full text-center">
                                <Link :data="kiralamobilForm" :href="route('sendBusinessRequest')" method="post" class="font-santralextrabold border-2 border-transparent bg-white text-base lg:text-lg text-black rounded-full py-2 px-4 md:px-12 self-center font-santralextrabold hover:bg-black hover:border-kbblue hover:text-white transition-all ease-in-out duration-300">Gönder</Link>
                            </div>
                        </div>

                    </form>
                </div>

            </div>
        </section>


        <section class="flex flex-wrap mx-auto justify-center items-center relative z-40 w-full mt-12">
            <div class="w-full mts:w-6/12 ">
                <picture>
                    <source srcset="../../images/category-banner/marka-alba.webp" type="image/webp" />
                    <source srcset="../../images/category-banner/marka-alba.png" type="image/png" />
                    <img src="../../images/category-banner/marka-alba.png" alt="" />
                </picture>
            </div>
            <div class="w-full mts:w-6/12 mts:pl-6">
                <div class="my-4 text-4xl font-santralregular relative z-40">
                    Keşfetmenin Yeni Yolu: <br> Alba Explorer
                </div>
                <div class="font-santralregular text-base text-black">
                    Güçlü motoru, uzun menzili ve konforuyla Alba Explorer, her rotayı bir maceraya dönüştürür. Şimdi sürdürülebilir ulaşımda bir adım öne geçin.
                </div>
                <div class="my-4">
                    <div class="bg-black max-w-[200px] text-white text-sm lg:text-base py-2 px-6 rounded-full mt-2 hover:opacity-[90%] whitespace-nowrap leading-tight text-center">
                        <a class="font-santralextrabold whitespace-nowrap leading-tight text-center" href="/marka/alba">Ürünlere Git</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

</template>
