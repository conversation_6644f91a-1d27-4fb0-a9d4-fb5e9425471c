<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";

export default ({
    components: {
        Link,
        Head,
        UserMenu
    },
    layout: Layout

});
</script>

<template>
    <Head title="Tekno Girişim Rozeti | Kiralabunu" />

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="top-banner relative z-40 mt-8 w-full mb-10">
            <div class="flex flex-col w-full rounded-lg my-10">
                <h2 class="text-3xl font-santralextrabold mb-0 text-center">Tekno Girişim Rozeti</h2>
            </div>
            <picture>
                <source srcset="../../images/about/tekno-girisim-rozeti.webp">
                <img class="" src="../../images/about/tekno-girisim-rozeti.png" />
            </picture>
        </section>
    </div>
</template>
