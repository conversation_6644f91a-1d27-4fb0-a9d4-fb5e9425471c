<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";

export default {
    components: {
        <PERSON>,
        Head,
    },
    props: {
        order: Object,
    },
    created() {
        this.$inertia.on("navigate", (event) => {
            console.log("teşekk<PERSON>r navigate", event.detail.page.url);

            let cartProducts = [];
            this.order.data.order.items.map((item) => {
                let product = {};
                product.item_id = item.id;
                product.product_id = item.product_id;
                product.item_name = item.product.attribute_data.name?.tr;
                product.price = item.total;
                // product.item_brand = item.product.brand.name;
                // product.item_category = item.collections[0]?.attribute_data?.name?.tr;
                // product.item_category2 = item.collections[1]?.attribute_data?.name?.tr ?? "";
                // product.item_comments = item.reviews.total;
                // product.item_rating = item.id;
                // (product.item_color = item.variants[0]?.options[0] ?? ""), (product.index = 0);
                product.discount = 0;
                product.item_variant = item.plan?.name;
                product.quantity = item.quantity;
                cartProducts.push(product);
            });

            if (event.detail.page.url.startsWith("/siparis-basarili")) {
                dataLayer.push({
                    event: "purchase",
                    ecommerce: {
                        transaction_id: this.order.data.order.order_number,
                        value: this.order.data.order.total,
                        tax: this.order.data.order.tax_amount,
                        shipping: 0.0,
                        currency: "TRY",
                        coupon: null,
                        shipping_tier: "Address",
                        payment_type: "Visa", // or Mastercard
                        items: cartProducts,
                    },
                });

                gtag("event", "purchase", {
                    allow_custom_scripts: true,
                    value: this.order.data.order.total,
                    transaction_id: this.order.data.order.order_number,
                    send_to: "DC-8726756/sales/kiral0+transactions",
                });
            }
        });
    },
    layout: Layout,
};
</script>

<template>

    <Head title="Kiralama Başarılı" />
    <main class="my-6 max-w-tablet lg:max-w-7xl mt-12 mx-auto">
        <section class="mt-6 mb-12 flex justify-center">
            <div class="w-10/12 lg:w-5/12 pl-3.5 pt-2">
                <div class="flex flex-col justify-center items-center mb-10">
                    <div class="mb-10">
                        <img class="max-w-[392px]" src="../../images/siparis-basarili.jpg" alt="" />
                    </div>
                    <h1 class="p-1 text-2xl lg:text-3xl font-medium lg:font-bold text-center text-black box-border whitespace-no-wrap mb-3 lg:mb-4">
                        <span class="text-kbgreen">“{{ order.data.order.order_number }}”</span> kiralaman<br />
                        başarıyla oluşturuldu.
                    </h1>
                    <p class="p-0 text-base text-center text-kbgray box-border whitespace-no-wrap mb-5">
                        Siparişinin durumunu
                        <Link href="/kiralamalar" class="text-black font-bold">kiralamalarım</Link>
                        bölümü altından kontrol edebilirsin.
                    </p>
                    <Link href="/" class="bg-black text-white rounded-full py-2 px-4 self-center text-lg font-bold">Anasayfaya Dön</Link>
                </div>
            </div>
        </section>
    </main>
</template>
