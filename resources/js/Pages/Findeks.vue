<script>
import { <PERSON>, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";

export default {
    components: {
        <PERSON>,
        Head
    },
    data() {
        return {};
    },
    methods: {
        toogleBlock(block) {
            block.showBlock = !block.showBlock;
        }
    },
    layout: Layout
};
</script>

<template>

    <Head title="Kimlik ve Findeks Raporu" />

    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <!--        <section class="flex justify-center items-center flex-col w-full mb-12">-->
        <!--            <div class="flex justify-center items-center mb-10">-->
        <!--                <div class="flex justify-center items-center bg-kbgreen rounded-full w-28 h-28">-->
        <!--                    <svg xmlns="http://www.w3.org/2000/svg" width="43.015" height="78.955" viewBox="0 0 43.015 78.955">-->
        <!--                        <g id="Group_4141" data-name="Group 4141" transform="translate(0 0)">-->
        <!--                            <path-->
        <!--                                id="Path_3349"-->
        <!--                                data-name="Path 3349"-->
        <!--                                d="M473.847,415.2H472.2a5.587,5.587,0,0,1-5.581-5.581v-1.387a17.076,17.076,0,0,1,1.934-8.548,44.963,44.963,0,0,1,5.417-7.278,32.075,32.075,0,0,0,3.683-4.439,7.467,7.467,0,0,0,1.163-4.13c0-3.445-2.245-5.121-6.863-5.121a27.868,27.868,0,0,0-7.564,1.135,6.368,6.368,0,0,1-8.116-6.123v-2.807a6.246,6.246,0,0,1,3.685-5.725,36.563,36.563,0,0,1,27-.235,21.4,21.4,0,0,1,9.074,6.877,16.563,16.563,0,0,1,3.255,10.039,16.327,16.327,0,0,1-3.147,10.04,57.393,57.393,0,0,1-8.8,8.966,62.707,62.707,0,0,0-5.642,5.315,9.811,9.811,0,0,0-2.346,4.367,5.566,5.566,0,0,1-5.5,4.636Z"-->
        <!--                                transform="translate(-456.268 -362.491)"-->
        <!--                                fill="#fff"-->
        <!--                            />-->
        <!--                            <path id="Path_3350" data-name="Path 3350"-->
        <!--                                  d="M480.951,518.1h-.218a10.344,10.344,0,0,1,0-20.688h.218a10.344,10.344,0,0,1,0,20.688Z"-->
        <!--                                  transform="translate(-464.291 -439.141)" fill="#fff" />-->
        <!--                        </g>-->
        <!--                    </svg>-->
        <!--                </div>-->
        <!--            </div>-->
        <!--            <button class="bg-black text-white rounded-full py-2 px-4 self-center text-2xl">Sıkça Sorulan Sorular-->
        <!--            </button>-->
        <!--        </section>-->
        <section class="mt-6 flex flex-col items-center justify-center mb-20">
            <div class="text-2xl font-semibold text-center mb-7">Kimlik ve Findeks Raporu</div>
            <div class="flex justify-between w-full lg:w-full py-4 rounded-xl mb-4">
                <div>
                    <p class="text-sm text-justify">
                        Sitemiz <a href="https://kiralabunu.com">www.kiralabunu.com </a> talep ve ilgilerinizi daha iyi
                        analiz ederek en doğru hizmeti sunmak amacıyla bazı kişisel bilgilerinizi üyelik işlemi
                        sırasında (İsim, soy isim, telefon numarası, T.C. Kimlik Numarası, kredi kartı bilgileri,
                        e-posta adresi, kimlik görüntüsü, finansal durumunuz ve kimlik görüntüsünde ibraz edilen
                        bilgiler) talep etmektedir. Kiralabunu veri tabanında toplanan bu veriler; kampanya çalışmaları,
                        daha iyi müşteri hizmetleri sunabilmek ya da size yönelik özel promosyon ve indirimlerin
                        yapılabilmesi için şirketimiz bünyesinde kullanılmaktadır. Ayrıca sitemiz <a href="https://kiralabunu.com">www.kiralabunu.com </a>
                        üzerinden yapmış olduğunuz işlemlere ait istatistiksel veriler tarafımızdan analiz edilmekte ve
                        bilgi güvenliği çerçevesinde saklanmaktadır. <br>
                    </p>

                </div>
            </div>

        </section>
    </main>
</template>
