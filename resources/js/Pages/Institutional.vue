<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { vMaska } from "maska";
import { Disclosure, DisclosureButton, DisclosurePanel } from "@headlessui/vue";
import { TransitionRoot, TransitionChild, Dialog, DialogPanel, DialogTitle } from "@headlessui/vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Disclosure,
        DisclosureButton,
        DisclosurePanel,
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle
    },
    layout: Layout,
    directives: { maska: vMaska },
    data() {
        return {
            blocks: [
                {
                    title: "Kiralabunu Kurumsal hangi işletmeler içindir?",
                    content:
                        "                    <p class='px-4 mt-5 text-sm font-santralregular'><PERSON><PERSON><PERSON><PERSON>, teknoloji ürünleri ve donanı<PERSON>ı kiralamak isteyen her türlü işletmeye hizmet vermektedir. İşletmeler, 10 adet ve üzeri cihaz kiralamalarında kurumsal avantajlardan faydalanırlar. Kiralabunu Kurumsal, tüm Türkiye'ye hizmet verir.</p>\n",
                    showBlock: true
                },
                {
                    title: "Kiralabunu Kurumsal avantajları nelerdir?",
                    content:
                        "               <p class=' px-5 text-sm mt-5 '>Nakit akışı optimizasyonu: </p>\n" +
                        "               <ul class='ml-3 list-outside text-sm mt-2 space-y-4 pr-5'>\n" +
                        "                    <li class='font-santralregular'>Kiralamak bütçenize hareket alanı sağlar. Kiraladığınız cihazların kira bedellerini aylık düzende kolayca ödeyebilirsiniz. Böylece satın alma durumunda tek seferde yapılacak yüksek maliyetlerden kurtulursunuz.</li>\n" +
                        "                </ul>" +
                        "                <p class=' px-5 text-sm mt-5 '>Vergi avantajı: </p>\n" +
                        "                <ul class='ml-3 list-outside text-sm mt-2 space-y-4 pr-5'>\n" +
                        "                    <li class='font-santralregular'>Kira bedelleri, operasyonel kira bedeli olarak faturalandırılmaktadır. Cihazlar için ödenen kira bedellerinin tamamı, cari maliyetler altında işletme gideri olarak kabul edilmektedir. Kira bedelleri, işletmenizin matrahından düşülebilir ve vergi yükünüzü azaltır.  Satın aldığınızda durum oldukça farklıdır. Satın aldığınızda ekipmanınız sabit kıymetlerinizin bir parçası haline gelir.Bunlar bir yatırım olarak ve yalnızca zaman içinde amortismana tabi tutulabilir. Kira bedellerine ilişkin faturada yer alan KDV, işletmenin KDV yükünden düşülebilir.</li>\n" +
                        "                </ul>" +
                        "                <p class=' px-5 text-sm mt-5 '>Hasar onarım garantisi: </p>\n " +
                        "                <ul class='ml-3 list-outside text-sm mt-2 space-y-4 pr-5'>\n" +
                        "                    <li class='font-santralregular'>Kiralabunu tarafından kiralanan bütün cihazlar hasar ve arızalara karşı sigorta kapsamındadır.</li>\n" +
                        "                </ul>" +
                        "                <p class=' px-5 text-sm mt-5 '>Esnek sözleşmeler: </p>\n " +
                        "                <ul class='ml-3 list-outside text-sm mt-2 space-y-4 pr-5'>\n" +
                        "                    <li class='font-santralregular'>İşletmeler kiraladıkları ürünü kira süresinin sonunda yenileyebilir veya iade edebilir ya da kira süresi içerisinde satın alabilirler.</li>\n" +
                        "                </ul>" +
                        "                <p class=' px-5 text-sm mt-5 '>Son teknoloji yeni cihazlar: </p>\n " +
                        "                <ul class='ml-3 list-outside text-sm mt-2 space-y-4 pr-5'>\n" +
                        "                    <li class='font-santralregular'>Kiralabunu Kurumsal'dan kiralanan bütün cihazlar yenidir.</li>\n" +
                        "                </ul>" +
                        "                <p class=' px-5 text-sm mt-5 '>Sürdürlebilirlik: </p>\n " +
                        "                <ul class='ml-3 list-outside text-sm mt-2 space-y-4 pr-5'>\n" +
                        "                    <li class='font-santralregular'>Teknoloji ürünlerini kiralayan işletmeler döngüsel ekonomiye katkı sağlar. Kiralabunu ile bir cihaz azami ömür süresi boyunca kullanılır, kullanım ömrü bittikten sonra çevre dostu yöntemlerle dönüştürülür.</li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: "Kiralabunu ile Kurumsal kiralama nasıl yapılır?",
                    content:
                        "                <p class='px-4 mt-5 text-sm font-santralregular'>\n" +
                        "                    Kiralama yapmak isteyen işletmelerin talebi alınır, fiyat ve sözleşme koşulları ile ilgili bilgi verilir ve işletme talebi değerlendirilmeye alınır.Talep değerlendirme 3 iş gününde tamamlanır. Kiralama talebi onaylanan işletmeler ile Kurumsal Kira Sözleşmesi imzalanır ve ürünleri temin edilir. \n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Kira süreleri nedir?",
                    content: "                <p class='px-4 mt-5 text-sm font-santralregular'>\n" + "                    Kiralabunu Kurumsal'da kira süreleri işletmenin talebine göre 6 ay, 12 ay, 24 ay ve 36 ay olarak belirlenebilir. \n" + "                </p>",
                    showBlock: false
                }
            ],
            readMoreBlocks: [
                {
                    title: " Kiralabunu Kurumsal Kiralama hizmetine neler dahildir? ",
                    content:
                        "                <p class='px-4 mt-5 text-sm font-santralregular'>\n" +
                        "                    Kiralabunu Kurumsal üzerinden kiralanan bütün cihazlar sigortalı ve hasar onarım kapsamındadır. Kiralanan cihazların kullanımları sırasında oluşan hasarların asgari %70'i Kiralabunu tarafından karşılanır. Seçilen hizmet paketinin içeriği dahilinde hasar karşılama oranı artabilir.\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Ödemeler nasıl yapılır?",
                    content: "                <p class='px-4 mt-5 text-sm font-santralregular'>\n" + "                    Ödemeler kira süresi boyunca aylık olarak yapılır.\n" + "                </p>",
                    showBlock: false
                },
                {
                    title: " Hasar onarım kapsamına ne dahildir?",
                    content:
                        "                <p class=' px-5 text-sm mt-5 '>Kiralabunu Kurumsal hasar onarım kapsamı dahilinde aşağıdaki hizmetler sunulur: </p>\n " +
                        "                <ul class=\"ml-3 list-outside px-5 text-sm mt-5 space-y-4 pr-5\">\n" +
                        "                    <li class='font-santralregular'>İhtiyaç duyulan durumlarında uzaktan teknik servis hizmeti;</li>\n" +
                        "                    <li class='font-santralregular'>Arıza durumlarında ürünün bakım onarımlarının Kiralabunu tarafından gerçekleştirilmesi;</li>\n" +
                        "                    <li class='font-santralregular'>Servis süresi boyunca kesintisiz teknoloji hizmet kapsamı dahilinde ikame ürün desteği;</li>\n" +
                        "                    <li class='font-santralregular'>Arızalanan cihazın bakımı için gerekli bütün kargo süreçlerinin takibi;</li>\n" +
                        "                    <li class='font-santralregular'>Ürünlerin gönderim, iade ve hasar süreçlerinde oluşan bütün kargo masraflarının karşılanması.</li>\n" +
                        "                    <li class='font-santralregular'>Envanter takibinin yapılması.</li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: "Sigorta kapsamına neler dahildir? ",
                    content:
                        "                <p class='px-4 mt-5 text-sm font-santralregular'>\n" +
                        "                    Cihazlarda oluşan arızalar Kiralabunu tarafından karşılanır. Cihazlarda kasıtlı eylem (fırlatma, çekiç ile vurma, vs.) dışında oluşan hasarları asgari yüzde 70 oranında Kiralabunu tarafından karşılanır. Cihazın ev veya araba gibi kilitli ve bunların kapalı alanlarından çalınması durumlarının polis tutanağı ile belgelendirilmesi durumunda cihaz bedelinin yüzde 70'i Kiralabunu tarafından karşılanır. Tamir edilemeyecek kadar hasar almış veya hasar tamir bedeli cihaz bedelini aşan cihazların bedelleri kiracı tarafından karşılanır. \n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Hangi ürünler kiralanabilir?",
                    content: "                <p class='px-4 mt-5 text-sm font-santralregular'>\n" + "                    kiralabunu.com'da yer alan bütün cihazlar kurumsal müşteriler tarafından kiralanabilir. kiralabunu.com'da yer almayan cihazların talep doğrultusunda kiralanması mümkündür. \n" + "                </p>",
                    showBlock: false
                },
                {
                    title: "Kira süresinin sonunda ne olur?",
                    content:
                        "                <p class=' px-5 text-sm mt-5 '>Kira süresinin sonunda kiralanan cihazlar,</p>\n " +
                        "                <ul class=\"ml-3 list-outside px-5 text-sm mt-5 space-y-4\">\n" +
                        "                    <li class='font-santralregular'>Yeni cihazlar ile model artışına gidilebilir.</li>\n" +
                        "                    <li class='font-santralregular'>İade edilebilir;</li>\n" +
                        "                    <li class='font-santralregular'>Satın alınabilir.</li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: "Kiralanan cihazların mülkiyeti kimde oluyor?",
                    content:
                        "                <p class='px-4 mt-5 text-sm font-santralregular'>\n" +
                        "                    Kiralama yapmaya karar verdiğinizde Kiralabunu cihazları sizin için satın alır. Kira süresi içinde ilgili cihazların kullanım hakkına sahip olursunuz. Tüm süre zarfında cihazlar Kiralabunu mülkiyetindedir. Ürünlerin mülkiyeti bu esnada başka bir kuruma ya da kullanıcıya devredilemez. İsterseniz kira süresi içerisinde kira sözleşmesi kapsamındaki cihazları satın alabilir ve mülkiyeti kurumunuza geçirebilirsiniz. \n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Verilerim Kiralabunu ile güvende mi?",
                    content:
                        "                <p class='px-4 mt-5 text-sm font-santralregular'>\n" +
                        "                    Kesinlikle! Veri güvenliği en önem verdiğimiz konuların başında geliyor. Aksi kararlaştırılmadığı durumda tüm cihazlar yeni cihaz olarak teslim ediliyor. Cihazlar iade edilirken kurumunuz tarafından hafızaları silinerek sıfırlanıyor. Buna ek olarak cihazlar ekibimize ulaştığında hafızlarının temizlendiği, cihazlarda veri bırakılmadığı kontrol ediliyor. Aksi bir durumda kurumunuz ile irtibata geçilerek \Veri Güvenlik Politikası\ işletiliyor. \n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Kiralamak için depozito ödemem gerekiyor mu?",
                    content: "                <p class='px-4 mt-5 text-sm font-santralregular'>\n" + "                    Hayır, yalnızca aylık kira ödemeleri yapmanız yeterli. \n" + "                </p>",
                    showBlock: false
                },
                {
                    title: "Kiraladığımız ürünler çalınırsa ya da kaybolursa ne olur?",
                    content:
                        "                <p class='px-4 mt-5 text-sm font-santralregular'>\n" +
                        "                    Kiralanan ürünlerin çalınması veya kaybolması durumunda kural olarak kiralayan cihazın güncel perakende bedelinden sorumludur. Bunun tek istisnası cihazın ev veya araba gibi kilitli ve kapalı alanlarından çalınmasıdır. Bu durumlarının polis tutanağı ile belgelendirilmesi durumunda cihaz bedelinin yüzde 70'i Kiralabunu tarafından karşılanır. \n" +
                        "                </p>",
                    showBlock: false
                }
            ],
            hiddenbtn1: true,
            isOpen: this.$page.props.success.success != null ? true : false,
            businessForm: this.$inertia.form({
                name: null,
                email: null,
                gsm: null,
                firmName: null,
                message: null,
                solutions: "Çözümlerimiz"
            })
        };
    },
    props: {
        errors: { type: Object, default: false }
    },
    methods: {
        toogleBlock(block) {
            block.showBlock = !block.showBlock;
        },
        closeModal() {
            this.isOpen = false;
        },
        openModal() {
            this.isOpen = true;
        }
    },
    watch: {
        "$page.props.success": function(val) {
            this.isOpen = val.success != null ? true : false;
            if (val.success == "Mesajınız başarıyla gönderildi.") {
                this.businessForm.reset();
            }
        }
    }
};
</script>

<template>

    <Head title="Kurumsal" />
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-50">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black bg-opacity-25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95" enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                        <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white px-5 py-5 z-70 text-left align-middle shadow-xl transition-all flex flex-col justify-center items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 62.051 62.051">
                                <g id="Group_4884" data-name="Group 4884" transform="translate(-73.036 -250.295)">
                                    <circle id="Ellipse_199" data-name="Ellipse 199" cx="31.025" cy="31.025" r="31.025" transform="translate(73.036 250.295)" fill="#70d44b" />
                                    <path id="Path_3299" data-name="Path 3299"
                                          d="M351.649,476.232a3.45,3.45,0,0,1-2.761-1.381l-8.464-11.286a3.451,3.451,0,0,1,5.522-4.141l5.538,7.383,14.223-21.334a3.451,3.451,0,1,1,5.743,3.829l-16.928,25.393a3.453,3.453,0,0,1-2.776,1.536Z"
                                          transform="translate(-251.82 -178.764)" fill="#fff" />
                                </g>
                            </svg>
                            <DialogTitle>
                                <h2 class="text-base font-semibold text-center mb-2 mt-2">Başarılı</h2>
                            </DialogTitle>
                            <p class="text-xs font-kiralabunuthin ts:text-sm text-center">Formunuz Başarıyla Gönderildi.
                            </p>
                            <!--                            <div class="mt-4">-->
                            <!--                                <button-->
                            <!--                                    type="button"-->
                            <!--                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"-->
                            <!--                                    @click="closeModal"-->
                            <!--                                >-->
                            <!--                                    Got it, thanks!-->
                            <!--                                </button>-->
                            <!--                            </div>-->
                            <div class="absolute top-2 right-5 cursor-pointer p-2" @click="closeModal">x</div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>

    <div class="max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="top-banner relative z-40 w-full mt-6">
            <img class="hidden ts:block rounded-lg" src="../../images/kurumsal-gorsel.png" />
            <img class="hidden mts:block ts:hidden rounded-lg" src="../../images/kurumsal-gorsel.png" />
            <img class="block mts:hidden rounded-lg" src="../../images/institutional_mobil.jpeg" />
        </section>
        <section class="mt-12 flex flex-row">
            <div class="flex flex-col w-full rounded-lg">
                <h2 class="text-2xl ts:text-3xl font-semibold text-center mb-1 ts:mb-5">İşletmeniz için kiralamaya
                    başlayın</h2>
                <div class="text-base text-center text-kbgray mb-5">Talebini gönder, 1 iş günü içerisinde seni arayalım.
                </div>
                <form class="w-full max-w-kbmobile mts:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto" action="#">
                    <div class="w-full flex justify-center items-center mb-5" v-if="Object.entries(errors).length > 0">
                        <div class="flex mt-4 whitespace-nowrap self-center flex-col p-4">
                            <div class="text-sm mx-auto text-black my-1" v-for="error in errors">{{ error }}</div>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full lg:w-1/2 lg:pr-2">
                            <div class="group relative">
                                <input id="fullname" required="" type="text" name="AdSoyad*" autofocus="" v-model="businessForm.name"
                                       class="!py-2 !leading-none w-full mb-5 ts:mb-7 rounded-lg border-2 border-bordergray placeholder:text-xs text-lg tracking-wider placeholder:text-placeholdergray focus:border-acordion-green hover:border-kbblue focus:outline-none focus:ring-kbgreen group-hover:placeholder-white peer" />
                                <label for="fullname"
                                       class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-0 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">
                                    İsim, Soyisim*
                                </label>
                            </div>
                            <div class="relative group">
                                <input v-maska data-maska="(5##) ### ## ##" id="telephone"
                                       class="!py-2 peer placeholder-transparent w-full mb-5 ts:mb-7 rounded-lg border-2 border-bordergray placeholder:text-sm placeholder:text-placeholdergray focus:border-acordion-green focus:outline-none focus:ring-kbgreen text-base tracking-wider"
                                       required type="text" placeholder="" name="telno" v-model="businessForm.gsm" />
                                <label for="telephone"
                                       class="transform transition-all absolute top-3 group-focus-within:top-1 peer-valid:top-1 left-3 group-focus-within:left-4 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Telefon*</label>
                            </div>
                        </div>
                        <div class="w-full lg:w-1/2 lg:pl-2 relative group">
                            <div class="relative group">
                                <input id="email" type="email" v-model="businessForm.email" placeholder="E-posta" autocomplete="off" required=""
                                       class="!py-2 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />
                                <label for="email"
                                       class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">E-Posta*
                                </label>
                            </div>
                            <div class="relative group">
                                <input id="website" type="text" v-model="businessForm.firmName" placeholder="Websitesi*" autocomplete="off" required=""
                                       class="!py-1.5 w-full mb-5 ts:mb-7 peer placeholder-transparent border-2 rounded-lg border-kb-light-grey hover:border-kbblue focus:border-acordion-green focus:outline-none focus:ring-kbgreen group-hover:placeholder-white" />
                                <label for="website"
                                       class="absolute left-3 top-0 transform transition-all peer-placeholder-shown:translate-y-0 peer-focus:-translate-y-full -translate-y-full text-black text-2xs peer-placeholder-shown:text-sm peer-placeholder-shown:text-placeholdergray peer-placeholder-shown:top-3 peer-placeholder-shown:left-4 peer-focus:top-0 peer-focus:left-4 peer-focus:text-black peer-focus:text-2xs">
                                    Şirket İsmi*
                                </label>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" :name="businessForm.solutions" value="Çözümlerimiz">

                    <div class="relative group w-full text-right">
                        <textarea
                            class="peer w-full bg-white mb-5 ts:mb-7 rounded-lg border-2 border-bordergray placeholder:text-xs placeholder:text-placeholdergray text-lg tracking-wider placeholder:mt-2 h-20 focus:border-acordion-green hover:border-kbblue focus:outline-none focus:ring-kbgreen group-hover:placeholder-white"
                            required="" name="Mesaj" v-model="businessForm.message" cols="20" rows="10"></textarea>
                        <label for="fullname"
                               class="transform transition-all absolute top-3 group-focus-within:top-0 peer-valid:top-0 left-7 group-focus-within:left-7 group-focus-within:px-1 pl-0 text-sm group-focus-within:text-2xs peer-valid:text-2xs group-focus-within:-translate-y-full peer-valid:-translate-y-full group-focus-within:pl-0 peer-valid:pl-0 text-placeholdergray group-focus-within:text-black peer-valid:text-black">Mesaj</label>
                        <Link :href="route('sendBusinessRequest')" method="post" class="bg-black text-white rounded-full py-2 px-4 self-center font-bold w-full lg:w-24" :data="businessForm" preserve-scroll>Gönder</Link>
                    </div>
                </form>
            </div>
        </section>
    </div>

    <section class="flex flex-wrap mx-auto max-w-kbmobile mts:max-w-tablet ts:max-w-ts 2xl:max-w-7xl w-full mt-12">
        <div class="font-bold text-2xl ts:text-3xl self-center text-center w-full">Kiralabunu Kurumsal'ın Avantajları
        </div>

        <div class="flex flex-wrap mt-4 w-full">
            <div class="w-full mts:w-1/2 lg:w-1/4 mts:pl-4 mt-4">
                <div class="w-full h-full flex flex-col justify-center items-center border-2 hover:border-transparent shadow-institutional hover:shadow-searchshadow border-kbblue rounded-2lg p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full items center justify-center mb-1">
                        <img class="grayscale h-18" src="../../images/kurumsalikon1.png" />
                    </div>
                    <div class="text-sm mts:text-base w-full mt-2 text-center">Aylık avantajlı ödemelerle bütçeni etkin
                        kullan
                    </div>
                </div>
            </div>
            <div class="w-full mts:w-1/2 lg:w-1/4 mts:pl-4 mt-4">
                <div class="w-full h-full flex flex-col justify-center items-center border-2 hover:border-transparent shadow-institutional hover:shadow-searchshadow border-kbblue rounded-2lg p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full justify-center items-center mb-1">
                        <img class="grayscale h-18" src="../../images/kurumsalikon2.png" alt="" />
                    </div>
                    <div class="text-sm mts:text-base w-full mt-2 text-center">Kira faturalarını giderleştirerek vergi
                        avantajı sağla
                    </div>
                </div>
            </div>
            <div class="w-full mts:w-1/2 lg:w-1/4 mts:pl-4 mt-4">
                <div class="w-full h-full flex flex-col justify-center items-center border-2 hover:border-transparent shadow-institutional hover:shadow-searchshadow border-kbblue rounded-2lg p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full justify-center items-center mb-1">
                        <svg class="grayscale w-full h-18 mx-auto" id="Layer_2" viewBox="0 0 148.11 137.57">
                            <g id="Layer_1-2">
                                <g>
                                    <g>
                                        <rect class="cls-1re" x="0" y="79.35" width="20.8" height="54.14" rx="2.9" ry="2.9"></rect>
                                        <path class="cls-2" d="m11.13,128.75c-2.61.49-4.85-1.74-4.37-4.36.27-1.46,1.45-2.65,2.91-2.93,2.62-.5,4.89,1.76,4.38,4.37-.28,1.46-1.46,2.63-2.92,2.91Z">
                                        </path>
                                        <path class="cls-2"
                                              d="m108.64,106.42l30.07-12.44c5.69-2.35,10.59,4.85,6.22,9.19-.07.07-.14.14-.22.21-.37.35-.78.65-1.23.89l-57.41,31.1c-1.22.66-2.65.85-4,.53l-56.9-13.48c-2.56-.61-4.36-2.89-4.36-5.51v-20.81c0-2.65,1.83-4.94,4.41-5.53l25.11-5.69c.93-.21,1.9-.18,2.81.08l46.7,13.37c.08.02.17.05.25.07.96.23,7.9,2.06,8.51,7.26.06.52.02,1.04-.08,1.55-.4,2.06-2.06,7.24-8.54,5.59l-26.8-6.4">
                                        </path>
                                    </g>
                                    <path class="cls-2re"
                                          d="m114.96,42.8c0-.5-.01-.99-.04-1.48-.04-.94.57-1.79,1.49-2.03l3.35-.88c1.07-.28,1.71-1.37,1.43-2.44l-2.07-7.91c-.28-1.07-1.37-1.71-2.44-1.43l-3.52.92c-.88.23-1.82-.15-2.25-.96-1.12-2.09-2.45-4.04-3.96-5.85-.6-.71-.6-1.75-.03-2.48l2.28-2.95c.68-.87.51-2.13-.36-2.81l-6.47-4.99c-.87-.67-2.13-.51-2.81.36l-2.34,3.04c-.56.73-1.55.99-2.38.61-2.1-.95-4.3-1.7-6.59-2.22-.9-.2-1.52-1.02-1.52-1.94v-3.87c0-1.1-.9-2-2-2h-8.18c-1.1,0-2,.9-2,2v3.87c0,.92-.63,1.74-1.53,1.94-2.12.48-4.17,1.16-6.12,2.01-.84.37-1.82.09-2.37-.64l-2.31-3.08c-.66-.88-1.92-1.06-2.8-.4l-6.54,4.9c-.88.66-1.06,1.92-.4,2.8l2.24,2.99c.56.74.54,1.78-.07,2.48-.93,1.07-1.79,2.2-2.58,3.38-.51.75-1.48,1.05-2.33.73l-3.45-1.27c-1.04-.38-2.18.15-2.57,1.19l-2.83,7.68c-.38,1.04.15,2.19,1.19,2.57l3.29,1.21c.88.32,1.42,1.21,1.29,2.14-.22,1.57-.34,3.17-.34,4.8,0,.22,0,.45,0,.67.02.92-.59,1.74-1.49,1.97l-3.32.87c-1.07.28-1.71,1.37-1.43,2.44l2.07,7.92c.28,1.07,1.37,1.71,2.44,1.43l3.13-.82c.9-.24,1.85.17,2.27,1,1.12,2.2,2.46,4.28,4.01,6.18.58.71.56,1.73,0,2.45l-1.93,2.5c-.68.87-.51,2.13.36,2.81l6.47,5c.87.67,2.13.51,2.81-.36l1.85-2.4c.57-.73,1.57-1,2.4-.6,2.23,1.06,4.6,1.88,7.06,2.44.9.2,1.52,1.02,1.52,1.94v3c0,1.1.9,2,2,2h8.18c1.1,0,2-.9,2-2v-3c0-.92.62-1.74,1.52-1.94,2.29-.52,4.5-1.27,6.6-2.22.84-.38,1.84-.11,2.4.63l1.81,2.43c.66.89,1.92,1.07,2.8.4l6.55-4.9c.88-.66,1.06-1.92.4-2.8l-1.89-2.52c-.55-.73-.55-1.75.04-2.45.97-1.16,1.87-2.39,2.69-3.68.5-.78,1.49-1.09,2.36-.77l3,1.11c1.04.38,2.19-.15,2.57-1.19l2.82-7.67c.38-1.04-.15-2.19-1.19-2.57l-3.17-1.17c-.86-.32-1.4-1.17-1.3-2.08.16-1.32.24-2.67.24-4.03Zm-22.17,12.18c-18,13.39-38.17-6.8-24.77-24.78.12-.17.28-.32.45-.45,17.99-13.38,38.16,6.79,24.77,24.78-.12.17-.28.32-.45.45Z">
                                    </path>
                                </g>
                            </g>
                        </svg>
                    </div>

                    <div class="text-sm mts:text-base w-full mt-2 text-center">Hasar onarım garantisi ile minimum %70
                        hasar kapsamından faydalan
                    </div>
                </div>
            </div>
            <div class="w-full mts:w-1/2 lg:w-1/4 mts:pl-4 mt-4">
                <div class="w-full h-full flex flex-col justify-center items-center border-2 hover:border-transparent shadow-institutional hover:shadow-searchshadow border-kbblue rounded-2lg p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full justify-center items-center mb-1">
                        <img class="grayscale h-18" src="../../images/kurumsalikon3.png" alt="" />
                    </div>
                    <div class="text-sm mts:text-base w-full mt-2 text-center">Envanter yönetimi ile operasyonunu
                        rahatlat
                    </div>
                </div>
            </div>
            <div class="w-full mts:w-1/2 lg:w-1/4 mts:pl-4 mt-4">
                <div class="w-full h-full flex flex-col justify-center items-center border-2 hover:border-transparent shadow-institutional hover:shadow-searchshadow border-kbblue rounded-2lg p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full justify-center items-center mb-1">
                        <img class="grayscale h-18" src="../../images/kurumsalikon4.png" alt="" />
                    </div>
                    <div class="text-sm mts:text-base w-full mt-2 text-center">İkame ürün ile kesintisiz teknolojiyi
                        yakala
                    </div>
                </div>
            </div>
            <div class="w-full mts:w-1/2 lg:w-1/4 mts:pl-4 mt-4">
                <div class="w-full h-full flex flex-col justify-center items-center border-2 hover:border-transparent shadow-institutional hover:shadow-searchshadow border-kbblue rounded-2lg p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full justify-center items-center mb-1">
                        <img class="grayscale h-18" src="../../images/kurumsalikon5.png" alt="" />
                    </div>
                    <div class="text-sm mts:text-base w-full mt-2 text-center">Esnek kira süreleri ile ihtiyacına göre
                        kirala
                    </div>
                </div>
            </div>
            <div class="w-full mts:w-1/2 lg:w-1/4 mts:pl-4 mt-4">
                <div class="w-full h-full flex flex-col justify-center items-center border-2 hover:border-transparent shadow-institutional hover:shadow-searchshadow border-kbblue rounded-2lg p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full justify-center items-center mb-1">
                        <img class="grayscale h-18" src="../../images/kurumsalikon6.png" alt="" />
                    </div>
                    <div class="text-sm mts:text-base w-full mt-2 text-center">Kiralayarak en güncel teknolojiyi kullan,
                        işinin verimini arttır
                    </div>
                </div>
            </div>
            <div class="w-full mts:w-1/2 lg:w-1/4 mts:pl-4 mt-4">
                <div class="w-full h-full flex flex-col justify-center items-center border-2 hover:border-transparent shadow-institutional hover:shadow-searchshadow border-kbblue rounded-2lg p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full justify-center items-center mb-1">
                        <img class="grayscale h-18" src="../../images/kurumsalikon7.png" alt="" />
                    </div>
                    <div class="text-sm mts:text-base w-full mt-2 text-center">Döngüsel ekonomiye katkı sağla,
                        sürdürülebilirliği destele
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="flex flex-wrap mx-auto max-w-kbmobile mts:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-8 w-full">
        <div class="font-bold text-2xl ts:text-3xl self-center text-center w-full">Kiralabunu Kurumsal İş Partnerleri
        </div>
        <div class="text-base mt-2 text-textgray self-center text-center w-full">Kiralabunu Kurumsal abonesi iş
            partnerlerinin bir kısmı
        </div>
        <div class="w-full flex flex-wrap justify-center mt-8 relative">
            <div class="w-1/2 mts:w-1/3 lg:w-1/4 flex justify-center items-center">
                <img class="grayscale p-4 mts:p-10 ts:p-0 ts:w-32" src="../../images/brands/danone-logo.png" />
            </div>
            <div class="w-1/2 mts:w-1/3 lg:w-1/4 flex justify-center items-center">
                <img class="grayscale p-4 mts:p-10 ts:p-0 ts:w-32" src="../../images/brands/a101_logo.png" />
            </div>
            <div class="w-1/2 mts:w-1/3 lg:w-1/4 flex justify-center items-center">
                <img class="grayscale p-0 mts:p-10 ts:p-0 ts:w-40" src="../../images/brands/sekerbank-logo.png" />
            </div>
            <div class="w-1/2 mts:w-1/3 lg:w-1/4 flex justify-center items-center">
                <img class="grayscale p-4 mts:p-10 ts:p-0 ts:w-32" src="../../images/brands/qualogo.png" />
            </div>
            <!--            <div class="w-1/2 mts:w-1/3 lg:w-1/4 flex justify-center items-center">-->
            <!--                <img class="grayscale p-4 mts:p-10 ts:w-32" src="../../images/brands/sabanci_holding_logo.png">-->
            <!--            </div>-->
            <div class="w-1/2 mts:w-1/3 lg:w-1/4 flex justify-center items-center">
                <img class="grayscale p-4 mts:p-10 ts:p-0 ts:w-32" src="../../images/brands/hopi-logo.png" />
            </div>
            <div class="w-1/2 mts:w-1/3 lg:w-1/4 flex justify-center items-center">
                <img class="grayscale p-0 mts:p-10 ts:p-0 ts:w-40" src="../../images/brands/teknosa_logo.png" />
            </div>
            <div class="w-1/2 mts:w-1/3 lg:w-1/4 flex justify-center items-center">
                <img class="grayscale p-0 mts:p-6 ts:p-0 ts:w-52" src="../../images/brands/sabancidx.png" />
            </div>
            <div class="w-1/2 mts:w-1/3 lg:w-1/4 flex justify-center items-center">
                <img class="grayscale p-0 mts:p-6 ts:p-0 ts:w-52" src="../../images/brands/kopas-logo.png" />
            </div>
        </div>
    </section>

    <section class="flex flex-wrap mx-auto max-w-kbmobile mts:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-8 w-full">
        <div class="font-bold text-2xl ts:text-3xl self-center text-center w-full mx-[-15px] w-[115%] md:mx-0 md:w-full">
            Kurumlar Neler Kiralıyorlar?
        </div>
        <div class="flex flex-wrap mt-4 w-full">
            <div class="w-full mts:w-1/2 lg:w-1/4 mts:px-8 mt-4">
                <div onclick="window.location='/kategoriler/laptoplar'"
                     class="cursor-pointer w-full h-full bg-kb-mid-grey flex flex-col justify-center items-center border-0 border-bordergray hover:border-transparent shadow-searchshadow hover:shadow-aboutshadow rounded-xl p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full items center justify-center mb-1">
                        <picture>
                            <source srcset="../../images/institutional/computers.webp" type="image/webp" />
                            <img class="h-18" src="../../images/institutional/computers.png" alt="" loading="lazy" />
                        </picture>
                    </div>
                    <div class="text-xl w-full mt-2 text-center">Bilgisayarlar</div>
                </div>
            </div>
            <div class="w-full mts:w-1/2 lg:w-1/4 mts:px-8 mt-4">
                <div onclick="window.location='/kategoriler/telefon/'"
                     class="cursor-pointer w-full h-full bg-kb-mid-grey flex flex-col justify-center items-center border-0 border-bordergray hover:border-transparent shadow-searchshadow hover:shadow-aboutshadow rounded-xl p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full items center justify-center mb-1">
                        <picture>
                            <source srcset="../../images/institutional/smartphones.webp" type="image/webp" />
                            <img class="h-18" src="../../images/institutional/smartphones.png" alt="" loading="lazy" />
                        </picture>
                    </div>
                    <div class="text-xl w-full mt-2 text-center">Telefonlar</div>
                </div>
            </div>
            <div class="w-full mts:w-1/2 lg:w-1/4 mts:px-8 mt-4">
                <div onclick="window.location='/kategoriler/laptoplar/monitor'"
                     class="cursor-pointer w-full h-full bg-kb-mid-grey flex flex-col justify-center items-center border-0 border-bordergray hover:border-transparent shadow-searchshadow hover:shadow-aboutshadow rounded-xl p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full items center justify-center mb-1">
                        <picture>
                            <source srcset="../../images/institutional/monitors.webp" type="image/webp" />
                            <img class="h-18" src="../../images/institutional/monitors.png" alt="" loading="lazy" />
                        </picture>
                    </div>
                    <div class="text-xl w-full mt-2 text-center">Monitörler</div>
                </div>
            </div>
            <div class="w-full mts:w-1/2 lg:w-1/4 mts:px-8 mt-4">
                <div onclick="window.location='/kategoriler/ses-muzik/kulaklik'"
                     class="cursor-pointer w-full h-full bg-kb-mid-grey flex flex-col justify-center items-center border-0 border-bordergray hover:border-transparent shadow-searchshadow hover:shadow-aboutshadow rounded-xl p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full items center justify-center mb-1">
                        <picture>
                            <source srcset="../../images/institutional/headphones.webp" type="image/webp" />
                            <img class="h-18" src="../../images/institutional/headphones.png" alt="" loading="lazy" />
                        </picture>
                    </div>
                    <div class="text-xl w-full mt-2 text-center">Giyilebilir Teknoloji</div>
                </div>
            </div>
        </div>
        <div class="flex flex-wrap mt-4 w-full">
            <div onclick="window.location='kategoriler/kiralamobil/'" class="w-full mts:w-1/2 lg:w-1/4 mts:px-8 mt-4">
                <div class="cursor-pointer w-full h-full bg-kb-mid-grey flex flex-col justify-center items-center border-0 border-bordergray hover:border-transparent shadow-searchshadow hover:shadow-aboutshadow rounded-xl p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full items center justify-center mb-1">
                        <picture>
                            <source srcset="../../images/institutional/elektrikli-arac-cozumleri.webp" type="image/webp" />
                            <img class="h-18" src="../../images/institutional/elektrikli-arac-cozumleri.png" alt="" loading="lazy" />
                        </picture>
                    </div>
                    <div class="text-xl w-full mt-2 text-center">Elektrikli Araç Çözümleri</div>
                </div>
            </div>
            <div onclick="window.location='/kategoriler/tablet/'" class="w-full mts:w-1/2 lg:w-1/4 mts:px-8 mt-4">
                <div class="cursor-pointer w-full h-full bg-kb-mid-grey flex flex-col justify-center items-center border-0 border-bordergray hover:border-transparent shadow-searchshadow hover:shadow-aboutshadow rounded-xl p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full items center justify-center mb-1">
                        <picture>
                            <source srcset="../../images/institutional/tablet.webp" type="image/webp" />
                            <img class="h-18" src="../../images/institutional/tablet.png" alt="" loading="lazy" />
                        </picture>
                    </div>
                    <div class="text-xl w-full mt-2 text-center">Tablet</div>
                </div>
            </div>
            <div onclick="window.location='kategoriler/elektrikli-ev-aletleri/'" class="w-full mts:w-1/2 lg:w-1/4 mts:px-8 mt-4">
                <div class="cursor-pointer w-full h-full bg-kb-mid-grey flex flex-col justify-center items-center border-0 border-bordergray hover:border-transparent shadow-searchshadow hover:shadow-aboutshadow rounded-xl p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full items center justify-center mb-1">
                        <picture>
                            <source srcset="../../images/institutional/ofis-elektronigi.webp" type="image/webp" />
                            <img class="h-18" src="../../images/institutional/ofis-elektronigi.png" alt="" loading="lazy" />
                        </picture>
                    </div>
                    <div class="text-xl w-full mt-2 text-center">Ofis Elektroniği</div>
                </div>
            </div>
            <div onclick="window.location='kategoriler/kiralamobil/'" class="w-full mts:w-1/2 lg:w-1/4 mts:px-8 mt-4">
                <div class="cursor-pointer w-full h-full bg-kb-mid-grey flex flex-col justify-center items-center border-0 border-bordergray hover:border-transparent shadow-searchshadow hover:shadow-aboutshadow rounded-xl p-4 mts:pt-8 min-h-[200px]">
                    <div class="flex w-full items center justify-center mb-1">
                        <picture>
                            <source srcset="../../images/institutional/mobilite.webp" type="image/webp" />
                            <img class="h-18" src="../../images/institutional/mobilite.png" alt="" loading="lazy" />
                        </picture>
                    </div>
                    <div class="text-xl w-full mt-2 text-center">Mobilite</div>
                </div>
            </div>
        </div>

    </section>

    <div class="flex justify-center items-center flex-col w-full mb-12 mt-8">
        <div class="max-w-kbmobile w-full mts:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto flex flex-col justify-center items-center mb-10">
            <div class="text-2xl ts:text-3xl text-center font-bold">Sıkça Sorulan Sorular</div>
            <div class="mt-6 flex w-full ts:w-3/4 flex-col justify-center items-center">
                <div :class="[block.showBlock ? ' py-4 rounded-xl mb-4 w-full px-4' : 'flex justify-between items-center w-full py-4 px-4 rounded-full mb-4']" v-for="(block, index) in blocks" :key="index">
                    <div class="flex justify-between w-full" :class="[!block.showBlock ? '' : '']">
                        <div>
                            <p @click="toogleBlock(block)" class="text-base font-santralextrabold ts:text-xl text-left cursor-pointer">
                                {{ block.title }}
                            </p>
                        </div>
                        <div class="rounded-full w-8 h-8 bg-white" :class="[block.showBlock ? ' rounded-full ' : ' ']" @click="toogleBlock(block)">
                            <button type="button" class="flex justify-center items-center w-8 h-8" v-if="!block.showBlock">
                                <svg xmlns="http://www.w3.org/2000/svg" width="23.872" height="21.712" viewBox="0 0 23.872 21.712">
                                    <g id="Group_119" data-name="Group 119" transform="translate(0)">
                                        <path id="Path_19" data-name="Path 19" d="M1.6,21.712A1.528,1.528,0,0,1,0,20.261V1.451A1.528,1.528,0,0,1,1.6,0a1.528,1.528,0,0,1,1.6,1.451v18.81A1.528,1.528,0,0,1,1.6,21.712Z" transform="translate(10.341 0)"
                                              fill="#000000" />
                                        <path id="Path_20" data-name="Path 20" d="M1.451,23.872A1.528,1.528,0,0,1,0,22.277V1.6A1.528,1.528,0,0,1,1.451,0,1.528,1.528,0,0,1,2.9,1.6V22.277A1.528,1.528,0,0,1,1.451,23.872Z"
                                              transform="translate(23.872 9.405) rotate(90)" fill="#000000" />
                                    </g>
                                </svg>
                            </button>
                            <button type="button" class="flex justify-center items-center w-8 h-8" v-if="block.showBlock">
                                <svg id="Group_119" data-name="Group 119" xmlns="http://www.w3.org/2000/svg" width="22.332" height="2.985" viewBox="0 0 22.332 2.985">
                                    <path id="Path_19" data-name="Path 19" d="M1.492,21.712A1.472,1.472,0,0,1,0,20.261V1.451A1.472,1.472,0,0,1,1.492,0,1.472,1.472,0,0,1,2.985,1.451v18.81A1.472,1.472,0,0,1,1.492,21.712Z"
                                          transform="translate(22.022) rotate(90)" fill="#000" />
                                    <path id="Path_20" data-name="Path 20" d="M1.451,22.332A1.472,1.472,0,0,1,0,20.84V1.492A1.472,1.472,0,0,1,1.451,0,1.472,1.472,0,0,1,2.9,1.492V20.84A1.472,1.472,0,0,1,1.451,22.332Z"
                                          transform="translate(22.332 0.042) rotate(90)" fill="#000" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <span v-html="block.content" v-if="block.showBlock"></span>
                </div>
                <Disclosure as="div" v-slot="{ open }" class="w-full">
                    <DisclosurePanel class="w-full">
                        <div :class="[block.showBlock ? ' py-4 rounded-xl mb-4 w-full px-4' : 'flex justify-between items-center w-full py-4 px-4 rounded-full mb-4']" v-for="(block, index) in readMoreBlocks" :key="index">
                            <div class="flex justify-between w-full" :class="[!block.showBlock ? '' : '']">
                                <div>
                                    <p @click="toogleBlock(block)" class="text-base font-santralextrabold ts:text-xl text-left cursor-pointer">
                                        {{ block.title }}
                                    </p>
                                </div>
                                <div class="rounded-full w-8 h-8 bg-white" :class="[block.showBlock ? ' rounded-full ' : ' ']" @click="toogleBlock(block)">
                                    <button type="button" class="flex justify-center items-center w-8 h-8" v-if="!block.showBlock">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="23.872" height="21.712" viewBox="0 0 23.872 21.712">
                                            <g id="Group_119" data-name="Group 119" transform="translate(0)">
                                                <path id="Path_19" data-name="Path 19" d="M1.6,21.712A1.528,1.528,0,0,1,0,20.261V1.451A1.528,1.528,0,0,1,1.6,0a1.528,1.528,0,0,1,1.6,1.451v18.81A1.528,1.528,0,0,1,1.6,21.712Z"
                                                      transform="translate(10.341 0)" fill="#000000" />
                                                <path id="Path_20" data-name="Path 20" d="M1.451,23.872A1.528,1.528,0,0,1,0,22.277V1.6A1.528,1.528,0,0,1,1.451,0,1.528,1.528,0,0,1,2.9,1.6V22.277A1.528,1.528,0,0,1,1.451,23.872Z"
                                                      transform="translate(23.872 9.405) rotate(90)" fill="#000000" />
                                            </g>
                                        </svg>
                                    </button>
                                    <button type="button" class="flex justify-center items-center w-8 h-8" v-if="block.showBlock">
                                        <svg id="Group_119" data-name="Group 119" xmlns="http://www.w3.org/2000/svg" width="22.332" height="2.985" viewBox="0 0 22.332 2.985">
                                            <path id="Path_19" data-name="Path 19" d="M1.492,21.712A1.472,1.472,0,0,1,0,20.261V1.451A1.472,1.472,0,0,1,1.492,0,1.472,1.472,0,0,1,2.985,1.451v18.81A1.472,1.472,0,0,1,1.492,21.712Z"
                                                  transform="translate(22.022) rotate(90)" fill="#000" />
                                            <path id="Path_20" data-name="Path 20" d="M1.451,22.332A1.472,1.472,0,0,1,0,20.84V1.492A1.472,1.472,0,0,1,1.451,0,1.472,1.472,0,0,1,2.9,1.492V20.84A1.472,1.472,0,0,1,1.451,22.332Z"
                                                  transform="translate(22.332 0.042) rotate(90)" fill="#000" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <span v-html="block.content" v-if="block.showBlock"></span>
                        </div>
                    </DisclosurePanel>
                    <DisclosureButton class="text-center w-full" @click="hiddenbtn1 = !hiddenbtn1" v-if="hiddenbtn1">
                        <button class="bg-black text-base lg:text-lg text-white rounded-full py-1.5 px-4 self-center font-bold hover:bg-kbgreen">Daha
                            Fazla
                        </button>
                    </DisclosureButton>
                </Disclosure>
            </div>
        </div>
    </div>
</template>
