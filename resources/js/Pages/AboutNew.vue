<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
import UserMenu from "@/Pages/Shared/UserMenu.vue";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/vue-splide";
import "@splidejs/splide/dist/css/themes/splide-default.min.css";
import CategoryBox from "@/Pages/Shared/CategoryBox.vue";
import { Disclosure, DisclosureButton, DisclosurePanel } from "@headlessui/vue";
import Comments from "@/Pages/Shared/Comments.vue";

export default {
    components: {
        Link,
        Head,
        UserMenu,
        Splide,
        SplideSlide,
        SplideTrack,
        CategoryBox,
        Disclosure,
        DisclosureButton,
        DisclosurePanel,
        Comments
    },
    layout: Layout,
    data() {
        return {
            blocks: [
                {
                    title: "Kiralabunu.com nedir?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular \">\n" +
                        "                    <span class='font-santralregular'><PERSON><PERSON><PERSON><PERSON>, istediğin ürünü aylık kira bedeli karşılığında, ihtiyacın olan kira süresi için kiralayabildiğin bir abonelik sistemidir. Satın alarak yüksek maliyetlere katlanmak yerine kiralayarak finansman özgürlüğü ve esnekliği kazanmana yardımcı olur.</span>\n" +
                        "                </p>",
                    showBlock: true
                },
                {
                    title: "Nasıl kiralama yapabilirim?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular \">Kiralabunu.com, çağrı merkezimiz veya anlaşmalı noktalar üzerinden istediğin ürünü online olarak kiralayabilirsin. Sistem basitçe şöyle işliyor:</p>\n" +
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4 font-santralregular \">\n" +
                        "                    <li class='font-santralregular'> Kiralabunu’da her ürün için 3-6-12 bazı ürünler için de 1 ve 18 aylık kiralama seçenekleri vardır. Kiralamaya istediğin ürünü ve kira süreni seçerek başlarsın.</li>\n" +
                        "                    <li class='font-santralregular'>Sepetine attıktan sonra Kiralabunu hesabını oluşturman gerekiyor. Kimlik bilgilerin üyelik adımında otomatik kimlik teyit sistemi ile kontrol edilerek onaylanır.</li>\n" +
                        "                    <li class='font-santralregular'>Üyeliğini oluşturduktan sonra ödeme adımına yönlendirileceksin. Bu aşamada yalnızca 1 aylık kira bedeli tahsil edilir.</li>\n" +
                        "                    <li class='font-santralregular'>Ödemeni gerçekleştirdikten sonra telefonuna findeks onay SMS’i gelecek. Bu SMS’e onay verdiğin durumda kiralama talebin 24 saat içinde incelenir.</li>\n" +
                        "                    <li class='font-santralregular'>Siparişin onaylandığı durumda ürün tedarik ve teslimat süresi başlar; ve 5 iş günü içerisinde ürünün tarafına ulaştırılır. Kira süren ürünü teslim aldığın tarihte başlar. Onaylanamayan kiralama taleplerinde %100 ücret iadesi aynı gün içinde gerçekleştirilir, bankana bağlı olarak 3-5 iş günü içerisinde iaden kartına yansır.</li>\n" +
                        "                    <li class='font-santralregular'> Kiralama süren içinde ilk 3 ay kullandığın ürünü satın alabilirsin.</li>\n" +
                        "                    <li class='font-santralregular'>Kiralama süresi bittikten sonra kira süreni uzatabilir, ürünü yenisiyle değiştirebilir ya da ürünü iade edip kira sözleşmeni sonlandırabilirsin.</li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: "Kiralamak neden avantajlı?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular \">Kiralabunu’da kiralama yapmanın birçok avantajı var. Bazıları,</p>\n" +
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4 font-santralregular\">\n" +
                        "                    <li class='font-santralregular'>İstediğin ürünü Kiralabunu ile satın alma maliyetine katlanmadan istediğin kadar kullanabilirsin.</li>\n" +
                        "                    <li class='font-santralregular'>Kredi kartı limitini doldurmak yok. Kiralabunu ile her ay sadece aylık kira ücretini ödersin, kart limitin sana kalır.</li>\n" +
                        "                    <li class='font-santralregular'>Kiralabunu’da tüm ürünler sigortalı ve hasar onarım garantisine Hasarı, zararı dert etmeden keyifle ürününü kullanırsın.</li>\n" +
                        "                    <li class='font-santralregular'>Hasar durumlarında onarım süreçlerinin hepsini Kiralabunu üstlenir, onarım masraflarının %70’ini Kiralabunu karşılar.</li>\n" +
                        "                    <li class='font-santralregular'>Ön ödeme, gizli ücret, ek masraf yoktur. Esneklik ve kolaylık vardır. Kira ücretin neyse her ay onu ödersin.</li>\n" +
                        "                    <li class='font-santralregular'>Kargo ücretsizdir.</li>\n" +
                        "                    <li class='font-santralregular'>Kira süren içinde kullandığın ürünü istediğin zaman ve istediğin kadar aynı kategoriden başka bir ürünle değiştirebilirmek için başvurabilirsin. Ödeme performansın, kullanım alışkanlıkların ve findeks skorun bir üst pakete geçmek için belirleyici olacaktır. (Yeni ürün taleplerinde yeni findeks sorgusu yapılabilir.)</li>\n" +
                        "                    <li class='font-santralregular'>Kullandığın ürünü test ettin ve sevdin, satın almak istersen kira sürenin başlangıç tarihinden itibaren 3 ay içinde 18 aylık kira ücretini ödeyerek satın alabilirsin.</li>\n" +
                        "                </ul>",
                    showBlock: false
                },
                {
                    title: "Kiralama talepleri hangi durumlarda onaylanmaz?",
                    content:
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4 font-santralregular\">\n" +
                        "                    <li class='font-santralregular'>Üye olurken paylaşılan kimlik bilgileri doğru ya da geçerli değilse</li>\n" +
                        "                    <li class='font-santralregular'>Kimlik bilgileri ödeme yapılan banka kartı ya da kredi kartında yazan isimle uyumlu değilse</li>\n" +
                        "                    <li class='font-santralregular'>Findeks rapor SMS’ine 72 saat boyunca onay verilmediyse</li>\n" +
                        "                    <li class='font-santralregular'>Findeks skoru ve diğer kriterler kiralamak istenen ürün için yeterli değilse</li>\n" +
                        "                    <li class='font-santralregular'>Kira talepleri bu gibi sebeplerle onaylanmamış olabilir. Bu süreçlerin hepsinde Müşteri Hizmetleri ekibimiz sizinle e-posta ya da telefon yoluyla iletişime geçerek bilgilendirme yapar ve eksik bilgilerin tamamlanabilmesi için yardımcı olur.</li>\n" +
                        "                </ul>",
                    showBlock: false
                }
            ],
            readMoreBlocks: [
                {
                    title: "Kiralama talebim onaylanmazsa ne olacak?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Kiralama talebinin onaylanmadığı durumlarda e- posta ile bilgilendirir ve 24 saat içinde %100 ücret iadesini gerçekleştiririz. İade işlemleri bankana bağlı olarak 5-10 iş günü içinde hesabına yansıyacaktır.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Kiralama için ön ödeme yapmam gerekir mi? Toplam kira ücreti kartımdan bloke edilir mi?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Hayır! Kiralabunu’da ön ödeme yapmana gerek yok. Ayrıca toplam kira ücreti kredi kartından bloke edilmez. Sen yalnızca aylık kira bedelini ödersin.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Ürünü teslim alırken neye dikkat etmeliyim?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Ürünü teslim alırken kargo görevlisinin önünde kutuyu açıp içeriğini kontrol etmelisin. Eğer teslim aldığın ürün gönderim sırasında hasar görmüşse tutanak tutulması gerekiyor. Bu gibi durumlarda tutanak örneğini müşteri hizmetleri ekibimize bildirmelisin.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Kira süresi bitince ürünü nasıl teslim edebilirim?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Çok kolay. Kira süren bittiğinde ürünü ilk teslim aldığın durumda tüm aksesuar ve kutusuyla birlikte bize yollamalısın. Ürün bize ulaştığında gerekli kontrolleri sağlıyoruz ve kira sözleşmeni sonlandırıyoruz. Tüm bu süreçlerde Müşteri Hizmetleri ekibimiz seninle iletişimde oluyor.</span>\n" +
                        "                    <br><br><span class='font-santralregular'>İade talebini hesabına giriş yaparak Hesabım > Destek Taleplerim > Talep Oluştur menüsünden ürününü seçerek \"Ürünü iade etmek istiyorum\" seçeneği ile iletebilirsin. İade işlemini talep sonrası tarafına iletilen kargo firma bilgisi ve iade kodu ile yapabilirsin.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Kiraladığım ürünü ne zaman teslim etmem gerekir?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Kiraladığın ürünü kira süresi bitmeden en az bir gün önce kargoya vermiş olmalısın. Kargoya zamanında verilmeyen ürünler için kira bedeli tahsil edilebilir.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Kira süresini uzatabilir miyim?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Tabi ki! Kiralabunu 1, 3, 6, 12 ve bazı ürünlerde 18 aylık kiralama seçenekleri sunar. Dilediğin zaman kira süresini uzatabilir* ve ödediğin aylık kira ücretini azaltabilirsin. (*Maksimum 18 veya 24 aya uzatılabilmektedir.)</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Kiraladığım ürün arızalanırsa ya da hasar görürse ne olacak?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Hasardır olur, sen bunları dert etme ve rahatça ürününü kullan diye Kiralabunu’da tüm ürünler sigortalıdır ve %70 hasar onarım garantisi var. Kullanıcı kaynaklı hasarlarda yetkili servisler tarafından gerçekleştirilen hasar onarım ve yedek parça bedelinin %70’ini Kiralabunu, %30’unu müşteri ödemektedir. Teknik donanım kaynaklı arızalar garanti kapsamındadır.</span><br> \n" +
                        "                    <span class='font-santralregular'>Kiraladığın ürünün arıza ya da hasar durumunda Müşteri Hizmetleri ekibimize bilgi vermen yeterli. Bu gibi durumlarda hemen hasar onarım talebi açıyoruz ve ürünü kargo ile ilgili servis noktasına yönlendiriyoruz. Servis noktamızda hızlıca onarım yapılıyor ve ürünü çalışır durumda sana geri gönderiyoruz.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Arıza ve hasar durumlarında ne ödüyorum?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Ürün arızalarında bir ücret almıyoruz. Ürün hasarlarında, tamir bedelinin %70’ini biz karşılıyoruz. Geri kalan tutar kartınızdan tahsil ediliyor. Oluşan hasarların kazayla olmuş olması önemli. Cihazlara kasten verilen hasarlar maalesef kapsam dışında.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Müşteri hizmetleri ile nasıl iletişime geçebilirim?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Bize <EMAIL> e-posta adresimiz ya da internet sitemizde bulunan canlı yardım üzerinden ulaşabilirsiniz. Aklınıza takılan bir şey olursa bize yazmayı ihmal etmeyin.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Kiraladığım ürün ne durumda oluyor?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Kiralabunu’da bulunan tüm ürünler yeni veya yeni durumdadır. Tüm ürünler tekrar kiralanmadan önce detaylı bir kalite kontrol ve yenileme işleminden geçer. Böylece kiralanan her ürün harika durumda olur. Eğer cihaz beklediğin gibi çıkmazsa bize yazman yeterli, müşteri hizmetlerimiz yardımcı olmaktan memnuniyet duyacaktır.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Cihazın orijinal kutusunu muhafaza etmem gerekir mi?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Kesinlikle evet! Kullanıcılarımızdan ürünleri teslim aldıkları gibi iade etmelerini bekliyoruz. Böylece bütün kullanıcılarımız seninki kadar keyifli bir deneyime sahip olsun.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "İade edeceğim ürünlerden verilerimi ve hesaplarımı silmeli miyim?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Evet, iade işlemini onaylayabilmemiz için her türlü belge, bilgi ve kullanıcı hesaplarının kaldırılması, cihazın fabrika ayarlarına geri getirilmesi gerekiyor. Kullanıcı verilerin korunması için de bu hususu çok önemsiyoruz.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Normal kullanımdan doğan eskimelerden kim sorumlu?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Normal kullanımdan doğan çizilme gibi eskimeleri dert etmenizi istemiyoruz. Telefon bize iade edildikten sonra biz bunları tamir ediyoruz. Daha büyük hasarlar için ise hasar onarım politikamız doğrultusunda onarım masrafının %70’ini biz karşılıyoruz.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Hasarlı bir ürün teslim alırsam ne yapmalıyım?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Müşteri hizmetlerimiz deneyimini iyileştirmek için var! Bu gibi durumlarda kullanıcı tarafından tespit edilen hasarların ürün teslim alındıktan beş (5) gün içerisinde bize bildirilmesi gerekiyor.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Kiralamak istediğim ürün Kiralabunu'da yok, ne yapabilirim?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Bize yazabilirsin. En ilgi çeken ürünlerimizden bazıları kullanıcılarımızın önerileriyle siteye eklendi. Örneğin projeksiyon cihazı ve VR gözlük. Senin ilgini çeken, kiralamak istediğin ürün neyse lütfen bize yaz. Ürün araştırma ekibimiz senden mail aldığına çok sevinecek.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Kapıda ödeme yapabilir miyim?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Ne yazık ki kapıda ödeme seçeneği bulunmuyor. Yalnızca kredi kartı ve banka kartı ile ödeme yapılabilir.</span>\n" +
                        "                </p>",
                    showBlock: false
                },
                {
                    title: "Kiraladığım ürünü satın alabilir miyim?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular\">\n" +
                        "                    <span class='font-santralregular'>Kiralamış olduğunuz ürünü satın almak istediğiniz tarihte güncel bir ürün bedeli tarafınıza teklif edilecektir. <br><br>Teklif ürün bedeli ürünün fiyatı, kullanım süresi, ödenen toplam kira, hasar onarım işlemleri ve piyasa güncel ürün bedelleri göz önünde bulundurularak oluşturulmaktadır. Farklılık gösterebilmektedir.</span>\n" +
                        "                </p>",
                    showBlock: false
                }
            ],
            hiddenbtn1: true,
            isOpen: false
        };
    },
    methods: {
        toogleBlock(block) {
            block.showBlock = !block.showBlock;
        },
        splidedArray(arr, size) {
            return arr.reduce((acc, e, i) => (i % size ? acc[acc.length - 1].push(e) : acc.push([e]), acc), []);
        }
    },
    props: {
        allProducts: Object
    },
    computed: {
        splidedAllProducts() {
            return this.splidedArray(this.allProducts.items.data, 1);
        }
    }
};
</script>

<template>

    <Head title="Nasıl Çalışır" />
    <div class=" max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto">
        <section class="top-banner relative z-40 mt-8 w-full">
            <img class="hidden ts:block" src="../../images/<EMAIL>" />
            <img class="hidden mts:block ts:hidden" src="../../images/<EMAIL>" />
            <img class="block mts:hidden" src="../../images/<EMAIL>" />
            <Link href="/kategoriler/tum-urunler" class="hidden ts:block absolute bottom-[33%] left-[9%] bg-black font-santralextrabold text-base text-white rounded-full py-1.5 px-8 self-center font-bold hover:bg-kbgreen border-2 border-white">
                Hemen Kirala
            </Link>
            <Link href="/kategoriler/tum-urunler" class="block mts:hidden absolute bottom-[30%] left-[9%] bg-black font-santralextrabold text-base text-white rounded-full py-1.5 px-8 self-center font-bold hover:bg-kbgreen border-2 border-white">
                Hemen Kirala
            </Link>
            <Link href="/kategoriler/tum-urunler"
                  class="hidden mts:block ts:hidden absolute bottom-[20%] left-[9%] bg-black font-santralextrabold text-base text-white rounded-full py-1.5 px-8 self-center font-bold hover:bg-kbgreen border-2 border-white">
                Hemen Kirala
            </Link>
        </section>
        <section class="bg-white">
            <div class="relative">
                <div class="flex flex-wrap mx-auto justify-center items-start relative z-40 w-full mt-[50px]">
                    <div class="w-full mts:w-4/12 ">
                        <img src="../../images/<EMAIL>" class="w-10/12" alt="" />
                    </div>
                    <div class="w-full mts:w-8/12">
                        <div class="mt-4 mts:mt-0 text-4xl font-bold relative z-40">Seç</div>
                        <ul class="relative list-outside z-40 mt-3 mts:mt-6 lg:mt-10 text-checkoutgray text-base leading-tight">
                            <li class="mt-5 pl-6">Yüzlerce ürün arasından istediğini seç</li>
                            <li class="mt-5 pl-6">Kiralamak istediğin süreyi belirle.</li>
                            <li class="mt-5 pl-6">Siparişini oluşturduğunda ilk ay kira ücreti alınır.</li>
                            <li class="mt-5 pl-6">Otomatik kimlik teyidi ve findeks rapor onayının ardından kiralaman
                                onaylanır ve ürünün ücretsiz kargo ile sana gönderilir.
                            </li>
                            <li class="mt-5 pl-6">Kiralama talebi onaylanmazsa alınan ilk ay kira ücreti iade edilir.
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="flex flex-wrap mx-auto justify-center items-start relative z-40 w-full mt-[40px] mts:mt-[100px]">
                    <div class="w-full mts:w-4/12 ">
                        <img src="../../images/<EMAIL>" class="w-10/12" alt="" />
                    </div>
                    <div class="w-full mts:w-8/12">
                        <div class="mt-4 mts:mt-0 text-4xl font-bold relative z-40">Kullan</div>
                        <ul class="relative list-outside z-40 mt-3 mts:mt-6 lg:mt-10 text-checkoutgray text-base leading-tight">
                            <li class="mt-5 pl-6">
                                İlk ay kira ücreti kiralama yaptığın gün alınır. Kira süren ise ürünü teslim aldığın gün
                                başlar. Sonraki ayların kira ücretleri de siparişini oluşturduğun günlerde kartından
                                otomatik olarak alınır. Kargoda geçen süre kira sürenin sonuna eklenir.
                            </li>
                            <li class="mt-5 pl-6">Ürünü kullanırken hasar olursa hasar onarım süreçlerini biz üstlenir,
                                hasar onarım masraflarının %70’ini biz karşılarız.
                            </li>
                            <li class="mt-5 pl-6">Kiraladığın ürünü daha uzun süre kullanmak istersen de istediğin zaman
                                kira süreni uzatabilir ya da kiraladığın ürünü satın alabilirsin.
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="flex flex-wrap mx-auto ts:justify-center items-start relative z-40 w-full mt-[40px] mts:mt-[100px]">
                    <div class="w-full mts:w-4/12 ">
                        <img src="../../images/<EMAIL>" class="w-10/12" alt="" />
                    </div>
                    <div class="w-full mts:w-8/12">
                        <div class="mt-4 mts:mt-0 text-4xl font-bold relative z-40">İade Et</div>
                        <ul class="relative list-outside z-40 mt-3 mts:mt-6 lg:mt-10 text-checkoutgray text-base leading-tight">
                            <li class="mt-5 pl-6">
                                Kira süren bittiğinde ürünü orijinal kutusu ve aksesuarlarıyla ücretsiz kargo ile geri
                                gönder.
                            </li>
                            <li class="mt-5 pl-6">
                                Her iade edilen ürünün teknik kalite kontrol işlemleri yapılır.
                            </li>
                            <li class="mt-5 pl-6">
                                Kalite kontrol işlemleri onaylandığında kartından çekilen bir sonraki ayın kira ücreti
                                iade edilir.
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <section class="flex flex-wrap mx-auto max-w-kbmobile mts:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-10 w-full">
        <div class="font-bold text-3xl self-center text-center w-full">Müşteri Yorumları</div>
        <div class="w-full flex justify-center mt-1">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="142.533 79.652 189.594 30.345" width="182.594" height="30.345">
                <g id="Group_568" data-name="Group 568" transform="matrix(1, 0, 0, 1, 146.708847, 82.000595)">
                    <g id="star-solid" transform="translate(153.538 -2.432)" fill="none" stroke-linejoin="round">
                        <path
                            d="M28.647,14.4a1.852,1.852,0,0,0-1.076-3.336l-7.506-.284a.192.192,0,0,1-.167-.125L17.3,3.648a1.852,1.852,0,0,0-3.478,0l-2.586,7.031a.192.192,0,0,1-.167.125l-7.506.284a1.852,1.852,0,0,0-1.076,3.336L8.38,19.053a.192.192,0,0,1,.067.2L6.42,26.434a1.852,1.852,0,0,0,2.819,2.043l6.222-4.17a.184.184,0,0,1,.209,0l6.222,4.17a1.829,1.829,0,0,0,2.819-2L22.667,19.27a.183.183,0,0,1,.067-.2Z"
                            stroke="none" />
                        <path
                            d="M 15.56581592559814 4.712684631347656 L 13.11771202087402 11.36995697021484 C 12.81991195678711 12.17903709411621 12.07173156738281 12.74015617370605 11.21163177490234 12.79948616027832 C 11.19094085693359 12.80091667175293 11.17024230957031 12.80201721191406 11.14951133728027 12.80279731750488 L 4.008689880371094 13.07256507873535 L 9.616781234741211 17.48119735717773 C 10.30588150024414 18.02341651916504 10.60516166687012 18.92123794555664 10.37922096252441 19.76847839355469 C 10.37673187255859 19.77779769897461 10.37418174743652 19.7870979309082 10.37156105041504 19.79637718200684 L 8.45207405090332 26.5975456237793 L 14.34182357788086 22.64998245239258 C 14.70442581176758 22.40457725524902 15.12701416015625 22.27497673034668 15.56535148620605 22.27497673034668 C 16.00387954711914 22.27497673034668 16.42667579650879 22.40470314025879 16.78940200805664 22.65032577514648 L 22.66341590881348 26.58734130859375 L 20.74460220336914 19.82059097290039 C 20.4981746673584 18.96349334716797 20.80209732055664 18.03650093078613 21.50568389892578 17.49121475219727 L 27.13162803649902 13.04759216308594 L 19.98955154418945 12.77777671813965 C 19.96883010864258 12.77699661254883 19.9481315612793 12.7758960723877 19.92745208740234 12.77446746826172 C 19.06741142272949 12.71515655517578 18.31924057006836 12.15408706665039 18.02266120910645 11.34850692749023 L 15.56581592559814 4.712684631347656 M 15.56536102294922 2.432346343994141 C 16.3427619934082 2.432346343994141 17.03738021850586 2.917966842651367 17.30434036254883 3.648096084594727 L 19.89824104309082 10.65409660339355 C 19.92435073852539 10.72501754760742 19.98965072631836 10.77400779724121 20.0650520324707 10.77920722961426 L 27.57149124145508 11.06278610229492 C 28.36390113830566 11.07572746276855 29.06021118164062 11.5915470123291 29.30345153808594 12.34580612182617 C 29.54671096801758 13.10007667541504 29.28295135498047 13.92551612854004 28.64740180969238 14.39897727966309 L 22.73400115966797 19.06964683532715 C 22.67178153991699 19.11562728881836 22.64510154724121 19.19569778442383 22.66728210449219 19.26981735229492 L 24.71070098876953 26.47599792480469 C 24.92720031738281 27.2225170135498 24.64986038208008 28.02444648742676 24.0184326171875 28.47771644592285 C 23.38178253173828 28.93046569824219 22.52827453613281 28.93046760559082 21.89161109924316 28.47771644592285 L 15.66961097717285 24.30746650695801 C 15.60686111450195 24.26414680480957 15.52384185791016 24.26414680480957 15.46110153198242 24.30746650695801 L 9.239101409912109 28.47771644592285 C 8.594064712524414 28.9152660369873 7.744083404541016 28.90202713012695 7.112991333007812 28.44457626342773 C 6.481901168823242 27.98713684082031 6.204860687255859 27.18345642089844 6.420021057128906 26.43428802490234 L 8.446750640869141 19.25313568115234 C 8.466501235961914 19.1790771484375 8.440271377563477 19.10036659240723 8.380031585693359 19.0529670715332 L 2.49165153503418 14.42399787902832 C 1.856111526489258 13.95053672790527 1.592351913452148 13.12509727478027 1.835601806640625 12.37083625793457 C 2.078851699829102 11.61656761169434 2.775161743164062 11.10074615478516 3.567581176757812 11.08780670166016 L 11.07401084899902 10.80422782897949 C 11.14940071105957 10.79902648925781 11.21471214294434 10.75003623962402 11.24081230163574 10.67912673950195 L 13.82636070251465 3.648096084594727 C 14.09332084655762 2.917966842651367 14.78795051574707 2.432346343994141 15.56536102294922 2.432346343994141 Z"
                            style="fill: rgb(112, 212, 75); stroke: rgba(112, 212, 75, 0);" />
                    </g>
                    <path id="star-solid-2" data-name="star-solid"
                          d="M28.647,14.4a1.852,1.852,0,0,0-1.076-3.336l-7.506-.284a.192.192,0,0,1-.167-.125L17.3,3.648a1.852,1.852,0,0,0-3.478,0l-2.586,7.031a.192.192,0,0,1-.167.125l-7.506.284a1.852,1.852,0,0,0-1.076,3.336L8.38,19.053a.192.192,0,0,1,.067.2L6.42,26.434a1.852,1.852,0,0,0,2.819,2.043l6.222-4.17a.184.184,0,0,1,.209,0l6.222,4.17a1.829,1.829,0,0,0,2.819-2L22.667,19.27a.183.183,0,0,1,.067-.2Z"
                          transform="translate(114.717 -2.432)" fill="#70d44b" />
                    <path id="star-solid-3" data-name="star-solid"
                          d="M28.647,14.4a1.852,1.852,0,0,0-1.076-3.336l-7.506-.284a.192.192,0,0,1-.167-.125L17.3,3.648a1.852,1.852,0,0,0-3.478,0l-2.586,7.031a.192.192,0,0,1-.167.125l-7.506.284a1.852,1.852,0,0,0-1.076,3.336L8.38,19.053a.192.192,0,0,1,.067.2L6.42,26.434a1.852,1.852,0,0,0,2.819,2.043l6.222-4.17a.184.184,0,0,1,.209,0l6.222,4.17a1.829,1.829,0,0,0,2.819-2L22.667,19.27a.183.183,0,0,1,.067-.2Z"
                          transform="translate(75.896 -2.432)" fill="#70d44b" />
                    <path id="star-solid-4" data-name="star-solid"
                          d="M28.647,14.4a1.852,1.852,0,0,0-1.076-3.336l-7.506-.284a.192.192,0,0,1-.167-.125L17.3,3.648a1.852,1.852,0,0,0-3.478,0l-2.586,7.031a.192.192,0,0,1-.167.125l-7.506.284a1.852,1.852,0,0,0-1.076,3.336L8.38,19.053a.192.192,0,0,1,.067.2L6.42,26.434a1.852,1.852,0,0,0,2.819,2.043l6.222-4.17a.184.184,0,0,1,.209,0l6.222,4.17a1.829,1.829,0,0,0,2.819-2L22.667,19.27a.183.183,0,0,1,.067-.2Z"
                          transform="translate(37.075 -2.432)" fill="#70d44b" />
                    <path id="star-solid-5" data-name="star-solid"
                          d="M28.647,14.4a1.852,1.852,0,0,0-1.076-3.336l-7.506-.284a.192.192,0,0,1-.167-.125L17.3,3.648a1.852,1.852,0,0,0-3.478,0l-2.586,7.031a.192.192,0,0,1-.167.125l-7.506.284a1.852,1.852,0,0,0-1.076,3.336L8.38,19.053a.192.192,0,0,1,.067.2L6.42,26.434a1.852,1.852,0,0,0,2.819,2.043l6.222-4.17a.184.184,0,0,1,.209,0l6.222,4.17a1.829,1.829,0,0,0,2.819-2L22.667,19.27a.183.183,0,0,1,.067-.2Z"
                          transform="translate(-1.746 -2.432)" fill="#70d44b" />
                </g>
                <path id="path-2" data-name="star-solid"
                      d="M 263.007 35.705 C 263.029 35.772 258.516 32.905 259.685 33.722 C 263.176 36.164 259.58 14.045 261.27 14.016 C 260.758 14.078 264.616 20.627 264.59 20.556 L 261.992 13.549 C 261.502 12.21 259.746 11.904 258.832 12.998 C 258.695 13.162 258.587 13.349 258.514 13.549 L 255.928 20.58 C 255.902 20.651 255.836 20.7 255.761 20.705 L 248.255 20.989 C 246.83 21.013 245.965 22.572 246.699 23.794 C 246.823 24.001 246.986 24.181 247.179 24.325 L 253.072 28.954 C 253.132 29.001 253.159 29.08 253.139 29.154 L 251.112 36.335 C 250.72 37.706 251.958 38.987 253.342 38.641 C 253.552 38.589 253.751 38.5 253.931 38.378 L 260.153 34.208 C 260.216 34.165 260.299 34.165 260.362 34.208 C 260.362 34.208 258.858 33.152 263.007 35.705 Z"
                      fill="#70d44b" transform="matrix(1, 0, 0, 1, 55.493668, 69.600685)" />
            </svg>
        </div>
        <!--        <div class="text-lg self-center text-center w-full mt-1">-->
        <!--            744.539 <br>-->
        <!--            kiralama değerlendirmesi-->
        <!--        </div>-->
        <Comments></Comments>
    </section>

    <section class="mt-6 mts:mt-4 py-1 md:py-9 bg-[#f8f8f8]">
        <div class="flex mx-auto flex-col max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl">
            <div class="flex w-full justify-between">
                <div class="font-bold text-2xl md:text-3xl my-2 lg:my-0 mx-0 lg:mx-4 self-center text-center w-full md:w-auto">
                    Son Eklenen Ürünler
                </div>
                <div class="flex-1 ml-6 hidden md:flex">
                    <div class="flex-1 self-center border border-gray-200"></div>
                    <Link href="/kategoriler/tum-urunler" class="cursor-pointer text-sm font-bold flex justify-center items-center border-2 rounded-full px-4 border-2 whitespace-nowrap md:ml-4 hover:bg-kbgreen hover:text-white">
                        Tümünü Gör
                    </Link>
                </div>
            </div>
            <div class="flex flex-col max-h-p-box space-x-1 pl-0 lg:pl-0 lg:p-0 lg:mt-9">
                <Splide :has-track="false" aria-label="" :options="{ rewind: true, perPage: 4, pagination: false, breakpoints: { 640: { perPage: 1 }, 1100: { perPage: 2 }, 1270: { perPage: 3 } } }">
                    <SplideTrack>
                        <SplideSlide v-for="(productGroup, index) in splidedAllProducts" :key="index" class="flex">
                            <category-box v-for="(product, index) in productGroup" :product="product" :new-container-classes="`w-full`" />
                        </SplideSlide>
                    </SplideTrack>

                    <div class="splide__arrows">
                        <button class="splide__arrow splide__arrow--prev !-left-9">
                            <svg class="" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                      transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                        <button class="splide__arrow splide__arrow--next !-right-9">
                            <svg class="rotate-180" xmlns="http://www.w3.org/2000/svg" width="11.926" height="20.737" viewBox="0 0 11.926 20.737">
                                <path id="Path_18" data-name="Path 18" d="M86.422,320.457a1.558,1.558,0,0,1,2.2,0l7.71,7.71,7.71-7.71a1.557,1.557,0,1,1,2.2,2.2l-8.811,8.811a1.558,1.558,0,0,1-2.2,0l-8.811-8.811A1.558,1.558,0,0,1,86.422,320.457Z"
                                      transform="translate(331.927 -85.966) rotate(90)" fill="#231f20" />
                            </svg>
                        </button>
                    </div>
                </Splide>
            </div>
            <div class="flex-1 ml-2 flex md:hidden justify-center">
                <Link href="/kategoriler/tum-urunler" class="cursor-pointer border-3 rounded-full py-1.5 px-2 text-center mb-4 w-32 hover:bg-kbgreen hover:text-white font-bold text-sm">
                    Tümünü Gör
                </Link>
            </div>
        </div>
    </section>
    <div class=" flex justify-center items-center flex-col w-full mb-12 mt-10">
        <div class="w-full max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mx-auto flex flex-col justify-center items-center mb-10">
            <div class="text-3xl mt-10 font-bold">
                Sıkça Sorulan Sorular
            </div>
            <div class="mt-6 flex flex-col justify-center items-center w-full">
                <div :class="[block.showBlock ? ' py-4 rounded-xl mb-4 w-full px-4' : 'flex justify-between items-center w-full py-4 px-4 rounded-full mb-4']" v-for="(block, index) in blocks" :key="index">
                    <div class="flex justify-between w-full" :class="[!block.showBlock ? '' : '']">
                        <div>
                            <p @click="toogleBlock(block)" class="text-base font-santralextrabold ts:text-xl text-left cursor-pointer">
                                {{ block.title }}</p>
                        </div>
                        <div class="rounded-full w-8 h-8 bg-white " :class="[block.showBlock ? ' rounded-full ' : ' ']" @click="toogleBlock(block)">
                            <button type="button" class="flex justify-center items-center w-8 h-8" v-if="!block.showBlock">
                                <svg xmlns="http://www.w3.org/2000/svg" width="23.872" height="21.712" viewBox="0 0 23.872 21.712">
                                    <g id="Group_119" data-name="Group 119" transform="translate(0)">
                                        <path id="Path_19" data-name="Path 19" d="M1.6,21.712A1.528,1.528,0,0,1,0,20.261V1.451A1.528,1.528,0,0,1,1.6,0a1.528,1.528,0,0,1,1.6,1.451v18.81A1.528,1.528,0,0,1,1.6,21.712Z" transform="translate(10.341 0)"
                                              fill="#000000" />
                                        <path id="Path_20" data-name="Path 20" d="M1.451,23.872A1.528,1.528,0,0,1,0,22.277V1.6A1.528,1.528,0,0,1,1.451,0,1.528,1.528,0,0,1,2.9,1.6V22.277A1.528,1.528,0,0,1,1.451,23.872Z"
                                              transform="translate(23.872 9.405) rotate(90)" fill="#000000" />
                                    </g>
                                </svg>
                            </button>
                            <button type="button" class="flex justify-center items-center w-8 h-8" v-if="block.showBlock">
                                <svg id="Group_119" data-name="Group 119" xmlns="http://www.w3.org/2000/svg" width="22.332" height="2.985" viewBox="0 0 22.332 2.985">
                                    <path id="Path_19" data-name="Path 19" d="M1.492,21.712A1.472,1.472,0,0,1,0,20.261V1.451A1.472,1.472,0,0,1,1.492,0,1.472,1.472,0,0,1,2.985,1.451v18.81A1.472,1.472,0,0,1,1.492,21.712Z"
                                          transform="translate(22.022) rotate(90)" fill="#000" />
                                    <path id="Path_20" data-name="Path 20" d="M1.451,22.332A1.472,1.472,0,0,1,0,20.84V1.492A1.472,1.472,0,0,1,1.451,0,1.472,1.472,0,0,1,2.9,1.492V20.84A1.472,1.472,0,0,1,1.451,22.332Z"
                                          transform="translate(22.332 0.042) rotate(90)" fill="#000" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="mr-5" v-html="block.content" v-if="block.showBlock"></div>
                </div>
                <Disclosure as="div" v-slot="{ open }" class="w-full">
                    <DisclosurePanel class="w-full">
                        <div :class="[block.showBlock ? ' py-4 rounded-xl mb-4 w-full px-4' : 'flex justify-between items-center w-full py-4 px-4 rounded-full mb-4']" v-for="(block, index) in readMoreBlocks" :key="index">
                            <div class="flex justify-between w-full" :class="[!block.showBlock ? '' : '']">
                                <div>
                                    <p @click="toogleBlock(block)" class="text-base font-santralextrabold ts:text-xl text-left cursor-pointer">
                                        {{ block.title }}</p>
                                </div>
                                <div class="rounded-full w-8 h-8 bg-white " :class="[block.showBlock ? ' rounded-full ' : ' ']" @click="toogleBlock(block)">
                                    <button type="button" class="flex justify-center items-center w-8 h-8" v-if="!block.showBlock">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="23.872" height="21.712" viewBox="0 0 23.872 21.712">
                                            <g id="Group_119" data-name="Group 119" transform="translate(0)">
                                                <path id="Path_19" data-name="Path 19" d="M1.6,21.712A1.528,1.528,0,0,1,0,20.261V1.451A1.528,1.528,0,0,1,1.6,0a1.528,1.528,0,0,1,1.6,1.451v18.81A1.528,1.528,0,0,1,1.6,21.712Z"
                                                      transform="translate(10.341 0)" fill="#000000" />
                                                <path id="Path_20" data-name="Path 20" d="M1.451,23.872A1.528,1.528,0,0,1,0,22.277V1.6A1.528,1.528,0,0,1,1.451,0,1.528,1.528,0,0,1,2.9,1.6V22.277A1.528,1.528,0,0,1,1.451,23.872Z"
                                                      transform="translate(23.872 9.405) rotate(90)" fill="#000000" />
                                            </g>
                                        </svg>
                                    </button>
                                    <button type="button" class="flex justify-center items-center w-8 h-8" v-if="block.showBlock">
                                        <svg id="Group_119" data-name="Group 119" xmlns="http://www.w3.org/2000/svg" width="22.332" height="2.985" viewBox="0 0 22.332 2.985">
                                            <path id="Path_19" data-name="Path 19" d="M1.492,21.712A1.472,1.472,0,0,1,0,20.261V1.451A1.472,1.472,0,0,1,1.492,0,1.472,1.472,0,0,1,2.985,1.451v18.81A1.472,1.472,0,0,1,1.492,21.712Z"
                                                  transform="translate(22.022) rotate(90)" fill="#000" />
                                            <path id="Path_20" data-name="Path 20" d="M1.451,22.332A1.472,1.472,0,0,1,0,20.84V1.492A1.472,1.472,0,0,1,1.451,0,1.472,1.472,0,0,1,2.9,1.492V20.84A1.472,1.472,0,0,1,1.451,22.332Z"
                                                  transform="translate(22.332 0.042) rotate(90)" fill="#000" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="mr-5" v-html="block.content" v-if="block.showBlock"></div>
                        </div>

                    </DisclosurePanel>
                    <DisclosureButton class="text-center w-full" @click="hiddenbtn1 = !hiddenbtn1" v-if="hiddenbtn1">
                        <button class="bg-black font-santralextrabold text-base lg:text-lg text-white rounded-full py-1.5 px-4 self-center font-bold hover:bg-kbgreen">
                            Daha Fazla
                        </button>
                    </DisclosureButton>
                </Disclosure>
            </div>
        </div>
    </div>
</template>
