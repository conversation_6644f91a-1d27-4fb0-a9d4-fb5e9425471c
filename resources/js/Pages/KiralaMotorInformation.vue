<script>
import { Head, <PERSON> } from "@inertiajs/inertia-vue3";
import Layout from "@/Pages/Shared/LayoutDemo.vue";
// import { SchemaOrgQuestion } from "@unhead/schema-org";

export default {
    components: {
        Link,
        Head
        // SchemaOrgQuestion,
    },
    layout: Layout,
    data() {
        return {
            blocks: [
                {
                    title: "Motosiklet Kiralama İçin Gerekli Belgeler Nelerdir?",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular \">Motosiklet kiralama işlemi için:</p>\n" +
                        "                <ul class=\"ml-6 list-outside text-sm mt-5 space-y-4 font-santralregular \">\n" +
                        "                    <li class=\"font-santralregular\"> Sürücü belgeniz,</li>\n" +
                        "                    <li class=\"font-santralregular\">Vergi levhanız,</li>\n" +
                        "                    <li class=\"font-santralregular\"><PERSON><PERSON> sicili kaydınız,</li>\n" +
                        "                    <li class=\"font-santralregular\">Ödeme yapacağınız kredi/banka kartı gereklidir.</li>\n" +
                        "                </ul>" +
                        "                <p class=\" text-sm mt-5 font-santralregular \">Gerekli görülmesi durumunda araç güvenliği ve geçerli mevzuatlar nedeniyle ek bilgi ve belge talebi yapılabilir, bilgi ve belge taleplerini karşılayamayan müşterilerimizin kiralama talepleri iptal edilebilmektedir.</p>\n"
                    ,
                    showBlock: true
                },
                {
                    title: "Kiralama değerlendirme aşamasında hangi ek belgeler istenebilir? İstenilen belgeleri nasıl iletmeyelim? ",
                    content:
                        "                <p class=\" text-sm mt-5 font-santralregular \">İstenen bilgi ve belgeler, e-posta kanalı ile veya panel üzerinden yüklenerek tarafımıza iletilmektedir. Bilgi ve belgeler araç/motor ve diğer ek cihazlarda değişkenlik gösterip kiralama süreniz ve bilgileriniz incelendikten sonra tarafınıza iletilecektir. İkametgâh, sürücü belgesi kopyası, gelir belgesi, ticari firma adına kiralama yapılıyorsa faaliyet belgesi/işyeri kaydı vb. evraklar talep edilebilmektedir.</p>\n",
                    showBlock: false
                },
                { title: "	Kiraladığım aracı nasıl teslim alacağım? 	", content: " <p class=' text-sm mt-5 font-santralregular '>	Kiralama yaptığınız aracı adresinize en yakın yetkili bayiden teslim alabilirsiniz, teslim alacağınız adres ve yetkili servis/bayi tarafınıza bildirilecektir. Aracı teslim aldığınız bayiye veya tarafınıza bildirilecek bayi/depo adresine teslim etmeniz gerekmektedir. 	</p>", showBlock: false },
                { title: "	Motor kiralamak için kaç yaşında olmalıyım?	", content: " <p class=' text-sm mt-5 font-santralregular '>	Araç kiralamak için 20 yaşını doldurmuş olmanız ve en az 1 yıllık A sınıfı ehliyet sahibi olmanız gerekmektedir.	</p>", showBlock: false },
                {
                    title: "	Elektrikli motosiklet kiralamak için kaç yaşında olmalıyım?	",
                    content: " <p class=' text-sm mt-5 font-santralregular '>	Elektrikli motosiklet için en az 18 yaşını doldurmuş olmanız ve en az 1 yıllık ehliyetiniz olması gerekmektedir. Azami hızı saatte 45 kilometreyi ve azami sürekli nominal güç çıkışı 4 kW’ı geçmeyen motosikletler için en az M sınıfı ehliyet gerekmektedir. Güç sınırı 11 kW sınır değerini aşmayan ve kW /kg oranı 0,1’i geçmeyen motorlar en az A1 sınıfı ehliyet gerekmektedir.	</p>",
                    showBlock: false
                },
                {
                    title: "	Kiraladığım aracı farklı biri kullanabilir mi?	",
                    content: " <p class=' text-sm mt-5 font-santralregular '>	Kiralama sözleşmesi, aracı kiralayan kişi ile yapılmaktadır. Sözleşmeye bağlı olarak, aracın tüm sigorta ve güvence şartlarında aracı kiralayan kişinin bilgileri yer almaktadır. Kira sözleşmesinde bilgileri olmayan bir kişinin sürücü olarak maddi ya da manevi hasarlı bir kazanın tarafı olması durumunda; tüm sigorta ve güvenceler geçersiz sayılır, hem araç kiralamayı yapan hem de kazaya karışan kişiler kazadan sorumlu tutulur.	</p>",
                    showBlock: false
                },
                { title: "	Motosiklet Kullanırken Hangi Sigorta Türleri Geçerlidir?	", content: " <p class=' text-sm mt-5 font-santralregular '>	Araçların temel trafik sigortası bulunmaktadır.	</p>", showBlock: false },
                { title: "	Araç Kiralama Süresini Uzatmak Mümkün müdür?	", content: " <p class=' text-sm mt-5 font-santralregular '>	Araç kiralama süresini uzatmak mümkündür. Müşteri hizmetlerimiz ile iletişime geçebilirsiniz.	</p>", showBlock: false },
                { title: "	Motosiklet Kiralama Ücretine Neler Dahildir?	", content: " <p class=' text-sm mt-5 font-santralregular '>	Kiralama ücretleri MTV, trafik tescil masrafları ve zorunlu trafik sigortasını kapsar. Bakım, onarım ve zayii durumları kiracıya aittir.	</p>", showBlock: false },
                {
                    title: "	Kiraladığım araçla ilgili uymam gereken trafik kurallarına uymazsam ne olur?	",
                    content: " <p class=' text-sm mt-5 font-santralregular '>	Trafik kurallarına uyulmaması sonucu ortaya çıkacak her tür trafik cezasının (köprü ve otoyollarda hatalı gişe kullanılması da dâhil olmak üzere) ödemesi ve hizmet bedeli sözleşmede ismi geçen kiracıya ait kredi kartından yapılacaktır. Ayrıca trafik kurallarına uyulmaması dolayısıyla oluşabilecek her tür kaza, yasal süreçler ve 3. kişilerden gelecek talep ve 3. kişilere karşı sorumluluklar kullanıcı tarafından karşılanacaktır.	</p>",
                    showBlock: false
                },
                { title: "	Ödemeyi yapacak/aracı kiralayacak kişinin ehliyeti yok ise ne olur?	", content: " <p class=' text-sm mt-5 font-santralregular '>	Ehliyeti olmayan kişi araç kiralaması yapamaz. Aracı kiralayan kişinin ehliyeti olmalıdır ve ödemeyi kendisinin yapması gerekir. Eğer ehliyet yok ise kiralama yapamaz, ek sürücü ekleyerek araç kiralama hizmeti alamaz.	</p>", showBlock: false },
                { title: "	Kiraladığım araç ve motosiklet ile yurt dışına çıkış yapabilir miyim? 	", content: " <p class=' text-sm mt-5 font-santralregular '>	Kiraladığınız mobilite ürünler ile yurt dışına çıkış yapamazsınız. 	</p>", showBlock: false },
                {
                    title: "	Ticari kiralama yapacağım farklı bir işlem yapmam gerekir mi? 	",
                    content: " <p class=' text-sm mt-5 font-santralregular '>	Ticari faaliyet için kiralama yapılan araçlarla ilgili ticari faaliyetin ibraz edilmesi ve ticari işletme hesabı açılması gerekmektedir. Ticari evraklarla başvuru yapmanız, şirket yetkililerinin talebi oluşturması gerekmektedir. Filo kiralama talepleri için lütfen kurumsal kiralama ekibimizle iletişime geçiniz. Aklınıza takılan herhangi bir soru için lütfen bizimle iletişime geçmekten çekinmeyin. Müşteri hizmetleri ekibimiz her zaman yardımcı olmaktan memnuniyet duyacaktır.</p>",
                    showBlock: false
                }
            ],
            readMoreBlocks: []
        };
    },
    // {
    //     title: "Kira süresi içerisinde veya kira süresi bittiği zaman (araç geri teslimi sırasında) fark edilen hasar sonucunda ne olur? ",
    //         content:
    //     "                <p class=\" text-sm mt-5 font-santralregular \">Kiralamobil araçlarının tamamı Kiralabunu A.Ş. mülkiyetinde olup, olası kaza ve araç hasarlarında öncelikle Kiralabunu A.Ş. bilgilendirmeli ve araç tarafınıza bildirilecek olan yetkili servise tarafınızca teslim edilmelidir. Yetkili servis incelemesi akabinde, araç hasarları belirlenerek ilgili hasarların % 100’ü müşteri tarafından karşılanmaktadır. </p>\n",
    //         showBlock: false
    // },
    // { title: "	Kiraladığım aracın arızalanması durumunda ne yapmam gerekir? 	", content: " <p class=' text-sm mt-5 font-santralregular '>	Kiraladığınız araçta hasar veya arıza olması durumunda Kiralabunu iletişime geçmelisiniz. Herhangi bir servise Kiralabunu onayı olmadan götürülmemelidir. Yasal yetkili servisler dışında herhangi bir yerde tamir, bakım onarım işlemleri yapılması yasaktır. 	</p>", showBlock: false },
    // { title: "	Kiraladığım araçla ilgili parça değişikliği yapabilir miyim? 	", content: " <p class=' text-sm mt-5 font-santralregular '>	Kiraladığınız araçta herhangi bir parça veya batarya değişimi yapamaz, söküp takma işlemi uygulayamazsınız. Tüm servis ve bakım işlemleriyle, yedek parça onayları Kiralabunu onayına tabii olup, yetkili anlaşmalı servislerde yaptırılmaktadır.	</p>", showBlock: false },
    // { title: "	Kiraladığım araçla ilgili bir kilometre kısıtı bulunmakta mı? 	", content: " <p class=' text-sm mt-5 font-santralregular '>	Kiralanan araçlarda kilometre kısıtı bulunmamaktadır; araç kiraları hesaplanırken yıllık 15.000 km kullanım hakkı baz alınmıştır. Kilometre aşımı yapmanız halinde 5 TL/km olarak ücretlendirilecektir. Tarafınızdan/kartınızdan ek olarak tahsil edilecektir.	</p>", showBlock: false },

    methods: {
        toogleBlock(block) {
            block.showBlock = !block.showBlock;
        }
    }
};
</script>

<template>

    <Head title="Kiralamotor Bilgilendirme" />
    <main class="my-6 max-w-kbmobile md:max-w-tablet ts:max-w-ts 2xl:max-w-7xl mt-12 mx-auto">
        <section class="flex justify-center items-center flex-col w-full mb-12">
            <div class="flex justify-center items-center mb-10">
                <div class="flex justify-center items-center bg-kbgreen rounded-full w-28 h-28">
                    <svg xmlns="http://www.w3.org/2000/svg" width="43.015" height="78.955" viewBox="0 0 43.015 78.955">
                        <g id="Group_4141" data-name="Group 4141" transform="translate(0 0)">
                            <path id="Path_3349" data-name="Path 3349"
                                d="M473.847,415.2H472.2a5.587,5.587,0,0,1-5.581-5.581v-1.387a17.076,17.076,0,0,1,1.934-8.548,44.963,44.963,0,0,1,5.417-7.278,32.075,32.075,0,0,0,3.683-4.439,7.467,7.467,0,0,0,1.163-4.13c0-3.445-2.245-5.121-6.863-5.121a27.868,27.868,0,0,0-7.564,1.135,6.368,6.368,0,0,1-8.116-6.123v-2.807a6.246,6.246,0,0,1,3.685-5.725,36.563,36.563,0,0,1,27-.235,21.4,21.4,0,0,1,9.074,6.877,16.563,16.563,0,0,1,3.255,10.039,16.327,16.327,0,0,1-3.147,10.04,57.393,57.393,0,0,1-8.8,8.966,62.707,62.707,0,0,0-5.642,5.315,9.811,9.811,0,0,0-2.346,4.367,5.566,5.566,0,0,1-5.5,4.636Z"
                                transform="translate(-456.268 -362.491)" fill="#fff" />
                            <path id="Path_3350" data-name="Path 3350" d="M480.951,518.1h-.218a10.344,10.344,0,0,1,0-20.688h.218a10.344,10.344,0,0,1,0,20.688Z" transform="translate(-464.291 -439.141)" fill="#fff" />
                        </g>
                    </svg>
                </div>
            </div>
            <button class="bg-black text-white rounded-full py-2 px-4 self-center text-2xl hover:bg-kbgreen">Kiralamotor Bilgilendirme</button>
        </section>
        <!--        <section class="mt-6 flex  mb-14 overflow-y-scroll lg:overflow-y-visible space-x-4">-->
        <!--            <div-->
        <!--                class="flex-1 p-0 text-sm md:text-lg lg:text-2xl text-center font-bold py-2 border-1 border-kbgreen rounded-full text-gray-900 box-border whitespace-no-wrap min-w-[130px] mb-3">-->
        <!--                <a class="whitespace-no-wrap" href="">Teknik Sorular</a></div>-->
        <!--            <div-->
        <!--                class="flex-1 p-0 text-sm md:text-lg lg:text-2xl text-center font-bold py-2 border-1 border-kbgreen rounded-full text-gray-900 box-border whitespace-no-wrap min-w-[130px] mb-3">-->
        <!--                <a class="whitespace-no-wrap" href="">Ödeme</a></div>-->
        <!--            <div-->
        <!--                class="flex-1 p-0 text-sm md:text-lg lg:text-2xl text-center font-bold py-2 border-1 border-kbgreen rounded-full text-gray-900 box-border whitespace-no-wrap min-w-[130px] mb-3">-->
        <!--                <a class="whitespace-no-wrap" href="">Gönderim</a></div>-->
        <!--            <div-->
        <!--                class="flex-1 p-0 text-sm md:text-lg lg:text-2xl text-center font-bold py-2 border-1 border-kbgreen rounded-full text-gray-900 box-border whitespace-no-wrap min-w-[130px] mb-3">-->
        <!--                <a class="whitespace-no-wrap" href="">Ürün</a></div>-->
        <!--            <div-->
        <!--                class="flex-1 p-0 text-sm md:text-lg lg:text-2xl text-center font-bold py-2 border-1 border-kbgreen rounded-full text-gray-900 box-border whitespace-no-wrap min-w-[130px] mb-3">-->
        <!--                <a class="whitespace-no-wrap" href="">Kiralabunu Nedir?</a></div>-->
        <!--        </section>-->
        <!--        <form class="flex justify-center relative" action="#">-->
        <!--            <div class="w-11/12 lg:w-10/12">-->
        <!--                <input class="w-full mb-7 p-3 border-none shadow-gray-200 shadow-lg rounded-full placeholder:text-sm lg:placeholder:text-xl placeholder:text-kbgray focus:shadow-inner focus:border-kbgreen focus:outline-none focus:ring-kbgreen text-lg" required type="text" placeholder="Sana nasıl yardımcı olabiliriz?" name="SSS" autofocus>-->
        <!--                <div class="absolute top-4 right-10 lg:right-32">-->
        <!--                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24.192"-->
        <!--                         height="24.193" viewBox="0 0 24.192 24.193">-->
        <!--                        <defs>-->
        <!--                            <clipPath id="clip-path">-->
        <!--                                <path id="Mask"-->
        <!--                                      d="M8.991,0a8.991,8.991,0,1,0,5.845,15.815l.381.378v1.1l6.914,6.9,2.062-2.063-6.9-6.915h-1.1l-.382-.38A8.982,8.982,0,0,0,8.991,0Zm0,15.215a6.224,6.224,0,1,1,6.224-6.224A6.231,6.231,0,0,1,8.991,15.215Z"-->
        <!--                                      fill="none" />-->
        <!--                            </clipPath>-->
        <!--                        </defs>-->
        <!--                        <g id="Icon_Search_Sharp" data-name="Icon / Search / Sharp" opacity="0.607">-->
        <!--                            <path id="Mask-2" data-name="Mask"-->
        <!--                                  d="M8.991,0a8.991,8.991,0,1,0,5.845,15.815l.381.378v1.1l6.914,6.9,2.062-2.063-6.9-6.915h-1.1l-.382-.38A8.982,8.982,0,0,0,8.991,0Zm0,15.215a6.224,6.224,0,1,1,6.224-6.224A6.231,6.231,0,0,1,8.991,15.215Z"-->
        <!--                                  fill="none" />-->
        <!--                            <g id="Icon_Search_Sharp-2" data-name="Icon / Search / Sharp" clip-path="url(#clip-path)">-->
        <!--                                <g id="_Icon_Color" data-name="↳ Icon Color" transform="translate(-4.15 -3.884)">-->
        <!--                                    <rect id="Rectangle" width="33.197" height="32.653" fill="#70d44b" />-->
        <!--                                </g>-->
        <!--                            </g>-->
        <!--                        </g>-->
        <!--                    </svg>-->
        <!--                </div>-->
        <!--            </div>-->
        <!--        </form>-->
        <section class="mt-6 flex flex-col justify-center items-center w-full ts:w-3/4 mx-auto">
            <div :class="[block.showBlock ? ' py-4 rounded-xl mb-4 w-full px-4' : 'flex justify-between items-center w-full py-4 px-4 rounded-full mb-4']" v-for="(block, index) in blocks" :key="index">
                <div class="flex justify-between w-full" :class="[!block.showBlock ? '' : '']">
                    <div>
                        <p @click="toogleBlock(block)" class="text-base font-santralextrabold ts:text-xl text-left cursor-pointer">
                            {{ block.title }}
                        </p>
                    </div>
                    <div class="rounded-full w-8 h-8 bg-white" :class="[block.showBlock ? ' rounded-full ' : ' ']" @click="toogleBlock(block)">
                        <button type="button" class="flex justify-center items-center w-8 h-8" v-if="!block.showBlock">
                            <svg xmlns="http://www.w3.org/2000/svg" width="23.872" height="21.712" viewBox="0 0 23.872 21.712">
                                <g id="Group_119" data-name="Group 119" transform="translate(0)">
                                    <path id="Path_19" data-name="Path 19" d="M1.6,21.712A1.528,1.528,0,0,1,0,20.261V1.451A1.528,1.528,0,0,1,1.6,0a1.528,1.528,0,0,1,1.6,1.451v18.81A1.528,1.528,0,0,1,1.6,21.712Z" transform="translate(10.341 0)"
                                        fill="#000000" />
                                    <path id="Path_20" data-name="Path 20" d="M1.451,23.872A1.528,1.528,0,0,1,0,22.277V1.6A1.528,1.528,0,0,1,1.451,0,1.528,1.528,0,0,1,2.9,1.6V22.277A1.528,1.528,0,0,1,1.451,23.872Z"
                                        transform="translate(23.872 9.405) rotate(90)" fill="#000000" />
                                </g>
                            </svg>
                        </button>
                        <button type="button" class="flex justify-center items-center w-8 h-8" v-if="block.showBlock">
                            <svg id="Group_119" data-name="Group 119" xmlns="http://www.w3.org/2000/svg" width="22.332" height="2.985" viewBox="0 0 22.332 2.985">
                                <path id="Path_19" data-name="Path 19" d="M1.492,21.712A1.472,1.472,0,0,1,0,20.261V1.451A1.472,1.472,0,0,1,1.492,0,1.472,1.472,0,0,1,2.985,1.451v18.81A1.472,1.472,0,0,1,1.492,21.712Z"
                                    transform="translate(22.022) rotate(90)" fill="#000" />
                                <path id="Path_20" data-name="Path 20" d="M1.451,22.332A1.472,1.472,0,0,1,0,20.84V1.492A1.472,1.472,0,0,1,1.451,0,1.472,1.472,0,0,1,2.9,1.492V20.84A1.472,1.472,0,0,1,1.451,22.332Z"
                                    transform="translate(22.332 0.042) rotate(90)" fill="#000" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="mr-5" v-html="block.content" v-if="block.showBlock"></div>
            </div>
        </section>
    </main>
</template>
