<script setup>

import { Link } from "@inertiajs/inertia-vue3";
import { Disclosure, DisclosureButton, DisclosurePanel } from "@headlessui/vue";

defineProps({
    total: {
        type: Number,
        default: 0
    },
    products: {
        type: Array,
        default: []
    },
    component: {
        Link,
        Disclosure,
        DisclosureButton,
        DisclosurePanel
    }
});
</script>

<template>
    <div>
        <div class="w-full flex flex-wrap justify-between border-2 border-bordergray rounded-lg p-3 mb-5">
            <slot name="ourcart"></slot>
            <div class="flex flex-wrap w-full items-center">
                <div class="w-full p-2 mb-2" v-for="product in products" :key="product.product.id">
                    <div class="flex justify-start items-center shadow-lg bg-white rounded-lg px-2">
                        <picture class="w-16">
                            <source :srcset="product.product.imagesWebP?.thumb" type="image/webp" />
                            <source :srcset="product.product.imagesWebP?.thumb_webp" type="image/jpeg" />
                            <img :src="product.product.imagesWebP?.zoom_webp" alt="Alt Text!" />
                        </picture>
                        <div class="w-[calc(100%-4rem)] ml-2 text-xs lg:text-base tracking-wide text-black font-santralregular font-semibold">{{ product.product.attribute_data.name?.tr }}</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>
