<script setup>
import {Link} from '@inertiajs/inertia-vue3';
import WarningCard from '@/Components/WarningCard.vue'

defineProps({
    carts: {
        type: Array,
        default: [],
    },
})
</script>

<template>
    <div>
        <div
            class="flex justify-start items-center w-full mb-3 bg-white border-2 border-kb-light-grey py-3 px-5 rounded-lg"
            v-for="cart in carts"
            :key="cart.id"
        >
            <input
                class="border-2 border-black checked:bg-acordion-green focus:outline-none focus:ring-acordion-green"
                type="radio"
                id="kadin"
                name="cinsiyet"
                value="kadin"
            />
            <label
                class="pl-3 text-base lg:text-lg text-black font-bold cursor-pointer"
                for="kadin"
            >
                Alışveriş Kartım ( Akbank )
            </label>
        </div>
        <WarningCard v-if="carts.length == 0">
            <template #header>
                <p class="text-xl mb-2 text-black font-bold"></p>
            </template>
            <template #message>
                <p class="text-sm text-black">
                    Kayıtlı ödeme yönetimin yok. Yeni bir ödeme yönetimi ekleyerek devam
                    edebilirsin.
                </p>
            </template>
        </WarningCard>
        <p class="pl-2 text-md mb-5 mt-4">
            <a href="/odeme"><u>Ödeme Yöntemi Ekle</u></a>
        </p>
    </div>
</template>
