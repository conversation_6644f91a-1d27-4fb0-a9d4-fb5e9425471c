<script setup>

import {Link} from '@inertiajs/inertia-vue3';

defineProps({
    total: {
        type: Number,
        default: 0
    },
    products: {
        type: Array,
        default: []
    },
    component: {
        Link
    }
});
</script>

<template>
    <div>
        <p class="text-xl mb-5 text-black font-bold">
            Sepetinde<PERSON>
            <span class="font-medium">({{ products.length }} ürün)</span>
        </p>
        <div class="w-full flex flex-wrap justify-between border-2 border-bordergray rounded-lg p-3 mb-5">
            <div class="flex flex-wrap w-full lg:w-10/12 items-center">
                <div class="w-full p-2 mb-2" v-for="product in products" :key="product.product.id">
                    <div class="flex justify-start items-center shadow-lg bg-white rounded-2lg">
                        <picture class="w-10 md:w-16">
                            <source :srcset="product.product.imagesWebP?.thumb" type="image/webp"/>
                            <source :srcset="product.product.imagesWebP?.thumb_webp" type="image/jpeg"/>
                            <img :src="product.product.imagesWebP?.zoom_webp" alt="Alt Text!"/>
                        </picture>
                        <div class=" ml-2 text-base tracking-wide text-black font-medium pb-2">{{ product.product.attribute_data.name?.tr }}</div>
                    </div>
                </div>
            </div>
            <div class="w-full lg:w-2/12 pr-3 flex items-center justify-center lg:justify-end mt-2 lg:mt-0">
                <p class="text-sm lg:text-lg text-black font-bold">
                    <Link href="/sepetim">Düzenle</Link>
                </p>
            </div>
        </div>
    </div>
</template>
