<script setup>
import { Link, useForm } from "@inertiajs/inertia-vue3";
import { ref, computed } from "vue";

const props = defineProps({
    total: {
        type: Number,
        default: 0
    },
    products: {
        type: Array,
        default: []
    },
    discount: {
        type: Number,
        default: 0
    },
    sub_total: {
        type: Number,
        default: 0
    },
    non_mass_payment_total: {
        type: Number,
        default: 0
    },
    is_mass_payment_enabled: {
        type: Boolean,
        default: false
    },
    is_mass_payment_local_enable: {
        type: Boolean,
        default: false
    },
    coupon: { type: Object, default: false },
    insurance: {
        type: Number,
        default: 0
    }
});

const form = useForm({
    coupon: null
});

// let campaginIsActive = ref(0);
// campaginIsActive = props.discount == 0 && props.products.filter((x) => x.tags.filter((y) => y.id == 5).length > 0 && x.month.id > 2).length > 0;

const campaginIsActive = computed(() => {
    return props.discount == 0 && props.products.filter((x) => x.tags.filter((y) => y.id == 5).length > 0 && x.month.id > 2).length > 0;
})

const mountlyTotalVATIncluded = computed(() => {
    let total = props.products.reduce((acc, obj) => {
        return acc + (obj.tags.filter((y) => y.id === 5).length > 0 && obj.month.id > 2 ? 1 * obj.quantity : obj.total);
    }, 0) + props.insurance;
    if (total < 0) {
        return 1;
    }
    return total.toFixed(0);
})
</script>

<template>
    <div class="w-full lg:w-3/12">
        <div class="w-full border-3 rounded-lg flex flex-col justify-start py-2 lg:py-6 px-4 items-start relative left-0 bottom-0 bg-white z-50">
            <div class="-top-5 absolute flex hidden justify-center w-full left-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="36.672" height="15.878" viewBox="0 0 36.672 15.878">
                    <path id="ios-arrow-back" d="M4.787,18.332,15.3,4.467a3.229,3.229,0,0,0,0-3.7,1.673,1.673,0,0,0-2.812,0L.578,16.477A3.247,3.247,0,0,0,.521,20.09L12.476,35.908a1.674,1.674,0,0,0,2.81,0,3.229,3.229,0,0,0,0-3.7Z"
                        transform="translate(36.672) rotate(90)" fill="#d3d3d3" />
                </svg>
            </div>
            <h3 class="hidden lg:block p-0 text-xl font-bold text-left text-gray-900 box-border whitespace-no-wrap mb-4">
                Kiralanacak Ürünler<span class="font-medium text-xl">({{ products.length }})</span>
            </h3>

            <div class="hidden lg:block w-full flex justify-between items-start pb-2 mb-2 border-b-2 border-dashed border-bordergray" v-if="campaginIsActive">
                <p class="text-base font-medium tracking-wide text-checkoutgray">
                    İlk Ay Kiralama Ücreti
                    <br />
                </p>
                <p class="text-base font-bold tracking-wide text-black w-25 whitespace-nowrap">
                    {{
                    products.reduce((acc, obj) => {
                    return acc + (obj.tags.filter((y) => y.id === 5).length > 0 && obj.month.id > 2 ? 1 * obj.quantity : obj.total);
                    }, 0)
                    }}
                    TL
                </p>
            </div>

            <div class="hidden lg:block w-full flex justify-between items-start pb-2 mb-2 border-b-2 border-dashed border-bordergray">
                <p class="text-base font-medium tracking-wide text-checkoutgray">
                    {{ is_mass_payment_enabled ? "Toplu ödemenin geçerli olduğu kiralama süresi sonundaki aylık ödeme tutarı" : "Aylık Kiralama Ücreti" }}
                    <br />
                </p>
                <p class="text-base font-bold tracking-wide text-black w-25 whitespace-nowrap">{{
                    (non_mass_payment_total - discount - insurance).toFixed(0)
                    }} TL
                    {{ is_mass_payment_enabled ? "" : "/ Ay" }}
                </p>
            </div>

            <div class="hidden lg:block w-full flex justify-between items-start pb-2 mb-2 border-b-2 border-dashed border-bordergray" v-if="insurance > 0">
                <p class="text-base font-medium tracking-wide text-checkoutgray">
                    Sigorta Bedeli
                    <br />
                </p>
                <p class="text-base font-bold tracking-wide text-black w-25 whitespace-nowrap">{{ insurance }} TL</p>
            </div>

            <div v-if="coupon && coupon?.code != null" class="relative block w-full pb-2 mb-2 border-b-2 border-dashed border-bordergray">
                <p class="text-lg tracking-wide text-black font-bold">Kupon</p>
                <p class="text-base font-bold tracking-wide text-black" v-if="discount < 0">{{ discount }} TL</p>
                <p class="text-base font-bold tracking-wide text-black" v-else>{{ coupon.code }}</p>
                <Link href="/coupon" :data="{
                    coupon: null,
                }" method="post" as="button" class="text-sm absolute top-1 text-kbred right-0 px-1 rounded-lg leading-none">Kaldır
                </Link>
            </div>
            <div class="hidden lg:block w-full flex justify-between items-start pb-2 mt-2 mb-2 border-b-2 border-dashed border-bordergray">
                <p class="text-base tracking-wide text-checkoutgray font-bold">Kargo</p>
                <p class="text-base font-bold tracking-wide text-black">Ücretsiz</p>
            </div>
            <!--            <div class="hidden lg:block w-full flex justify-between items-start pb-2 mt-2 mb-2 border-b-2 border-dashed border-bordergray">-->
            <!--                <p class="text-base tracking-wide text-checkoutgray font-bold">İndirim</p>-->
            <!--                <p class="text-base font-bold tracking-wide text-black">124 TL (%15)</p>-->
            <!--            </div>-->
            <div v-if="false" class="hidden lg:block w-full flex justify-between items-start pb-2 mb-10">
                <p class="text-sm tracking-wide text-black font-medium">İndirim kodu uygulandı.</p>
                <p class="text-sm font-bold tracking-wide text-black">
                    <a href="">Kaldır</a>
                </p>
            </div>
            <div class="w-full flex flex-col lg:hidden justify-between mb-4" v-if="products.length > 0">
                <form class="w-full flex items-center justify-between border-b-3 border-black pb-1" @submit.prevent="
                    form.post('/coupon', {
                        // onSuccess: () => increment(),
                        preserveScroll: true,
                    })
                    ">
                    <input class="rounded-md border-none placeholder:text-sm placeholder:text-placeholdergray focus:border-none focus:outline-none focus:ring-white text-base tracking-wider w-[calc(100%-60px)]" required type="text"
                        placeholder="Kupon kodu" name="couponCode" v-model="form.coupon" :disabled="is_mass_payment_local_enable" />
                    <button class="bg-textgray text-white rounded-full py-2 font-santralextrabold px-3 self-center text-base font-bold hover:bg-black" type="submit" :disabled="form.processing">Ekle</button>
                </form>

                <div class="flex mt-4 self-center justify-center flex-col" v-if="Object.entries(form.errors).length > 0">
                    <!--                    <div class="flex text-sm text-kbred mr-[2px] tracking-normal" v-for="error in errors">{{ error }}</div>-->
                    <div class="flex text-sm text-kbred mr-[2px] tracking-normal" v-for="error in form.errors">{{ error }}</div>
                </div>

                <div class="flex mt-4 self-center justify-center flex-col" v-if="is_mass_payment_local_enable">
                    <div class="flex text-sm text-kbred mr-[2px] tracking-normal">Toplu ödemede kupon kullanılamaz</div>
                </div>

                <!--                <div class="flex mt-4 self-center justify-center flex-col"-->
                <!--                     v-if="Object.entries($page.props.errors).length > 0">-->
                <!--                    <div class="flex text-sm text-kbred mr-[2px] tracking-normal" v-for="error in $page.props.errors">-->
                <!--                        {{ error }}-->
                <!--                    </div>-->
                <!--                </div>-->
            </div>
            <div class="w-full flex flex-col justify-center items-center lg:items-start pb-4">
                <p class="text-base tracking-wide text-black font-medium pb-2">Vergiler Dahil Toplam:</p>
                <p class="text-3xl font-bold tracking-wide text-black" v-if="campaginIsActive">
                    {{ mountlyTotalVATIncluded }}
                    TL/
                    <sup>Ay</sup>
                </p>

                <p class="text-3xl font-bold tracking-wide text-black" v-else>
                    {{ total }}
                    TL
                    <template v-if="!is_mass_payment_enabled && insurance == 0">/
                        <sup>Ay</sup>
                    </template>
                </p>
            </div>

            <div class="w-full flex flex-col justify-center items-center lg:items-start pb-4" v-if="campaginIsActive">
                <p class="text-base tracking-wide text-black font-medium pb-2">Gelecek Ay Ödenecek Tutar:</p>
                <p class="text-3xl font-bold tracking-wide text-black">
                    {{ total - insurance }}
                    TL/
                    <sup>Ay</sup>
                </p>
            </div>

            <slot name="aggrements" />

            <slot name="action" />
        </div>
    </div>
</template>
