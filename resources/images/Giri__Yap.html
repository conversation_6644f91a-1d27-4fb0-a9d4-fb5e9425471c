<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title><PERSON><PERSON><PERSON></title>
<style id="applicationStylesheet" type="text/css">
	.mediaViewInfo {
		--web-view-name: <PERSON><PERSON><PERSON>;
		--web-view-id: Giri_Yap;
		--web-scale-on-resize: true;
		--web-enable-deep-linking: true;
	}
	:root {
		--web-view-ids: Giri_Yap;
	}
	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
		border: none;
	}
	#Giri_Yap {
		position: absolute;
		width: 1280px;
		height: 1200px;
		background-color: rgba(255,255,255,1);
		overflow: hidden;
		--web-view-name: <PERSON><PERSON><PERSON>;
		--web-view-id: <PERSON>iri_Yap;
		--web-scale-on-resize: true;
		--web-enable-deep-linking: true;
	}
	#Rectangle_6 {
		fill: rgba(255,255,255,1);
		stroke: rgba(255,255,255,0.133);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 4;
		shape-rendering: auto;
	}
	.Rectangle_6 {
		filter: drop-shadow(0px 8px 20px rgba(108, 108, 108, 0.173));
		position: absolute;
		overflow: visible;
		width: 1172px;
		height: 106px;
		left: 84px;
		top: 111px;
	}
	#Icon__Search__Sharp {
		opacity: 0.607;
		position: absolute;
		width: 24.192px;
		height: 24.193px;
		left: 1130px;
		top: 124.807px;
		overflow: visible;
	}
	#Mask {
		fill: transparent;
	}
	.Mask {
		overflow: visible;
		position: absolute;
		width: 24.191px;
		height: 24.193px;
		left: 0px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Icon__Search__Sharp_b {
		position: absolute;
		width: 24.191px;
		height: 24.193px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Marka_rn_ad_veya_kategori_yazn {
		opacity: 0.4;
		left: 117px;
		top: 125px;
		position: absolute;
		overflow: visible;
		width: 854px;
		height: 21px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		color: rgba(186,186,186,1);
	}
	#Group_475 {
		position: absolute;
		width: 331.828px;
		height: 25.061px;
		left: 852.172px;
		top: 48.969px;
		overflow: visible;
		padding: 0px;
	}
	#Group_4102 {
		position: absolute;
		width: 102px;
		height: 25px;
		left: 229.828px;
		top: 0px;
		overflow: visible;
	}
	#Group_3489 {
		position: absolute;
		width: 25px;
		height: 25px;
		left: 77px;
		top: 0px;
		overflow: visible;
	}
	#Ellipse_138 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 4;
		shape-rendering: auto;
	}
	.Ellipse_138 {
		position: absolute;
		overflow: visible;
		width: 25px;
		height: 25px;
		left: 0px;
		top: 0px;
	}
	#MA {
		left: 5px;
		top: 6.5px;
		position: absolute;
		overflow: visible;
		width: 16px;
		white-space: nowrap;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 10px;
		color: rgba(112,212,75,1);
	}
	#Group_3495 {
		opacity: 0.999;
		position: absolute;
		width: 64px;
		height: 21px;
		left: 0px;
		top: 1px;
		overflow: visible;
	}
	#Group_3414 {
		position: absolute;
		width: 64px;
		height: 21px;
		left: 0px;
		top: 0px;
		overflow: visible;
		padding: 0px;
	}
	#Hesabm {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 65px;
		white-space: nowrap;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		color: rgba(35,31,32,1);
	}
	#Group_3490 {
		position: absolute;
		width: 25.117px;
		height: 25px;
		left: 174.711px;
		top: 0px;
		overflow: visible;
	}
	#Group_3491 {
		position: absolute;
		width: 20.078px;
		height: 20.763px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Path_2958 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Path_2958 {
		overflow: visible;
		position: absolute;
		width: 22.092px;
		height: 16.155px;
		left: 0px;
		top: 6.607px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2959 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Path_2959 {
		overflow: visible;
		position: absolute;
		width: 11.79px;
		height: 8.169px;
		left: 5.088px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Group_467 {
		position: absolute;
		width: 15.298px;
		height: 15.298px;
		left: 9.819px;
		top: 9.702px;
		overflow: visible;
	}
	#Group_468 {
		position: absolute;
		width: 15.298px;
		height: 15.298px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Oval {
		fill: rgba(112,212,75,1);
		stroke: rgba(255,255,255,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Oval {
		position: absolute;
		overflow: visible;
		width: 19.298px;
		height: 19.298px;
		left: 0px;
		top: 0px;
	}
	#n_ {
		left: 4.648px;
		top: 0.298px;
		position: absolute;
		overflow: visible;
		width: 7px;
		white-space: nowrap;
		text-align: center;
		font-family: Poppins-Regular;
		font-style: normal;
		font-weight: normal;
		font-size: 10px;
		color: rgba(255,255,255,1);
		letter-spacing: 0.3463807678222656px;
	}
	#Group_3494 {
		opacity: 0.999;
		position: absolute;
		width: 59px;
		height: 21px;
		left: 110.711px;
		top: 1px;
		overflow: visible;
	}
	#Group_3414_cq {
		position: absolute;
		width: 59px;
		height: 21px;
		left: 0px;
		top: 0px;
		overflow: visible;
		padding: 0px;
	}
	#Sepetim {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 60px;
		white-space: nowrap;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		color: rgba(35,31,32,1);
	}
	#Group_4098 {
		position: absolute;
		width: 27.711px;
		height: 25.061px;
		left: 53px;
		top: 0px;
		overflow: visible;
	}
	#heart {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 4;
		shape-rendering: auto;
	}
	.heart {
		overflow: visible;
		position: absolute;
		width: 21.966px;
		height: 20.12px;
		left: 0px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Group_3496 {
		position: absolute;
		width: 14.797px;
		height: 15px;
		left: 12.914px;
		top: 10.062px;
		overflow: visible;
	}
	#Group_468_cv {
		position: absolute;
		width: 14.797px;
		height: 15px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Oval_cw {
		fill: rgba(112,212,75,1);
		stroke: rgba(255,255,255,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Oval_cw {
		position: absolute;
		overflow: visible;
		width: 18.797px;
		height: 18.938px;
		left: 0px;
		top: 0px;
	}
	#n__cx {
		left: 5.398px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 5px;
		white-space: nowrap;
		line-height: 11px;
		margin-top: -0.5px;
		text-align: center;
		font-family: Poppins-Regular;
		font-style: normal;
		font-weight: normal;
		font-size: 10px;
		color: rgba(255,255,255,1);
		letter-spacing: 0.3463807678222656px;
	}
	#Group_477 {
		opacity: 0.999;
		position: absolute;
		width: 48px;
		height: 21px;
		left: 0px;
		top: 1px;
		overflow: visible;
	}
	#Group_3414_cz {
		position: absolute;
		width: 48px;
		height: 21px;
		left: 0px;
		top: 0px;
		overflow: visible;
		padding: 0px;
	}
	#Listem {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 49px;
		white-space: nowrap;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		color: rgba(35,31,32,1);
	}
	#Group_3499 {
		position: absolute;
		width: 343px;
		height: 19px;
		left: 378px;
		top: 52px;
		overflow: visible;
		padding: 0px;
	}
	#Nav_Label {
		position: absolute;
		width: 121px;
		height: 19px;
		left: 222px;
		top: 0px;
		overflow: visible;
	}
	#En_ok_Kiralanan {
		left: 0px;
		top: -2px;
		position: absolute;
		overflow: visible;
		width: 122px;
		white-space: nowrap;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		color: rgba(35,31,32,1);
	}
	#Line_52 {
		opacity: 0.3;
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 4;
		shape-rendering: auto;
	}
	.Line_52 {
		overflow: visible;
		position: absolute;
		width: 1px;
		height: 18.297px;
		left: 207px;
		top: 0.5px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Nav_Label_c {
		position: absolute;
		width: 68px;
		height: 19px;
		left: 124px;
		top: 0px;
		overflow: visible;
	}
	#Kurumsal {
		left: 0px;
		top: -2px;
		position: absolute;
		overflow: visible;
		width: 69px;
		white-space: nowrap;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		color: rgba(35,31,32,1);
	}
	#Line_51 {
		opacity: 0.3;
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 4;
		shape-rendering: auto;
	}
	.Line_51 {
		overflow: visible;
		position: absolute;
		width: 1px;
		height: 18.297px;
		left: 109px;
		top: 0.5px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Nav_Label_da {
		position: absolute;
		width: 94px;
		height: 19px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Nasl_alr {
		left: 0px;
		top: -2px;
		position: absolute;
		overflow: visible;
		width: 95px;
		white-space: nowrap;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		color: rgba(35,31,32,1);
	}
	#Group_4193 {
		position: absolute;
		width: 882.909px;
		height: 26.206px;
		left: 313.092px;
		top: 183px;
		overflow: visible;
		padding: 0px;
	}
	#Group_4181 {
		position: absolute;
		width: 95.562px;
		height: 22.971px;
		left: 787.347px;
		top: 2px;
		overflow: visible;
	}
	#DRONLAR {
		left: 40.563px;
		top: 2.001px;
		position: absolute;
		overflow: visible;
		width: 56px;
		white-space: nowrap;
		line-height: 25px;
		margin-top: -6.5px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 12px;
		color: rgba(35,31,32,1);
	}
	#Group_4182 {
		position: absolute;
		width: 32.007px;
		height: 22.971px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Line_77 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Line_77 {
		overflow: visible;
		position: absolute;
		width: 7.86px;
		height: 1px;
		left: 0px;
		top: 1.281px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Line_78 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Line_78 {
		overflow: visible;
		position: absolute;
		width: 1px;
		height: 5.187px;
		left: 3.43px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Rectangle_960 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Rectangle_960 {
		position: absolute;
		overflow: visible;
		width: 8.183px;
		height: 6.569px;
		left: 0.398px;
		top: 4.187px;
	}
	#Line_79 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Line_79 {
		overflow: visible;
		position: absolute;
		width: 7.86px;
		height: 1px;
		left: 25.146px;
		top: 1.281px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Line_80 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Line_80 {
		overflow: visible;
		position: absolute;
		width: 1px;
		height: 5.187px;
		left: 28.576px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Rectangle_961 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Rectangle_961 {
		position: absolute;
		overflow: visible;
		width: 8.183px;
		height: 6.569px;
		left: 25.545px;
		top: 4.187px;
	}
	#Rectangle_962 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Rectangle_962 {
		position: absolute;
		overflow: visible;
		width: 10.321px;
		height: 9.295px;
		left: 11.668px;
		top: 13.411px;
	}
	#Path_3726 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Path_3726 {
		overflow: visible;
		position: absolute;
		width: 9.784px;
		height: 16.058px;
		left: 3.928px;
		top: 8.889px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_3727 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Path_3727 {
		overflow: visible;
		position: absolute;
		width: 9.78px;
		height: 16.345px;
		left: 20.415px;
		top: 8.889px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Line_81 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Line_81 {
		overflow: visible;
		position: absolute;
		width: 2.611px;
		height: 4.063px;
		left: 12.66px;
		top: 10.463px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Line_82 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Line_82 {
		overflow: visible;
		position: absolute;
		width: 2.611px;
		height: 4.063px;
		left: 17.478px;
		top: 10.678px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Ellipse_304 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Ellipse_304 {
		position: absolute;
		overflow: visible;
		width: 4.748px;
		height: 4.748px;
		left: 13.954px;
		top: 15.185px;
	}
	#Path_3728 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Path_3728 {
		overflow: visible;
		position: absolute;
		width: 20.841px;
		height: 9.753px;
		left: 6.614px;
		top: 2.905px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Group_4166 {
		position: absolute;
		width: 132.129px;
		height: 26.083px;
		left: 618.217px;
		top: 0px;
		overflow: visible;
	}
	#SES_VE_GRNT {
		left: 34.13px;
		top: 4px;
		position: absolute;
		overflow: visible;
		width: 99px;
		white-space: nowrap;
		line-height: 25px;
		margin-top: -6.5px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 12px;
		color: rgba(35,31,32,1);
	}
	#Group_4173 {
		position: absolute;
		width: 25.714px;
		height: 26.083px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Rectangle_956 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Rectangle_956 {
		position: absolute;
		overflow: visible;
		width: 17.919px;
		height: 23.531px;
		left: 4.837px;
		top: 0px;
	}
	#Ellipse_297 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Ellipse_297 {
		position: absolute;
		overflow: visible;
		width: 4.076px;
		height: 4.076px;
		left: 11.349px;
		top: 4.244px;
	}
	#Ellipse_298 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Ellipse_298 {
		position: absolute;
		overflow: visible;
		width: 7.8px;
		height: 7.8px;
		left: 9.486px;
		top: 2.382px;
	}
	#Ellipse_299 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Ellipse_299 {
		position: absolute;
		overflow: visible;
		width: 4.076px;
		height: 4.076px;
		left: 11.349px;
		top: 13.981px;
	}
	#Ellipse_300 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Ellipse_300 {
		position: absolute;
		overflow: visible;
		width: 7.8px;
		height: 7.8px;
		left: 9.486px;
		top: 12.119px;
	}
	#Rectangle_957 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Rectangle_957 {
		position: absolute;
		overflow: visible;
		width: 4.076px;
		height: 3.276px;
		left: 11.349px;
		top: 21.53px;
	}
	#Rectangle_958 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Rectangle_958 {
		position: absolute;
		overflow: visible;
		width: 16.741px;
		height: 3.276px;
		left: 5.014px;
		top: 23.807px;
	}
	#Line_72 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Line_72 {
		overflow: visible;
		position: absolute;
		width: 2px;
		height: 7.629px;
		left: 0px;
		top: 7.689px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Line_73 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Line_73 {
		overflow: visible;
		position: absolute;
		width: 2px;
		height: 5.291px;
		left: 2.676px;
		top: 8.858px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Line_74 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Line_74 {
		overflow: visible;
		position: absolute;
		width: 2px;
		height: 7.629px;
		left: 25.715px;
		top: 7.689px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Line_75 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Line_75 {
		overflow: visible;
		position: absolute;
		width: 2px;
		height: 5.291px;
		left: 23.039px;
		top: 8.858px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Group_4164 {
		position: absolute;
		width: 108.214px;
		height: 23.034px;
		left: 473.003px;
		top: 2px;
		overflow: visible;
	}
	#KAMERELAR {
		left: 38.214px;
		top: 2px;
		position: absolute;
		overflow: visible;
		width: 71px;
		white-space: nowrap;
		line-height: 25px;
		margin-top: -6.5px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 12px;
		color: rgba(35,31,32,1);
	}
	#Group_4180 {
		position: absolute;
		width: 29.594px;
		height: 23.034px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Path_3723 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Path_3723 {
		overflow: visible;
		position: absolute;
		width: 31.595px;
		height: 25.033px;
		left: 0px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Ellipse_295 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Ellipse_295 {
		position: absolute;
		overflow: visible;
		width: 15.057px;
		height: 15.057px;
		left: 8.022px;
		top: 6.385px;
	}
	#Ellipse_296 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Ellipse_296 {
		position: absolute;
		overflow: visible;
		width: 3.652px;
		height: 3.652px;
		left: 23.94px;
		top: 5.514px;
	}
	#Group_4162 {
		position: absolute;
		width: 105.276px;
		height: 20.122px;
		left: 330.727px;
		top: 3px;
		overflow: visible;
	}
	#BLGSAYAR {
		left: 39.276px;
		top: 1.722px;
		position: absolute;
		overflow: visible;
		width: 67px;
		white-space: nowrap;
		line-height: 25px;
		margin-top: -6.5px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 12px;
		color: rgba(35,31,32,1);
		text-transform: uppercase;
	}
	#Group_4179 {
		position: absolute;
		width: 30.721px;
		height: 20.122px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Rectangle_963 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Rectangle_963 {
		position: absolute;
		overflow: visible;
		width: 28.549px;
		height: 18.101px;
		left: 2.018px;
		top: 0px;
	}
	#Rectangle_964 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: round;
		stroke-linecap: round;
		stroke-miterlimit: 4;
		shape-rendering: auto;
	}
	.Rectangle_964 {
		position: absolute;
		overflow: visible;
		width: 32.721px;
		height: 6.021px;
		left: 0px;
		top: 16.101px;
	}
	#Line_83 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Line_83 {
		overflow: visible;
		position: absolute;
		width: 3.623px;
		height: 1px;
		left: 14.049px;
		top: 2.487px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Group_4168 {
		position: absolute;
		width: 156.637px;
		height: 24.252px;
		left: 137.089px;
		top: 1px;
		overflow: visible;
	}
	#Group_4163 {
		position: absolute;
		width: 156.637px;
		height: 24.252px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Component_212__44 {
		position: absolute;
		width: 132px;
		height: 14px;
		left: 24.637px;
		top: 5px;
		overflow: visible;
	}
	#TELEFON_VE_AKSESUAR {
		left: 0px;
		top: -2px;
		position: absolute;
		overflow: visible;
		width: 133px;
		white-space: nowrap;
		line-height: 25px;
		margin-top: -6.5px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 12px;
		color: rgba(35,31,32,1);
	}
	#Group_4178 {
		position: absolute;
		width: 16.081px;
		height: 24.252px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Rectangle_955 {
		fill: rgba(0,0,0,0);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Rectangle_955 {
		position: absolute;
		overflow: visible;
		width: 18.081px;
		height: 26.252px;
		left: 0px;
		top: 0px;
	}
	#Line_71 {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: round;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Line_71 {
		overflow: visible;
		position: absolute;
		width: 4.19px;
		height: 1px;
		left: 6.569px;
		top: 21.425px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Group_4167 {
		position: absolute;
		width: 100.089px;
		height: 25.206px;
		left: 0px;
		top: 1px;
		overflow: visible;
	}
	#kiralamini {
		left: 35.089px;
		top: 4.7px;
		position: absolute;
		overflow: visible;
		width: 66px;
		white-space: nowrap;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 12px;
		color: rgba(35,31,32,1);
		text-transform: uppercase;
	}
	#bxs-baby-carriage {
		fill: transparent;
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: round;
		stroke-linecap: butt;
		stroke-miterlimit: 4;
		shape-rendering: auto;
	}
	.bxs-baby-carriage {
		overflow: visible;
		position: absolute;
		width: 26.534px;
		height: 25.206px;
		transform: translate(0px, 0px) matrix(1,0,0,1,0,0) rotate(0deg);
		transform-origin: center;
		left: 0px;
		top: 0px;
	}
	#Layer_1-2 {
		position: absolute;
		width: 242px;
		height: 40.67px;
		left: 84px;
		top: 38px;
		overflow: visible;
	}
	#Group_3804 {
		position: absolute;
		width: 242px;
		height: 40.67px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Group_3803 {
		position: absolute;
		width: 41.157px;
		height: 40.67px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Path_2976 {
		fill: rgba(95,74,244,1);
	}
	.Path_2976 {
		overflow: visible;
		position: absolute;
		width: 24.422px;
		height: 23.318px;
		left: 0px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2977 {
		fill: rgba(112,212,75,1);
	}
	.Path_2977 {
		overflow: visible;
		position: absolute;
		width: 24.422px;
		height: 23.318px;
		left: 16.735px;
		top: 17.351px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2978 {
		fill: rgba(112,212,75,1);
	}
	.Path_2978 {
		overflow: visible;
		position: absolute;
		width: 7.369px;
		height: 29.971px;
		left: 69.96px;
		top: 4.959px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2979 {
		fill: rgba(112,212,75,1);
	}
	.Path_2979 {
		overflow: visible;
		position: absolute;
		width: 14.697px;
		height: 20.27px;
		left: 79.326px;
		top: 14.659px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2980 {
		fill: rgba(112,212,75,1);
	}
	.Path_2980 {
		overflow: visible;
		position: absolute;
		width: 22.341px;
		height: 21.188px;
		left: 95.047px;
		top: 14.339px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2981 {
		fill: rgba(112,212,75,1);
	}
	.Path_2981 {
		overflow: visible;
		position: absolute;
		width: 6.372px;
		height: 29.133px;
		left: 119.873px;
		top: 5.796px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2982 {
		fill: rgba(112,212,75,1);
	}
	.Path_2982 {
		overflow: visible;
		position: absolute;
		width: 22.342px;
		height: 21.188px;
		left: 127.906px;
		top: 14.339px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2983 {
		fill: rgba(112,212,75,1);
	}
	.Path_2983 {
		overflow: visible;
		position: absolute;
		width: 22.342px;
		height: 29.729px;
		left: 152.661px;
		top: 5.796px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2984 {
		fill: rgba(112,212,75,1);
	}
	.Path_2984 {
		overflow: visible;
		position: absolute;
		width: 20.271px;
		height: 20.588px;
		left: 176.561px;
		top: 14.938px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2985 {
		fill: rgba(112,212,75,1);
	}
	.Path_2985 {
		overflow: visible;
		position: absolute;
		width: 20.271px;
		height: 20.588px;
		left: 199.643px;
		top: 14.341px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2986 {
		fill: rgba(112,212,75,1);
	}
	.Path_2986 {
		overflow: visible;
		position: absolute;
		width: 20.271px;
		height: 20.588px;
		left: 221.729px;
		top: 14.938px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2987 {
		fill: rgba(112,212,75,1);
	}
	.Path_2987 {
		overflow: visible;
		position: absolute;
		width: 21.831px;
		height: 29.133px;
		left: 47.507px;
		top: 5.796px;
		transform: matrix(1,0,0,1,0,0);
	}
	#katergoriler_button {
		position: absolute;
		width: 187.367px;
		height: 38.5px;
		left: 84px;
		top: 177px;
		overflow: visible;
	}
	#Group_64 {
		position: absolute;
		width: 187.367px;
		height: 38.5px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Kategoriler {
		left: 51.333px;
		top: 9.122px;
		position: absolute;
		overflow: visible;
		width: 92px;
		white-space: nowrap;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 18px;
		color: rgba(35,31,32,1);
		letter-spacing: 0.07px;
	}
	#Group_4192 {
		position: absolute;
		width: 187.367px;
		height: 38.5px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Rectangle_818 {
		fill: rgba(255,255,255,1);
	}
	.Rectangle_818 {
		filter: drop-shadow(0px 10px 20px rgba(145, 145, 145, 0.173));
		position: absolute;
		overflow: visible;
		width: 247.367px;
		height: 98.5px;
		left: 0px;
		top: 0px;
	}
	#Group_3484 {
		position: absolute;
		width: 146.656px;
		height: 21.389px;
		left: 17.111px;
		top: 8.556px;
		overflow: visible;
	}
	#Group_3484_fc {
		position: absolute;
		width: 22.245px;
		height: 21.389px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Rectangle_814 {
		fill: rgba(52,52,52,1);
	}
	.Rectangle_814 {
		position: absolute;
		overflow: visible;
		width: 10.267px;
		height: 10.267px;
		left: 0px;
		top: 0px;
	}
	#Rectangle_816 {
		fill: rgba(35,31,32,1);
	}
	.Rectangle_816 {
		position: absolute;
		overflow: visible;
		width: 10.267px;
		height: 10.267px;
		left: 0px;
		top: 11.122px;
	}
	#Rectangle_815 {
		fill: rgba(35,31,32,1);
	}
	.Rectangle_815 {
		position: absolute;
		overflow: visible;
		width: 10.267px;
		height: 10.267px;
		left: 11.978px;
		top: 0px;
	}
	#Rectangle_817 {
		fill: rgba(112,212,75,1);
	}
	.Rectangle_817 {
		position: absolute;
		overflow: visible;
		width: 10.267px;
		height: 10.267px;
		left: 11.978px;
		top: 11.122px;
	}
	#TM_KATEGORLER {
		left: 31.655px;
		top: 0.566px;
		position: absolute;
		overflow: visible;
		width: 116px;
		white-space: nowrap;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 13px;
		color: rgba(35,31,32,1);
	}
	#Group_4066 {
		position: absolute;
		width: 549.629px;
		height: 543.571px;
		left: 365.371px;
		top: 248.205px;
		overflow: visible;
	}
	#Rectangle_856 {
		fill: rgba(255,255,255,1);
	}
	.Rectangle_856 {
		filter: drop-shadow(0px 10px 30px rgba(132, 132, 132, 0.122));
		position: absolute;
		overflow: visible;
		width: 639.629px;
		height: 633.571px;
		left: 0px;
		top: 0px;
	}
	#Group_4854 {
		position: absolute;
		width: 364px;
		height: 134px;
		left: 458px;
		top: 574.491px;
		overflow: visible;
		padding: 0px;
	}
	#Group_4848 {
		position: absolute;
		width: 364px;
		height: 48px;
		left: 0px;
		top: 86px;
		overflow: visible;
	}
	#Component_249__2 {
		position: absolute;
		width: 364px;
		height: 48px;
		left: 0px;
		top: -0.001px;
		overflow: visible;
	}
	#Rectangle_738 {
		fill: rgba(255,255,255,1);
		stroke: rgba(240,240,240,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 4;
		shape-rendering: auto;
	}
	.Rectangle_738 {
		position: absolute;
		overflow: visible;
		width: 364px;
		height: 48px;
		left: 0px;
		top: 0px;
	}
	#Google_ile_Giri_Yap {
		left: 125px;
		top: 14.999px;
		position: absolute;
		overflow: visible;
		width: 204px;
		height: 21px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 16px;
		color: rgba(35,31,32,1);
	}
	#n_56aba9695709cb3ab9e705feb639 {
		position: absolute;
		width: 30px;
		height: 30px;
		left: 23px;
		top: 9.999px;
		overflow: visible;
	}
	#Group_4849 {
		position: absolute;
		width: 364px;
		height: 48px;
		left: 0px;
		top: 28px;
		overflow: visible;
	}
	#Component_249__1 {
		position: absolute;
		width: 364px;
		height: 48px;
		left: 0px;
		top: -0.001px;
		overflow: visible;
	}
	#Rectangle_738_fs {
		fill: rgba(255,255,255,1);
		stroke: rgba(240,240,240,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 4;
		shape-rendering: auto;
	}
	.Rectangle_738_fs {
		position: absolute;
		overflow: visible;
		width: 364px;
		height: 48px;
		left: 0px;
		top: 0px;
	}
	#Facebook_ile_Giri_Yap {
		left: 110px;
		top: 13.999px;
		position: absolute;
		overflow: visible;
		width: 189px;
		height: 21px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 16px;
		color: rgba(35,31,32,1);
	}
	#n_6d8ae5089a0f28acb86616180c91 {
		position: absolute;
		width: 34px;
		height: 30px;
		left: 19px;
		top: 8.999px;
		overflow: visible;
	}
	#Sosyal_medya_hesaplarna_giri_y {
		left: 71px;
		top: -0.001px;
		position: absolute;
		overflow: visible;
		width: 223px;
		white-space: nowrap;
		text-align: center;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(35,31,32,1);
	}
	#Group_3258 {
		position: absolute;
		width: 536px;
		height: 56px;
		left: 372px;
		top: 451.491px;
		overflow: visible;
	}
	#Group_969 {
		position: absolute;
		width: 536px;
		height: 56px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Text_Field__Outlined__Filled__ {
		position: absolute;
		width: 536px;
		height: 56px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Group_4857 {
		position: absolute;
		width: 364px;
		height: 88.728px;
		left: 458px;
		top: 463.764px;
		overflow: visible;
	}
	#Sign_Up_Button {
		position: absolute;
		width: 364px;
		height: 48px;
		left: 0px;
		top: 40.728px;
		overflow: visible;
	}
	#Rectangle_738_f {
		fill: rgba(35,31,32,1);
	}
	.Rectangle_738_f {
		filter: drop-shadow(0px 12px 20px rgba(21, 165, 89, 0));
		position: absolute;
		overflow: visible;
		width: 424px;
		height: 108px;
		left: 0px;
		top: 0px;
	}
	#Giri_Yap_f {
		left: 76px;
		top: 13px;
		position: absolute;
		overflow: hidden;
		width: 213px;
		height: 22px;
		text-align: center;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 18px;
		color: rgba(255,255,255,1);
	}
	#Group_4850 {
		position: absolute;
		width: 364px;
		height: 20px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#ifremi_Unuttum {
		left: 259px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 106px;
		white-space: nowrap;
		text-align: right;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(35,31,32,1);
		text-decoration: underline;
	}
	#Group_4847 {
		position: absolute;
		width: 108px;
		height: 20px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Caption {
		left: 33px;
		top: 1px;
		position: absolute;
		overflow: visible;
		width: 76px;
		white-space: nowrap;
		line-height: 24px;
		margin-top: -5px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 14px;
		color: rgba(35,31,32,1);
		letter-spacing: 0.0974195957183838px;
	}
	#Component_116__52 {
		position: absolute;
		width: 20px;
		height: 20px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Rectangle_781 {
		fill: rgba(255,255,255,1);
		stroke: rgba(35,31,32,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 4;
		shape-rendering: auto;
	}
	.Rectangle_781 {
		position: absolute;
		overflow: visible;
		width: 20px;
		height: 20px;
		left: 0px;
		top: 0px;
	}
	#md-checkmark {
		fill: rgba(255,255,255,1);
	}
	.md-checkmark {
		overflow: visible;
		position: absolute;
		width: 11px;
		height: 8.364px;
		left: 5px;
		top: 5.817px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Group_4858 {
		position: absolute;
		width: 367.506px;
		height: 166.999px;
		left: 456px;
		top: 274.492px;
		overflow: visible;
	}
	#Group_4067 {
		position: absolute;
		width: 89px;
		height: 29px;
		left: 140.137px;
		top: 0px;
		overflow: visible;
	}
	#Giri_Yap_gc {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 90px;
		white-space: nowrap;
		line-height: 31px;
		margin-top: -4.5px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 22px;
		color: rgba(18,17,17,1);
		letter-spacing: 0.01px;
	}
	#Group_4844 {
		position: absolute;
		width: 367.506px;
		height: 48px;
		left: 0px;
		top: 51.999px;
		overflow: visible;
	}
	#Surface {
		fill: transparent;
		stroke: rgba(235,235,235,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Surface {
		overflow: visible;
		position: absolute;
		width: 367.506px;
		height: 48px;
		left: 0px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Group_4843 {
		position: absolute;
		width: 246px;
		height: 21px;
		left: 19px;
		top: 15px;
		overflow: visible;
	}
	#CAPTION_TEXT_COLOR__STYLE {
		position: absolute;
		width: 400.183px;
		height: 16px;
		left: -36.958px;
		top: 38px;
		display: none;
		overflow: visible;
	}
	#Caption_gh {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 401.183px;
		height: 16px;
		line-height: 16px;
		margin-top: -2.2260074615478516px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 11.547985076904297px;
		color: rgba(49,52,64,1);
		letter-spacing: 0.3463807678222656px;
	}
	#SUBTITLE_1_COLOR__STYLE {
		position: absolute;
		width: 246px;
		height: 21px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Subtitle_1 {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 247px;
		height: 21px;
		line-height: 24px;
		margin-top: -4px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		color: rgba(186,186,186,1);
		letter-spacing: 0.0974195957183838px;
	}
	#Group_4845 {
		position: absolute;
		width: 367.506px;
		height: 48px;
		left: 0px;
		top: 118.999px;
		overflow: visible;
	}
	#Surface_gl {
		fill: transparent;
		stroke: rgba(235,235,235,1);
		stroke-width: 2px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 10;
		shape-rendering: auto;
	}
	.Surface_gl {
		overflow: visible;
		position: absolute;
		width: 367.506px;
		height: 48px;
		left: 0px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Group_4843_gm {
		position: absolute;
		width: 245px;
		height: 21px;
		left: 20px;
		top: 15px;
		overflow: visible;
	}
	#CAPTION_TEXT_COLOR__STYLE_gn {
		position: absolute;
		width: 398.918px;
		height: 16px;
		left: -36.895px;
		top: 38px;
		display: none;
		overflow: visible;
	}
	#Caption_go {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 399.918px;
		height: 16px;
		line-height: 16px;
		margin-top: -2.2260074615478516px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 11.547985076904297px;
		color: rgba(49,52,64,1);
		letter-spacing: 0.3463807678222656px;
	}
	#SUBTITLE_1_COLOR__STYLE_gp {
		position: absolute;
		width: 245px;
		height: 21px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Subtitle_1_gq {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 246px;
		height: 21px;
		line-height: 24px;
		margin-top: -4px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		color: rgba(186,186,186,1);
		letter-spacing: 0.0974195957183838px;
	}
	#Group_974 {
		position: absolute;
		width: 361.494px;
		height: 56px;
		left: 456.506px;
		top: 361.491px;
		overflow: visible;
	}
	#Group_969_gs {
		position: absolute;
		width: 361.494px;
		height: 56px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Text_Field__Outlined__Filled___gt {
		position: absolute;
		width: 361.494px;
		height: 56px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Group_4856 {
		position: absolute;
		width: 210px;
		height: 43px;
		left: 535px;
		top: 730.491px;
		overflow: visible;
	}
	#Component_250__1 {
		position: absolute;
		width: 106px;
		height: 19px;
		left: 52px;
		top: 23.999px;
		overflow: visible;
	}
	#yelik_Olutur {
		left: 0px;
		top: -2px;
		position: absolute;
		overflow: visible;
		width: 107px;
		white-space: nowrap;
		line-height: 31px;
		margin-top: -7.5px;
		text-align: center;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 16px;
		color: rgba(35,31,32,1);
	}
	#Group_4855 {
		position: absolute;
		width: 210px;
		height: 19px;
		left: 0px;
		top: -0.001px;
		overflow: visible;
	}
	#kiralabunucoma_ye_deil_misin {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 211px;
		white-space: nowrap;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 14px;
		color: rgba(0,0,0,1);
	}
	#Footer {
		position: absolute;
		width: 1283px;
		height: 317.879px;
		left: -1px;
		top: 882.121px;
		overflow: visible;
	}
	#Rectangle_13 {
		fill: rgba(245,245,245,1);
	}
	.Rectangle_13 {
		position: absolute;
		overflow: visible;
		width: 1283px;
		height: 62px;
		left: 0px;
		top: 255.879px;
	}
	#Terms__conditions_ {
		left: 87px;
		top: 281.879px;
		position: absolute;
		overflow: visible;
		width: 194px;
		white-space: nowrap;
		line-height: 20px;
		margin-top: -4px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 12px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Image_1 {
		mix-blend-mode: multiply;
		position: absolute;
		width: 454px;
		height: 24px;
		left: 369px;
		top: 277.879px;
		overflow: visible;
	}
	#n_020_All_right_rese {
		left: 960px;
		top: 281.879px;
		position: absolute;
		overflow: visible;
		width: 240px;
		white-space: nowrap;
		line-height: 20px;
		margin-top: -4px;
		text-align: right;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 12px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Group_4366 {
		position: absolute;
		width: 1111.715px;
		height: 228.879px;
		left: 87px;
		top: 0px;
		overflow: visible;
		padding: 0px;
	}
	#Group_4364 {
		position: absolute;
		width: 266px;
		height: 178px;
		left: 845.715px;
		top: 8.879px;
		overflow: visible;
	}
	#Group_4360 {
		position: absolute;
		width: 266px;
		height: 178px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#HEAD_OFFICE {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 267px;
		height: 21px;
		line-height: 31px;
		margin-top: -7.5px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 16px;
		color: rgba(18,17,17,1);
	}
	#Group_3884 {
		position: absolute;
		width: 246px;
		height: 135px;
		left: 0px;
		top: 43px;
		overflow: visible;
		padding: 0px;
	}
	#Component_172__1 {
		position: absolute;
		width: 232px;
		height: 36px;
		left: 0px;
		top: 99px;
		overflow: visible;
		padding: 10px 15px 10px 15px;
	}
	#Rectangle_29 {
		fill: rgba(199,199,199,1);
	}
	.Rectangle_29 {
		position: absolute;
		overflow: visible;
		width: 232px;
		height: 36px;
		left: 0px;
		top: 0px;
	}
	#Mteri_Hizmetlerimize_Balann {
		left: 15px;
		top: 8px;
		position: absolute;
		overflow: visible;
		width: 203px;
		white-space: nowrap;
		line-height: 16px;
		margin-top: -1px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(255,255,255,1);
	}
	#Group_3883 {
		position: absolute;
		width: 225.505px;
		height: 19px;
		left: 0px;
		top: 71px;
		overflow: visible;
	}
	#Line_63 {
		opacity: 0.2;
		fill: transparent;
		stroke: rgba(18,17,17,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 4;
		shape-rendering: auto;
	}
	.Line_63 {
		overflow: visible;
		position: absolute;
		width: 63.251px;
		height: 1px;
		left: 0px;
		top: 12.084px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Line_64 {
		opacity: 0.2;
		fill: transparent;
		stroke: rgba(18,17,17,1);
		stroke-width: 1px;
		stroke-linejoin: miter;
		stroke-linecap: butt;
		stroke-miterlimit: 4;
		shape-rendering: auto;
	}
	.Line_64 {
		overflow: visible;
		position: absolute;
		width: 75.168px;
		height: 1px;
		left: 150.337px;
		top: 12.084px;
		transform: matrix(1,0,0,1,0,0);
	}
	#VEYA {
		left: 90.752px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 35px;
		white-space: nowrap;
		line-height: 18px;
		margin-top: -2px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#HEAD_OFFICE_hg {
		left: 0px;
		top: 28px;
		position: absolute;
		overflow: visible;
		width: 247px;
		height: 39px;
		line-height: 31px;
		margin-top: -0.5px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 30px;
		color: rgba(95,74,244,1);
		letter-spacing: 0.01px;
	}
	#ar_Merkezimizi_arayn {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 157px;
		white-space: nowrap;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Group_4363 {
		position: absolute;
		width: 185px;
		height: 162px;
		left: 658px;
		top: 8.879px;
		overflow: visible;
	}
	#HEAD_OFFICE_hj {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 181px;
		height: 21px;
		line-height: 31px;
		margin-top: -7.5px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 16px;
		color: rgba(18,17,17,1);
	}
	#Group_4357 {
		position: absolute;
		width: 185px;
		height: 117px;
		left: 0px;
		top: 45px;
		overflow: visible;
		padding: 0px;
	}
	#erez_Cookie_Politikas {
		left: 0px;
		top: 98px;
		position: absolute;
		overflow: visible;
		width: 172px;
		height: 19px;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Cayma_Fesih_ve_ade_Koullar {
		left: 0px;
		top: 52px;
		position: absolute;
		overflow: visible;
		width: 172px;
		height: 39px;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Aydnlatma_Metni {
		left: 0px;
		top: 26px;
		position: absolute;
		overflow: visible;
		width: 186px;
		height: 19px;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#yelik_Szlemesi {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 172px;
		height: 19px;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Group_4362 {
		position: absolute;
		width: 172px;
		height: 116px;
		left: 446px;
		top: 8.879px;
		overflow: visible;
	}
	#HEAD_OFFICE_hq {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 152px;
		height: 21px;
		line-height: 31px;
		margin-top: -7.5px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 16px;
		color: rgba(18,17,17,1);
	}
	#Group_4358 {
		position: absolute;
		width: 172px;
		height: 71px;
		left: 0px;
		top: 45px;
		overflow: visible;
		padding: 0px;
	}
	#deme_ve_Teslimat {
		left: 0px;
		top: 52px;
		position: absolute;
		overflow: visible;
		width: 154px;
		height: 19px;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Kimlik_ve_Findeks_Raporu {
		left: 0px;
		top: 26px;
		position: absolute;
		overflow: visible;
		width: 173px;
		height: 19px;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Nasl_Kiralarm {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 127px;
		height: 19px;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Group_4361 {
		position: absolute;
		width: 101px;
		height: 220px;
		left: 281.715px;
		top: 8.879px;
		overflow: visible;
	}
	#LATEST_POSTS {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 102px;
		height: 21px;
		line-height: 31px;
		margin-top: -7.5px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: bold;
		font-size: 16px;
		color: rgba(18,17,17,1);
	}
	#Group_4359 {
		position: absolute;
		width: 74px;
		height: 175px;
		left: 0px;
		top: 45px;
		overflow: visible;
		padding: 0px;
	}
	#letiim {
		left: 0px;
		top: 156px;
		position: absolute;
		overflow: visible;
		width: 47px;
		white-space: nowrap;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Blog {
		left: 0px;
		top: 130px;
		position: absolute;
		overflow: visible;
		width: 29px;
		white-space: nowrap;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Kiralamini_h {
		left: 0px;
		top: 104px;
		position: absolute;
		overflow: visible;
		width: 63px;
		white-space: nowrap;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Basnda_Biz {
		left: 0px;
		top: 78px;
		position: absolute;
		overflow: visible;
		width: 74px;
		white-space: nowrap;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Kurumsal_h {
		left: 0px;
		top: 52px;
		position: absolute;
		overflow: visible;
		width: 60px;
		white-space: nowrap;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Hakkmzda {
		left: 0px;
		top: 26px;
		position: absolute;
		overflow: visible;
		width: 75px;
		white-space: nowrap;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Anasayfa {
		left: 0px;
		top: 0px;
		position: absolute;
		overflow: visible;
		width: 60px;
		white-space: nowrap;
		line-height: 20px;
		margin-top: -3px;
		text-align: left;
		font-family: Roboto;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;
		color: rgba(152,152,152,1);
		letter-spacing: 0.01px;
	}
	#Group_4365 {
		position: absolute;
		width: 218.715px;
		height: 104.057px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Layer_1-2_h {
		position: absolute;
		width: 218.715px;
		height: 36.756px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Group_3804_h {
		position: absolute;
		width: 218.715px;
		height: 36.756px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Group_3803_h {
		position: absolute;
		width: 37.197px;
		height: 36.756px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Path_2976_h {
		fill: rgba(95,74,244,1);
	}
	.Path_2976_h {
		overflow: visible;
		position: absolute;
		width: 22.072px;
		height: 21.075px;
		left: 0px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2977_ia {
		fill: rgba(112,212,75,1);
	}
	.Path_2977_ia {
		overflow: visible;
		position: absolute;
		width: 22.072px;
		height: 21.075px;
		left: 15.125px;
		top: 15.682px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2978_ib {
		fill: rgba(112,212,75,1);
	}
	.Path_2978_ib {
		overflow: visible;
		position: absolute;
		width: 6.66px;
		height: 27.087px;
		left: 63.229px;
		top: 4.481px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2979_ic {
		fill: rgba(112,212,75,1);
	}
	.Path_2979_ic {
		overflow: visible;
		position: absolute;
		width: 13.283px;
		height: 18.319px;
		left: 71.693px;
		top: 13.249px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2980_id {
		fill: rgba(112,212,75,1);
	}
	.Path_2980_id {
		overflow: visible;
		position: absolute;
		width: 20.192px;
		height: 19.148px;
		left: 85.9px;
		top: 12.959px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2981_ie {
		fill: rgba(112,212,75,1);
	}
	.Path_2981_ie {
		overflow: visible;
		position: absolute;
		width: 5.759px;
		height: 26.33px;
		left: 108.339px;
		top: 5.238px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2982_if {
		fill: rgba(112,212,75,1);
	}
	.Path_2982_if {
		overflow: visible;
		position: absolute;
		width: 20.191px;
		height: 19.148px;
		left: 115.6px;
		top: 12.959px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2983_ig {
		fill: rgba(112,212,75,1);
	}
	.Path_2983_ig {
		overflow: visible;
		position: absolute;
		width: 20.192px;
		height: 26.869px;
		left: 137.972px;
		top: 5.238px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2984_ih {
		fill: rgba(112,212,75,1);
	}
	.Path_2984_ih {
		overflow: visible;
		position: absolute;
		width: 18.321px;
		height: 18.607px;
		left: 159.572px;
		top: 13.5px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2985_ii {
		fill: rgba(112,212,75,1);
	}
	.Path_2985_ii {
		overflow: visible;
		position: absolute;
		width: 18.321px;
		height: 18.607px;
		left: 180.433px;
		top: 12.962px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2986_ij {
		fill: rgba(112,212,75,1);
	}
	.Path_2986_ij {
		overflow: visible;
		position: absolute;
		width: 18.321px;
		height: 18.607px;
		left: 200.394px;
		top: 13.5px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2987_ik {
		fill: rgba(112,212,75,1);
	}
	.Path_2987_ik {
		overflow: visible;
		position: absolute;
		width: 19.73px;
		height: 26.33px;
		left: 42.936px;
		top: 5.238px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Social_1 {
		position: absolute;
		width: 218.715px;
		height: 33.648px;
		left: 0px;
		top: 70.408px;
		overflow: visible;
		padding: 0px;
	}
	#LinkedIn {
		position: absolute;
		width: 33.648px;
		height: 33.648px;
		left: 185.066px;
		top: 0px;
		overflow: visible;
	}
	#Group_3880 {
		position: absolute;
		width: 33.648px;
		height: 33.648px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Background {
		opacity: 0.5;
		fill: rgba(235,235,235,1);
	}
	.Background {
		overflow: visible;
		position: absolute;
		width: 33.648px;
		height: 33.648px;
		left: 0px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#social-youtube-outline {
		position: absolute;
		width: 17.173px;
		height: 12.883px;
		left: 8.237px;
		top: 10.383px;
		overflow: visible;
	}
	#Path_2998 {
		fill: rgba(95,74,244,1);
	}
	.Path_2998 {
		overflow: visible;
		position: absolute;
		width: 17.173px;
		height: 12.883px;
		left: 0px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2999 {
		fill: rgba(95,74,244,1);
	}
	.Path_2999 {
		overflow: visible;
		position: absolute;
		width: 4.863px;
		height: 6.588px;
		left: 6.943px;
		top: 3.134px;
		transform: matrix(1,0,0,1,0,0);
	}
	#LinkedIn_is {
		position: absolute;
		width: 33.648px;
		height: 33.648px;
		left: 123.821px;
		top: 0px;
		overflow: visible;
	}
	#Background_it {
		opacity: 0.5;
		fill: rgba(235,235,235,1);
	}
	.Background_it {
		overflow: visible;
		position: absolute;
		width: 33.648px;
		height: 33.648px;
		left: 0px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#LinkedIn_Icon {
		fill: rgba(95,74,244,1);
	}
	.LinkedIn_Icon {
		overflow: visible;
		position: absolute;
		width: 14.444px;
		height: 14.444px;
		left: 10.031px;
		top: 10.593px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Twitter {
		position: absolute;
		width: 32.741px;
		height: 32.741px;
		left: 62.593px;
		top: 0px;
		overflow: visible;
	}
	#Background_iw {
		opacity: 0.5;
		fill: rgba(235,235,235,1);
	}
	.Background_iw {
		position: absolute;
		overflow: visible;
		width: 32.741px;
		height: 32.741px;
		left: 0px;
		top: 0px;
	}
	#twitter {
		fill: rgba(95,74,244,1);
	}
	.twitter {
		overflow: visible;
		position: absolute;
		width: 15.142px;
		height: 12.313px;
		left: 9.259px;
		top: 10.668px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Facebook {
		position: absolute;
		width: 32.741px;
		height: 32.741px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Background_iz {
		opacity: 0.5;
		fill: rgba(235,235,235,1);
	}
	.Background_iz {
		position: absolute;
		overflow: visible;
		width: 32.741px;
		height: 32.741px;
		left: 0px;
		top: 0px;
	}
	#Facebook_Icon {
		fill: rgba(95,74,244,1);
	}
	.Facebook_Icon {
		overflow: visible;
		position: absolute;
		width: 6.212px;
		height: 13.459px;
		left: 13.459px;
		top: 10.095px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Component_119__5 {
		position: absolute;
		width: 17.749px;
		height: 15px;
		left: 787px;
		top: 409.991px;
		overflow: visible;
	}
	#eye {
		opacity: 0;
		position: absolute;
		width: 17.749px;
		height: 12.984px;
		left: 0px;
		top: 1.009px;
		overflow: visible;
	}
	#Path_2946 {
		fill: rgba(35,31,32,1);
	}
	.Path_2946 {
		overflow: visible;
		position: absolute;
		width: 17.749px;
		height: 12.984px;
		left: 0px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2947 {
		fill: rgba(35,31,32,1);
	}
	.Path_2947 {
		overflow: visible;
		position: absolute;
		width: 7.765px;
		height: 8.263px;
		left: 4.992px;
		top: 2.36px;
		transform: matrix(1,0,0,1,0,0);
	}
	#eye-slash {
		position: absolute;
		width: 17.749px;
		height: 15px;
		left: 0px;
		top: 0px;
		overflow: visible;
	}
	#Path_2948 {
		fill: rgba(35,31,32,1);
	}
	.Path_2948 {
		overflow: visible;
		position: absolute;
		width: 11.97px;
		height: 10.313px;
		left: 5.779px;
		top: 1.009px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2949 {
		fill: rgba(35,31,32,1);
	}
	.Path_2949 {
		overflow: visible;
		position: absolute;
		width: 7.77px;
		height: 8.268px;
		left: 4.988px;
		top: 3.366px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2950 {
		fill: rgba(35,31,32,1);
	}
	.Path_2950 {
		overflow: visible;
		position: absolute;
		width: 11.969px;
		height: 10.313px;
		left: 0px;
		top: 3.679px;
		transform: matrix(1,0,0,1,0,0);
	}
	#Path_2951 {
		fill: rgba(35,31,32,1);
	}
	.Path_2951 {
		overflow: visible;
		position: absolute;
		width: 14.097px;
		height: 15px;
		left: 1.826px;
		top: 0px;
		transform: matrix(1,0,0,1,0,0);
	}
</style>
<script id="applicationScript">
///////////////////////////////////////
// INITIALIZATION
///////////////////////////////////////

/**
 * Functionality for scaling, showing by media query, and navigation between multiple pages on a single page. 
 * Code subject to change.
 **/

if (window.console==null) { window["console"] = { log : function() {} } }; // some browsers do not set console

var Application = function() {
	// event constants
	this.prefix = "--web-";
	this.NAVIGATION_CHANGE = "viewChange";
	this.VIEW_NOT_FOUND = "viewNotFound";
	this.VIEW_CHANGE = "viewChange";
	this.VIEW_CHANGING = "viewChanging";
	this.STATE_NOT_FOUND = "stateNotFound";
	this.APPLICATION_COMPLETE = "applicationComplete";
	this.APPLICATION_RESIZE = "applicationResize";
	this.SIZE_STATE_NAME = "data-is-view-scaled";
	this.STATE_NAME = this.prefix + "state";

	this.lastTrigger = null;
	this.lastView = null;
	this.lastState = null;
	this.lastOverlay = null;
	this.currentView = null;
	this.currentState = null;
	this.currentOverlay = null;
	this.currentQuery = {index: 0, rule: null, mediaText: null, id: null};
	this.inclusionQuery = "(min-width: 0px)";
	this.exclusionQuery = "none and (min-width: 99999px)";
	this.LastModifiedDateLabelName = "LastModifiedDateLabel";
	this.viewScaleSliderId = "ViewScaleSliderInput";
	this.pageRefreshedName = "showPageRefreshedNotification";
	this.application = null;
	this.applicationStylesheet = null;
	this.showByMediaQuery = null;
	this.mediaQueryDictionary = {};
	this.viewsDictionary = {};
	this.addedViews = [];
	this.viewStates = [];
	this.views = [];
	this.viewIds = [];
	this.viewQueries = {};
	this.overlays = {};
	this.overlayIds = [];
	this.numberOfViews = 0;
	this.verticalPadding = 0;
	this.horizontalPadding = 0;
	this.stateName = null;
	this.viewScale = 1;
	this.viewLeft = 0;
	this.viewTop = 0;
	this.horizontalScrollbarsNeeded = false;
	this.verticalScrollbarsNeeded = false;

	// view settings
	this.showUpdateNotification = false;
	this.showNavigationControls = false;
	this.scaleViewsToFit = false;
	this.scaleToFitOnDoubleClick = false;
	this.actualSizeOnDoubleClick = false;
	this.scaleViewsOnResize = false;
	this.navigationOnKeypress = false;
	this.showViewName = false;
	this.enableDeepLinking = true;
	this.refreshPageForChanges = false;
	this.showRefreshNotifications = true;

	// view controls
	this.scaleViewSlider = null;
	this.lastModifiedLabel = null;
	this.supportsPopState = false; // window.history.pushState!=null;
	this.initialized = false;

	// refresh properties
	this.refreshDuration = 250;
	this.lastModifiedDate = null;
	this.refreshRequest = null;
	this.refreshInterval = null;
	this.refreshContent = null;
	this.refreshContentSize = null;
	this.refreshCheckContent = false;
	this.refreshCheckContentSize = false;

	var self = this;

	self.initialize = function(event) {
		var view = self.getVisibleView();
		var views = self.getVisibleViews();
		if (view==null) view = self.getInitialView();
		self.collectViews();
		self.collectOverlays();
		self.collectMediaQueries();

		for (let index = 0; index < views.length; index++) {
			var view = views[index];
			self.setViewOptions(view);
			self.setViewVariables(view);
			self.centerView(view);
		}

		// sometimes the body size is 0 so we call this now and again later
		if (self.initialized) {
			window.addEventListener(self.NAVIGATION_CHANGE, self.viewChangeHandler);
			window.addEventListener("keyup", self.keypressHandler);
			window.addEventListener("keypress", self.keypressHandler);
			window.addEventListener("resize", self.resizeHandler);
			window.document.addEventListener("dblclick", self.doubleClickHandler);

			if (self.supportsPopState) {
				window.addEventListener('popstate', self.popStateHandler);
			}
			else {
				window.addEventListener('hashchange', self.hashChangeHandler);
			}

			// we are ready to go
			window.dispatchEvent(new Event(self.APPLICATION_COMPLETE));
		}

		if (self.initialized==false) {
			if (self.enableDeepLinking) {
				self.syncronizeViewToURL();
			} 
	
			if (self.refreshPageForChanges) {
				self.setupRefreshForChanges();
			}
	
			self.initialized = true;
		}
		
		if (self.scaleViewsToFit) {
			self.viewScale = self.scaleViewToFit(view);
			
			if (self.viewScale<0) {
				setTimeout(self.scaleViewToFit, 500, view);
			}
		}
		else if (view) {
			self.viewScale = self.getViewScaleValue(view);
			self.centerView(view);
			self.updateSliderValue(self.viewScale);
		}
		else {
			// no view found
		}
	
		if (self.showUpdateNotification) {
			self.showNotification();
		}

		//"addEventListener" in window ? null : window.addEventListener = window.attachEvent;
		//"addEventListener" in document ? null : document.addEventListener = document.attachEvent;
	}


	///////////////////////////////////////
	// AUTO REFRESH 
	///////////////////////////////////////

	self.setupRefreshForChanges = function() {
		self.refreshRequest = new XMLHttpRequest();

		if (!self.refreshRequest) {
			return false;
		}

		// get document start values immediately
		self.requestRefreshUpdate();
	}

	/**
	 * Attempt to check the last modified date by the headers 
	 * or the last modified property from the byte array (experimental)
	 **/
	self.requestRefreshUpdate = function() {
		var url = document.location.href;
		var protocol = window.location.protocol;
		var method;
		
		try {

			if (self.refreshCheckContentSize) {
				self.refreshRequest.open('HEAD', url, true);
			}
			else if (self.refreshCheckContent) {
				self.refreshContent = document.documentElement.outerHTML;
				self.refreshRequest.open('GET', url, true);
				self.refreshRequest.responseType = "text";
			}
			else {

				// get page last modified date for the first call to compare to later
				if (self.lastModifiedDate==null) {

					// File system does not send headers in FF so get blob if possible
					if (protocol=="file:") {
						self.refreshRequest.open("GET", url, true);
						self.refreshRequest.responseType = "blob";
					}
					else {
						self.refreshRequest.open("HEAD", url, true);
						self.refreshRequest.responseType = "blob";
					}

					self.refreshRequest.onload = self.refreshOnLoadOnceHandler;

					// In some browsers (Chrome & Safari) this error occurs at send: 
					// 
					// Chrome - Access to XMLHttpRequest at 'file:///index.html' from origin 'null' 
					// has been blocked by CORS policy: 
					// Cross origin requests are only supported for protocol schemes: 
					// http, data, chrome, chrome-extension, https.
					// 
					// Safari - XMLHttpRequest cannot load file:///Users/<USER>/Public/index.html. Cross origin requests are only supported for HTTP.
					// 
					// Solution is to run a local server, set local permissions or test in another browser
					self.refreshRequest.send(null);

					// In MS browsers the following behavior occurs possibly due to an AJAX call to check last modified date: 
					// 
					// DOM7011: The code on this page disabled back and forward caching.

					// In Brave (Chrome) error when on the server
					// index.js:221 HEAD https://www.example.com/ net::ERR_INSUFFICIENT_RESOURCES
					// self.refreshRequest.send(null);

				}
				else {
					self.refreshRequest = new XMLHttpRequest();
					self.refreshRequest.onreadystatechange = self.refreshHandler;
					self.refreshRequest.ontimeout = function() {
						self.log("Couldn't find page to check for updates");
					}
					
					var method;
					if (protocol=="file:") {
						method = "GET";
					}
					else {
						method = "HEAD";
					}

					//refreshRequest.open('HEAD', url, true);
					self.refreshRequest.open(method, url, true);
					self.refreshRequest.responseType = "blob";
					self.refreshRequest.send(null);
				}
			}
		}
		catch (error) {
			self.log("Refresh failed for the following reason:")
			self.log(error);
		}
	}

	self.refreshHandler = function() {
		var contentSize;

		try {

			if (self.refreshRequest.readyState === XMLHttpRequest.DONE) {
				
				if (self.refreshRequest.status === 2 || 
					self.refreshRequest.status === 200) {
					var pageChanged = false;

					self.updateLastModifiedLabel();

					if (self.refreshCheckContentSize) {
						var lastModifiedHeader = self.refreshRequest.getResponseHeader("Last-Modified");
						contentSize = self.refreshRequest.getResponseHeader("Content-Length");
						//lastModifiedDate = refreshRequest.getResponseHeader("Last-Modified");
						var headers = self.refreshRequest.getAllResponseHeaders();
						var hasContentHeader = headers.indexOf("Content-Length")!=-1;
						
						if (hasContentHeader) {
							contentSize = self.refreshRequest.getResponseHeader("Content-Length");

							// size has not been set yet
							if (self.refreshContentSize==null) {
								self.refreshContentSize = contentSize;
								// exit and let interval call this method again
								return;
							}

							if (contentSize!=self.refreshContentSize) {
								pageChanged = true;
							}
						}
					}
					else if (self.refreshCheckContent) {

						if (self.refreshRequest.responseText!=self.refreshContent) {
							pageChanged = true;
						}
					}
					else {
						lastModifiedHeader = self.getLastModified(self.refreshRequest);

						if (self.lastModifiedDate!=lastModifiedHeader) {
							self.log("lastModifiedDate:" + self.lastModifiedDate + ",lastModifiedHeader:" +lastModifiedHeader);
							pageChanged = true;
						}

					}

					
					if (pageChanged) {
						clearInterval(self.refreshInterval);
						self.refreshUpdatedPage();
						return;
					}

				}
				else {
					self.log('There was a problem with the request.');
				}

			}
		}
		catch( error ) {
			//console.log('Caught Exception: ' + error);
		}
	}

	self.refreshOnLoadOnceHandler = function(event) {

		// get the last modified date
		if (self.refreshRequest.response) {
			self.lastModifiedDate = self.getLastModified(self.refreshRequest);

			if (self.lastModifiedDate!=null) {

				if (self.refreshInterval==null) {
					self.refreshInterval = setInterval(self.requestRefreshUpdate, self.refreshDuration);
				}
			}
			else {
				self.log("Could not get last modified date from the server");
			}
		}
	}

	self.refreshUpdatedPage = function() {
		if (self.showRefreshNotifications) {
			var date = new Date().setTime((new Date().getTime()+10000));
			document.cookie = encodeURIComponent(self.pageRefreshedName) + "=true" + "; max-age=6000;" + " path=/";
		}

		document.location.reload(true);
	}

	self.showNotification = function(duration) {
		var notificationID = self.pageRefreshedName+"ID";
		var notification = document.getElementById(notificationID);
		if (duration==null) duration = 4000;

		if (notification!=null) {return;}

		notification = document.createElement("div");
		notification.id = notificationID;
		notification.textContent = "PAGE UPDATED";
		var styleRule = ""
		styleRule = "position: fixed; padding: 7px 16px 6px 16px; font-family: Arial, sans-serif; font-size: 10px; font-weight: bold; left: 50%;";
		styleRule += "top: 20px; background-color: rgba(0,0,0,.5); border-radius: 12px; color:rgb(235, 235, 235); transition: all 2s linear;";
		styleRule += "transform: translateX(-50%); letter-spacing: .5px; filter: drop-shadow(2px 2px 6px rgba(0, 0, 0, .1)); cursor: pointer";
		notification.setAttribute("style", styleRule);

		notification.className = "PageRefreshedClass";
		notification.addEventListener("click", function() {
			notification.parentNode.removeChild(notification);
		});
		
		document.body.appendChild(notification);

		setTimeout(function() {
			notification.style.opacity = "0";
			notification.style.filter = "drop-shadow( 0px 0px 0px rgba(0,0,0, .5))";
			setTimeout(function() {
				try {
					notification.parentNode.removeChild(notification);
				} catch(error) {}
			}, duration)
		}, duration);

		document.cookie = encodeURIComponent(self.pageRefreshedName) + "=; max-age=1; path=/";
	}

	/**
	 * Get the last modified date from the header 
	 * or file object after request has been received
	 **/
	self.getLastModified = function(request) {
		var date;

		// file protocol - FILE object with last modified property
		if (request.response && request.response.lastModified) {
			date = request.response.lastModified;
		}
		
		// http protocol - check headers
		if (date==null) {
			date = request.getResponseHeader("Last-Modified");
		}

		return date;
	}

	self.updateLastModifiedLabel = function() {
		var labelValue = "";
		
		if (self.lastModifiedLabel==null) {
			self.lastModifiedLabel = document.getElementById("LastModifiedLabel");
		}

		if (self.lastModifiedLabel) {
			var seconds = parseInt(((new Date().getTime() - Date.parse(document.lastModified)) / 1000 / 60) * 100 + "");
			var minutes = 0;
			var hours = 0;

			if (seconds < 60) {
				seconds = Math.floor(seconds/10)*10;
				labelValue = seconds + " seconds";
			}
			else {
				minutes = parseInt((seconds/60) + "");

				if (minutes>60) {
					hours = parseInt((seconds/60/60) +"");
					labelValue += hours==1 ? " hour" : " hours";
				}
				else {
					labelValue = minutes+"";
					labelValue += minutes==1 ? " minute" : " minutes";
				}
			}
			
			if (seconds<10) {
				labelValue = "Updated now";
			}
			else {
				labelValue = "Updated " + labelValue + " ago";
			}

			if (self.lastModifiedLabel.firstElementChild) {
				self.lastModifiedLabel.firstElementChild.textContent = labelValue;

			}
			else if ("textContent" in self.lastModifiedLabel) {
				self.lastModifiedLabel.textContent = labelValue;
			}
		}
	}

	self.getShortString = function(string, length) {
		if (length==null) length = 30;
		string = string!=null ? string.substr(0, length).replace(/\n/g, "") : "[String is null]";
		return string;
	}

	self.getShortNumber = function(value, places) {
		if (places==null || places<1) places = 4;
		value = Math.round(value * Math.pow(10,places)) / Math.pow(10, places);
		return value;
	}

	///////////////////////////////////////
	// NAVIGATION CONTROLS
	///////////////////////////////////////

	self.updateViewLabel = function() {
		var viewNavigationLabel = document.getElementById("ViewNavigationLabel");
		var view = self.getVisibleView();
		var viewIndex = view ? self.getViewIndex(view) : -1;
		var viewName = view ? self.getViewPreferenceValue(view, self.prefix + "view-name") : null;
		var viewId = view ? view.id : null;

		if (viewNavigationLabel && view) {
			if (viewName && viewName.indexOf('"')!=-1) {
				viewName = viewName.replace(/"/g, "");
			}

			if (self.showViewName) {
				viewNavigationLabel.textContent = viewName;
				self.setTooltip(viewNavigationLabel, viewIndex + 1 + " of " + self.numberOfViews);
			}
			else {
				viewNavigationLabel.textContent = viewIndex + 1 + " of " + self.numberOfViews;
				self.setTooltip(viewNavigationLabel, viewName);
			}

		}
	}

	self.updateURL = function(view) {
		view = view == null ? self.getVisibleView() : view;
		var viewId = view ? view.id : null
		var viewFragment = view ? "#"+ viewId : null;

		if (viewId && self.viewIds.length>1 && self.enableDeepLinking) {

			if (self.supportsPopState==false) {
				self.setFragment(viewId);
			}
			else {
				if (viewFragment!=window.location.hash) {

					if (window.location.hash==null) {
						window.history.replaceState({name:viewId}, null, viewFragment);
					}
					else {
						window.history.pushState({name:viewId}, null, viewFragment);
					}
				}
			}
		}
	}

	self.updateURLState = function(view, stateName) {
		stateName = view && (stateName=="" || stateName==null) ? self.getStateNameByViewId(view.id) : stateName;

		if (self.supportsPopState==false) {
			self.setFragment(stateName);
		}
		else {
			if (stateName!=window.location.hash) {

				if (window.location.hash==null) {
					window.history.replaceState({name:view.viewId}, null, stateName);
				}
				else {
					window.history.pushState({name:view.viewId}, null, stateName);
				}
			}
		}
	}

	self.setFragment = function(value) {
		window.location.hash = "#" + value;
	}

	self.setTooltip = function(element, value) {
		// setting the tooltip in edge causes a page crash on hover
		if (/Edge/.test(navigator.userAgent)) { return; }

		if ("title" in element) {
			element.title = value;
		}
	}

	self.getStylesheetRules = function(styleSheet) {
		try {
			if (styleSheet) return styleSheet.cssRules || styleSheet.rules;
	
			return document.styleSheets[0]["cssRules"] || document.styleSheets[0]["rules"];
		}
		catch (error) {
			// ERRORS:
			// SecurityError: The operation is insecure.
			// Errors happen when script loads before stylesheet or loading an external css locally

			// InvalidAccessError: A parameter or an operation is not supported by the underlying object
			// Place script after stylesheet

			console.log(error);
			if (error.toString().indexOf("The operation is insecure")!=-1) {
				console.log("Load the stylesheet before the script or load the stylesheet inline until it can be loaded on a server")
			}
			return [];
		}
	}

	/**
	 * If single page application hide all of the views. 
	 * @param {Number} selectedIndex if provided shows the view at index provided
	 **/
	self.hideViews = function(selectedIndex, animation) {
		var rules = self.getStylesheetRules();
		var queryIndex = 0;
		var numberOfRules = rules!=null ? rules.length : 0;

		// loop through rules and hide media queries except selected
		for (var i=0;i<numberOfRules;i++) {
			var rule = rules[i];
			var cssText = rule && rule.cssText;

			if (rule.media!=null && cssText.match("--web-view-name:")) {

				if (queryIndex==selectedIndex) {
					self.currentQuery.mediaText = rule.conditionText;
					self.currentQuery.index = selectedIndex;
					self.currentQuery.rule = rule;
					self.enableMediaQuery(rule);
				}
				else {
					if (animation) {
						self.fadeOut(rule)
					}
					else {
						self.disableMediaQuery(rule);
					}
				}
				
				queryIndex++;
			}
		}

		self.numberOfViews = queryIndex;
		self.updateViewLabel();
		self.updateURL();

		self.dispatchViewChange();

		var view = self.getVisibleView();
		var viewIndex = view ? self.getViewIndex(view) : -1;

		return viewIndex==selectedIndex ? view : null;
	}

	/**
	 * If single page application hide all of the views. 
	 * @param {HTMLElement} selectedView if provided shows the view passed in
	 **/
	 self.hideAllViews = function(selectedView, animation) {
		var views = self.views;
		var queryIndex = 0;
		var numberOfViews = views!=null ? views.length : 0;

		// loop through rules and hide media queries except selected
		for (var i=0;i<numberOfViews;i++) {
			var viewData = views[i];
			var view = viewData && viewData.view;
			var mediaRule = viewData && viewData.mediaRule;
			
			if (view==selectedView) {
				self.currentQuery.mediaText = mediaRule.conditionText;
				self.currentQuery.index = queryIndex;
				self.currentQuery.rule = mediaRule;
				self.enableMediaQuery(mediaRule);
			}
			else {
				if (animation) {
					self.fadeOut(mediaRule)
				}
				else {
					self.disableMediaQuery(mediaRule);
				}
			}
			
			queryIndex++;
		}

		self.numberOfViews = queryIndex;
		self.updateViewLabel();
		self.updateURL();
		self.dispatchViewChange();

		var visibleView = self.getVisibleView();

		return visibleView==selectedView ? selectedView : null;
	}

	/**
	 * Hide view
	 * @param {Object} view element to hide
	 **/
	self.hideView = function(view) {
		var rule = view ? self.mediaQueryDictionary[view.id] : null;

		if (rule) {
			self.disableMediaQuery(rule);
		}
	}

	/**
	 * Hide overlay
	 * @param {Object} overlay element to hide
	 **/
	self.hideOverlay = function(overlay) {
		var rule = overlay ? self.mediaQueryDictionary[overlay.id] : null;

		if (rule) {
			self.disableMediaQuery(rule);

			//if (self.showByMediaQuery) {
				overlay.style.display = "none";
			//}
		}
	}

	/**
	 * Show the view by media query. Does not hide current views
	 * Sets view options by default
	 * @param {Object} view element to show
	 * @param {Boolean} setViewOptions sets view options if null or true
	 */
	self.showViewByMediaQuery = function(view, setViewOptions) {
		var id = view ? view.id : null;
		var query = id ? self.mediaQueryDictionary[id] : null;
		var isOverlay = view ? self.isOverlay(view) : false;
		setViewOptions = setViewOptions==null ? true : setViewOptions;

		if (query) {
			self.enableMediaQuery(query);

			if (isOverlay && view && setViewOptions) {
				self.setViewVariables(null, view);
			}
			else {
				if (view && setViewOptions) self.setViewOptions(view);
				if (view && setViewOptions) self.setViewVariables(view);
			}
		}
	}

	/**
	 * Show the view. Does not hide current views
	 */
	self.showView = function(view, setViewOptions) {
		var id = view ? view.id : null;
		var query = id ? self.mediaQueryDictionary[id] : null;
		var display = null;
		setViewOptions = setViewOptions==null ? true : setViewOptions;

		if (query) {
			self.enableMediaQuery(query);
			if (view==null) view =self.getVisibleView();
			if (view && setViewOptions) self.setViewOptions(view);
		}
		else if (id) {
			display = window.getComputedStyle(view).getPropertyValue("display");
			if (display=="" || display=="none") {
				view.style.display = "block";
			}
		}

		if (view) {
			if (self.currentView!=null) {
				self.lastView = self.currentView;
			}

			self.currentView = view;
		}
	}

	self.showViewById = function(id, setViewOptions) {
		var view = id ? self.getViewById(id) : null;

		if (view) {
			self.showView(view);
			return;
		}

		self.log("View not found '" + id + "'");
	}

	self.getElementView = function(element) {
		var view = element;
		var viewFound = false;

		while (viewFound==false || view==null) {
			if (view && self.viewsDictionary[view.id]) {
				return view;
			}
			view = view.parentNode;
		}
	}

	/**
	 * Show overlay over view
	 * @param {Event | HTMLElement} event event or html element with styles applied
	 * @param {String} id id of view or view reference
	 * @param {Number} x x location
	 * @param {Number} y y location
	 */
	self.showOverlay = function(event, id, x, y) {
		var overlay = id && typeof id === 'string' ? self.getViewById(id) : id ? id : null;
		var query = overlay ? self.mediaQueryDictionary[overlay.id] : null;
		var centerHorizontally = false;
		var centerVertically = false;
		var anchorLeft = false;
		var anchorTop = false;
		var anchorRight = false;
		var anchorBottom = false;
		var display = null;
		var reparent = true;
		var view = null;
		
		if (overlay==null || overlay==false) {
			self.log("Overlay not found, '"+ id + "'");
			return;
		}

		// get enter animation - event target must have css variables declared
		if (event) {
			var button = event.currentTarget || event; // can be event or htmlelement
			var buttonComputedStyles = getComputedStyle(button);
			var actionTargetValue = buttonComputedStyles.getPropertyValue(self.prefix+"action-target").trim();
			var animation = buttonComputedStyles.getPropertyValue(self.prefix+"animation").trim();
			var isAnimated = animation!="";
			var targetType = buttonComputedStyles.getPropertyValue(self.prefix+"action-type").trim();
			var actionTarget = self.application ? null : self.getElement(actionTargetValue);
			var actionTargetStyles = actionTarget ? actionTarget.style : null;

			if (actionTargetStyles) {
				actionTargetStyles.setProperty("animation", animation);
			}

			if ("stopImmediatePropagation" in event) {
				event.stopImmediatePropagation();
			}
		}
		
		if (self.application==false || targetType=="page") {
			document.location.href = "./" + actionTargetValue;
			return;
		}

		// remove any current overlays
		if (self.currentOverlay) {

			// act as switch if same button
			if (self.currentOverlay==actionTarget || self.currentOverlay==null) {
				if (self.lastTrigger==button) {
					self.removeOverlay(isAnimated);
					return;
				}
			}
			else {
				self.removeOverlay(isAnimated);
			}
		}

		if (reparent) {
			view = self.getElementView(button);
			if (view) {
				view.appendChild(overlay);
			}
		}

		if (query) {
			//self.setElementAnimation(overlay, null);
			//overlay.style.animation = animation;
			self.enableMediaQuery(query);
			
			var display = overlay && overlay.style.display;
			
			if (overlay && display=="" || display=="none") {
				overlay.style.display = "block";
				//self.setViewOptions(overlay);
			}

			// add animation defined in event target style declaration
			if (animation && self.supportAnimations) {
				self.fadeIn(overlay, false, animation);
			}
		}
		else if (id) {

			display = window.getComputedStyle(overlay).getPropertyValue("display");

			if (display=="" || display=="none") {
				overlay.style.display = "block";
			}

			// add animation defined in event target style declaration
			if (animation && self.supportAnimations) {
				self.fadeIn(overlay, false, animation);
			}
		}

		// do not set x or y position if centering
		var horizontal = self.prefix + "center-horizontally";
		var vertical = self.prefix + "center-vertically";
		var style = overlay.style;
		var transform = [];

		centerHorizontally = self.getIsStyleDefined(id, horizontal) ? self.getViewPreferenceBoolean(overlay, horizontal) : false;
		centerVertically = self.getIsStyleDefined(id, vertical) ? self.getViewPreferenceBoolean(overlay, vertical) : false;
		anchorLeft = self.getIsStyleDefined(id, "left");
		anchorRight = self.getIsStyleDefined(id, "right");
		anchorTop = self.getIsStyleDefined(id, "top");
		anchorBottom = self.getIsStyleDefined(id, "bottom");

		
		if (self.viewsDictionary[overlay.id] && self.viewsDictionary[overlay.id].styleDeclaration) {
			style = self.viewsDictionary[overlay.id].styleDeclaration.style;
		}
		
		if (centerHorizontally) {
			style.left = "50%";
			style.transformOrigin = "0 0";
			transform.push("translateX(-50%)");
		}
		else if (anchorRight && anchorLeft) {
			style.left = x + "px";
		}
		else if (anchorRight) {
			//style.right = x + "px";
		}
		else {
			style.left = x + "px";
		}
		
		if (centerVertically) {
			style.top = "50%";
			transform.push("translateY(-50%)");
			style.transformOrigin = "0 0";
		}
		else if (anchorTop && anchorBottom) {
			style.top = y + "px";
		}
		else if (anchorBottom) {
			//style.bottom = y + "px";
		}
		else {
			style.top = y + "px";
		}

		if (transform.length) {
			style.transform = transform.join(" ");
		}

		self.currentOverlay = overlay;
		self.lastTrigger = button;
	}

	self.goBack = function() {
		if (self.currentOverlay) {
			self.removeOverlay();
		}
		else if (self.lastView) {
			self.goToView(self.lastView.id);
		}
	}

	self.removeOverlay = function(animate) {
		var overlay = self.currentOverlay;
		animate = animate===false ? false : true;

		if (overlay) {
			var style = overlay.style;
			
			if (style.animation && self.supportAnimations && animate) {
				self.reverseAnimation(overlay, true);

				var duration = self.getAnimationDuration(style.animation, true);
		
				setTimeout(function() {
					self.setElementAnimation(overlay, null);
					self.hideOverlay(overlay);
					self.currentOverlay = null;
				}, duration);
			}
			else {
				self.setElementAnimation(overlay, null);
				self.hideOverlay(overlay);
				self.currentOverlay = null;
			}
		}
	}

	/**
	 * Reverse the animation and hide after
	 * @param {Object} target element with animation
	 * @param {Boolean} hide hide after animation ends
	 */
	self.reverseAnimation = function(target, hide) {
		var lastAnimation = null;
		var style = target.style;

		style.animationPlayState = "paused";
		lastAnimation = style.animation;
		style.animation = null;
		style.animationPlayState = "paused";

		if (hide) {
			//target.addEventListener("animationend", self.animationEndHideHandler);
	
			var duration = self.getAnimationDuration(lastAnimation, true);
			var isOverlay = self.isOverlay(target);
	
			setTimeout(function() {
				self.setElementAnimation(target, null);

				if (isOverlay) {
					self.hideOverlay(target);
				}
				else {
					self.hideView(target);
				}
			}, duration);
		}

		setTimeout(function() {
			style.animation = lastAnimation;
			style.animationPlayState = "paused";
			style.animationDirection = "reverse";
			style.animationPlayState = "running";
		}, 30);
	}

	self.animationEndHandler = function(event) {
		var target = event.currentTarget;
		self.dispatchEvent(new Event(event.type));
	}

	self.isOverlay = function(view) {
		var result = view ? self.getViewPreferenceBoolean(view, self.prefix + "is-overlay") : false;

		return result;
	}

	self.animationEndHideHandler = function(event) {
		var target = event.currentTarget;
		self.setViewVariables(null, target);
		self.hideView(target);
		target.removeEventListener("animationend", self.animationEndHideHandler);
	}

	self.animationEndShowHandler = function(event) {
		var target = event.currentTarget;
		target.removeEventListener("animationend", self.animationEndShowHandler);
	}

	self.setViewOptions = function(view) {

		if (view) {
			self.minimumScale = self.getViewPreferenceValue(view, self.prefix + "minimum-scale");
			self.maximumScale = self.getViewPreferenceValue(view, self.prefix + "maximum-scale");
			self.scaleViewsToFit = self.getViewPreferenceBoolean(view, self.prefix + "scale-to-fit");
			self.scaleToFitType = self.getViewPreferenceValue(view, self.prefix + "scale-to-fit-type");
			self.scaleToFitOnDoubleClick = self.getViewPreferenceBoolean(view, self.prefix + "scale-on-double-click");
			self.actualSizeOnDoubleClick = self.getViewPreferenceBoolean(view, self.prefix + "actual-size-on-double-click");
			self.scaleViewsOnResize = self.getViewPreferenceBoolean(view, self.prefix + "scale-on-resize");
			self.enableScaleUp = self.getViewPreferenceBoolean(view, self.prefix + "enable-scale-up");
			self.centerHorizontally = self.getViewPreferenceBoolean(view, self.prefix + "center-horizontally");
			self.centerVertically = self.getViewPreferenceBoolean(view, self.prefix + "center-vertically");
			self.navigationOnKeypress = self.getViewPreferenceBoolean(view, self.prefix + "navigate-on-keypress");
			self.showViewName = self.getViewPreferenceBoolean(view, self.prefix + "show-view-name");
			self.refreshPageForChanges = self.getViewPreferenceBoolean(view, self.prefix + "refresh-for-changes");
			self.refreshPageForChangesInterval = self.getViewPreferenceValue(view, self.prefix + "refresh-interval");
			self.showNavigationControls = self.getViewPreferenceBoolean(view, self.prefix + "show-navigation-controls");
			self.scaleViewSlider = self.getViewPreferenceBoolean(view, self.prefix + "show-scale-controls");
			self.enableDeepLinking = self.getViewPreferenceBoolean(view, self.prefix + "enable-deep-linking");
			self.singlePageApplication = self.getViewPreferenceBoolean(view, self.prefix + "application");
			self.showByMediaQuery = self.getViewPreferenceBoolean(view, self.prefix + "show-by-media-query");
			self.showUpdateNotification = document.cookie!="" ? document.cookie.indexOf(self.pageRefreshedName)!=-1 : false;
			self.imageComparisonDuration = self.getViewPreferenceValue(view, self.prefix + "image-comparison-duration");
			self.supportAnimations = self.getViewPreferenceBoolean(view, self.prefix + "enable-animations", true);

			if (self.scaleViewsToFit) {
				var newScaleValue = self.scaleViewToFit(view);
				
				if (newScaleValue<0) {
					setTimeout(self.scaleViewToFit, 500, view);
				}
			}
			else {
				self.viewScale = self.getViewScaleValue(view);
				self.viewToFitWidthScale = self.getViewFitToViewportWidthScale(view, self.enableScaleUp)
				self.viewToFitHeightScale = self.getViewFitToViewportScale(view, self.enableScaleUp);
				self.updateSliderValue(self.viewScale);
			}

			if (self.imageComparisonDuration!=null) {
				// todo
			}

			if (self.refreshPageForChangesInterval!=null) {
				self.refreshDuration = Number(self.refreshPageForChangesInterval);
			}
		}
	}

	self.previousView = function(event) {
		var rules = self.getStylesheetRules();
		var view = self.getVisibleView()
		var index = view ? self.getViewIndex(view) : -1;
		var prevQueryIndex = index!=-1 ? index-1 : self.currentQuery.index-1;
		var queryIndex = 0;
		var numberOfRules = rules!=null ? rules.length : 0;

		if (event) {
			event.stopImmediatePropagation();
		}

		if (prevQueryIndex<0) {
			return;
		}

		// loop through rules and hide media queries except selected
		for (var i=0;i<numberOfRules;i++) {
			var rule = rules[i];
			
			if (rule.media!=null) {

				if (queryIndex==prevQueryIndex) {
					self.currentQuery.mediaText = rule.conditionText;
					self.currentQuery.index = prevQueryIndex;
					self.currentQuery.rule = rule;
					self.enableMediaQuery(rule);
					self.updateViewLabel();
					self.updateURL();
					self.dispatchViewChange();
				}
				else {
					self.disableMediaQuery(rule);
				}

				queryIndex++;
			}
		}
	}

	self.nextView = function(event) {
		var rules = self.getStylesheetRules();
		var view = self.getVisibleView();
		var index = view ? self.getViewIndex(view) : -1;
		var nextQueryIndex = index!=-1 ? index+1 : self.currentQuery.index+1;
		var queryIndex = 0;
		var numberOfRules = rules!=null ? rules.length : 0;
		var numberOfMediaQueries = self.getNumberOfMediaRules();

		if (event) {
			event.stopImmediatePropagation();
		}

		if (nextQueryIndex>=numberOfMediaQueries) {
			return;
		}

		// loop through rules and hide media queries except selected
		for (var i=0;i<numberOfRules;i++) {
			var rule = rules[i];
			
			if (rule.media!=null) {

				if (queryIndex==nextQueryIndex) {
					self.currentQuery.mediaText = rule.conditionText;
					self.currentQuery.index = nextQueryIndex;
					self.currentQuery.rule = rule;
					self.enableMediaQuery(rule);
					self.updateViewLabel();
					self.updateURL();
					self.dispatchViewChange();
				}
				else {
					self.disableMediaQuery(rule);
				}

				queryIndex++;
			}
		}
	}

	/**
	 * Enables a view via media query
	 */
	self.enableMediaQuery = function(rule) {

		try {
			rule.media.mediaText = self.inclusionQuery;
		}
		catch(error) {
			//self.log(error);
			rule.conditionText = self.inclusionQuery;
		}
	}

	self.disableMediaQuery = function(rule) {

		try {
			rule.media.mediaText = self.exclusionQuery;
		}
		catch(error) {
			rule.conditionText = self.exclusionQuery;
		}
	}

	self.dispatchViewChange = function() {
		try {
			var event = new Event(self.NAVIGATION_CHANGE);
			window.dispatchEvent(event);
		}
		catch (error) {
			// In IE 11: Object doesn't support this action
		}
	}

	self.getNumberOfMediaRules = function() {
		var rules = self.getStylesheetRules();
		var numberOfRules = rules ? rules.length : 0;
		var numberOfQueries = 0;

		for (var i=0;i<numberOfRules;i++) {
			if (rules[i].media!=null) { numberOfQueries++; }
		}
		
		return numberOfQueries;
	}

	/////////////////////////////////////////
	// VIEW SCALE 
	/////////////////////////////////////////

	self.sliderChangeHandler = function(event) {
		var value = self.getShortNumber(event.currentTarget.value/100);
		var view = self.getVisibleView();
		self.setViewScaleValue(view, false, value, true);
	}

	self.updateSliderValue = function(scale) {
		var slider = document.getElementById(self.viewScaleSliderId);
		var tooltip = parseInt(scale * 100 + "") + "%";
		var inputType;
		var inputValue;
		
		if (slider) {
			inputValue = self.getShortNumber(scale * 100);
			if (inputValue!=slider["value"]) {
				slider["value"] = inputValue;
			}
			inputType = slider.getAttributeNS(null, "type");

			if (inputType!="range") {
				// input range is not supported
				slider.style.display = "none";
			}

			self.setTooltip(slider, tooltip);
		}
	}

	self.viewChangeHandler = function(event) {
		var view = self.getVisibleView();
		var matrix = view ? getComputedStyle(view).transform : null;
		
		if (matrix) {
			self.viewScale = self.getViewScaleValue(view);
			
			var scaleNeededToFit = self.getViewFitToViewportScale(view);
			var isViewLargerThanViewport = scaleNeededToFit<1;
			
			// scale large view to fit if scale to fit is enabled
			if (self.scaleViewsToFit) {
				self.scaleViewToFit(view);
			}
			else {
				self.updateSliderValue(self.viewScale);
			}
		}
	}

	self.getViewScaleValue = function(view) {
		var matrix = getComputedStyle(view).transform;

		if (matrix) {
			var matrixArray = matrix.replace("matrix(", "").split(",");
			var scaleX = parseFloat(matrixArray[0]);
			var scaleY = parseFloat(matrixArray[3]);
			var scale = Math.min(scaleX, scaleY);
		}

		return scale;
	}

	/**
	 * Scales view to scale. 
	 * @param {Object} view view to scale. views are in views array
	 * @param {Boolean} scaleToFit set to true to scale to fit. set false to use desired scale value
	 * @param {Number} desiredScale scale to define. not used if scale to fit is false
	 * @param {Boolean} isSliderChange indicates if slider is callee
	 */
	self.setViewScaleValue = function(view, scaleToFit, desiredScale, isSliderChange) {
		var enableScaleUp = self.enableScaleUp;
		var scaleToFitType = self.scaleToFitType;
		var minimumScale = self.minimumScale;
		var maximumScale = self.maximumScale;
		var hasMinimumScale = !isNaN(minimumScale) && minimumScale!="";
		var hasMaximumScale = !isNaN(maximumScale) && maximumScale!="";
		var scaleNeededToFit = self.getViewFitToViewportScale(view, enableScaleUp);
		var scaleNeededToFitWidth = self.getViewFitToViewportWidthScale(view, enableScaleUp);
		var scaleNeededToFitHeight = self.getViewFitToViewportHeightScale(view, enableScaleUp);
		var scaleToFitFull = self.getViewFitToViewportScale(view, true);
		var scaleToFitFullWidth = self.getViewFitToViewportWidthScale(view, true);
		var scaleToFitFullHeight = self.getViewFitToViewportHeightScale(view, true);
		var scaleToWidth = scaleToFitType=="width";
		var scaleToHeight = scaleToFitType=="height";
		var shrunkToFit = false;
		var topPosition = null;
		var leftPosition = null;
		var translateY = null;
		var translateX = null;
		var transformValue = "";
		var canCenterVertically = true;
		var canCenterHorizontally = true;
		var style = view.style;

		if (view && self.viewsDictionary[view.id] && self.viewsDictionary[view.id].styleDeclaration) {
			style = self.viewsDictionary[view.id].styleDeclaration.style;
		}

		if (scaleToFit && isSliderChange!=true) {
			if (scaleToFitType=="fit" || scaleToFitType=="") {
				desiredScale = scaleNeededToFit;
			}
			else if (scaleToFitType=="width") {
				desiredScale = scaleNeededToFitWidth;
			}
			else if (scaleToFitType=="height") {
				desiredScale = scaleNeededToFitHeight;
			}
		}
		else {
			if (isNaN(desiredScale)) {
				desiredScale = 1;
			}
		}

		self.updateSliderValue(desiredScale);
		
		// scale to fit width
		if (scaleToWidth && scaleToHeight==false) {
			canCenterVertically = scaleNeededToFitHeight>=scaleNeededToFitWidth;
			canCenterHorizontally = scaleNeededToFitWidth>=1 && enableScaleUp==false;

			if (isSliderChange) {
				canCenterHorizontally = desiredScale<scaleToFitFullWidth;
			}
			else if (scaleToFit) {
				desiredScale = scaleNeededToFitWidth;
			}

			if (hasMinimumScale) {
				desiredScale = Math.max(desiredScale, Number(minimumScale));
			}

			if (hasMaximumScale) {
				desiredScale = Math.min(desiredScale, Number(maximumScale));
			}

			desiredScale = self.getShortNumber(desiredScale);

			canCenterHorizontally = self.canCenterHorizontally(view, "width", enableScaleUp, desiredScale, minimumScale, maximumScale);
			canCenterVertically = self.canCenterVertically(view, "width", enableScaleUp, desiredScale, minimumScale, maximumScale);

			if (desiredScale>1 && (enableScaleUp || isSliderChange)) {
				transformValue = "scale(" + desiredScale + ")";
			}
			else if (desiredScale>=1 && enableScaleUp==false) {
				transformValue = "scale(" + 1 + ")";
			}
			else {
				transformValue = "scale(" + desiredScale + ")";
			}

			if (self.centerVertically) {
				if (canCenterVertically) {
					translateY = "-50%";
					topPosition = "50%";
				}
				else {
					translateY = "0";
					topPosition = "0";
				}
				
				if (style.top != topPosition) {
					style.top = topPosition + "";
				}

				if (canCenterVertically) {
					transformValue += " translateY(" + translateY+ ")";
				}
			}

			if (self.centerHorizontally) {
				if (canCenterHorizontally) {
					translateX = "-50%";
					leftPosition = "50%";
				}
				else {
					translateX = "0";
					leftPosition = "0";
				}

				if (style.left != leftPosition) {
					style.left = leftPosition + "";
				}

				if (canCenterHorizontally) {
					transformValue += " translateX(" + translateX+ ")";
				}
			}

			style.transformOrigin = "0 0";
			style.transform = transformValue;

			self.viewScale = desiredScale;
			self.viewToFitWidthScale = scaleNeededToFitWidth;
			self.viewToFitHeightScale = scaleNeededToFitHeight;
			self.viewLeft = leftPosition;
			self.viewTop = topPosition;

			return desiredScale;
		}

		// scale to fit height
		if (scaleToHeight && scaleToWidth==false) {
			//canCenterVertically = scaleNeededToFitHeight>=scaleNeededToFitWidth;
			//canCenterHorizontally = scaleNeededToFitHeight<=scaleNeededToFitWidth && enableScaleUp==false;
			canCenterVertically = scaleNeededToFitHeight>=scaleNeededToFitWidth;
			canCenterHorizontally = scaleNeededToFitWidth>=1 && enableScaleUp==false;
			
			if (isSliderChange) {
				canCenterHorizontally = desiredScale<scaleToFitFullHeight;
			}
			else if (scaleToFit) {
				desiredScale = scaleNeededToFitHeight;
			}

			if (hasMinimumScale) {
				desiredScale = Math.max(desiredScale, Number(minimumScale));
			}

			if (hasMaximumScale) {
				desiredScale = Math.min(desiredScale, Number(maximumScale));
				//canCenterVertically = desiredScale>=scaleNeededToFitHeight && enableScaleUp==false;
			}

			desiredScale = self.getShortNumber(desiredScale);

			canCenterHorizontally = self.canCenterHorizontally(view, "height", enableScaleUp, desiredScale, minimumScale, maximumScale);
			canCenterVertically = self.canCenterVertically(view, "height", enableScaleUp, desiredScale, minimumScale, maximumScale);

			if (desiredScale>1 && (enableScaleUp || isSliderChange)) {
				transformValue = "scale(" + desiredScale + ")";
			}
			else if (desiredScale>=1 && enableScaleUp==false) {
				transformValue = "scale(" + 1 + ")";
			}
			else {
				transformValue = "scale(" + desiredScale + ")";
			}

			if (self.centerHorizontally) {
				if (canCenterHorizontally) {
					translateX = "-50%";
					leftPosition = "50%";
				}
				else {
					translateX = "0";
					leftPosition = "0";
				}

				if (style.left != leftPosition) {
					style.left = leftPosition + "";
				}

				if (canCenterHorizontally) {
					transformValue += " translateX(" + translateX+ ")";
				}
			}

			if (self.centerVertically) {
				if (canCenterVertically) {
					translateY = "-50%";
					topPosition = "50%";
				}
				else {
					translateY = "0";
					topPosition = "0";
				}
				
				if (style.top != topPosition) {
					style.top = topPosition + "";
				}

				if (canCenterVertically) {
					transformValue += " translateY(" + translateY+ ")";
				}
			}

			style.transformOrigin = "0 0";
			style.transform = transformValue;

			self.viewScale = desiredScale;
			self.viewToFitWidthScale = scaleNeededToFitWidth;
			self.viewToFitHeightScale = scaleNeededToFitHeight;
			self.viewLeft = leftPosition;
			self.viewTop = topPosition;

			return scaleNeededToFitHeight;
		}

		if (scaleToFitType=="fit") {
			//canCenterVertically = scaleNeededToFitHeight>=scaleNeededToFitWidth;
			//canCenterHorizontally = scaleNeededToFitWidth>=scaleNeededToFitHeight;
			canCenterVertically = scaleNeededToFitHeight>=scaleNeededToFit;
			canCenterHorizontally = scaleNeededToFitWidth>=scaleNeededToFit;

			if (hasMinimumScale) {
				desiredScale = Math.max(desiredScale, Number(minimumScale));
			}

			desiredScale = self.getShortNumber(desiredScale);

			if (isSliderChange || scaleToFit==false) {
				canCenterVertically = scaleToFitFullHeight>=desiredScale;
				canCenterHorizontally = desiredScale<scaleToFitFullWidth;
			}
			else if (scaleToFit) {
				desiredScale = scaleNeededToFit;
			}

			transformValue = "scale(" + desiredScale + ")";

			//canCenterHorizontally = self.canCenterHorizontally(view, "fit", false, desiredScale);
			//canCenterVertically = self.canCenterVertically(view, "fit", false, desiredScale);
			
			if (self.centerVertically) {
				if (canCenterVertically) {
					translateY = "-50%";
					topPosition = "50%";
				}
				else {
					translateY = "0";
					topPosition = "0";
				}
				
				if (style.top != topPosition) {
					style.top = topPosition + "";
				}

				if (canCenterVertically) {
					transformValue += " translateY(" + translateY+ ")";
				}
			}

			if (self.centerHorizontally) {
				if (canCenterHorizontally) {
					translateX = "-50%";
					leftPosition = "50%";
				}
				else {
					translateX = "0";
					leftPosition = "0";
				}

				if (style.left != leftPosition) {
					style.left = leftPosition + "";
				}

				if (canCenterHorizontally) {
					transformValue += " translateX(" + translateX+ ")";
				}
			}

			style.transformOrigin = "0 0";
			style.transform = transformValue;

			self.viewScale = desiredScale;
			self.viewToFitWidthScale = scaleNeededToFitWidth;
			self.viewToFitHeightScale = scaleNeededToFitHeight;
			self.viewLeft = leftPosition;
			self.viewTop = topPosition;

			self.updateSliderValue(desiredScale);
			
			return desiredScale;
		}

		if (scaleToFitType=="default" || scaleToFitType=="") {
			desiredScale = 1;

			if (hasMinimumScale) {
				desiredScale = Math.max(desiredScale, Number(minimumScale));
			}
			if (hasMaximumScale) {
				desiredScale = Math.min(desiredScale, Number(maximumScale));
			}

			canCenterHorizontally = self.canCenterHorizontally(view, "none", false, desiredScale, minimumScale, maximumScale);
			canCenterVertically = self.canCenterVertically(view, "none", false, desiredScale, minimumScale, maximumScale);

			if (self.centerVertically) {
				if (canCenterVertically) {
					translateY = "-50%";
					topPosition = "50%";
				}
				else {
					translateY = "0";
					topPosition = "0";
				}
				
				if (style.top != topPosition) {
					style.top = topPosition + "";
				}

				if (canCenterVertically) {
					transformValue += " translateY(" + translateY+ ")";
				}
			}

			if (self.centerHorizontally) {
				if (canCenterHorizontally) {
					translateX = "-50%";
					leftPosition = "50%";
				}
				else {
					translateX = "0";
					leftPosition = "0";
				}

				if (style.left != leftPosition) {
					style.left = leftPosition + "";
				}

				if (canCenterHorizontally) {
					transformValue += " translateX(" + translateX+ ")";
				}
				else {
					transformValue += " translateX(" + 0 + ")";
				}
			}

			style.transformOrigin = "0 0";
			style.transform = transformValue;


			self.viewScale = desiredScale;
			self.viewToFitWidthScale = scaleNeededToFitWidth;
			self.viewToFitHeightScale = scaleNeededToFitHeight;
			self.viewLeft = leftPosition;
			self.viewTop = topPosition;

			self.updateSliderValue(desiredScale);
			
			return desiredScale;
		}
	}

	/**
	 * Returns true if view can be centered horizontally
	 * @param {HTMLElement} view view
	 * @param {String} type type of scaling - width, height, all, none
	 * @param {Boolean} scaleUp if scale up enabled 
	 * @param {Number} scale target scale value 
	 */
	self.canCenterHorizontally = function(view, type, scaleUp, scale, minimumScale, maximumScale) {
		var scaleNeededToFit = self.getViewFitToViewportScale(view, scaleUp);
		var scaleNeededToFitHeight = self.getViewFitToViewportHeightScale(view, scaleUp);
		var scaleNeededToFitWidth = self.getViewFitToViewportWidthScale(view, scaleUp);
		var canCenter = false;
		var minScale;

		type = type==null ? "none" : type;
		scale = scale==null ? scale : scaleNeededToFitWidth;
		scaleUp = scaleUp == null ? false : scaleUp;

		if (type=="width") {
	
			if (scaleUp && maximumScale==null) {
				canCenter = false;
			}
			else if (scaleNeededToFitWidth>=1) {
				canCenter = true;
			}
		}
		else if (type=="height") {
			minScale = Math.min(1, scaleNeededToFitHeight);
			if (minimumScale!="" && maximumScale!="") {
				minScale = Math.max(minimumScale, Math.min(maximumScale, scaleNeededToFitHeight));
			}
			else {
				if (minimumScale!="") {
					minScale = Math.max(minimumScale, scaleNeededToFitHeight);
				}
				if (maximumScale!="") {
					minScale = Math.max(minimumScale, Math.min(maximumScale, scaleNeededToFitHeight));
				}
			}
	
			if (scaleUp && maximumScale=="") {
				canCenter = false;
			}
			else if (scaleNeededToFitWidth>=minScale) {
				canCenter = true;
			}
		}
		else if (type=="fit") {
			canCenter = scaleNeededToFitWidth>=scaleNeededToFit;
		}
		else {
			if (scaleUp) {
				canCenter = false;
			}
			else if (scaleNeededToFitWidth>=1) {
				canCenter = true;
			}
		}

		self.horizontalScrollbarsNeeded = canCenter;
		
		return canCenter;
	}

	/**
	 * Returns true if view can be centered horizontally
	 * @param {HTMLElement} view view to scale
	 * @param {String} type type of scaling
	 * @param {Boolean} scaleUp if scale up enabled 
	 * @param {Number} scale target scale value 
	 */
	self.canCenterVertically = function(view, type, scaleUp, scale, minimumScale, maximumScale) {
		var scaleNeededToFit = self.getViewFitToViewportScale(view, scaleUp);
		var scaleNeededToFitWidth = self.getViewFitToViewportWidthScale(view, scaleUp);
		var scaleNeededToFitHeight = self.getViewFitToViewportHeightScale(view, scaleUp);
		var canCenter = false;
		var minScale;

		type = type==null ? "none" : type;
		scale = scale==null ? 1 : scale;
		scaleUp = scaleUp == null ? false : scaleUp;
	
		if (type=="width") {
			canCenter = scaleNeededToFitHeight>=scaleNeededToFitWidth;
		}
		else if (type=="height") {
			minScale = Math.max(minimumScale, Math.min(maximumScale, scaleNeededToFit));
			canCenter = scaleNeededToFitHeight>=minScale;
		}
		else if (type=="fit") {
			canCenter = scaleNeededToFitHeight>=scaleNeededToFit;
		}
		else {
			if (scaleUp) {
				canCenter = false;
			}
			else if (scaleNeededToFitHeight>=1) {
				canCenter = true;
			}
		}

		self.verticalScrollbarsNeeded = canCenter;
		
		return canCenter;
	}

	self.getViewFitToViewportScale = function(view, scaleUp) {
		var enableScaleUp = scaleUp;
		var availableWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
		var availableHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
		var elementWidth = parseFloat(getComputedStyle(view, "style").width);
		var elementHeight = parseFloat(getComputedStyle(view, "style").height);
		var newScale = 1;

		// if element is not added to the document computed values are NaN
		if (isNaN(elementWidth) || isNaN(elementHeight)) {
			return newScale;
		}

		availableWidth -= self.horizontalPadding;
		availableHeight -= self.verticalPadding;

		if (enableScaleUp) {
			newScale = Math.min(availableHeight/elementHeight, availableWidth/elementWidth);
		}
		else if (elementWidth > availableWidth || elementHeight > availableHeight) {
			newScale = Math.min(availableHeight/elementHeight, availableWidth/elementWidth);
		}
		
		return newScale;
	}

	self.getViewFitToViewportWidthScale = function(view, scaleUp) {
		// need to get browser viewport width when element
		var isParentWindow = view && view.parentNode && view.parentNode===document.body;
		var enableScaleUp = scaleUp;
		var availableWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
		var elementWidth = parseFloat(getComputedStyle(view, "style").width);
		var newScale = 1;

		// if element is not added to the document computed values are NaN
		if (isNaN(elementWidth)) {
			return newScale;
		}

		availableWidth -= self.horizontalPadding;

		if (enableScaleUp) {
			newScale = availableWidth/elementWidth;
		}
		else if (elementWidth > availableWidth) {
			newScale = availableWidth/elementWidth;
		}
		
		return newScale;
	}

	self.getViewFitToViewportHeightScale = function(view, scaleUp) {
		var enableScaleUp = scaleUp;
		var availableHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
		var elementHeight = parseFloat(getComputedStyle(view, "style").height);
		var newScale = 1;

		// if element is not added to the document computed values are NaN
		if (isNaN(elementHeight)) {
			return newScale;
		}

		availableHeight -= self.verticalPadding;

		if (enableScaleUp) {
			newScale = availableHeight/elementHeight;
		}
		else if (elementHeight > availableHeight) {
			newScale = availableHeight/elementHeight;
		}
		
		return newScale;
	}

	self.keypressHandler = function(event) {
		var rightKey = 39;
		var leftKey = 37;
		
		// listen for both events 
		if (event.type=="keypress") {
			window.removeEventListener("keyup", self.keypressHandler);
		}
		else {
			window.removeEventListener("keypress", self.keypressHandler);
		}
		
		if (self.showNavigationControls) {
			if (self.navigationOnKeypress) {
				if (event.keyCode==rightKey) {
					self.nextView();
				}
				if (event.keyCode==leftKey) {
					self.previousView();
				}
			}
		}
		else if (self.navigationOnKeypress) {
			if (event.keyCode==rightKey) {
				self.nextView();
			}
			if (event.keyCode==leftKey) {
				self.previousView();
			}
		}
	}

	///////////////////////////////////
	// GENERAL FUNCTIONS
	///////////////////////////////////

	self.getViewById = function(id) {
		id = id ? id.replace("#", "") : "";
		var view = self.viewIds.indexOf(id)!=-1 && self.getElement(id);
		return view;
	}

	self.getViewIds = function() {
		var viewIds = self.getViewPreferenceValue(document.body, self.prefix + "view-ids");
		var viewId = null;

		viewIds = viewIds!=null && viewIds!="" ? viewIds.split(",") : [];

		if (viewIds.length==0) {
			viewId = self.getViewPreferenceValue(document.body, self.prefix + "view-id");
			viewIds = viewId ? [viewId] : [];
		}

		return viewIds;
	}

	self.getInitialViewId = function() {
		var viewId = self.getViewPreferenceValue(document.body, self.prefix + "view-id");
		return viewId;
	}

	self.getApplicationStylesheet = function() {
		var stylesheetId = self.getViewPreferenceValue(document.body, self.prefix + "stylesheet-id");
		self.applicationStylesheet = document.getElementById("applicationStylesheet");
		return self.applicationStylesheet.sheet;
	}

	self.getVisibleView = function() {
		var viewIds = self.getViewIds();
		
		for (var i=0;i<viewIds.length;i++) {
			var viewId = viewIds[i].replace(/[\#?\.?](.*)/, "$" + "1");
			var view = self.getElement(viewId);
			var postName = "_Class";

			if (view==null && viewId && viewId.lastIndexOf(postName)!=-1) {
				view = self.getElement(viewId.replace(postName, ""));
			}
			
			if (view) {
				var display = getComputedStyle(view).display;
		
				if (display=="block" || display=="flex") {
					return view;
				}
			}
		}

		return null;
	}

	self.getVisibleViews = function() {
		var viewIds = self.getViewIds();
		var views = [];
		
		for (var i=0;i<viewIds.length;i++) {
			var viewId = viewIds[i].replace(/[\#?\.?](.*)/, "$" + "1");
			var view = self.getElement(viewId);
			var postName = "_Class";

			if (view==null && viewId && viewId.lastIndexOf(postName)!=-1) {
				view = self.getElement(viewId.replace(postName, ""));
			}
			
			if (view) {
				var display = getComputedStyle(view).display;
				
				if (display=="none") {
					continue;
				}

				if (display=="block" || display=="flex") {
					views.push(view);
				}
			}
		}

		return views;
	}

	self.getStateNameByViewId = function(id) {
		var state = self.viewsDictionary[id];
		return state && state.stateName;
	}

	self.getMatchingViews = function(ids) {
		var views = self.addedViews.slice(0);
		var matchingViews = [];

		if (self.showByMediaQuery) {
			for (let index = 0; index < views.length; index++) {
				var viewId = views[index];
				var state = self.viewsDictionary[viewId];
				var rule = state && state.rule; 
				var matchResults = window.matchMedia(rule.conditionText);
				var view = self.views[viewId];
				
				if (matchResults.matches) {
					if (ids==true) {
						matchingViews.push(viewId);
					}
					else {
						matchingViews.push(view);
					}
				}
			}
		}

		return matchingViews;
	}

	self.ruleMatchesQuery = function(rule) {
		var result = window.matchMedia(rule.conditionText);
		return result.matches;
	}

	self.getViewsByStateName = function(stateName, matchQuery) {
		var views = self.addedViews.slice(0);
		var matchingViews = [];

		if (self.showByMediaQuery) {

			// find state name
			for (let index = 0; index < views.length; index++) {
				var viewId = views[index];
				var state = self.viewsDictionary[viewId];
				var rule = state.rule;
				var mediaRule = state.mediaRule;
				var view = self.views[viewId];
				var viewStateName = self.getStyleRuleValue(mediaRule, self.STATE_NAME, state);
				var stateFoundAtt = view.getAttribute(self.STATE_NAME)==state;
				var matchesResults = false;
				
				if (viewStateName==stateName) {
					if (matchQuery) {
						matchesResults = self.ruleMatchesQuery(rule);

						if (matchesResults) {
							matchingViews.push(view);
						}
					}
					else {
						matchingViews.push(view);
					}
				}
			}
		}

		return matchingViews;
	}

	self.getInitialView = function() {
		var viewId = self.getInitialViewId();
		viewId = viewId.replace(/[\#?\.?](.*)/, "$" + "1");
		var view = self.getElement(viewId);
		var postName = "_Class";

		if (view==null && viewId && viewId.lastIndexOf(postName)!=-1) {
			view = self.getElement(viewId.replace(postName, ""));
		}

		return view;
	}

	self.getViewIndex = function(view) {
		var viewIds = self.getViewIds();
		var id = view ? view.id : null;
		var index = id && viewIds ? viewIds.indexOf(id) : -1;

		return index;
	}

	self.syncronizeViewToURL = function() {
		var fragment = self.getHashFragment();

		if (self.showByMediaQuery) {
			var stateName = fragment;
			
			if (stateName==null || stateName=="") {
				var initialView = self.getInitialView();
				stateName = initialView ? self.getStateNameByViewId(initialView.id) : null;
			}
			
			self.showMediaQueryViewsByState(stateName);
			return;
		}

		var view = self.getViewById(fragment);
		var index = view ? self.getViewIndex(view) : 0;
		if (index==-1) index = 0;
		var currentView = self.hideViews(index);

		if (self.supportsPopState && currentView) {

			if (fragment==null) {
				window.history.replaceState({name:currentView.id}, null, "#"+ currentView.id);
			}
			else {
				window.history.pushState({name:currentView.id}, null, "#"+ currentView.id);
			}
		}
		
		self.setViewVariables(view);
		return view;
	}

	/**
	 * Set the currentView or currentOverlay properties and set the lastView or lastOverlay properties
	 */
	self.setViewVariables = function(view, overlay, parentView) {
		if (view) {
			if (self.currentView) {
				self.lastView = self.currentView;
			}
			self.currentView = view;
		}

		if (overlay) {
			if (self.currentOverlay) {
				self.lastOverlay = self.currentOverlay;
			}
			self.currentOverlay = overlay;
		}
	}

	self.getViewPreferenceBoolean = function(view, property, altValue) {
		var computedStyle = window.getComputedStyle(view);
		var value = computedStyle.getPropertyValue(property);
		var type = typeof value;
		
		if (value=="true" || (type=="string" && value.indexOf("true")!=-1)) {
			return true;
		}
		else if (value=="" && arguments.length==3) {
			return altValue;
		}

		return false;
	}

	self.getViewPreferenceValue = function(view, property, defaultValue) {
		var value = window.getComputedStyle(view).getPropertyValue(property);

		if (value===undefined) {
			return defaultValue;
		}
		
		value = value.replace(/^[\s\"]*/, "");
		value = value.replace(/[\s\"]*$/, "");
		value = value.replace(/^[\s"]*(.*?)[\s"]*$/, function (match, capture) { 
			return capture;
		});

		return value;
	}

	self.getStyleRuleValue = function(cssRule, property) {
		var value = cssRule ? cssRule.style.getPropertyValue(property) : null;

		if (value===undefined) {
			return null;
		}
		
		value = value.replace(/^[\s\"]*/, "");
		value = value.replace(/[\s\"]*$/, "");
		value = value.replace(/^[\s"]*(.*?)[\s"]*$/, function (match, capture) { 
			return capture;
		});

		return value;
	}

	/**
	 * Get the first defined value of property. Returns empty string if not defined
	 * @param {String} id id of element
	 * @param {String} property 
	 */
	self.getCSSPropertyValueForElement = function(id, property) {
		var styleSheets = document.styleSheets;
		var numOfStylesheets = styleSheets.length;
		var values = [];
		var selectorIDText = "#" + id;
		var selectorClassText = "." + id + "_Class";
		var value;

		for(var i=0;i<numOfStylesheets;i++) {
			var styleSheet = styleSheets[i];
			var cssRules = self.getStylesheetRules(styleSheet);
			var numOfCSSRules = cssRules.length;
			var cssRule;
			
			for (var j=0;j<numOfCSSRules;j++) {
				cssRule = cssRules[j];
				
				if (cssRule.media) {
					var mediaRules = cssRule.cssRules;
					var numOfMediaRules = mediaRules ? mediaRules.length : 0;
					
					for(var k=0;k<numOfMediaRules;k++) {
						var mediaRule = mediaRules[k];
						
						if (mediaRule.selectorText==selectorIDText || mediaRule.selectorText==selectorClassText) {
							
							if (mediaRule.style && mediaRule.style.getPropertyValue(property)!="") {
								value = mediaRule.style.getPropertyValue(property);
								values.push(value);
							}
						}
					}
				}
				else {

					if (cssRule.selectorText==selectorIDText || cssRule.selectorText==selectorClassText) {
						if (cssRule.style && cssRule.style.getPropertyValue(property)!="") {
							value = cssRule.style.getPropertyValue(property);
							values.push(value);
						}
					}
				}
			}
		}

		return values.pop();
	}

	self.getIsStyleDefined = function(id, property) {
		var value = self.getCSSPropertyValueForElement(id, property);
		return value!==undefined && value!="";
	}

	self.collectViews = function() {
		var viewIds = self.getViewIds();

		for (let index = 0; index < viewIds.length; index++) {
			const id = viewIds[index];
			const view = self.getElement(id);
			self.views[id] = view;
		}
		
		self.viewIds = viewIds;
	}

	self.collectOverlays = function() {
		var viewIds = self.getViewIds();
		var ids = [];

		for (let index = 0; index < viewIds.length; index++) {
			const id = viewIds[index];
			const view = self.getViewById(id);
			const isOverlay = view && self.isOverlay(view);
			
			if (isOverlay) {
				ids.push(id);
				self.overlays[id] = view;
			}
		}
		
		self.overlayIds = ids;
	}

	self.collectMediaQueries = function() {
		var viewIds = self.getViewIds();
		var styleSheet = self.getApplicationStylesheet();
		var cssRules = self.getStylesheetRules(styleSheet);
		var numOfCSSRules = cssRules ? cssRules.length : 0;
		var cssRule;
		var id = viewIds.length ? viewIds[0]: ""; // single view
		var selectorIDText = "#" + id;
		var selectorClassText = "." + id + "_Class";
		var viewsNotFound = viewIds.slice();
		var viewsFound = [];
		var selectorText = null;
		var property = self.prefix + "view-id";
		var stateName = self.prefix + "state";
		var stateValue = null;
		var view = null;
		
		for (var j=0;j<numOfCSSRules;j++) {
			cssRule = cssRules[j];
			
			if (cssRule.media) {
				var mediaRules = cssRule.cssRules;
				var numOfMediaRules = mediaRules ? mediaRules.length : 0;
				var mediaViewInfoFound = false;
				var mediaId = null;
				
				for(var k=0;k<numOfMediaRules;k++) {
					var mediaRule = mediaRules[k];

					selectorText = mediaRule.selectorText;
					
					if (selectorText==".mediaViewInfo" && mediaViewInfoFound==false) {

						mediaId = self.getStyleRuleValue(mediaRule, property);
						stateValue = self.getStyleRuleValue(mediaRule, stateName);

						selectorIDText = "#" + mediaId;
						selectorClassText = "." + mediaId + "_Class";
						view = self.getElement(mediaId);
						
						// prevent duplicates from load and domcontentloaded events
						if (self.addedViews.indexOf(mediaId)==-1) {
							self.addView(view, mediaId, cssRule, mediaRule, stateValue);
						}

						viewsFound.push(mediaId);

						if (viewsNotFound.indexOf(mediaId)!=-1) {
							viewsNotFound.splice(viewsNotFound.indexOf(mediaId));
						}

						mediaViewInfoFound = true;
					}

					if (selectorIDText==selectorText || selectorClassText==selectorText) {
						var styleObject = self.viewsDictionary[mediaId];
						if (styleObject) {
							styleObject.styleDeclaration = mediaRule;
						}
						break;
					}
				}
			}
			else {
				selectorText = cssRule.selectorText;
				
				if (selectorText==null) continue;

				selectorText = selectorText.replace(/[#|\s|*]?/g, "");

				if (viewIds.indexOf(selectorText)!=-1) {
					view = self.getElement(selectorText);
					self.addView(view, selectorText, cssRule, null, stateValue);

					if (viewsNotFound.indexOf(selectorText)!=-1) {
						viewsNotFound.splice(viewsNotFound.indexOf(selectorText));
					}

					break;
				}
			}
		}

		if (viewsNotFound.length) {
			console.log("Could not find the following views:" + viewsNotFound.join(",") + "");
			console.log("Views found:" + viewsFound.join(",") + "");
		}
	}

	/**
	 * Adds a view
	 * @param {HTMLElement} view view element
	 * @param {String} id id of view element
	 * @param {CSSRule} cssRule of view element
	 * @param {CSSMediaRule} mediaRule media rule of view element
	 * @param {String} stateName name of state if applicable
	 **/
	self.addView = function(view, viewId, cssRule, mediaRule, stateName) {
		var viewData = {};
		viewData.name = viewId;
		viewData.rule = cssRule;
		viewData.id = viewId;
		viewData.mediaRule = mediaRule;
		viewData.stateName = stateName;

		self.views.push(viewData);
		self.addedViews.push(viewId);
		self.viewsDictionary[viewId] = viewData;
		self.mediaQueryDictionary[viewId] = cssRule;
	}

	self.hasView = function(name) {

		if (self.addedViews.indexOf(name)!=-1) {
			return true;
		}
		return false;
	}

	/**
	 * Go to view by id. Views are added in addView()
	 * @param {String} id id of view in current
	 * @param {Boolean} maintainPreviousState if true then do not hide other views
	 * @param {String} parent id of parent view
	 **/
	self.goToView = function(id, maintainPreviousState, parent) {
		var state = self.viewsDictionary[id];

		if (state) {
			if (maintainPreviousState==false || maintainPreviousState==null) {
				self.hideViews();
			}
			self.enableMediaQuery(state.rule);
			self.updateViewLabel();
			self.updateURL();
		}
		else {
			var event = new Event(self.STATE_NOT_FOUND);
			self.stateName = id;
			window.dispatchEvent(event);
		}
	}

	/**
	 * Go to the view in the event targets CSS variable
	 **/
	self.goToTargetView = function(event) {
		var button = event.currentTarget;
		var buttonComputedStyles = getComputedStyle(button);
		var actionTargetValue = buttonComputedStyles.getPropertyValue(self.prefix+"action-target").trim();
		var animation = buttonComputedStyles.getPropertyValue(self.prefix+"animation").trim();
		var targetType = buttonComputedStyles.getPropertyValue(self.prefix+"action-type").trim();
		var targetView = self.application ? null : self.getElement(actionTargetValue);
		var targetState = targetView ? self.getStateNameByViewId(targetView.id) : null;
		var actionTargetStyles = targetView ? targetView.style : null;
		var state = self.viewsDictionary[actionTargetValue];
		
		// navigate to page
		if (self.application==false || targetType=="page") {
			document.location.href = "./" + actionTargetValue;
			return;
		}

		// if view is found
		if (targetView) {

			if (self.currentOverlay) {
				self.removeOverlay(false);
			}

			if (self.showByMediaQuery) {
				var stateName = targetState;
				
				if (stateName==null || stateName=="") {
					var initialView = self.getInitialView();
					stateName = initialView ? self.getStateNameByViewId(initialView.id) : null;
				}
				self.showMediaQueryViewsByState(stateName, event);
				return;
			}

			// add animation set in event target style declaration
			if (animation && self.supportAnimations) {
				self.crossFade(self.currentView, targetView, false, animation);
			}
			else {
				self.setViewVariables(self.currentView);
				self.hideViews();
				self.enableMediaQuery(state.rule);
				self.scaleViewIfNeeded(targetView);
				self.centerView(targetView);
				self.updateViewLabel();
				self.updateURL();
			}
		}
		else {
			var stateEvent = new Event(self.STATE_NOT_FOUND);
			self.stateName = name;
			window.dispatchEvent(stateEvent);
		}

		event.stopImmediatePropagation();
	}

	/**
	 * Cross fade between views
	 **/
	self.crossFade = function(from, to, update, animation) {
		var targetIndex = to.parentNode
		var fromIndex = Array.prototype.slice.call(from.parentElement.children).indexOf(from);
		var toIndex = Array.prototype.slice.call(to.parentElement.children).indexOf(to);

		if (from.parentNode==to.parentNode) {
			var reverse = self.getReverseAnimation(animation);
			var duration = self.getAnimationDuration(animation, true);

			// if target view is above (higher index)
			// then fade in target view 
			// and after fade in then hide previous view instantly
			if (fromIndex<toIndex) {
				self.setElementAnimation(from, null);
				self.setElementAnimation(to, null);
				self.showViewByMediaQuery(to);
				self.fadeIn(to, update, animation);

				setTimeout(function() {
					self.setElementAnimation(to, null);
					self.setElementAnimation(from, null);
					self.hideView(from);
					self.updateURL();
					self.setViewVariables(to);
					self.updateViewLabel();
				}, duration)
			}
			// if target view is on bottom
			// then show target view instantly 
			// and fade out current view
			else if (fromIndex>toIndex) {
				self.setElementAnimation(to, null);
				self.setElementAnimation(from, null);
				self.showViewByMediaQuery(to);
				self.fadeOut(from, update, reverse);

				setTimeout(function() {
					self.setElementAnimation(to, null);
					self.setElementAnimation(from, null);
					self.hideView(from);
					self.updateURL();
					self.setViewVariables(to);
				}, duration)
			}
		}
	}

	self.fadeIn = function(element, update, animation) {
		self.showViewByMediaQuery(element);

		if (update) {
			self.updateURL(element);

			element.addEventListener("animationend", function(event) {
				element.style.animation = null;
				self.setViewVariables(element);
				self.updateViewLabel();
				element.removeEventListener("animationend", arguments.callee);
			});
		}

		self.setElementAnimation(element, null);
		
		element.style.animation = animation;
	}

	self.fadeOutCurrentView = function(animation, update) {
		if (self.currentView) {
			self.fadeOut(self.currentView, update, animation);
		}
		if (self.currentOverlay) {
			self.fadeOut(self.currentOverlay, update, animation);
		}
	}

	self.fadeOut = function(element, update, animation) {
		if (update) {
			element.addEventListener("animationend", function(event) {
				element.style.animation = null;
				self.hideView(element);
				element.removeEventListener("animationend", arguments.callee);
			});
		}

		element.style.animationPlayState = "paused";
		element.style.animation = animation;
		element.style.animationPlayState = "running";
	}

	self.getReverseAnimation = function(animation) {
		if (animation && animation.indexOf("reverse")==-1) {
			animation += " reverse";
		}

		return animation;
	}

	/**
	 * Get duration in animation string
	 * @param {String} animation animation value
	 * @param {Boolean} inMilliseconds length in milliseconds if true
	 */
	self.getAnimationDuration = function(animation, inMilliseconds) {
		var duration = 0;
		var expression = /.+(\d\.\d)s.+/;

		if (animation && animation.match(expression)) {
			duration = parseFloat(animation.replace(expression, "$" + "1"));
			if (duration && inMilliseconds) duration = duration * 1000;
		}

		return duration;
	}

	self.setElementAnimation = function(element, animation, priority) {
		element.style.setProperty("animation", animation, "important");
	}

	self.getElement = function(id) {
		id = id ? id.trim() : id;
		var element = id ? document.getElementById(id) : null;

		return element;
	}

	self.getElementById = function(id) {
		id = id ? id.trim() : id;
		var element = id ? document.getElementById(id) : null;

		return element;
	}

	self.getElementByClass = function(className) {
		className = className ? className.trim() : className;
		var elements = document.getElementsByClassName(className);

		return elements.length ? elements[0] : null;
	}

	self.resizeHandler = function(event) {
		
		if (self.showByMediaQuery) {
			if (self.enableDeepLinking) {
				var stateName = self.getHashFragment();

				if (stateName==null || stateName=="") {
					var initialView = self.getInitialView();
					stateName = initialView ? self.getStateNameByViewId(initialView.id) : null;
				}
				self.showMediaQueryViewsByState(stateName, event);
			}
		}
		else {
			var visibleViews = self.getVisibleViews();

			for (let index = 0; index < visibleViews.length; index++) {	
				var view = visibleViews[index];
				self.scaleViewIfNeeded(view);
			}
		}

		window.dispatchEvent(new Event(self.APPLICATION_RESIZE));
	}

	self.scaleViewIfNeeded = function(view) {

		if (self.scaleViewsOnResize) {
			if (view==null) {
				view = self.getVisibleView();
			}

			var isViewScaled = view.getAttributeNS(null, self.SIZE_STATE_NAME)=="false" ? false : true;

			if (isViewScaled) {
				self.scaleViewToFit(view, true);
			}
			else {
				self.scaleViewToActualSize(view);
			}
		}
		else if (view) {
			self.centerView(view);
		}
	}

	self.centerView = function(view) {

		if (self.scaleViewsToFit) {
			self.scaleViewToFit(view, true);
		}
		else {
			self.scaleViewToActualSize(view);  // for centering support for now
		}
	}

	self.preventDoubleClick = function(event) {
		event.stopImmediatePropagation();
	}

	self.getHashFragment = function() {
		var value = window.location.hash ? window.location.hash.replace("#", "") : "";
		return value;
	}

	self.showBlockElement = function(view) {
		view.style.display = "block";
	}

	self.hideElement = function(view) {
		view.style.display = "none";
	}

	self.showStateFunction = null;

	self.showMediaQueryViewsByState = function(state, event) {
		// browser will hide and show by media query (small, medium, large)
		// but if multiple views exists at same size user may want specific view
		// if showStateFunction is defined that is called with state fragment and user can show or hide each media matching view by returning true or false
		// if showStateFunction is not defined and state is defined and view has a defined state that matches then show that and hide other matching views
		// if no state is defined show view 
		// an viewChanging event is dispatched before views are shown or hidden that can be prevented 

		// get all matched queries
		// if state name is specified then show that view and hide other views
		// if no state name is defined then show
		var matchedViews = self.getMatchingViews();
		var matchMediaQuery = true;
		var foundViews = self.getViewsByStateName(state, matchMediaQuery);
		var showViews = [];
		var hideViews = [];

		// loop views that match media query 
		for (let index = 0; index < matchedViews.length; index++) {
			var view = matchedViews[index];
			
			// let user determine visible view
			if (self.showStateFunction!=null) {
				if (self.showStateFunction(view, state)) {
					showViews.push(view);
				}
				else {
					hideViews.push(view);
				}
			}
			// state was defined so check if view matches state
			else if (foundViews.length) {

				if (foundViews.indexOf(view)!=-1) {
					showViews.push(view);
				}
				else {
					hideViews.push(view);
				}
			}
			// if no state names are defined show view (define unused state name to exclude)
			else if (state==null || state=="") {
				showViews.push(view);
			}
		}

		if (showViews.length) {
			var viewChangingEvent = new Event(self.VIEW_CHANGING);
			viewChangingEvent.showViews = showViews;
			viewChangingEvent.hideViews = hideViews;
			window.dispatchEvent(viewChangingEvent);

			if (viewChangingEvent.defaultPrevented==false) {
				for (var index = 0; index < hideViews.length; index++) {
					var view = hideViews[index];

					if (self.isOverlay(view)) {
						self.removeOverlay(view);
					}
					else {
						self.hideElement(view);
					}
				}

				for (var index = 0; index < showViews.length; index++) {
					var view = showViews[index];

					if (index==showViews.length-1) {
						self.clearDisplay(view);
						self.setViewOptions(view);
						self.setViewVariables(view);
						self.centerView(view);
						self.updateURLState(view, state);
					}
				}
			}

			var viewChangeEvent = new Event(self.VIEW_CHANGE);
			viewChangeEvent.showViews = showViews;
			viewChangeEvent.hideViews = hideViews;
			window.dispatchEvent(viewChangeEvent);
		}
		
	}

	self.clearDisplay = function(view) {
		view.style.setProperty("display", null);
	}

	self.hashChangeHandler = function(event) {
		var fragment = self.getHashFragment();
		var view = self.getViewById(fragment);

		if (self.showByMediaQuery) {
			var stateName = fragment;

			if (stateName==null || stateName=="") {
				var initialView = self.getInitialView();
				stateName = initialView ? self.getStateNameByViewId(initialView.id) : null;
			}
			self.showMediaQueryViewsByState(stateName);
		}
		else {
			if (view) {
				self.hideViews();
				self.showView(view);
				self.setViewVariables(view);
				self.updateViewLabel();
				
				window.dispatchEvent(new Event(self.VIEW_CHANGE));
			}
			else {
				window.dispatchEvent(new Event(self.VIEW_NOT_FOUND));
			}
		}
	}

	self.popStateHandler = function(event) {
		var state = event.state;
		var fragment = state ? state.name : window.location.hash;
		var view = self.getViewById(fragment);

		if (view) {
			self.hideViews();
			self.showView(view);
			self.updateViewLabel();
		}
		else {
			window.dispatchEvent(new Event(self.VIEW_NOT_FOUND));
		}
	}

	self.doubleClickHandler = function(event) {
		var view = self.getVisibleView();
		var scaleValue = view ? self.getViewScaleValue(view) : 1;
		var scaleNeededToFit = view ? self.getViewFitToViewportScale(view) : 1;
		var scaleNeededToFitWidth = view ? self.getViewFitToViewportWidthScale(view) : 1;
		var scaleNeededToFitHeight = view ? self.getViewFitToViewportHeightScale(view) : 1;
		var scaleToFitType = self.scaleToFitType;

		// Three scenarios
		// - scale to fit on double click
		// - set scale to actual size on double click
		// - switch between scale to fit and actual page size

		if (scaleToFitType=="width") {
			scaleNeededToFit = scaleNeededToFitWidth;
		}
		else if (scaleToFitType=="height") {
			scaleNeededToFit = scaleNeededToFitHeight;
		}

		// if scale and actual size enabled then switch between
		if (self.scaleToFitOnDoubleClick && self.actualSizeOnDoubleClick) {
			var isViewScaled = view.getAttributeNS(null, self.SIZE_STATE_NAME);
			var isScaled = false;
			
			// if scale is not 1 then view needs scaling
			if (scaleNeededToFit!=1) {

				// if current scale is at 1 it is at actual size
				// scale it to fit
				if (scaleValue==1) {
					self.scaleViewToFit(view);
					isScaled = true;
				}
				else {
					// scale is not at 1 so switch to actual size
					self.scaleViewToActualSize(view);
					isScaled = false;
				}
			}
			else {
				// view is smaller than viewport 
				// so scale to fit() is scale actual size
				// actual size and scaled size are the same
				// but call scale to fit to retain centering
				self.scaleViewToFit(view);
				isScaled = false;
			}
			
			view.setAttributeNS(null, self.SIZE_STATE_NAME, isScaled+"");
			isViewScaled = view.getAttributeNS(null, self.SIZE_STATE_NAME);
		}
		else if (self.scaleToFitOnDoubleClick) {
			self.scaleViewToFit(view);
		}
		else if (self.actualSizeOnDoubleClick) {
			self.scaleViewToActualSize(view);
		}

	}

	self.scaleViewToFit = function(view) {
		return self.setViewScaleValue(view, true);
	}

	self.scaleViewToActualSize = function(view) {
		self.setViewScaleValue(view, false, 1);
	}

	self.onloadHandler = function(event) {
		self.initialize();
	}

	self.setElementHTML = function(id, value) {
		var element = self.getElement(id);
		element.innerHTML = value;
	}

	self.getStackArray = function(error) {
		var value = "";
		
		if (error==null) {
		  try {
			 error = new Error("Stack");
		  }
		  catch (e) {
			 
		  }
		}
		
		if ("stack" in error) {
		  value = error.stack;
		  var methods = value.split(/\n/g);
	 
		  var newArray = methods ? methods.map(function (value, index, array) {
			 value = value.replace(/\@.*/,"");
			 return value;
		  }) : null;
	 
		  if (newArray && newArray[0].includes("getStackTrace")) {
			 newArray.shift();
		  }
		  if (newArray && newArray[0].includes("getStackArray")) {
			 newArray.shift();
		  }
		  if (newArray && newArray[0]=="") {
			 newArray.shift();
		  }
	 
			return newArray;
		}
		
		return null;
	}

	self.log = function(value) {
		console.log.apply(this, [value]);
	}
	
	// initialize on load
	// sometimes the body size is 0 so we call this now and again later
	window.addEventListener("load", self.onloadHandler);
	window.document.addEventListener("DOMContentLoaded", self.onloadHandler);
}

window.application = new Application();
</script>
</head>
<body>
<div id="Giri_Yap">
	<svg class="Rectangle_6">
		<rect id="Rectangle_6" rx="23" ry="23" x="0" y="0" width="1112" height="46">
		</rect>
	</svg>
	<div id="Icon__Search__Sharp">
		<svg class="Mask" viewBox="0 0 24.192 24.193">
			<path id="Mask" d="M 8.990522384643555 0 C 4.033134460449219 0 0 4.033133029937744 0 8.990535736083984 C 0 13.94792366027832 4.033134460449219 17.98105812072754 8.990522384643555 17.98105812072754 C 11.127760887146 17.98105812072754 13.20345973968506 17.21179008483887 14.83523559570312 15.81495380401611 L 15.21615409851074 16.19341087341309 L 15.21616744995117 17.28889083862305 L 22.13021659851074 24.19299507141113 L 24.19175148010254 22.13022994995117 L 17.29262733459473 15.21492195129395 L 16.19464302062988 15.21492195129395 L 15.81245136260986 14.83523273468018 C 17.21092224121094 13.20585155487061 17.98104667663574 11.12950134277344 17.98104667663574 8.990535736083984 C 17.98104667663574 4.033133029937744 13.94791221618652 0 8.990522384643555 0 Z M 8.990522384643555 15.21492195129395 C 5.558374881744385 15.21492195129395 2.766123056411743 12.42266845703125 2.766123056411743 8.990535736083984 C 2.766123056411743 5.558388233184814 5.558374881744385 2.766121625900269 8.990522384643555 2.766121625900269 C 12.4226713180542 2.766121625900269 15.21492385864258 5.558388233184814 15.21492385864258 8.990535736083984 C 15.21492385864258 12.42266845703125 12.4226713180542 15.21492195129395 8.990522384643555 15.21492195129395 Z">
			</path>
		</svg>
		<img id="Icon__Search__Sharp_b" src="Icon__Search__Sharp_b.png" srcset="Icon__Search__Sharp_b.png 1x, <EMAIL> 2x">
			
		</svg>
	</div>
	<div id="Marka_rn_ad_veya_kategori_yazn">
		<span>Marka, ürün adı veya kategori yazın…</span>
	</div>
	<div id="Group_475">
		<div id="Group_4102">
			<div id="Group_3489">
				<svg class="Ellipse_138">
					<ellipse id="Ellipse_138" rx="12.5" ry="12.5" cx="12.5" cy="12.5">
					</ellipse>
				</svg>
				<div id="MA">
					<span>MA</span>
				</div>
			</div>
			<div id="Group_3495">
				<div id="Group_3414">
					<div id="Hesabm">
						<span>Hesabım</span>
					</div>
				</div>
			</div>
		</div>
		<div id="Group_3490">
			<div id="Group_3491">
				<svg class="Path_2958" viewBox="192.356 199.054 20.078 14.155">
					<path id="Path_2958" d="M 207.3431091308594 213.2090606689453 L 197.447998046875 213.2090606689453 C 195.8227844238281 213.2090606689453 194.4505310058594 212.3936920166016 194.2436828613281 211.3047485351562 L 192.3832702636719 201.5137481689453 C 192.1352233886719 200.2089233398438 193.640380859375 199.0539855957031 195.5875854492188 199.0539855957031 L 209.2030639648438 199.0539855957031 C 211.1507263183594 199.0539855957031 212.6554260253906 200.2089233398438 212.4078063964844 201.5137481689453 L 210.5474243164062 211.3047485351562 C 210.3405456542969 212.3936920166016 208.9682922363281 213.2090606689453 207.3431091308594 213.2090606689453 Z">
					</path>
				</svg>
				<svg class="Path_2959" viewBox="202.477 183.208 10.126 6.608">
					<path id="Path_2959" d="M 202.4769592285156 189.8162078857422 L 206.276611328125 184.0125732421875 C 206.9690246582031 182.9546356201172 207.9442291259766 182.9387664794922 208.6457824707031 183.9746551513672 L 212.6028289794922 189.8162078857422">
					</path>
				</svg>
			</div>
			<div id="Group_467">
				<div id="Group_468">
					<svg class="Oval">
						<ellipse id="Oval" rx="7.6487812995910645" ry="7.6487812995910645" cx="7.6487812995910645" cy="7.6487812995910645">
						</ellipse>
					</svg>
					<div id="n_">
						<span>3</span>
					</div>
				</div>
			</div>
		</div>
		<div id="Group_3494">
			<div id="Group_3414_cq">
				<div id="Sepetim">
					<span>Sepetim</span>
				</div>
			</div>
		</div>
		<div id="Group_4098">
			<svg class="heart" viewBox="0 1.511 21.965 20.12">
				<path id="heart" d="M 21.53754043579102 5.171326160430908 C 22.53513145446777 8.253790855407715 21.70044898986816 11.39055824279785 19.94078636169434 13.75365924835205 C 18.77967643737793 15.35651683807373 17.39159202575684 16.73727798461914 15.97238731384277 17.94475746154785 C 14.66667175292969 19.16017150878906 11.74528789520264 21.56476211547852 10.97101020812988 21.63126754760742 C 10.28642272949219 21.50069618225098 9.518247604370117 20.72641754150391 8.975216865539551 20.32799339294434 C 5.921431541442871 18.00760459899902 2.634567499160767 15.18506526947021 0.9676452875137329 12.09832859039307 C -0.429591953754425 9.135454177856445 -0.4320324957370758 5.470298767089844 1.742532134056091 3.198720216751099 C 4.562633514404297 0.656846284866333 8.814139366149902 1.15350615978241 10.97101020812988 3.80947732925415 C 11.55003929138184 3.058385848999023 12.26269340515137 2.467153072357178 13.10774707794189 2.036389350891113 C 16.53433799743652 0.6684389710426331 20.09942817687988 2.062625408172607 21.53754425048828 5.171326160430908 Z">
				</path>
			</svg>
			<div id="Group_3496">
				<div id="Group_468_cv">
					<svg class="Oval_cw">
						<ellipse id="Oval_cw" rx="7.398660659790039" ry="7.4694695472717285" cx="7.398660659790039" cy="7.4694695472717285">
						</ellipse>
					</svg>
					<div id="n__cx">
						<span>1</span>
					</div>
				</div>
			</div>
		</div>
		<div id="Group_477">
			<div id="Group_3414_cz">
				<div id="Listem">
					<span>Listem</span>
				</div>
			</div>
		</div>
	</div>
	<div id="Group_3499">
		<div id="Nav_Label" class="Nav_Label">
			<div id="En_ok_Kiralanan">
				<span>En Çok Kiralanan</span>
			</div>
		</div>
		<svg class="Line_52" viewBox="0 0 1 18.297">
			<path id="Line_52" d="M 0 0 L 0 18.296875">
			</path>
		</svg>
		<div id="Nav_Label_c" class="Nav_Label">
			<div id="Kurumsal">
				<span>Kurumsal</span>
			</div>
		</div>
		<svg class="Line_51" viewBox="0 0 1 18.297">
			<path id="Line_51" d="M 0 0 L 0 18.296875">
			</path>
		</svg>
		<div id="Nav_Label_da" class="Nav_Label">
			<div id="Nasl_alr">
				<span>Nasıl Çalışır?</span>
			</div>
		</div>
	</div>
	<div id="Group_4193">
		<div id="Group_4181">
			<div id="DRONLAR">
				<span>DRONLAR</span>
			</div>
			<div id="Group_4182">
				<svg class="Line_77" viewBox="0 0 6.86 1">
					<path id="Line_77" d="M 0 0 L 6.860310077667236 0">
					</path>
				</svg>
				<svg class="Line_78" viewBox="0 0 1 4.187">
					<path id="Line_78" d="M 0 0 L 0 4.186620235443115">
					</path>
				</svg>
				<svg class="Rectangle_960">
					<rect id="Rectangle_960" rx="1.8548353910446167" ry="1.8548353910446167" x="0" y="0" width="6.183" height="4.569">
					</rect>
				</svg>
				<svg class="Line_79" viewBox="0 0 6.86 1">
					<path id="Line_79" d="M 0 0 L 6.860310077667236 0">
					</path>
				</svg>
				<svg class="Line_80" viewBox="0 0 1 4.187">
					<path id="Line_80" d="M 0 0 L 0 4.186620235443115">
					</path>
				</svg>
				<svg class="Rectangle_961">
					<rect id="Rectangle_961" rx="1.8548353910446167" ry="1.8548353910446167" x="0" y="0" width="6.183" height="4.569">
					</rect>
				</svg>
				<svg class="Rectangle_962">
					<rect id="Rectangle_962" rx="1.583772897720337" ry="1.583772897720337" x="0" y="0" width="8.321" height="7.295">
					</rect>
				</svg>
				<svg class="Path_3726" viewBox="445.491 171.302 7.484 13.79">
					<path id="Path_3726" d="M 449.5728149414062 171.302001953125 L 445.5645141601562 183.1728820800781 C 445.2436828613281 184.1242370605469 446.0077209472656 185.0918884277344 447.078857421875 185.0918884277344 L 447.078857421875 185.0918884277344 C 447.7538757324219 185.0918884277344 448.3545227050781 184.6948852539062 448.5769348144531 184.1019897460938 L 452.9745178222656 172.3817138671875">
					</path>
				</svg>
				<svg class="Path_3727" viewBox="464.762 171.302 7.483 14.082">
					<path id="Path_3727" d="M 468.1636962890625 171.302001953125 L 472.171142578125 183.4244079589844 C 472.4928283691406 184.3954467773438 471.7288208007812 185.3836364746094 470.6576538085938 185.3836364746094 L 470.6576538085938 185.3836364746094 C 469.9826354980469 185.3836364746094 469.3820190429688 184.9780883789062 469.1595764160156 184.3732299804688 L 464.7619934082031 172.4048156738281">
					</path>
				</svg>
				<svg class="Line_81" viewBox="0 0 1.281 2.733">
					<path id="Line_81" d="M 0 0 L 1.281312108039856 2.733465671539307">
					</path>
				</svg>
				<svg class="Line_82" viewBox="0 0 1.281 2.733">
					<path id="Line_82" d="M 1.281312108039856 0 L 0 2.733465671539307">
					</path>
				</svg>
				<svg class="Ellipse_304">
					<ellipse id="Ellipse_304" rx="1.8739674091339111" ry="1.8739674091339111" cx="1.8739674091339111" cy="1.8739674091339111">
					</ellipse>
				</svg>
				<svg class="Path_3728" viewBox="448.631 164.308 18.841 7.752">
					<path id="Path_3728" d="M 467.4721069335938 168.1349182128906 L 467.4721069335938 168.503662109375 C 467.4721069335938 169.0015869140625 467.158935546875 169.4498901367188 466.6918334960938 169.6278533935547 L 460.5360717773438 171.9806365966797 C 460.3949584960938 172.0362548828125 460.247802734375 172.0602111816406 460.1005859375 172.0602111816406 L 456.0272827148438 172.0602111816406 C 455.861328125 172.0602111816406 455.7022094726562 172.0302581787109 455.5481567382812 171.9626770019531 L 449.3624877929688 169.2899017333984 C 448.920166015625 169.0999755859375 448.6309814453125 168.6636505126953 448.6309814453125 168.1845397949219 L 448.6309814453125 168.1349182128906 C 448.6309814453125 167.4718475341797 449.1716918945312 166.9311370849609 449.8416137695312 166.9311370849609 L 454.5531616210938 166.9311370849609 C 454.9158935546875 165.4262084960938 456.2728271484375 164.3079986572266 457.89501953125 164.3079986572266 C 459.51025390625 164.3079986572266 460.8680419921875 165.4262084960938 461.2367553710938 166.9311370849609 L 466.261474609375 166.9311370849609 C 466.931396484375 166.9311370849609 467.4721069335938 167.4718475341797 467.4721069335938 168.1349182128906 Z">
					</path>
				</svg>
			</div>
		</div>
		<div id="Group_4166">
			<div id="SES_VE_GRNT">
				<span>SES VE GÖRÜNTÜ</span>
			</div>
			<div id="Group_4173">
				<svg class="Rectangle_956">
					<rect id="Rectangle_956" rx="1.7924505472183228" ry="1.7924505472183228" x="0" y="0" width="15.919" height="21.531">
					</rect>
				</svg>
				<svg class="Ellipse_297">
					<ellipse id="Ellipse_297" rx="1.5379410982131958" ry="1.5379410982131958" cx="1.5379410982131958" cy="1.5379410982131958">
					</ellipse>
				</svg>
				<svg class="Ellipse_298">
					<ellipse id="Ellipse_298" rx="3.4000511169433594" ry="3.4000511169433594" cx="3.4000511169433594" cy="3.4000511169433594">
					</ellipse>
				</svg>
				<svg class="Ellipse_299">
					<ellipse id="Ellipse_299" rx="1.5379410982131958" ry="1.5379410982131958" cx="1.5379410982131958" cy="1.5379410982131958">
					</ellipse>
				</svg>
				<svg class="Ellipse_300">
					<ellipse id="Ellipse_300" rx="3.4000511169433594" ry="3.4000511169433594" cx="3.4000511169433594" cy="3.4000511169433594">
					</ellipse>
				</svg>
				<svg class="Rectangle_957">
					<rect id="Rectangle_957" rx="0" ry="0" x="0" y="0" width="3.076" height="2.276">
					</rect>
				</svg>
				<svg class="Rectangle_958">
					<rect id="Rectangle_958" rx="1.1380764245986938" ry="1.1380764245986938" x="0" y="0" width="15.742" height="2.276">
					</rect>
				</svg>
				<svg class="Line_72" viewBox="0 0 2 5.629">
					<path id="Line_72" d="M 0 0 L 0 5.628864288330078">
					</path>
				</svg>
				<svg class="Line_73" viewBox="0 0 2 3.291">
					<path id="Line_73" d="M 0 0 L 0 3.291193962097168">
					</path>
				</svg>
				<svg class="Line_74" viewBox="0 0 2 5.629">
					<path id="Line_74" d="M 0 0 L 0 5.628864288330078">
					</path>
				</svg>
				<svg class="Line_75" viewBox="0 0 2 3.291">
					<path id="Line_75" d="M 0 0 L 0 3.291193962097168">
					</path>
				</svg>
			</div>
		</div>
		<div id="Group_4164">
			<div id="KAMERELAR">
				<span>KAMERELAR</span>
			</div>
			<div id="Group_4180">
				<svg class="Path_3723" viewBox="214.491 160.17 29.594 23.034">
					<path id="Path_3723" d="M 242.2140502929688 162.9620513916016 L 235.5703430175781 162.9620513916016 L 235.5703430175781 161.9430694580078 C 235.5703430175781 160.9638977050781 234.7764434814453 160.1699981689453 233.7972717285156 160.1699981689453 L 224.7791595458984 160.1699981689453 C 223.8000183105469 160.1699981689453 223.006103515625 160.9638977050781 223.006103515625 161.9430694580078 L 223.006103515625 162.9620513916016 L 216.3623962402344 162.9620513916016 C 215.3287811279297 162.9620513916016 214.4910125732422 163.7998199462891 214.4910125732422 164.8334350585938 L 214.4910125732422 181.3322143554688 C 214.4910125732422 182.3658294677734 215.3287811279297 183.20361328125 216.3623962402344 183.20361328125 L 242.2140502929688 183.20361328125 C 243.2476654052734 183.20361328125 244.08544921875 182.3658294677734 244.08544921875 181.3322143554688 L 244.08544921875 164.8334350585938 C 244.08544921875 163.7998199462891 243.2476654052734 162.9620513916016 242.2140502929688 162.9620513916016 Z">
					</path>
				</svg>
				<svg class="Ellipse_295">
					<ellipse id="Ellipse_295" rx="6.528417587280273" ry="6.528417587280273" cx="6.528417587280273" cy="6.528417587280273">
					</ellipse>
				</svg>
				<svg class="Ellipse_296">
					<ellipse id="Ellipse_296" rx="1.3261975049972534" ry="1.3261975049972534" cx="1.3261975049972534" cy="1.3261975049972534">
					</ellipse>
				</svg>
			</div>
		</div>
		<div id="Group_4162">
			<div id="BLGSAYAR">
				<span>BİLGİSAYAR</span>
			</div>
			<div id="Group_4179">
				<svg class="Rectangle_963">
					<rect id="Rectangle_963" rx="2.8346457481384277" ry="2.8346457481384277" x="0" y="0" width="26.549" height="16.101">
					</rect>
				</svg>
				<svg class="Rectangle_964">
					<rect id="Rectangle_964" rx="0" ry="0" x="0" y="0" width="30.721" height="4.021">
					</rect>
				</svg>
				<svg class="Line_83" viewBox="0 0 2.623 1">
					<path id="Line_83" d="M 0 0 L 2.623203277587891 0">
					</path>
				</svg>
			</div>
		</div>
		<div id="Group_4168">
			<div id="Group_4163">
				<div id="Component_212__44" class="Component_212___44">
					<div id="TELEFON_VE_AKSESUAR">
						<span>TELEFON VE AKSESUAR</span>
					</div>
				</div>
				<div id="Group_4178">
					<svg class="Rectangle_955">
						<rect id="Rectangle_955" rx="1.8259201049804688" ry="1.8259201049804688" x="0" y="0" width="16.081" height="24.252">
						</rect>
					</svg>
					<svg class="Line_71" viewBox="0 0 3.19 1">
						<path id="Line_71" d="M 0 0 L 3.190076112747192 0">
						</path>
					</svg>
				</div>
			</div>
		</div>
		<div id="Group_4167">
			<div id="kiralamini">
				<span style="text-transform:uppercase;">kirala</span><span style="color:rgba(255,189,100,1);">m</span><span style="color:rgba(255,100,207,1);">i</span><span style="color:rgba(97,186,63,1);">n</span><span style="color:rgba(136,84,208,1);">i</span>
			</div>
			<svg class="bxs-baby-carriage" viewBox="0 0 26.534 25.206">
				<path id="bxs-baby-carriage" d="M 26.08901786804199 13.63309097290039 C 26.1778392791748 13.34239864349365 26.25367736816406 13.04789352416992 26.31630706787109 12.75045680999756 C 26.32073020935059 12.72746276855469 26.32161331176758 12.70623683929443 26.3269214630127 12.68412590026855 C 26.35345077514648 12.55411815643311 26.3658332824707 12.42057228088379 26.38706016540527 12.28879642486572 C 26.42066764831543 12.08096027374268 26.46046829223633 11.87312698364258 26.47992324829102 11.66440868377686 C 26.79971694946289 8.498024940490723 25.67793273925781 5.355790138244629 23.42519569396973 3.107805252075195 C 21.43613815307617 1.114807367324829 18.73500061035156 -0.003585984231904149 15.91926002502441 8.771178727329243e-06 L 15.91926002502441 10.61285209655762 L 5.992717266082764 10.61285209655762 L 4.854490280151367 7.959641456604004 L 1.169736480803963e-21 7.959641456604004 L 1.559648136200971e-21 10.61285209655762 L 3.106024265289307 10.61285209655762 L 5.531057834625244 16.27214813232422 C 3.792265653610229 16.98844718933105 2.656355142593384 18.68183898925781 2.653209447860718 20.56238555908203 C 2.653209686279297 23.12273788452148 4.735978603363037 25.20550727844238 7.296328544616699 25.20550727844238 C 9.629384994506836 25.20550727844238 11.54677104949951 23.46942329406738 11.8713493347168 21.22569274902344 L 14.65987110137939 21.22569274902344 C 14.98444652557373 23.46942329406738 16.90183448791504 25.20550727844238 19.23489570617676 25.20550727844238 C 21.79524040222168 25.20550727844238 23.87800979614258 23.12273788452148 23.87800979614258 20.56238555908203 C 23.87800979614258 19.72663116455078 23.63833618164062 18.95277404785156 23.25008583068848 18.2735538482666 C 23.30580139160156 18.21960258483887 23.36859703063965 18.17184638977051 23.42431259155273 18.11612892150879 C 23.90188980102539 17.63766670227051 24.33701705932617 17.10879325866699 24.72084617614746 16.54542922973633 C 25.10290908813477 15.97498989105225 25.43190574645996 15.37005710601807 25.69722366333008 14.74301528930664 C 25.71845054626465 14.69437503814697 25.73171806335449 14.64219284057617 25.75117492675781 14.59266662597656 L 25.7529411315918 14.59266662597656 L 25.77239608764648 14.53695011138916 C 25.88913917541504 14.24509811401367 25.99526405334473 13.95236110687256 26.08370971679688 13.65077686309814 L 26.08813285827637 13.63397312164307 Z M 7.296328544616699 22.55229377746582 C 6.199668884277344 22.55229377746582 5.30642032623291 21.65904998779297 5.30642032623291 20.56238555908203 C 5.30642032623291 19.46572875976562 6.198783874511719 18.57248115539551 7.296328544616699 18.57248115539551 C 8.393874168395996 18.57248115539551 9.286238670349121 19.46572875976562 9.286238670349121 20.56238555908203 C 9.286238670349121 21.65904998779297 8.393874168395996 22.55229377746582 7.296328544616699 22.55229377746582 Z M 19.23577690124512 22.55229377746582 C 18.13911628723145 22.55229377746582 17.2458667755127 21.65904998779297 17.2458667755127 20.56238555908203 C 17.2458667755127 19.46572875976562 18.13911628723145 18.57248115539551 19.23577690124512 18.57248115539551 C 20.33243751525879 18.57248115539551 21.22568511962891 19.46572875976562 21.22568511962891 20.56238555908203 C 21.22568511962891 21.65904998779297 20.33243751525879 22.55229377746582 19.23577690124512 22.55229377746582 Z">
				</path>
			</svg>
		</div>
	</div>
	<div id="Layer_1-2">
		<div id="Group_3804">
			<div id="Group_3803">
				<svg class="Path_2976" viewBox="0 0 24.422 23.319">
					<path id="Path_2976" d="M 11.81330490112305 23.31679916381836 L 0 23.31679916381836 L 0 16.93750953674316 L 11.81330490112305 16.93750953674316 C 15.24763011932373 16.93750953674316 18.04297065734863 14.14392852783203 18.04297065734863 10.70784378051758 L 18.04297065734863 0 L 24.42226219177246 0 L 24.42226219177246 10.70960330963135 C 24.42226219177246 17.66098785400391 18.76644897460938 23.31855773925781 11.81330585479736 23.31855773925781 Z">
					</path>
				</svg>
				<svg class="Path_2977" viewBox="95.07 95.96 24.422 23.319">
					<path id="Path_2977" d="M 107.6789474487305 95.95999908447266 L 119.4922561645508 95.95999908447266 L 119.4922561645508 102.3392868041992 L 107.6789474487305 102.3392868041992 C 104.2446212768555 102.3392868041992 101.4492797851562 105.1328659057617 101.4492797851562 108.5689468383789 L 101.4492797851562 119.278564453125 L 95.06999206542969 119.278564453125 L 95.06999206542969 108.5689468383789 C 95.06999206542969 101.6175689697266 100.7258071899414 95.95999908447266 107.6789398193359 95.95999908447266 Z">
					</path>
				</svg>
			</div>
			<svg class="Path_2978" viewBox="392.81 28.17 7.369 29.97">
				<path id="Path_2978" d="M 399.1224365234375 29.26226806640625 C 399.8265380859375 29.9898567199707 400.1785888671875 30.85330772399902 400.1785888671875 31.85083198547363 C 400.1785888671875 32.84835815429688 399.8265380859375 33.73147201538086 399.1224365234375 34.41973114013672 C 398.4183044433594 35.10798645019531 397.536376953125 35.45122146606445 396.4731750488281 35.45122146606445 C 395.4099731445312 35.45122146606445 394.5350952148438 35.10798645019531 393.8450622558594 34.41973114013672 C 393.155029296875 33.73147201538086 392.8099975585938 32.87696075439453 392.8099975585938 31.85083198547363 C 392.8099975585938 30.82470321655273 393.155029296875 29.95589065551758 393.8450622558594 29.24260330200195 C 394.5350952148438 28.52753067016602 395.4117126464844 28.16999435424805 396.4731750488281 28.16999435424805 C 397.53466796875 28.16999435424805 398.4183044433594 28.53468132019043 399.1224365234375 29.26226806640625 Z M 393.288818359375 58.14048004150391 L 393.288818359375 37.8377799987793 L 399.6610717773438 37.8377799987793 L 399.6610717773438 58.14048767089844 L 393.288818359375 58.14048767089844 Z">
				</path>
			</svg>
			<svg class="Path_2979" viewBox="445.23 80.67 14.697 20.27">
				<path id="Path_2979" d="M 459.9248962402344 80.82842254638672 L 459.6467590332031 87.20066833496094 L 458.4919738769531 87.20066833496094 C 453.8994140625 87.20066833496094 451.6022033691406 89.69676208496094 451.6022033691406 94.68718719482422 L 451.6022033691406 100.9397354125977 L 445.2299499511719 100.9397354125977 L 445.2299499511719 80.9481201171875 L 451.6022033691406 80.9481201171875 L 451.6022033691406 84.77147674560547 C 453.2481079101562 82.03598785400391 455.5452575683594 80.66999816894531 458.4919738769531 80.66999816894531 C 459.0235900878906 80.66999816894531 459.5006408691406 80.72280120849609 459.9266357421875 80.83017730712891 Z">
				</path>
			</svg>
			<svg class="Path_2980" viewBox="533.23 78.85 22.342 21.187">
				<path id="Path_2980" d="M 555.5716552734375 99.44010925292969 L 549.1993408203125 99.44010925292969 L 549.1993408203125 97.8470458984375 C 547.5799560546875 99.30809020996094 545.509765625 100.0368423461914 542.9872436523438 100.0368423461914 C 540.280029296875 100.0368423461914 537.975830078125 99.04756927490234 536.0781860351562 97.07076263427734 C 534.1805419921875 95.09218597412109 533.22998046875 92.55032348632812 533.22998046875 89.44342803955078 C 533.22998046875 86.33650970458984 534.1788330078125 83.82809448242188 536.0781860351562 81.83721160888672 C 537.9775390625 79.84632110595703 540.280029296875 78.84999847412109 542.9872436523438 78.84999847412109 C 545.5098266601562 78.84999847412109 547.5799560546875 79.58052062988281 549.1993408203125 81.039794921875 L 549.1993408203125 79.44673156738281 L 555.5716552734375 79.44673156738281 L 555.5716552734375 99.4383544921875 Z M 547.745361328125 93.16819000244141 C 548.7152099609375 92.19827270507812 549.1993408203125 90.95726776123047 549.1993408203125 89.44518280029297 C 549.1993408203125 87.93309020996094 548.7152099609375 86.72377014160156 547.745361328125 85.74153137207031 C 546.7772216796875 84.75929260253906 545.627685546875 84.26816558837891 544.3004760742188 84.26816558837891 C 542.8939819335938 84.26816558837891 541.7392578125 84.75225067138672 540.836181640625 85.72216796875 C 539.9331665039062 86.69033050537109 539.4825439453125 87.93309020996094 539.4825439453125 89.44518280029297 C 539.4825439453125 90.95726776123047 539.9331665039062 92.23348236083984 540.836181640625 93.18932342529297 C 541.7392578125 94.14514923095703 542.8939819335938 94.6221923828125 544.3004760742188 94.6221923828125 C 545.627685546875 94.6221923828125 546.775390625 94.13811492919922 547.745361328125 93.16819763183594 Z">
				</path>
			</svg>
			<svg class="Path_2981" viewBox="673.09 32.93 6.372 29.133">
				<path id="Path_2981" d="M 673.0899658203125 62.06259536743164 L 673.0899658203125 32.93000030517578 L 679.4622802734375 32.93000030517578 L 679.4622802734375 62.06259536743164 L 673.0899658203125 62.06259536743164 Z">
				</path>
			</svg>
			<svg class="Path_2982" viewBox="717.58 78.85 22.342 21.187">
				<path id="Path_2982" d="M 739.9215698242188 99.44010925292969 L 733.5492553710938 99.44010925292969 L 733.5492553710938 97.8470458984375 C 731.9298706054688 99.30809020996094 729.8597412109375 100.0368423461914 727.3372802734375 100.0368423461914 C 724.6298828125 100.0368423461914 722.32568359375 99.04756927490234 720.4281005859375 97.07076263427734 C 718.5304565429688 95.09218597412109 717.5798950195312 92.55032348632812 717.5798950195312 89.44342803955078 C 717.5798950195312 86.33650970458984 718.5287475585938 83.82809448242188 720.4281005859375 81.83721160888672 C 722.3274536132812 79.84632110595703 724.6298828125 78.84999847412109 727.3372802734375 78.84999847412109 C 729.8597412109375 78.84999847412109 731.9298706054688 79.58052062988281 733.5492553710938 81.039794921875 L 733.5492553710938 79.44673156738281 L 739.9215698242188 79.44673156738281 L 739.9215698242188 99.4383544921875 Z M 732.0952758789062 93.16819000244141 C 733.065185546875 92.19827270507812 733.5492553710938 90.95726776123047 733.5492553710938 89.44518280029297 C 733.5492553710938 87.93309020996094 733.065185546875 86.72377014160156 732.0952758789062 85.74153137207031 C 731.1270751953125 84.75929260253906 729.9776611328125 84.26816558837891 728.650390625 84.26816558837891 C 727.243896484375 84.26816558837891 726.0892333984375 84.75225067138672 725.1861572265625 85.72216796875 C 724.2830810546875 86.69033050537109 723.83251953125 87.93309020996094 723.83251953125 89.44518280029297 C 723.83251953125 90.95726776123047 724.2830810546875 92.23348236083984 725.1861572265625 93.18932342529297 C 726.0892333984375 94.14514923095703 727.243896484375 94.6221923828125 728.650390625 94.6221923828125 C 729.9776611328125 94.6221923828125 731.1253662109375 94.13811492919922 732.0952758789062 93.16819763183594 Z">
				</path>
			</svg>
			<svg class="Path_2983" viewBox="856.46 32.93 22.342 29.729">
				<path id="Path_2983" d="M 875.9534301757812 44.17415618896484 C 877.85107421875 46.19627380371094 878.8015747070312 48.77265167236328 878.8015747070312 51.89970397949219 C 878.8015747070312 55.02674865722656 877.8528442382812 57.6370964050293 875.9534301757812 59.64670181274414 C 874.055908203125 61.65631866455078 871.7516479492188 62.65932464599609 869.0443115234375 62.65932464599609 C 866.5218505859375 62.65932464599609 864.4517211914062 61.9173469543457 862.832275390625 60.43517303466797 L 862.832275390625 62.05323028564453 L 856.4600219726562 62.05323028564453 L 856.4600219726562 32.93000030517578 L 862.832275390625 32.93000030517578 L 862.832275390625 43.36602020263672 C 864.4517211914062 41.88205718994141 866.5218505859375 41.14186096191406 869.0443115234375 41.14186096191406 C 871.7516479492188 41.14186096191406 874.055908203125 42.1538200378418 875.9534301757812 44.17594146728516 Z M 871.1936645507812 55.70258331298828 C 872.0966186523438 54.73174667358398 872.5473022460938 53.46411895751953 872.5473022460938 51.89970397949219 C 872.5473022460938 50.33528137207031 872.0966186523438 49.10162353515625 871.1936645507812 48.11827850341797 C 870.2905883789062 47.13492965698242 869.1358032226562 46.64146423339844 867.7293701171875 46.64146423339844 C 866.402099609375 46.64146423339844 865.25439453125 47.14028930664062 864.2845458984375 48.13794326782227 C 863.3145751953125 49.13559722900391 862.8304443359375 50.38891983032227 862.8304443359375 51.89970397949219 C 862.8304443359375 53.41048431396484 863.3145751953125 54.69777679443359 864.2845458984375 55.68112564086914 C 865.25439453125 56.66447448730469 866.402099609375 57.15793609619141 867.7293701171875 57.15793609619141 C 869.1358032226562 57.15793609619141 870.2923583984375 56.67341232299805 871.1936645507812 55.70257949829102 Z">
				</path>
			</svg>
			<svg class="Path_2984" viewBox="990.66 82.25 20.271 20.588">
				<path id="Path_2984" d="M 1010.929809570312 102.2416152954102 L 1004.5576171875 102.2416152954102 L 1004.5576171875 100.3299331665039 C 1002.964538574219 102.0022125244141 1000.920837402344 102.8383407592773 998.4247436523438 102.8383407592773 C 996.0888671875 102.8383407592773 994.2106323242188 102.0743789672852 992.7900390625 100.548210144043 C 991.3695678710938 99.02204132080078 990.66015625 97.01003265380859 990.66015625 94.51393890380859 L 990.66015625 82.25 L 996.9918823242188 82.25 L 996.9918823242188 93.16203308105469 C 996.9918823242188 94.33086395263672 997.3104858398438 95.27967071533203 997.9476928710938 96.01018524169922 C 998.5849609375 96.74069976806641 999.4088134765625 97.10508728027344 1000.417419433594 97.10508728027344 C 1001.71826171875 97.10508728027344 1002.734008789062 96.64741516113281 1003.464477539062 95.73206329345703 C 1004.195007324219 94.81671142578125 1004.559387207031 93.41551971435547 1004.559387207031 91.53025054931641 L 1004.559387207031 82.25175476074219 L 1010.931640625 82.25175476074219 L 1010.931640625 102.2433776855469 Z">
				</path>
			</svg>
			<svg class="Path_2985" viewBox="1118.62 78.86 20.271 20.588">
				<path id="Path_2985" d="M 1136.761474609375 81.14837646484375 C 1138.18212890625 82.67454528808594 1138.891357421875 84.68656158447266 1138.891357421875 87.18265533447266 L 1138.891357421875 99.44834899902344 L 1132.559692382812 99.44834899902344 L 1132.559692382812 88.53631591796875 C 1132.559692382812 87.36748504638672 1132.241088867188 86.41868591308594 1131.603759765625 85.68816375732422 C 1130.966552734375 84.9576416015625 1130.142700195312 84.59326934814453 1129.134155273438 84.59326934814453 C 1127.833251953125 84.59326934814453 1126.817626953125 85.05094909667969 1126.087036132812 85.96805572509766 C 1125.356567382812 86.88339996337891 1124.9921875 88.28459167480469 1124.9921875 90.16986083984375 L 1124.9921875 99.44834899902344 L 1118.619873046875 99.44834899902344 L 1118.619873046875 79.45674133300781 L 1124.9921875 79.45674133300781 L 1124.9921875 81.368408203125 C 1126.585205078125 79.69613647460938 1128.62890625 78.86000061035156 1131.125 78.86000061035156 C 1133.460815429688 78.86000061035156 1135.339111328125 79.62396240234375 1136.759643554688 81.15013885498047 Z">
				</path>
			</svg>
			<svg class="Path_2986" viewBox="1244.09 82.25 20.272 20.588">
				<path id="Path_2986" d="M 1264.35986328125 102.2416152954102 L 1257.987426757812 102.2416152954102 L 1257.987426757812 100.3299331665039 C 1256.394409179688 102.0022125244141 1254.350708007812 102.8383407592773 1251.854614257812 102.8383407592773 C 1249.518798828125 102.8383407592773 1247.640502929688 102.0743789672852 1246.219970703125 100.548210144043 C 1244.79931640625 99.02204132080078 1244.089965820312 97.01003265380859 1244.089965820312 94.51393890380859 L 1244.089965820312 82.25 L 1250.421630859375 82.25 L 1250.421630859375 93.16203308105469 C 1250.421630859375 94.33086395263672 1250.740356445312 95.27967071533203 1251.377563476562 96.01018524169922 C 1252.014892578125 96.74069976806641 1252.838745117188 97.10508728027344 1253.847412109375 97.10508728027344 C 1255.148071289062 97.10508728027344 1256.163940429688 96.64741516113281 1256.894409179688 95.73206329345703 C 1257.624877929688 94.81671142578125 1257.9892578125 93.41551971435547 1257.9892578125 91.53025054931641 L 1257.9892578125 82.25175476074219 L 1264.361572265625 82.25175476074219 L 1264.361572265625 102.2433776855469 Z">
				</path>
			</svg>
			<svg class="Path_2987" viewBox="266.53 32.93 21.831 29.133">
				<path id="Path_2987" d="M 288.3611145019531 62.06259536743164 L 278.6830444335938 51.42111968994141 L 287.4457397460938 41.75079345703125 L 279.7585754394531 41.75079345703125 L 272.9022521972656 49.24274826049805 L 272.9022521972656 32.93000030517578 L 266.5299987792969 32.93000030517578 L 266.5299987792969 62.06259536743164 L 272.9022521972656 62.06259536743164 L 272.9022521972656 53.83378219604492 L 280.1968688964844 62.06259536743164 L 288.3611145019531 62.06259536743164 Z">
				</path>
			</svg>
		</div>
	</div>
	<div id="katergoriler_button">
		<div id="Group_64">
			<div id="Kategoriler">
				<span>Kategoriler</span>
			</div>
			<div id="Group_4192">
				<svg class="Rectangle_818">
					<rect id="Rectangle_818" rx="19.25005340576172" ry="19.25005340576172" x="0" y="0" width="187.367" height="38.5">
					</rect>
				</svg>
				<div id="Group_3484">
					<div id="Group_3484_fc">
						<svg class="Rectangle_814">
							<rect id="Rectangle_814" rx="2" ry="2" x="0" y="0" width="10.267" height="10.267">
							</rect>
						</svg>
						<svg class="Rectangle_816">
							<rect id="Rectangle_816" rx="2" ry="2" x="0" y="0" width="10.267" height="10.267">
							</rect>
						</svg>
						<svg class="Rectangle_815">
							<rect id="Rectangle_815" rx="2" ry="2" x="0" y="0" width="10.267" height="10.267">
							</rect>
						</svg>
						<svg class="Rectangle_817">
							<rect id="Rectangle_817" rx="5.133347511291504" ry="5.133347511291504" x="0" y="0" width="10.267" height="10.267">
							</rect>
						</svg>
					</div>
					<div id="TM_KATEGORLER">
						<span>TÜM KATEGORİLER</span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div id="Group_4066">
		<svg class="Rectangle_856">
			<rect id="Rectangle_856" rx="20" ry="20" x="0" y="0" width="549.629" height="543.571">
			</rect>
		</svg>
	</div>
	<div id="Group_4854">
		<div id="Group_4848">
			<div id="Component_249__2" class="Component_249___2">
				<svg class="Rectangle_738">
					<rect id="Rectangle_738" rx="24" ry="24" x="0" y="0" width="364" height="48">
					</rect>
				</svg>
			</div>
			<div id="Google_ile_Giri_Yap">
				<span>Google ile Giriş Yap</span>
			</div>
			<img id="n_56aba9695709cb3ab9e705feb639" src="n_56aba9695709cb3ab9e705feb639.png" srcset="n_56aba9695709cb3ab9e705feb639.png 1x, <EMAIL> 2x">
				
		</div>
		<div id="Group_4849">
			<div id="Component_249__1" class="Component_249___1">
				<svg class="Rectangle_738_fs">
					<rect id="Rectangle_738_fs" rx="24" ry="24" x="0" y="0" width="364" height="48">
					</rect>
				</svg>
			</div>
			<div id="Facebook_ile_Giri_Yap">
				<span>Facebook ile Giriş Yap</span>
			</div>
			<img id="n_6d8ae5089a0f28acb86616180c91" src="n_6d8ae5089a0f28acb86616180c91.png" srcset="n_6d8ae5089a0f28acb86616180c91.png 1x, <EMAIL> 2x">
				
		</div>
		<div id="Sosyal_medya_hesaplarna_giri_y">
			<span>Sosyal medya hesaplarına giriş yap.</span>
		</div>
	</div>
	<div id="Group_3258">
		<div id="Group_969">
			<img id="Text_Field__Outlined__Filled__" src="Text_Field__Outlined__Filled__.png" srcset="Text_Field__Outlined__Filled__.png 1x, <EMAIL> 2x">
				
			</svg>
		</div>
	</div>
	<div id="Group_4857">
		<div id="Sign_Up_Button" class="Sign_Up_Button">
			<svg class="Rectangle_738_f">
				<rect id="Rectangle_738_f" rx="24" ry="24" x="0" y="0" width="364" height="48">
				</rect>
			</svg>
			<div id="Giri_Yap_f">
				<span>Giriş Yap</span>
			</div>
		</div>
		<div id="Group_4850">
			<div id="ifremi_Unuttum">
				<span>Şifremi Unuttum!</span>
			</div>
			<div id="Group_4847">
				<div id="Caption">
					<span>Beni Hatırla</span>
				</div>
				<div id="Component_116__52" class="Component_116___52">
					<svg class="Rectangle_781">
						<rect id="Rectangle_781" rx="4" ry="4" x="0" y="0" width="20" height="20">
						</rect>
					</svg>
					<svg class="md-checkmark" viewBox="4.5 7.734 11 8.365">
						<path id="md-checkmark" d="M 8.0032958984375 14.31974029541016 L 5.416666984558105 11.72903728485107 L 4.5 12.60292625427246 L 8.0032958984375 16.0988883972168 L 15.5 8.607889175415039 L 14.58333396911621 7.734000205993652 L 8.0032958984375 14.31974029541016 Z">
						</path>
					</svg>
				</div>
			</div>
		</div>
	</div>
	<div id="Group_4858">
		<div id="Group_4067">
			<div id="Giri_Yap_gc">
				<span>Giriş Yap</span>
			</div>
		</div>
		<div id="Group_4844">
			<svg class="Surface" viewBox="0 0 367.506 48">
				<path id="Surface" d="M 12 0 L 355.505859375 0 C 362.1332702636719 0 367.505859375 5.37258243560791 367.505859375 12 L 367.505859375 36 C 367.505859375 42.62741851806641 362.1332702636719 48 355.505859375 48 L 12 48 C 5.37258243560791 48 0 42.62741851806641 0 36 L 0 12 C 0 5.37258243560791 5.37258243560791 0 12 0 Z">
				</path>
			</svg>
			<div id="Group_4843">
				<div id="CAPTION_TEXT_COLOR__STYLE">
					<div id="Caption_gh">
						<span>Assistive text</span>
					</div>
				</div>
				<div id="SUBTITLE_1_COLOR__STYLE">
					<div id="Subtitle_1">
						<span>E-posta adresi</span>
					</div>
				</div>
			</div>
		</div>
		<div id="Group_4845">
			<svg class="Surface_gl" viewBox="0 0 367.506 48">
				<path id="Surface_gl" d="M 12 0 L 355.505859375 0 C 362.1332702636719 0 367.505859375 5.37258243560791 367.505859375 12 L 367.505859375 36 C 367.505859375 42.62741851806641 362.1332702636719 48 355.505859375 48 L 12 48 C 5.37258243560791 48 0 42.62741851806641 0 36 L 0 12 C 0 5.37258243560791 5.37258243560791 0 12 0 Z">
				</path>
			</svg>
			<div id="Group_4843_gm">
				<div id="CAPTION_TEXT_COLOR__STYLE_gn">
					<div id="Caption_go">
						<span>Assistive text</span>
					</div>
				</div>
				<div id="SUBTITLE_1_COLOR__STYLE_gp">
					<div id="Subtitle_1_gq">
						<span>Şifre</span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div id="Group_974">
		<div id="Group_969_gs">
			<img id="Text_Field__Outlined__Filled___gt" src="Text_Field__Outlined__Filled___gt.png" srcset="Text_Field__Outlined__Filled___gt.png 1x, <EMAIL> 2x">
				
			</svg>
		</div>
	</div>
	<div id="Group_4856">
		<div id="Component_250__1" class="Component_250___1">
			<div id="yelik_Olutur">
				<span>Üyelik Oluştur!</span>
			</div>
		</div>
		<div id="Group_4855">
			<div id="kiralabunucoma_ye_deil_misin">
				<span>kirala</span><span style="color:rgba(35,31,32,1);">bunu</span><span>.com</span><span style="font-style:normal;font-weight:normal;">’a üye değil misin?</span>
			</div>
		</div>
	</div>
	<div id="Footer" class="Footer">
		<svg class="Rectangle_13">
			<rect id="Rectangle_13" rx="0" ry="0" x="0" y="0" width="1283" height="62">
			</rect>
		</svg>
		<div id="Terms__conditions_">
			<span>Şartlar ve Koşullar | Gizlilik Politikası</span>
		</div>
		<img id="Image_1" src="Image_1.png" srcset="Image_1.png 1x, <EMAIL> 2x">
			
		<div id="n_020_All_right_rese">
			<span>Kiralabunu.com © 2022  Tüm Hakları Saklıdır</span>
		</div>
		<div id="Group_4366">
			<div id="Group_4364">
				<div id="Group_4360">
					<div id="HEAD_OFFICE">
						<span>AKLINIZA TAKILAN SORULAR İÇİN</span>
					</div>
					<div id="Group_3884">
						<div id="Component_172__1" class="Component_172___1">
							<svg class="Rectangle_29">
								<rect id="Rectangle_29" rx="18" ry="18" x="0" y="0" width="232" height="36">
								</rect>
							</svg>
							<div id="Mteri_Hizmetlerimize_Balann">
								<span>Müşteri Hizmetlerimize Bağlanın</span>
							</div>
						</div>
						<div id="Group_3883">
							<svg class="Line_63" viewBox="0 0 63.251 1">
								<path id="Line_63" d="M 0 0 L 63.25136947631836 0">
								</path>
							</svg>
							<svg class="Line_64" viewBox="0 0 75.168 1">
								<path id="Line_64" d="M 0 0 L 75.16829681396484 0">
								</path>
							</svg>
							<div id="VEYA">
								<span>VEYA</span>
							</div>
						</div>
						<div id="HEAD_OFFICE_hg">
							<span>0850 255 1552</span>
						</div>
						<div id="ar_Merkezimizi_arayn">
							<span>Çağrı Merkezimizi arayın.</span>
						</div>
					</div>
				</div>
			</div>
			<div id="Group_4363">
				<div id="HEAD_OFFICE_hj">
					<span>YARDIM</span>
				</div>
				<div id="Group_4357">
					<div id="erez_Cookie_Politikas">
						<span>Çerez (Cookie) Politikası</span>
					</div>
					<div id="Cayma_Fesih_ve_ade_Koullar">
						<span>Cayma, Fesih ve İade Koşulları</span>
					</div>
					<div id="Aydnlatma_Metni">
						<span>Aydınlatma Metni</span>
					</div>
					<div id="yelik_Szlemesi">
						<span>Üyelik Sözleşmesi</span>
					</div>
				</div>
			</div>
			<div id="Group_4362">
				<div id="HEAD_OFFICE_hq">
					<span>NASIL ÇALIŞIR?</span>
				</div>
				<div id="Group_4358">
					<div id="deme_ve_Teslimat">
						<span>Ödeme ve Teslimat</span>
					</div>
					<div id="Kimlik_ve_Findeks_Raporu">
						<span>Kimlik ve Findeks Raporu</span>
					</div>
					<div id="Nasl_Kiralarm">
						<span>Nasıl Kiralarım?</span>
					</div>
				</div>
			</div>
			<div id="Group_4361">
				<div id="LATEST_POSTS">
					<span>KİRALABUNU</span>
				</div>
				<div id="Group_4359">
					<div id="letiim">
						<span>İletişim</span>
					</div>
					<div id="Blog">
						<span>Blog</span>
					</div>
					<div id="Kiralamini_h">
						<span>Kiralamini</span>
					</div>
					<div id="Basnda_Biz">
						<span>Basında Biz</span>
					</div>
					<div id="Kurumsal_h">
						<span>Kurumsal</span>
					</div>
					<div id="Hakkmzda">
						<span>Hakkımızda</span>
					</div>
					<div id="Anasayfa">
						<span>Anasayfa</span>
					</div>
				</div>
			</div>
			<div id="Group_4365">
				<div id="Layer_1-2_h">
					<div id="Group_3804_h">
						<div id="Group_3803_h">
							<svg class="Path_2976_h" viewBox="0 0 22.072 21.075">
								<path id="Path_2976_h" d="M 10.67662715911865 21.07325172424316 L 0 21.07325172424316 L 0 15.30777835845947 L 10.67662715911865 15.30777835845947 C 13.78049945831299 15.30777835845947 16.30687141418457 12.78299522399902 16.30687141418457 9.677532196044922 L 16.30687141418457 0 L 22.07234764099121 0 L 22.07234764099121 9.679122924804688 C 22.07234764099121 15.96164226531982 16.96073722839355 21.07484245300293 10.67662715911865 21.07484245300293 Z">
								</path>
							</svg>
							<svg class="Path_2977_ia" viewBox="95.07 95.96 22.072 21.075">
								<path id="Path_2977_ia" d="M 106.4657135009766 95.96000671386719 L 117.142333984375 95.96000671386719 L 117.142333984375 101.7254791259766 L 106.4657135009766 101.7254791259766 C 103.3618469238281 101.7254791259766 100.8354721069336 104.250244140625 100.8354721069336 107.355712890625 L 100.8354721069336 117.0348358154297 L 95.06999206542969 117.0348358154297 L 95.06999206542969 107.355712890625 C 95.06999206542969 101.0731964111328 100.1816101074219 95.96000671386719 106.4657135009766 95.96000671386719 Z">
								</path>
							</svg>
						</div>
						<svg class="Path_2978_ib" viewBox="392.81 28.17 6.66 27.087">
							<path id="Path_2978_ib" d="M 398.5150756835938 29.15717315673828 C 399.1513977050781 29.81475067138672 399.4695739746094 30.59511947631836 399.4695739746094 31.49666213989258 C 399.4695739746094 32.3982048034668 399.1513977050781 33.19634628295898 398.5150756835938 33.81838607788086 C 397.8786926269531 34.4404182434082 397.0816040039062 34.75062561035156 396.1206970214844 34.75062561035156 C 395.1598205566406 34.75062561035156 394.369140625 34.4404182434082 393.7454833984375 33.81838607788086 C 393.121826171875 33.19634628295898 392.8099975585938 32.42405700683594 392.8099975585938 31.49666213989258 C 392.8099975585938 30.56926727294922 393.121826171875 29.78405380249023 393.7454833984375 29.13940048217773 C 394.369140625 28.49313354492188 395.161376953125 28.16999816894531 396.1206970214844 28.16999816894531 C 397.0800476074219 28.16999816894531 397.8786926269531 28.49959564208984 398.5150756835938 29.15717315673828 Z M 393.2427673339844 55.25671768188477 L 393.2427673339844 36.90754318237305 L 399.0018920898438 36.90754318237305 L 399.0018920898438 55.25672912597656 L 393.2427673339844 55.25672912597656 Z">
							</path>
						</svg>
						<svg class="Path_2979_ic" viewBox="445.23 80.67 13.283 18.319">
							<path id="Path_2979_ic" d="M 458.5109252929688 80.81318664550781 L 458.2595825195312 86.57228851318359 L 457.2159423828125 86.57228851318359 C 453.0652465820312 86.57228851318359 450.9890747070312 88.82821655273438 450.9890747070312 93.33845520019531 L 450.9890747070312 98.98937225341797 L 445.22998046875 98.98937225341797 L 445.22998046875 80.92136383056641 L 450.9890747070312 80.92136383056641 L 450.9890747070312 84.3768310546875 C 452.4766235351562 81.90455627441406 454.552734375 80.66999816894531 457.2159423828125 80.66999816894531 C 457.6964111328125 80.66999816894531 458.1275634765625 80.71773529052734 458.5125122070312 80.81476593017578 Z">
							</path>
						</svg>
						<svg class="Path_2980_id" viewBox="533.23 78.85 20.192 19.148">
							<path id="Path_2980_id" d="M 553.421875 97.45892333984375 L 547.662841796875 97.45892333984375 L 547.662841796875 96.01914978027344 C 546.19921875 97.339599609375 544.3282470703125 97.99824523925781 542.0484619140625 97.99824523925781 C 539.6016845703125 97.99824523925781 537.5191650390625 97.10415649414062 535.8040771484375 95.31755065917969 C 534.089111328125 93.52935791015625 533.22998046875 91.23208618164062 533.22998046875 88.42413330078125 C 533.22998046875 85.61614990234375 534.0875244140625 83.34910583496094 535.8040771484375 81.54978942871094 C 537.520751953125 79.75045776367188 539.6016845703125 78.85000610351562 542.0484619140625 78.85000610351562 C 544.3282470703125 78.85000610351562 546.19921875 79.51023864746094 547.662841796875 80.8291015625 L 547.662841796875 79.38931274414062 L 553.421875 79.38931274414062 L 553.421875 97.45733642578125 Z M 546.3487548828125 91.79049682617188 C 547.225341796875 90.91389465332031 547.662841796875 89.79231262207031 547.662841796875 88.42572021484375 C 547.662841796875 87.05912780761719 547.225341796875 85.96615600585938 546.3487548828125 85.07843017578125 C 545.4737548828125 84.19070434570312 544.434814453125 83.74684143066406 543.2353515625 83.74684143066406 C 541.964111328125 83.74684143066406 540.9205322265625 84.18434143066406 540.1043701171875 85.06092834472656 C 539.2882080078125 85.93594360351562 538.8809814453125 87.05912780761719 538.8809814453125 88.42572021484375 C 538.8809814453125 89.79231262207031 539.2882080078125 90.94572448730469 540.1043701171875 91.80960083007812 C 540.9205322265625 92.67344665527344 541.964111328125 93.10458374023438 543.2353515625 93.10458374023438 C 544.434814453125 93.10458374023438 545.4720458984375 92.66709899902344 546.3487548828125 91.79049682617188 Z">
							</path>
						</svg>
						<svg class="Path_2981_ie" viewBox="673.09 32.93 5.759 26.329">
							<path id="Path_2981_ie" d="M 673.0900268554688 59.25944900512695 L 673.0900268554688 32.93000030517578 L 678.84912109375 32.93000030517578 L 678.84912109375 59.25944900512695 L 673.0900268554688 59.25944900512695 Z">
							</path>
						</svg>
						<svg class="Path_2982_if" viewBox="717.58 78.85 20.192 19.148">
							<path id="Path_2982_if" d="M 737.7719116210938 97.45892333984375 L 732.0128173828125 97.45892333984375 L 732.0128173828125 96.01914978027344 C 730.5491943359375 97.339599609375 728.6781616210938 97.99824523925781 726.3984985351562 97.99824523925781 C 723.9515991210938 97.99824523925781 721.869140625 97.10415649414062 720.1541137695312 95.31755065917969 C 718.4390258789062 93.52935791015625 717.5799560546875 91.23208618164062 717.5799560546875 88.42413330078125 C 717.5799560546875 85.61614990234375 718.4375 83.34910583496094 720.1541137695312 81.54978942871094 C 721.8706665039062 79.75045776367188 723.9515991210938 78.85000610351562 726.3984985351562 78.85000610351562 C 728.6781616210938 78.85000610351562 730.5491943359375 79.51023864746094 732.0128173828125 80.8291015625 L 732.0128173828125 79.38931274414062 L 737.7719116210938 79.38931274414062 L 737.7719116210938 97.45733642578125 Z M 730.6986694335938 91.79049682617188 C 731.5753173828125 90.91389465332031 732.0128173828125 89.79231262207031 732.0128173828125 88.42572021484375 C 732.0128173828125 87.05912780761719 731.5753173828125 85.96615600585938 730.6986694335938 85.07843017578125 C 729.8236694335938 84.19070434570312 728.7848510742188 83.74684143066406 727.5852661132812 83.74684143066406 C 726.3141479492188 83.74684143066406 725.2704467773438 84.18434143066406 724.4544067382812 85.06092834472656 C 723.63818359375 85.93594360351562 723.2308959960938 87.05912780761719 723.2308959960938 88.42572021484375 C 723.2308959960938 89.79231262207031 723.63818359375 90.94572448730469 724.4544067382812 91.80960083007812 C 725.2704467773438 92.67344665527344 726.3141479492188 93.10458374023438 727.5852661132812 93.10458374023438 C 728.7848510742188 93.10458374023438 729.8220825195312 92.66709899902344 730.6986694335938 91.79049682617188 Z">
							</path>
						</svg>
						<svg class="Path_2983_ig" viewBox="856.46 32.93 20.192 26.869">
							<path id="Path_2983_ig" d="M 874.0778198242188 43.09223937988281 C 875.7927856445312 44.91979217529297 876.6519165039062 47.24826812744141 876.6519165039062 50.07443618774414 C 876.6519165039062 52.90059280395508 875.7944946289062 55.2597770690918 874.0778198242188 57.07601547241211 C 872.3628540039062 58.89226150512695 870.2803344726562 59.79876327514648 867.8334350585938 59.79876327514648 C 865.5537719726562 59.79876327514648 863.6828002929688 59.12817764282227 862.2191772460938 57.78861618041992 L 862.2191772460938 59.2509880065918 L 856.4600219726562 59.2509880065918 L 856.4600219726562 32.93000030517578 L 862.2191772460938 32.93000030517578 L 862.2191772460938 42.36186218261719 C 863.6828002929688 41.02068328857422 865.5537719726562 40.35171508789062 867.8334350585938 40.35171508789062 C 870.2803344726562 40.35171508789062 872.3628540039062 41.26629638671875 874.0778198242188 43.09385681152344 Z M 869.7760620117188 53.51140213012695 C 870.5921020507812 52.63397979736328 870.9994506835938 51.48831939697266 870.9994506835938 50.07443618774414 C 870.9994506835938 48.66054153442383 870.5921020507812 47.54558944702148 869.7760620117188 46.65685653686523 C 868.9598999023438 45.76812744140625 867.9161987304688 45.3221435546875 866.6450805664062 45.3221435546875 C 865.4454956054688 45.3221435546875 864.4081420898438 45.77297210693359 863.5316772460938 46.67462921142578 C 862.6549682617188 47.57629013061523 862.2174682617188 48.70901870727539 862.2174682617188 50.07443618774414 C 862.2174682617188 51.43984603881836 862.6549682617188 52.60327529907227 863.5316772460938 53.49200820922852 C 864.4081420898438 54.38073348999023 865.4454956054688 54.82671737670898 866.6450805664062 54.82671737670898 C 867.9161987304688 54.82671737670898 868.9614868164062 54.38881683349609 869.7760620117188 53.51139450073242 Z">
							</path>
						</svg>
						<svg class="Path_2984_ih" viewBox="990.66 82.25 18.321 18.607">
							<path id="Path_2984_ih" d="M 1008.979431152344 100.3180236816406 L 1003.220397949219 100.3180236816406 L 1003.220397949219 98.59028625488281 C 1001.780700683594 100.1016540527344 999.9336547851562 100.8573303222656 997.6776733398438 100.8573303222656 C 995.5665893554688 100.8573303222656 993.8690795898438 100.1668853759766 992.5851440429688 98.78756713867188 C 991.3013305664062 97.40823364257812 990.6600952148438 95.58982849121094 990.6600952148438 93.33390808105469 L 990.6600952148438 82.25 L 996.3827514648438 82.25 L 996.3827514648438 92.11209106445312 C 996.3827514648438 93.16844177246094 996.6707153320312 94.02595520019531 997.2465209960938 94.68618774414062 C 997.8224487304688 95.34640502929688 998.5670776367188 95.67573547363281 999.4785766601562 95.67573547363281 C 1000.654235839844 95.67573547363281 1001.572204589844 95.26210021972656 1002.232482910156 94.434814453125 C 1002.892761230469 93.6075439453125 1003.222106933594 92.34117126464844 1003.222106933594 90.63731384277344 L 1003.222106933594 82.2515869140625 L 1008.981140136719 82.2515869140625 L 1008.981140136719 100.3196105957031 Z">
							</path>
						</svg>
						<svg class="Path_2985_ii" viewBox="1118.62 78.86 18.321 18.607">
							<path id="Path_2985_ii" d="M 1135.015991210938 80.92818450927734 C 1136.299926757812 82.30751037597656 1136.941040039062 84.12592315673828 1136.941040039062 86.38184356689453 L 1136.941040039062 97.46733093261719 L 1131.218505859375 97.46733093261719 L 1131.218505859375 87.60525512695312 C 1131.218505859375 86.54889678955078 1130.930541992188 85.69139099121094 1130.3544921875 85.03115081787109 C 1129.778564453125 84.37092590332031 1129.0341796875 84.04160308837891 1128.12255859375 84.04160308837891 C 1126.946899414062 84.04160308837891 1126.029052734375 84.45525360107422 1125.36865234375 85.28411102294922 C 1124.708374023438 86.11138916015625 1124.379150390625 87.37775421142578 1124.379150390625 89.08161926269531 L 1124.379150390625 97.46733093261719 L 1118.619995117188 97.46733093261719 L 1118.619995117188 79.39932250976562 L 1124.379150390625 79.39932250976562 L 1124.379150390625 81.12704467773438 C 1125.81884765625 79.61568450927734 1127.665893554688 78.86000061035156 1129.921875 78.86000061035156 C 1132.032958984375 78.86000061035156 1133.73046875 79.55045318603516 1135.014282226562 80.92977905273438 Z">
							</path>
						</svg>
						<svg class="Path_2986_ij" viewBox="1244.09 82.25 18.321 18.607">
							<path id="Path_2986_ij" d="M 1262.40966796875 100.3180236816406 L 1256.650390625 100.3180236816406 L 1256.650390625 98.59028625488281 C 1255.210693359375 100.1016540527344 1253.363525390625 100.8573303222656 1251.107666015625 100.8573303222656 C 1248.996337890625 100.8573303222656 1247.298828125 100.1668853759766 1246.01513671875 98.78756713867188 C 1244.731201171875 97.40823364257812 1244.090087890625 95.58982849121094 1244.090087890625 93.33390808105469 L 1244.090087890625 82.25 L 1249.8125 82.25 L 1249.8125 92.11209106445312 C 1249.8125 93.16844177246094 1250.1005859375 94.02595520019531 1250.676513671875 94.68618774414062 C 1251.25244140625 95.34640502929688 1251.9970703125 95.67573547363281 1252.90869140625 95.67573547363281 C 1254.084228515625 95.67573547363281 1255.00244140625 95.26210021972656 1255.66259765625 94.434814453125 C 1256.32275390625 93.6075439453125 1256.652099609375 92.34117126464844 1256.652099609375 90.63731384277344 L 1256.652099609375 82.2515869140625 L 1262.4111328125 82.2515869140625 L 1262.4111328125 100.3196105957031 Z">
							</path>
						</svg>
						<svg class="Path_2987_ik" viewBox="266.53 32.93 19.73 26.329">
							<path id="Path_2987_ik" d="M 286.260498046875 59.25944900512695 L 277.513671875 49.64189910888672 L 285.4332275390625 40.90205383300781 L 278.4857177734375 40.90205383300781 L 272.2891235351562 47.67313003540039 L 272.2891235351562 32.93000030517578 L 266.5299987792969 32.93000030517578 L 266.5299987792969 59.25944900512695 L 272.2891235351562 59.25944900512695 L 272.2891235351562 51.82241439819336 L 278.8818359375 59.25944900512695 L 286.260498046875 59.25944900512695 Z">
							</path>
						</svg>
					</div>
				</div>
				<div id="Social_1">
					<div id="LinkedIn">
						<div id="Group_3880">
							<svg class="Background" viewBox="0 0 33.648 33.648">
								<path id="Background" d="M 33.64845275878906 16.82416725158691 C 33.64845275878906 26.11598205566406 26.11602973937988 33.64845275878906 16.82422637939453 33.64845275878906 C 7.532426834106445 33.64845275878906 0 26.11598205566406 0 16.82416725158691 C 0 7.532474040985107 7.532426834106445 0 16.82422637939453 0 C 26.11602973937988 0 33.64845275878906 7.532474040985107 33.64845275878906 16.82416725158691 Z">
								</path>
							</svg>
							<div id="social-youtube-outline">
								<svg class="Path_2998" viewBox="0 4.5 17.173 12.883">
									<path id="Path_2998" d="M 8.888347625732422 5.573328971862793 C 11.0784158706665 5.573328971862793 12.86968326568604 5.610061168670654 14.52690124511719 5.684001445770264 L 14.57365226745605 5.684001445770264 C 15.34835720062256 5.684001445770264 15.98233509063721 6.421975135803223 15.98233509063721 7.330726623535156 L 15.98233509063721 7.367458343505859 L 15.98567676544189 7.404189109802246 C 16.06295394897461 8.544780731201172 16.09968757629395 9.728781700134277 16.09968757629395 10.92280006408691 C 16.10302734375 12.11682033538818 16.06295394897461 13.30082130432129 15.98567676544189 14.4414119720459 L 15.98233509063721 14.47814273834229 L 15.98233509063721 14.51487350463867 C 15.98233509063721 14.9775972366333 15.82491397857666 15.40692901611328 15.53296852111816 15.7255916595459 C 15.27155208587646 16.01419830322266 14.92904186248779 16.17495727539062 14.57365036010742 16.17495727539062 L 14.51974678039551 16.17495727539062 C 12.74517631530762 16.2589168548584 10.87042713165283 16.30232620239258 8.938434600830078 16.30232620239258 L 8.583043098449707 16.30232620239258 C 6.644370555877686 16.30232620239258 4.76914644241333 16.2589168548584 3.008409261703491 16.17829704284668 L 2.954504728317261 16.17829704284668 C 2.59911322593689 16.17829704284668 2.260418653488159 16.01753616333008 1.998526573181152 15.728928565979 C 1.709920287132263 15.41027069091797 1.54915976524353 14.98093795776367 1.54915976524353 14.51821517944336 L 1.54915976524353 14.48148250579834 L 1.545820593833923 14.44474983215332 C 1.465201497077942 13.30081844329834 1.428469777107239 12.11682033538818 1.435148358345032 10.93281936645508 L 1.435148358345032 10.92613887786865 C 1.431809067726135 9.74213695526123 1.468540668487549 8.561476707458496 1.545820593833923 7.417546272277832 L 1.54915976524353 7.380814552307129 L 1.54915976524353 7.344083309173584 C 1.54915976524353 6.431992530822754 2.179800033569336 5.690679550170898 2.95450496673584 5.690679550170898 L 3.001254320144653 5.690679550170898 C 4.661336898803711 5.613400459289551 6.452603816986084 5.580007076263428 8.643149375915527 5.580007076263428 L 8.888347625732422 5.580007076263428 M 8.888347625732422 4.5 L 8.284420013427734 4.5 C 6.352427959442139 4.5 4.453827857971191 4.526713848114014 2.595775842666626 4.610672473907471 C 1.227162599563599 4.610672473907471 0.1171016842126846 5.828066349029541 0.1171016842126846 7.337404727935791 C 0.0331442691385746 8.538578987121582 -0.003587436629459262 9.732599258422852 -0.0002481905976310372 10.92995643615723 C -0.003587436629459262 12.12731552124023 0.02980501763522625 13.32133388519287 0.1137632057070732 14.51869201660156 C 0.1137632057070732 16.02803230285645 1.223824024200439 17.25544357299805 2.592437505722046 17.25544357299805 C 4.430453777313232 17.34273910522461 6.305679798126221 17.38281059265137 8.220499038696289 17.38281059265137 L 8.945114135742188 17.38281059265137 C 10.86041164398193 17.38281059265137 12.7351598739624 17.34273910522461 14.57651424407959 17.25544357299805 C 15.94846630096436 17.25544357299805 17.05852890014648 16.02803230285645 17.05852890014648 14.51869201660156 C 17.13914680480957 13.32133293151855 17.17587852478027 12.12397384643555 17.17253875732422 10.92661762237549 C 17.17587852478027 9.72925853729248 17.13914680480957 8.535237312316895 17.05852890014648 7.334542274475098 C 17.05852890014648 5.825202941894531 15.94846630096436 4.614487171173096 14.57651424407959 4.614487171173096 C 12.71846389770508 4.526713848114014 10.82367992401123 4.5 8.888347625732422 4.5 Z">
									</path>
								</svg>
								<svg class="Path_2999" viewBox="14.555 11.067 4.863 6.588">
									<path id="Path_2999" d="M 14.55500030517578 17.65485382080078 L 14.55500030517578 11.06700134277344 L 19.41837120056152 14.36092948913574 L 14.55500030517578 17.65485382080078 Z">
									</path>
								</svg>
							</div>
						</div>
					</div>
					<div id="LinkedIn_is">
						<svg class="Background_it" viewBox="0 0 33.648 33.648">
							<path id="Background_it" d="M 33.64845275878906 16.82416725158691 C 33.64845275878906 26.11598205566406 26.11602973937988 33.64845275878906 16.82422637939453 33.64845275878906 C 7.532426834106445 33.64845275878906 0 26.11598205566406 0 16.82416725158691 C 0 7.532474040985107 7.532426834106445 0 16.82422637939453 0 C 26.11602973937988 0 33.64845275878906 7.532474040985107 33.64845275878906 16.82416725158691 Z">
							</path>
						</svg>
						<svg class="LinkedIn_Icon" viewBox="0 0 14.445 14.445">
							<path id="LinkedIn_Icon" d="M 8.08922004699707 14.44456958770752 L 8.088315963745117 14.44456958770752 L 5.007418155670166 14.44366836547852 C 5.007418155670166 13.87981224060059 5.00810432434082 13.31662464141846 5.008781433105469 12.75387859344482 L 5.009693145751953 12.74841785430908 L 5.008790493011475 12.74841785430908 C 5.012121677398682 10.00170803070068 5.015570640563965 7.161486625671387 4.93510627746582 4.357148170471191 L 7.607409000396729 4.357148170471191 L 7.752032279968262 5.777304649353027 L 7.799989223480225 5.777304649353027 C 8.419408798217773 4.767686367034912 9.600580215454102 4.140225410461426 10.8817720413208 4.140225410461426 C 12.07671928405762 4.140225410461426 12.99795436859131 4.581681728363037 13.61988639831543 5.452325820922852 C 14.15940093994141 6.207594394683838 14.44456958770752 7.260560512542725 14.44456958770752 8.497374534606934 L 14.44456958770752 14.44276428222656 L 11.33920669555664 14.44366836547852 L 11.33920669555664 8.858911514282227 C 11.33920669555664 7.404958724975586 10.81270885467529 6.667745590209961 9.774337768554688 6.667745590209961 C 9.075716972351074 6.667745590209961 8.43704891204834 7.141816139221191 8.185093879699707 7.847417831420898 C 8.08922004699707 8.112274169921875 8.08922004699707 8.381564140319824 8.08922004699707 8.641988754272461 L 8.08922004699707 14.44366836547852 L 8.08922004699707 14.44456958770752 Z M 3.153300285339355 14.44366836547852 L 3.152397632598877 14.44366836547852 L 0.07231222093105316 14.44366836547852 L 0.07231222093105316 4.357148170471191 L 3.153300285339355 4.357148170471191 L 3.153300285339355 14.44276428222656 L 3.153300285339355 14.44366836547852 Z M 1.58924400806427 3.129542827606201 C 0.6683780550956726 3.129542827606201 0 2.471461296081543 0 1.564771413803101 C 0 0.6580813527107239 0.6885370016098022 0 1.637181401252747 0 C 2.571706056594849 0 3.249987363815308 0.6580813527107239 3.249987363815308 1.564771413803101 C 3.249987363815308 2.486085414886475 2.567065954208374 3.129542827606201 1.58924400806427 3.129542827606201 Z">
							</path>
						</svg>
					</div>
					<div id="Twitter">
						<svg class="Background_iw">
							<ellipse id="Background_iw" rx="16.370512008666992" ry="16.370512008666992" cx="16.370512008666992" cy="16.370512008666992">
							</ellipse>
						</svg>
						<svg class="twitter" viewBox="3 6 15.142 12.313">
							<path id="twitter" d="M 7.756545543670654 18.31281280517578 C 13.47005367279053 18.31281280517578 16.59633255004883 13.57393169403076 16.59633255004883 9.47252082824707 C 16.59633255004883 9.339273452758789 16.59633255004883 9.206026077270508 16.59027481079102 9.07277774810791 C 17.19917297363281 8.631513595581055 17.72456169128418 8.085270881652832 18.14180564880371 7.459670543670654 C 17.57373237609863 7.710755825042725 16.97163581848145 7.876419067382812 16.35507392883301 7.951275825500488 C 17.0047550201416 7.562307357788086 17.49115371704102 6.950723648071289 17.72389221191406 6.230155944824219 C 17.11330986022949 6.592475891113281 16.44486427307129 6.846878528594971 15.74788665771484 6.982198715209961 C 15.16143894195557 6.355496406555176 14.34147548675537 5.999881267547607 13.48317718505859 6.000000476837158 C 11.76679992675781 6.001114845275879 10.37567901611328 7.392236232757568 10.37456607818604 9.108613967895508 C 10.37456607818604 9.350882530212402 10.4048490524292 9.587094306945801 10.45330142974854 9.81725025177002 C 7.954358100891113 9.69175910949707 5.626205444335938 8.511171340942383 4.048317432403564 6.569332122802734 C 3.225537061691284 7.990527629852295 3.647087574005127 9.806352615356445 5.011842250823975 10.71970272064209 C 4.519204139709473 10.70256805419922 4.037505626678467 10.56978225708008 3.605672836303711 10.33207130432129 L 3.605672836303711 10.37446784973145 C 3.60714316368103 11.85293769836426 4.647950649261475 13.1265811920166 6.096500396728516 13.42251396179199 C 5.830193996429443 13.4959659576416 5.55509090423584 13.53264617919922 5.278842449188232 13.53153419494629 C 5.07796049118042 13.53153419494629 4.884146213531494 13.5133638381958 4.695882320404053 13.47702407836914 C 5.102002143859863 14.74332046508789 6.268957138061523 15.61077499389648 7.598567485809326 15.63473129272461 C 6.49818229675293 16.49976348876953 5.138607978820801 16.96913146972656 3.738921403884888 16.96721076965332 C 3.490594863891602 16.96721076965332 3.242269515991211 16.9550952911377 3.000000715255737 16.92481422424316 C 4.420939922332764 17.83145713806152 6.071503162384033 18.31288146972656 7.75705099105835 18.31230735778809">
							</path>
						</svg>
					</div>
					<div id="Facebook">
						<svg class="Background_iz">
							<ellipse id="Background_iz" rx="16.370512008666992" ry="16.370512008666992" cx="16.370512008666992" cy="16.370512008666992">
							</ellipse>
						</svg>
						<svg class="Facebook_Icon" viewBox="0 0 6.212 13.459">
							<path id="Facebook_Icon" d="M 5.872567653656006 2.380449295043945 C 5.5331130027771 2.27306056022644 5.142739772796631 2.201467990875244 4.786312103271484 2.201467990875244 C 4.34502124786377 2.201467990875244 3.394547700881958 2.505736112594604 3.394547700881958 3.096373558044434 L 3.394547700881958 4.510324954986572 L 5.651922702789307 4.510324954986572 L 5.651922702789307 6.890774250030518 L 3.394547700881958 6.890774250030518 L 3.394547700881958 13.45938205718994 L 1.120200753211975 13.45938205718994 L 1.120200753211975 6.890774250030518 L 0 6.890774250030518 L 0 4.510324954986572 L 1.120200753211975 4.510324954986572 L 1.120200753211975 3.311150789260864 C 1.120200753211975 1.503441572189331 1.900946855545044 0 3.784920930862427 0 C 4.429884910583496 0 5.584031581878662 0.03579622879624367 6.21202278137207 0.2684716880321503 L 5.872567653656006 2.380449295043945 Z">
							</path>
						</svg>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div id="Component_119__5" class="Component_119___5">
		<div id="eye">
			<svg class="Path_2946" viewBox="0 5.625 17.749 12.984">
				<path id="Path_2946" d="M 17.74861526489258 12.11697483062744 C 17.74861526489258 12.11697483062744 14.42074966430664 5.625 8.874307632446289 5.625 C 3.327865362167358 5.625 0 12.11697483062744 0 12.11697483062744 C 0 12.11697483062744 3.327865362167358 18.60894775390625 8.874307632446289 18.60894775390625 C 14.42074966430664 18.60894775390625 17.74861526489258 12.11697483062744 17.74861526489258 12.11697483062744 Z M 1.301072120666504 12.11697483062744 C 1.837431192398071 12.98443508148193 2.454334259033203 13.79236030578613 3.142492055892944 14.5285816192627 C 4.570269107818604 16.0499267578125 6.522616386413574 17.42858695983887 8.874307632446289 17.42858695983887 C 11.225998878479 17.42858695983887 13.1773624420166 16.0499267578125 14.60711097717285 14.52857780456543 C 15.29526710510254 13.79235744476318 15.91217231750488 12.98443412780762 16.44852828979492 12.11697578430176 C 15.91217994689941 11.24951171875 15.29527759552002 10.44158554077148 14.60711097717285 9.70536994934082 C 13.1773624420166 8.184019088745117 11.22501277923584 6.805358409881592 8.874307632446289 6.805358409881592 C 6.522616386413574 6.805358409881592 4.571254730224609 8.184019088745117 3.141504764556885 9.70536994934082 C 2.453338623046875 10.44158458709717 1.836434245109558 11.24950885772705 1.300086617469788 12.11697292327881 Z">
				</path>
			</svg>
			<svg class="Path_2947" viewBox="10.125 10.125 7.765 8.263">
				<path id="Path_2947" d="M 14.00750923156738 11.30535888671875 C 12.47590255737305 11.30535984039307 11.23428726196289 12.62652111053467 11.23428726196289 14.25625705718994 C 11.23428726196289 15.8859920501709 12.47590255737305 17.2071533203125 14.00750923156738 17.2071533203125 C 15.53911781311035 17.2071533203125 16.78073120117188 15.8859920501709 16.78073120117188 14.25625705718994 C 16.78073120117188 12.62652111053467 15.53911781311035 11.30535984039307 14.00750923156738 11.30535984039307 Z M 10.125 14.25625705718994 C 10.125 11.97462558746338 11.86326026916504 10.125 14.00750923156738 10.125 C 16.15176200866699 10.125 17.89001846313477 11.9746265411377 17.89001846313477 14.25625705718994 C 17.89001846313477 16.53788757324219 16.15176010131836 18.38751411437988 14.00750923156738 18.38751411437988 C 11.86325836181641 18.38751411437988 10.125 16.53788566589355 10.125 14.25625705718994 Z">
				</path>
			</svg>
		</div>
		<div id="eye-slash">
			<svg class="Path_2948" viewBox="11.723 5.625 11.969 10.314">
				<path id="Path_2948" d="M 20.76247024536133 15.93871307373047 C 22.64924430847168 14.14719200134277 23.69197654724121 12.11697483062744 23.69197654724121 12.11697483062744 C 23.69197654724121 12.11697483062744 20.36411094665527 5.625 14.81766700744629 5.625 C 13.75238418579102 5.6289381980896 12.69915962219238 5.865147590637207 11.72299957275391 6.319048881530762 L 12.576904296875 7.229238510131836 C 13.29508972167969 6.950810432434082 14.05330467224121 6.807381153106689 14.81766700744629 6.805357933044434 C 17.16936111450195 6.805358409881592 19.12072372436523 8.184019088745117 20.55047225952148 9.70536994934082 C 21.23862838745117 10.44158840179443 21.85553359985352 11.24951267242432 22.39188957214355 12.11697292327881 C 22.32730484008789 12.2197961807251 22.25680351257324 12.33311176300049 22.17545700073242 12.45691776275635 C 21.80372047424316 13.0234899520874 21.2549934387207 13.7789192199707 20.55047225952148 14.52857780456543 C 20.36756324768066 14.72320747375488 20.17676544189453 14.91573715209961 19.97709274291992 15.10197067260742 L 20.76247024536133 15.93871307373047 Z">
				</path>
			</svg>
			<svg class="Path_2949" viewBox="10.118 10.119 7.77 8.267">
				<path id="Path_2949" d="M 17.66106224060059 15.6414155960083 C 18.16654205322266 14.13752365112305 17.81205177307129 12.45831871032715 16.75067710876465 11.32894134521484 C 15.68929958343506 10.19956302642822 14.1112003326416 9.822361946105957 12.69786071777344 10.36022472381592 L 13.61092758178711 11.33179187774658 C 14.47523021697998 11.2002067565918 15.34730434417725 11.50940895080566 15.96468162536621 12.16634082794189 C 16.58206176757812 12.82327461242676 16.87264633178711 13.751220703125 16.74898338317871 14.6708984375 L 17.66057014465332 15.64089107513428 Z M 14.3963041305542 17.17483329772949 L 15.30789279937744 18.14482307434082 C 13.89455223083496 18.68268966674805 12.31645393371582 18.30548477172852 11.25507831573486 17.17610740661621 C 10.19370174407959 16.04673194885254 9.839212417602539 14.36752700805664 10.34468936920166 12.8636360168457 L 11.25775814056396 13.83520221710205 C 11.13409423828125 14.75487899780273 11.42467975616455 15.68282318115234 12.04205703735352 16.33975601196289 C 12.65943431854248 16.99669075012207 13.53150749206543 17.30589294433594 14.39580917358398 17.17430686950684 Z">
				</path>
			</svg>
			<svg class="Path_2950" viewBox="0 10.715 11.969 10.314">
				<path id="Path_2950" d="M 3.716362953186035 11.55016994476318 C 3.516690969467163 11.73902702331543 3.324907064437866 11.93050765991211 3.141997814178467 12.12513542175293 C 2.453673124313354 12.86132907867432 1.836601853370667 13.66925525665283 1.30008852481842 14.53673553466797 L 1.516520738601685 14.87668418884277 C 1.888255596160889 15.44325542449951 2.436983585357666 16.19868659973145 3.141505002975464 16.94834518432617 C 4.571254730224609 18.46969604492188 6.523602962493896 19.84835624694824 8.874307632446289 19.84835624694824 C 9.668558120727539 19.84835624694824 10.41597270965576 19.69149780273438 11.11507034301758 19.42342567443848 L 11.96897602081299 20.33466148376465 C 10.99280738830566 20.78853607177734 9.939589500427246 21.02474403381348 8.874306678771973 21.02871322631836 C 3.327865362167358 21.02871513366699 0 14.53674030303955 0 14.53674030303955 C 0 14.53674030303955 1.041745066642761 12.50547409057617 2.929507732391357 10.71500015258789 L 3.714883804321289 11.55174350738525 Z">
				</path>
			</svg>
			<svg class="Path_2951" viewBox="3.704 3.703 14.097 15">
				<path id="Path_2951" d="M 17.01546287536621 18.70300102233887 L 3.704000234603882 4.538695335388184 L 4.489377021789551 3.703001260757446 L 17.80084037780762 17.86730766296387 L 17.01546287536621 18.70300102233887 Z">
				</path>
			</svg>
		</div>
	</div>
</div>
</body>
</html>