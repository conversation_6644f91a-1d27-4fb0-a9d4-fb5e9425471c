<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="content-type">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1" name="viewport">
    <link rel="icon" href="/images/cropped-ikon-192x192.png" sizes="192x192" />

    <title inertia>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">
    <style>
        @font-face {
            font-family: kiralabunufont;
            src: url("/kiralabunu-font.otf");
        }

        @font-face {
            font-family: kiralabunuthin;
            src: url("/santral-thin.otf");
        }

        @font-face {
            font-family: santralextrabold;
            src: url("/santral-extrabold.otf");
        }

        @font-face {
            font-family: santralregular;
            src: url("/santral-regular.otf");
        }

        * {
            font-family: kiralabunufont;
        }

        .short-description {
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box !important;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            white-space: normal;
        }

        .font-santralregular p {
            font-family: santralregular !important;
            font-size: 14px;
        }

        .font-santralregular.font-light * {
            font-family: santralregular !important;
            font-size: 14px;
        }

        .splide__track--nav > .splide__list > .splide__slide {
            opacity: 0.5;
        }

        .splide__track--nav > .splide__list > .splide__slide.is-active {
            border: 0 !important;
            opacity: 1;
        }

        .splide__pagination__page.is-active {
            background: #5f4af4 !important;
            opacity: 1 !important;
        }

        .splide__pagination__page {
            background: #70d44b !important;
        }

        .product-description {
            font-family: santralregular;
        }

        .product-description ul li {
            font-family: santralregular;
            list-style: disc;
            font-size: 14px;
            list-style: inside;
            padding-left: 5px;
        }
    </style>
    <!-- Scripts -->
    @routes
    @vite(['resources/js/app.js', "resources/js/Pages/{$page['component']}.vue"])
    @inertiaHead

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-184830299-2"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }

        gtag("js", new Date());
        gtag("config", "UA-184830299-2");
    </script>

    <!-- Google Tag Manager -->
    <script>
        (function(w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                "gtm.start": new Date().getTime(),
                event: "gtm.js"
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != "dataLayer" ? "&l=" + l : "";
            j.async = true;
            j.src =
                "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, "script", "dataLayer", "GTM-K6P3G7L");
    </script>
    <!-- End Google Tag Manager -->

    {{--    <!-- Google Tag Manager -->--}}
    {{--    <script>--}}
    {{--        (function(w, d, s, l, i) {--}}
    {{--            w[l] = w[l] || [];--}}
    {{--            w[l].push({--}}
    {{--                "gtm.start": new Date().getTime(),--}}
    {{--                event: "gtm.js"--}}
    {{--            });--}}
    {{--            var f = d.getElementsByTagName(s)[0],--}}
    {{--                j = d.createElement(s),--}}
    {{--                dl = l != "dataLayer" ? "&l=" + l : "";--}}
    {{--            j.async = true;--}}
    {{--            j.src =--}}
    {{--                "https://www.googletagmanager.com/gtm.js?id=" + i + dl;--}}
    {{--            f.parentNode.insertBefore(j, f);--}}
    {{--        })(window, document, "script", "dataLayer", "GTM-W692PGL");--}}
    {{--    </script>--}}
    {{--    <!-- End Google Tag Manager -->--}}

    <!-- Google Tag Manager (noscript) -->
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-W692PGL" height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    <!-- End Google Tag Manager (noscript) -->

    <!-- Google tag (gtag.js) LAST ONE -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=**********"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }

        gtag("js", new Date());
        gtag("config", "**********");
    </script>
    <!-- End of global snippet: Please do not remove -->
    <script>
        gtag("event", "conversion", {
            "allow_custom_scripts": true,
            "send_to": "**********/invmedia/kiral0+standard"
        });
    </script>
    <noscript>
        <img src="https://ad.doubleclick.net/ddm/activity/src=8726756;type=invmedia;cat=kiral0;dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;tfua=;npa=;gdpr=${GDPR};gdpr_consent=${GDPR_CONSENT_755};ord=1?" width="1" height="1" alt="" />
    </noscript>
    <!-- End of event snippet: Please do not remove -->
    <!-- Twitter conversion tracking base code -->
    <script>
        !function(e, t, n, s, u, a) {
            e.twq || (s = e.twq = function() {
                s.exe ? s.exe.apply(s, arguments) : s.queue.push(arguments);
            }, s.version = "1.1", s.queue = [], u = t.createElement(n), u.async = !0, u.src = "https://static.ads-twitter.com/uwt.js",
                a = t.getElementsByTagName(n)[0], a.parentNode.insertBefore(u, a));
        }(window, document, "script");
        twq("config", "os8n7");
    </script>
    <!-- End Twitter conversion tracking base code -->
</head>

<body class="font-sans antialiased w-full" style="margin-bottom: 0!important;">
<!-- Google Tag Manager (noscript) -->
<noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-K6P3G7L"
            height="0" width="0" style="display:none;visibility:hidden"></iframe>
</noscript>
<!-- End Google Tag Manager (noscript) -->
@inertia
</body>
<style>
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }

    ul.list-outside {
        list-style-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='10' height='10' viewBox='-1 -1 2 2'><circle r='1' /></svg>");
    }

    @media screen and (min-width: 750px) {

        /* width */
        ::-webkit-scrollbar {
            width: 10px;
            height: 5px;
        }

        /* Track */
        ::-webkit-scrollbar-track {
            box-shadow: inset 0 0 10px rgb(235, 235, 235);
            border-radius: 10px;
        }

        /* Handle */
        ::-webkit-scrollbar-thumb {
            background: #9B9B9B;
            border-radius: 10px;
        }

        /* Handle on hover */
        ::-webkit-scrollbar-thumb:hover {
            background: rgb(112, 212, 75);
        }
    }

    .sliderclass .splide__arrow--prev svg {
        transform: scaleX(-1) !important;
    }

    .splide__arrow--prev svg {
        transform: scaleX(1) !important;
    }

    .splide__arrow {
        opacity: 1 !important;
        background: #ffffff;
        visibility: visible;
    }

    .cls-1 {
        stroke: #67cf61;
    }

    .cls-1,
    .cls-2 {
        fill: #fff;
    }

    .cls-1,
    .cls-2,
    .cls-3 {
        stroke-miterlimit: 10;
        stroke-width: 2px;
    }

    .cls-2,
    .cls-3 {
        stroke: #000;
    }

    .cls-3 {
        fill: #67cf61;
    }

    .cls-4 {
        fill: none;
        fill-rule: evenodd;
        stroke: #fff;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 4px;
    }

    .cls-1re {
        fill: #70d44b;
    }

    .cls-2re {
        fill: #fff;
        stroke: #000;
        stroke-miterlimit: 10;
        stroke-width: 3px;
    }

    .cls-1we {
        fill: #fff;
    }

    .cls-1we,
    .cls-2we {
        stroke-width: 2px;
    }

    .cls-1we,
    .cls-2we,
    .cls-3we {
        stroke-miterlimit: 10;
    }

    .cls-1we,
    .cls-3we {
        stroke: #000;
    }

    .cls-2we {
        stroke: #67cf61;
    }

    .cls-2we,
    .cls-3we {
        fill: none;
    }

    .cls-3we {
        fill-rule: evenodd;
        stroke-linecap: round;
        stroke-width: 4px;
    }

    .cls-1tr {
        fill: #70d44b;
    }

    .cls-2tr {
        fill: #fff;
        stroke: #000;
        stroke-miterlimit: 10;
        stroke-width: 3px;
    }

    .cls-1eq,
    .cls-2eq,
    .cls-3eq,
    .cls-4eq {
        stroke-miterlimit: 10;
    }

    .cls-1eq,
    .cls-2eq,
    .cls-4eq {
        stroke: #000;
        stroke-width: 5px;
    }

    .cls-1eq,
    .cls-5eq {
        fill: #fff;
    }

    .cls-6eq {
        fill: #73cc4a;
    }

    .cls-2eq,
    .cls-3eq,
    .cls-4eq {
        fill: none;
    }

    .cls-3eq {
        stroke: #70d44b;
        stroke-width: 4px;
    }

    .cls-5eq,
    .cls-4eq {
        fill-rule: evenodd;
    }

    .kiralasteps .splide__list {
        /*padding-left: 10px !important;*/
        /*padding-right: 10px !important;*/
        width: calc(100% - 40px);
    }

    @media screen and (max-width: 750px) {
        .kiralasteps .splide__list {
            width: calc(100% - 5px);
        }
    }
</style>
<script>
    function lettersOnly(input) {
        var regex = /[0-9]/g;
        input.value = input.value.replace(regex, "");
    }

    function lettersOnly2(input) {
        var regex = /[0-9]/g;
        input.value = input.value.replace(regex, "");
    }

    function validatename() {
        if (document.RegisterForm.name.value == "") {
            alert("Ad alanı boş bırakılamaz");
            document.RegisterForm.name.focus();
            return false;
        }
        if (!/^[a-zA-Z]*$/g.test(document.RegisterForm.name.value)) {
            alert("Ad alanı yanlızca harften oluşmalıdır");
            document.RegisterForm.name.focus();
            return false;
        }
    }

    function validatesurname() {
        if (document.RegisterForm.surname.value == "") {
            alert("Soyad alanı boş bırakılamaz");
            document.RegisterForm.surname.focus();
            return false;
        }
        if (!/^[a-zA-Z]*$/g.test(document.RegisterForm.surname.value)) {
            alert("Soyad Alanı yanlızca harften oluşmalıdır");
            document.RegisterForm.surname.focus();
            return false;
        }
    }

    function DateDisabled() {
        document.getElementById("date_of_birth").disabled = true;
    }
</script>
<script src="https://cdn.websitepolicies.io/lib/cconsent/cconsent.min.js" defer></script>
<script>
    window.addEventListener("load", function() {
        window.wpcb.init({
            "border": "thin",
            "corners": "small",
            "colors": {
                "popup": {
                    "background": "#e2f7d4",
                    "text": "#000000",
                    "border": "#7fc25e"
                },
                "button": {
                    "background": "#7fc25e",
                    "text": "#ffffff"
                }
            },
            "position": "bottom",
            "transparency": "25",
            "fontsize": "small",
            "margin": "small",
            "content": {
                "href": "/cerez-politikasi",
                "message": "Size daha iyi bir hizmet sunabilmek için web sitemizde çerezlerinizi kullanıyoruz. Detaylı bilgi için çerez politikamızı inceleyebilirsiniz.  ",
                "link": "Çerez Politikası",
                "button": "Anladım"
            }
        });
    });
</script>

</html>
