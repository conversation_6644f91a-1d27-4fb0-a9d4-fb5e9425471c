<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Secure Doğrulaması Tamamlandı</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 400px;
            width: 100%;
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: bounce 2s infinite;
        }
        
        .success-icon {
            color: #4ade80;
        }
        
        .error-icon {
            color: #f87171;
        }
        
        h1 {
            margin: 0 0 1rem 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        p {
            margin: 0 0 1.5rem 0;
            opacity: 0.9;
            line-height: 1.5;
        }
        
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .redirect-info {
            font-size: 0.875rem;
            opacity: 0.7;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        @if($success)
            <div class="icon success-icon">✅</div>
            <h1>Ödeme Başarılı!</h1>
            <p>{{ $message }}</p>
        @else
            <div class="icon error-icon">❌</div>
            <h1>Ödeme Başarısız</h1>
            <p>{{ $message }}</p>
        @endif
        
        <div class="spinner"></div>
        <div class="redirect-info">
            Sayfaya yönlendiriliyorsunuz...
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const success = @json($success);
            const token = @json($token);
            
            console.log('3DS Callback page loaded', {
                success: success,
                token: token,
                timestamp: new Date().toISOString()
            });
            
            // Send message to parent window to close modal and handle result
            if (window.parent && window.parent !== window) {
                try {
                    if (success) {
                        console.log('Sending success message to parent window');
                        window.parent.postMessage('3ds_payment_success', '*');
                        
                        // Also try the object format for compatibility
                        window.parent.postMessage({
                            type: '3ds_complete',
                            status: 'success',
                            token: token
                        }, '*');
                        
                        // Redirect parent window to success page after a short delay
                        setTimeout(() => {
                            console.log('Redirecting parent to success page');
                            window.parent.location.href = '/payment/' + token + '/success';
                        }, 2000);
                        
                    } else {
                        console.log('Sending failure message to parent window');
                        window.parent.postMessage('3ds_payment_failed', '*');
                        
                        // Also try the object format for compatibility
                        window.parent.postMessage({
                            type: '3ds_error',
                            status: 'failed',
                            token: token
                        }, '*');
                        
                        // For failures, just close modal and stay on payment page
                        setTimeout(() => {
                            console.log('Closing modal for failed payment');
                            // The parent window's message handler will close the modal
                        }, 2000);
                    }
                } catch (error) {
                    console.error('Error sending message to parent:', error);
                    
                    // Fallback: direct redirect if messaging fails
                    if (success) {
                        window.location.href = '/payment/' + token + '/success';
                    } else {
                        window.location.href = '/payment/' + token;
                    }
                }
            } else {
                console.log('No parent window detected, doing direct redirect');
                
                // Direct redirect if not in iframe
                if (success) {
                    window.location.href = '/payment/' + token + '/success';
                } else {
                    window.location.href = '/payment/' + token;
                }
            }
        });
    </script>
</body>
</html> 