# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Laravel 9 + Vue 3 + Inertia.js e-commerce/rental platform frontend for Kiralabunu, a technology product rental service in Turkey. The application handles product listings, rentals, payments, user management, and institutional services.

## Key Commands

### Development
```bash
# Start development server
npm run dev

# Start Laravel with Octane
php artisan octane:start

# Run migrations
php artisan migrate

# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

### Build & Deploy
```bash
# Build frontend assets
npm run build

# Build with Vite
vite build
```

### Testing
```bash
# Run PHPUnit tests
php artisan test
# or
./vendor/bin/phpunit

# Run specific test
php artisan test --filter PaymentControllerTest
```

### Code Quality
```bash
# Format PHP code with Laravel Pint
./vendor/bin/pint

# Debug with <PERSON>
ray()->showQueries()  # Show database queries
ray($variable)        # Debug variable
```

## Architecture & Structure

### Tech Stack
- **Backend**: Laravel 9 with Octane for performance
- **Frontend**: Vue 3 with Composition API
- **Routing**: Inertia.js for SPA-like experience
- **State Management**: Session-based with Laravel + Vue reactivity
- **Payment**: Custom payment service with multiple providers (Iyzico, Tosla, PGS)
- **Caching**: Redis with tagged caching strategy

### Core Patterns

#### 1. Inertia Page Components
All pages are in `resources/js/Pages/` and follow Inertia conventions:
- Props are passed from Laravel controllers
- Navigation uses Inertia links
- Forms use Inertia form helpers

#### 2. API Integration
- External product API: `config('app.product_api_url')`
- Internal API: `config('app.api_url')`
- Caching strategy uses tags: `anasayfa`, `kategori`, `arama`, `tags`

#### 3. Authentication Flow
- Session-based with cookie backup
- Token stored in `session('user.token')`
- Middleware: `checkCookieAuth` validates user session

#### 4. Payment System
- Multi-provider support via PaymentServiceInterface
- 3D Secure handling for credit cards
- SMS payment links with token-based URLs
- Saved cards functionality

### Key Business Logic

#### Product Types
1. **Regular Rentals**: Standard rental products
2. **Installment Products**: "Kirala Satın Al" products with special pricing
3. **Institutional**: B2B rental solutions
4. **KiralaMotor**: Motorcycle/scooter rentals

#### Critical Routes
- `/odeme` - Payment processing page
- `/sepetim` - Shopping cart
- `/kiralamalar` - User's active rentals
- `/kirala-satin-al` - Rent-to-own products
- `/payment/{token}` - SMS payment links

#### Cache Management
Uses Laravel's tagged caching:
```php
Cache::tags(['anasayfa'])->remember(...)  // Homepage cache
Cache::tags(['kategori'])->remember(...)  // Category cache
Cache::tags(['http'])->forget(...)        // HTTP response cache
```

#### Payment Token System
- 32-character alphanumeric tokens for SMS payment links
- Token validation in PaymentController
- Secure payment processing with CSRF protection

### Important Considerations

1. **Session Management**: User authentication relies heavily on session. Always check `session('user.isUserLoggedIn')` before user-specific operations.

2. **API Rate Limiting**: External API calls are cached aggressively (24 hours default) to avoid rate limits.

3. **Payment Security**: 
   - Never log sensitive payment data
   - Use payment service abstraction
   - Implement proper 3D Secure flow

4. **Multi-tenancy**: The app supports multiple integrations (Hopi, Pegasus, affiliates) tracked via session and cookies.

5. **Error Handling**: Bugsnag is configured for production error tracking. Use proper try-catch blocks for external API calls.

## Common Development Tasks

### Adding a New Product Category
1. Add route in `routes/web.php`
2. Create controller method to fetch from API
3. Implement caching with appropriate tags
4. Create Vue component in `resources/js/Pages/`

### Implementing Payment Features
1. Always use PaymentServiceInterface
2. Handle both 3D and non-3D payment flows
3. Implement proper error handling for payment failures
4. Log payment attempts (without sensitive data)

### Working with Inertia
1. Pass data from controller: `Inertia::render('PageName', ['data' => $data])`
2. Access in Vue: `defineProps(['data'])`
3. Navigate: `Inertia.visit('/path')`
4. Forms: Use `useForm()` helper

### Cache Invalidation
Clear specific cache tags when data changes:
```php
Cache::tags(['kategori'])->flush();  // Clear all category cache
Cache::forget('specific_key');        // Clear specific key
```