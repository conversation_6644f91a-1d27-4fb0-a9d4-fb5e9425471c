<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PaymentControllerTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test payment show page with valid token
     */
    public function test_payment_show_with_valid_token(): void
    {
        $response = $this->get('/payment/validtoken123456789012345678901234');

        $response->assertStatus(200);
        $response->assertInertia(
            fn($page) =>
            $page->component('Payment/Show')
                ->has('payment_data')
                ->where('error_type', null)
        );
    }

    /**
     * Test payment show page with invalid token
     */
    public function test_payment_show_with_invalid_token(): void
    {
        $response = $this->get('/payment/invalidtoken12345678901234567890');

        $response->assertStatus(200);
        $response->assertInertia(
            fn($page) =>
            $page->component('Payment/Show')
                ->where('error_type', 'invalid_token')
                ->has('error_data')
        );
    }

    /**
     * Test payment show page with expired token
     */
    public function test_payment_show_with_expired_token(): void
    {
        $response = $this->get('/payment/expiredtoken1234567890123456789012');

        $response->assertStatus(200);
        $response->assertInertia(
            fn($page) =>
            $page->component('Payment/Show')
                ->where('error_type', 'expired')
                ->has('error_data')
        );
    }

    /**
     * Test payment show page with already paid token
     */
    public function test_payment_show_with_already_paid_token(): void
    {
        $response = $this->get('/payment/paidtoken123456789012345678901234');

        $response->assertStatus(200);
        $response->assertInertia(
            fn($page) =>
            $page->component('Payment/Show')
                ->where('error_type', 'already_paid')
                ->has('error_data')
        );
    }

    /**
     * Test payment processing with valid data
     */
    public function test_payment_process_with_valid_data(): void
    {
        $paymentData = [
            'cardholder_name' => 'John Doe',
            'card_number' => '****************', // Valid test card
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'cvv' => '123',
        ];

        $response = $this->post('/payment/validtoken123456789012345678901234/process', $paymentData);

        $response->assertRedirect('/payment/validtoken123456789012345678901234/success');
    }

    /**
     * Test payment processing with invalid card data
     */
    public function test_payment_process_with_invalid_card_data(): void
    {
        $paymentData = [
            'cardholder_name' => '',
            'card_number' => '1234', // Invalid card
            'expiry_month' => '13', // Invalid month
            'expiry_year' => '2020', // Past year
            'cvv' => '12', // Invalid CVV
        ];

        $response = $this->post('/payment/validtoken123456789012345678901234/process', $paymentData);

        $response->assertSessionHasErrors([
            'cardholder_name',
            'card_number',
            'expiry_month',
            'expiry_year',
            'cvv'
        ]);
    }

    /**
     * Test payment processing with insufficient funds scenario
     */
    public function test_payment_process_with_insufficient_funds(): void
    {
        $paymentData = [
            'cardholder_name' => 'John Doe',
            'card_number' => '****************', // Triggers insufficient funds
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'cvv' => '123',
        ];

        $response = $this->post('/payment/validtoken123456789012345678901234/process', $paymentData);

        $response->assertSessionHasErrors(['payment_error']);
    }

    /**
     * Test payment processing with 3D Secure scenario
     */
    public function test_payment_process_with_3d_secure(): void
    {
        $paymentData = [
            'cardholder_name' => 'John Doe',
            'card_number' => '****************', // Triggers 3D secure
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'cvv' => '123',
        ];

        $response = $this->post('/payment/validtoken123456789012345678901234/process', $paymentData);

        $response->assertSessionHasErrors(['payment_error']);
    }

    /**
     * Test payment success page with valid paid token
     */
    public function test_payment_success_with_valid_paid_token(): void
    {
        $response = $this->get('/payment/paidtoken123456789012345678901234/success');

        $response->assertStatus(200);
        $response->assertInertia(
            fn($page) =>
            $page->component('Payment/Success')
                ->has('payment_data')
                ->has('transaction_data')
        );
    }

    /**
     * Test payment success page with non-paid token redirects to form
     */
    public function test_payment_success_with_non_paid_token_redirects(): void
    {
        $response = $this->get('/payment/validtoken123456789012345678901234/success');

        $response->assertRedirect('/payment/validtoken123456789012345678901234');
    }

    /**
     * Test rate limiting on payment processing
     */
    public function test_payment_processing_rate_limiting(): void
    {
        $paymentData = [
            'cardholder_name' => 'John Doe',
            'card_number' => '****************',
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'cvv' => '123',
        ];

        $token = 'validtoken123456789012345678901234';

        // Make multiple requests quickly to trigger rate limiting
        for ($i = 0; $i < 6; $i++) {
            $response = $this->post("/payment/{$token}/process", $paymentData);
        }

        // Last request should be rate limited
        $response->assertStatus(429);
    }

    /**
     * Test invalid token format
     */
    public function test_invalid_token_format(): void
    {
        // Too short token
        $response = $this->get('/payment/short');
        $response->assertStatus(404);

        // Token with invalid characters
        $response = $this->get('/payment/invalid-token-with-dashes-12345');
        $response->assertStatus(404);
    }

    /**
     * Test CSRF protection on payment processing
     */
    public function test_payment_processing_requires_csrf_token(): void
    {
        $paymentData = [
            'cardholder_name' => 'John Doe',
            'card_number' => '****************',
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'cvv' => '123',
        ];

        // Without CSRF token
        $response = $this->withoutMiddleware(\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken::class)
            ->post('/payment/validtoken123456789012345678901234/process', $paymentData);

        // With CSRF protection enabled, this should work
        $response = $this->post('/payment/validtoken123456789012345678901234/process', $paymentData);
        $response->assertRedirect(); // Should redirect (success or back with errors)
    }
}
