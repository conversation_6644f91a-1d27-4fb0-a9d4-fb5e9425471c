<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Services\MockPaymentService;
use App\DTOs\PaymentTokenResult;
use App\DTOs\PaymentResult;

class MockPaymentServiceTest extends TestCase
{
    private MockPaymentService $paymentService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->paymentService = new MockPaymentService();
    }

    /**
     * Test token validation with valid token
     */
    public function test_validate_token_with_valid_token(): void
    {
        $result = $this->paymentService->validateToken('validtoken123456789012345678901234');

        $this->assertInstanceOf(PaymentTokenResult::class, $result);
        $this->assertTrue($result->isValid);
        $this->assertEquals('pending', $result->status);
        $this->assertEquals(150.00, $result->amount);
        $this->assertEquals('Test Payment', $result->description);
        $this->assertNotNull($result->expiresAt);
        $this->assertNull($result->errorMessage);
    }

    /**
     * Test token validation with invalid token
     */
    public function test_validate_token_with_invalid_token(): void
    {
        $result = $this->paymentService->validateToken('invalidtoken12345678901234567890');

        $this->assertInstanceOf(PaymentTokenResult::class, $result);
        $this->assertFalse($result->isValid);
        $this->assertNull($result->status);
        $this->assertNull($result->amount);
        $this->assertNull($result->description);
        $this->assertNull($result->expiresAt);
        $this->assertEquals('Geçersiz ödeme linki', $result->errorMessage);
    }

    /**
     * Test token validation with expired token
     */
    public function test_validate_token_with_expired_token(): void
    {
        $result = $this->paymentService->validateToken('expiredtoken1234567890123456789012');

        $this->assertInstanceOf(PaymentTokenResult::class, $result);
        $this->assertFalse($result->isValid);
        $this->assertEquals('expired', $result->status);
        $this->assertEquals(200.00, $result->amount);
        $this->assertEquals('Expired Payment', $result->description);
        $this->assertNotNull($result->expiresAt);
        $this->assertEquals('Bu ödeme linkinin süresi dolmuş', $result->errorMessage);
    }

    /**
     * Test token validation with already paid token
     */
    public function test_validate_token_with_already_paid_token(): void
    {
        $result = $this->paymentService->validateToken('paidtoken123456789012345678901234');

        $this->assertInstanceOf(PaymentTokenResult::class, $result);
        $this->assertFalse($result->isValid);
        $this->assertEquals('paid', $result->status);
        $this->assertEquals(75.50, $result->amount);
        $this->assertEquals('Completed Payment', $result->description);
        $this->assertNotNull($result->expiresAt);
        $this->assertEquals('Bu ödeme daha önce tamamlanmış', $result->errorMessage);
    }

    /**
     * Test payment processing success scenario
     */
    public function test_process_payment_success(): void
    {
        $cardData = [
            'cardholder_name' => 'John Doe',
            'card_number' => '****************',
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'cvv' => '123',
        ];

        $result = $this->paymentService->processPayment('validtoken123456789012345678901234', $cardData);

        $this->assertInstanceOf(PaymentResult::class, $result);
        $this->assertTrue($result->success);
        $this->assertNotNull($result->transactionId);
        $this->assertStringStartsWith('txn_', $result->transactionId);
        $this->assertNull($result->errorMessage);
        $this->assertNull($result->errorCode);
    }

    /**
     * Test payment processing with insufficient funds
     */
    public function test_process_payment_insufficient_funds(): void
    {
        $cardData = [
            'cardholder_name' => 'John Doe',
            'card_number' => '****************', // Triggers insufficient funds
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'cvv' => '123',
        ];

        $result = $this->paymentService->processPayment('validtoken123456789012345678901234', $cardData);

        $this->assertInstanceOf(PaymentResult::class, $result);
        $this->assertFalse($result->success);
        $this->assertNull($result->transactionId);
        $this->assertEquals('Yetersiz bakiye', $result->errorMessage);
        $this->assertEquals('INSUFFICIENT_FUNDS', $result->errorCode);
    }

    /**
     * Test payment processing with 3D Secure requirement
     */
    public function test_process_payment_3d_secure(): void
    {
        $cardData = [
            'cardholder_name' => 'John Doe',
            'card_number' => '****************', // Triggers 3D secure
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'cvv' => '123',
        ];

        $result = $this->paymentService->processPayment('validtoken123456789012345678901234', $cardData);

        $this->assertInstanceOf(PaymentResult::class, $result);
        $this->assertFalse($result->success);
        $this->assertNull($result->transactionId);
        $this->assertEquals('3D Secure doğrulama gerekli', $result->errorMessage);
        $this->assertEquals('3D_SECURE_REQUIRED', $result->errorCode);
    }

    /**
     * Test payment processing with fraud detection
     */
    public function test_process_payment_fraud_detection(): void
    {
        $cardData = [
            'cardholder_name' => 'John Doe',
            'card_number' => '****************', // Triggers fraud detection
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'cvv' => '123',
        ];

        $result = $this->paymentService->processPayment('validtoken123456789012345678901234', $cardData);

        $this->assertInstanceOf(PaymentResult::class, $result);
        $this->assertFalse($result->success);
        $this->assertNull($result->transactionId);
        $this->assertEquals('Güvenlik kontrolü başarısız', $result->errorMessage);
        $this->assertEquals('FRAUD_DETECTED', $result->errorCode);
    }

    /**
     * Test payment processing with invalid token
     */
    public function test_process_payment_with_invalid_token(): void
    {
        $cardData = [
            'cardholder_name' => 'John Doe',
            'card_number' => '****************',
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'cvv' => '123',
        ];

        $result = $this->paymentService->processPayment('invalidtoken12345678901234567890', $cardData);

        $this->assertInstanceOf(PaymentResult::class, $result);
        $this->assertFalse($result->success);
        $this->assertNull($result->transactionId);
        $this->assertEquals('Geçersiz ödeme linki', $result->errorMessage);
        $this->assertEquals('INVALID_TOKEN', $result->errorCode);
    }

    /**
     * Test payment status retrieval for valid token
     */
    public function test_get_payment_status_valid_token(): void
    {
        $status = $this->paymentService->getPaymentStatus('validtoken123456789012345678901234');

        $this->assertEquals('pending', $status);
    }

    /**
     * Test payment status retrieval for paid token
     */
    public function test_get_payment_status_paid_token(): void
    {
        $status = $this->paymentService->getPaymentStatus('paidtoken123456789012345678901234');

        $this->assertEquals('paid', $status);
    }

    /**
     * Test payment status retrieval for expired token
     */
    public function test_get_payment_status_expired_token(): void
    {
        $status = $this->paymentService->getPaymentStatus('expiredtoken1234567890123456789012');

        $this->assertEquals('expired', $status);
    }

    /**
     * Test payment status retrieval for invalid token
     */
    public function test_get_payment_status_invalid_token(): void
    {
        $status = $this->paymentService->getPaymentStatus('invalidtoken12345678901234567890');

        $this->assertNull($status);
    }

    /**
     * Test token scenarios edge cases
     */
    public function test_token_scenarios_edge_cases(): void
    {
        // Test empty token
        $result = $this->paymentService->validateToken('');
        $this->assertFalse($result->isValid);

        // Test too short token
        $result = $this->paymentService->validateToken('short');
        $this->assertFalse($result->isValid);

        // Test too long token
        $result = $this->paymentService->validateToken('verylongtoken123456789012345678901234567890');
        $this->assertFalse($result->isValid);
    }

    /**
     * Test PaymentTokenResult DTO helper methods
     */
    public function test_payment_token_result_helper_methods(): void
    {
        // Test isPayable
        $validResult = $this->paymentService->validateToken('validtoken123456789012345678901234');
        $this->assertTrue($validResult->isPayable());

        $expiredResult = $this->paymentService->validateToken('expiredtoken1234567890123456789012');
        $this->assertFalse($expiredResult->isPayable());

        $paidResult = $this->paymentService->validateToken('paidtoken123456789012345678901234');
        $this->assertFalse($paidResult->isPayable());

        // Test isExpired
        $this->assertFalse($validResult->isExpired());
        $this->assertTrue($expiredResult->isExpired());
        $this->assertFalse($paidResult->isExpired());

        // Test isPaid
        $this->assertFalse($validResult->isPaid());
        $this->assertFalse($expiredResult->isPaid());
        $this->assertTrue($paidResult->isPaid());
    }

    /**
     * Test PaymentResult DTO toArray method
     */
    public function test_payment_result_to_array(): void
    {
        $cardData = [
            'cardholder_name' => 'John Doe',
            'card_number' => '****************',
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'cvv' => '123',
        ];

        $result = $this->paymentService->processPayment('validtoken123456789012345678901234', $cardData);
        $array = $result->toArray();

        $this->assertIsArray($array);
        $this->assertArrayHasKey('success', $array);
        $this->assertArrayHasKey('transactionId', $array);
        $this->assertArrayHasKey('errorMessage', $array);
        $this->assertArrayHasKey('errorCode', $array);
        $this->assertTrue($array['success']);
    }
}
